import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { MaterialLayers } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from 'src/app/admin-pages/new-quotation/summary-sales-order.service';
import { SnakbarService } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { PartNumberMasterDTO, UniqueIdentifier } from './manage-part-number-model';
@Component({
  selector: 'app-manage-part-number-master',
  templateUrl: './manage-part-number-master.component.html',
  styleUrls: ['./manage-part-number-master.component.css']
})
export class ManagePartNumberMasterComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  masterPartNumber: PartNumberMasterDTO;
  materialLayersList: MaterialLayers[];
  partNumberController: PartNumberMasterDTO;
  partNumber: PartNumberMasterDTO;
  title = '';
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  showLoader = false;
  identifierList: UniqueIdentifier;
  selectedFacingIdentifier: string[] = [];
  selectedLinerIdentifier: string[] = [];
  selectedInsulationIdentifier: string[] = [];
  constructor(
    public readonly dialogRef: MatDialogRef<ManagePartNumberMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data: any,
    private readonly masterDataService: MasterdataManagementService,
    private readonly salesOrderSummaryService: SalesOrderSummaryService,
    private readonly snakbarService: SnakbarService,
  ) {
    this.masterPartNumber = data;
    this.selectedFacingIdentifier = data.facings;
    this.selectedInsulationIdentifier = data.insulations;
    this.selectedLinerIdentifier = data.liners;
  }
  ngOnInit() {
    this.masterPartNumber = this.masterPartNumber.id ? Object.assign({}, this.masterPartNumber) : new PartNumberMasterDTO();
    this.masterPartNumber.id ? (this.title = 'Update Part Number Master') : (this.title = 'Add Part Number Master');
    this.getMaterialLayersList();
  }

  getMaterialLayersList() {
    this.showLoader = true;
    this.salesOrderSummaryService.getMaterialLayerIdentifiers().subscribe(
      (res) => {
        this.identifierList = res;
        this.showLoader = false;
      },
      () => (this.showLoader = false)
    );
  }

  facingIdentifierType(identifierObj, selected) {
    if (selected) {
      this.selectedFacingIdentifier.push(identifierObj);
    } else {
      this.selectedFacingIdentifier.splice(selected, 1)
    }
  }

  linerIdentifierType(identifierObj, selected) {
    if (selected) {
      this.selectedLinerIdentifier.push(identifierObj);
    } else {
      this.selectedLinerIdentifier.splice(selected, 1)
    }
  }

  insulationIdentifierType(identifierObj, selected) {
    if (selected) {
      this.selectedInsulationIdentifier.push(identifierObj);
    } else {
      this.selectedInsulationIdentifier.splice(selected, 1)
    }
  }


  updateMasterPartNumber() {
    this.showLoader = true;
    if (this.masterPartNumber.id) {
      this.masterPartNumber.facings = this.selectedFacingIdentifier;
      this.masterPartNumber.liners = this.selectedLinerIdentifier;
      this.masterPartNumber.insulations = this.selectedInsulationIdentifier;
      this.subscription.add(
        this.masterDataService.updateMasterPartNumber(this.masterPartNumber).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
        )
      );
    } else {
      this.masterPartNumber.facings = this.selectedFacingIdentifier;
      this.masterPartNumber.liners = this.selectedLinerIdentifier;
      this.masterPartNumber.insulations = this.selectedInsulationIdentifier;
      this.subscription.add(
        this.masterDataService.addMasterPartNumber(this.masterPartNumber).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            if (error.applicationStatusCode === 1227) {
              this.snakbarService.error(error.message);
            }
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog() {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}