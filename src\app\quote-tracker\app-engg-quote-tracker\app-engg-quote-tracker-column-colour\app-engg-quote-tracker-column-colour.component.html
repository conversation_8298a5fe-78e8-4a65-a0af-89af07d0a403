<h2 mat-dialog-title>Select Colour To Highlight Column
  <hr>
</h2>

<form #colourSelectForm="ngForm" role="form">
<div class="container-fluid">
  <mat-form-field fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="50" fxFlex.gt-xs="100">
    <mat-select placeholder="Select Colour" name = "Select Color" #selectedColor>
      <mat-option *ngFor="let color of highlightedColors" [value]="color?.value" [ngStyle]="{'background': color?.value}">
        {{ color?.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>&nbsp;&nbsp;&nbsp;
  <mat-form-field fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="50" fxFlex.gt-xs="100">
    <mat-select placeholder="Select Column" name = "Select Column" #selectedColumn>
      <mat-option *ngFor="let column of columnList" [value]="column?.value">
        {{ column?.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>
</div>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" [disabled]="!selectedColor?.value" (click)="selectColumn(selectedColor?.value, selectedColumn?.value)">Select Colour</button>
    <button mat-raised-button type="submit" (click)="closeDialog(false)">Cancel</button>
  </mat-dialog-actions>
</form>

