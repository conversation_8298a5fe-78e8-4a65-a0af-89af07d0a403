import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable()
export class SnakbarService {
  constructor(private snackBar: MatSnackBar) {}

  public success(message, durationMs = 2500) {
    this.snackBar.open(message, 'Close', {
      duration: durationMs,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: ['green-snackbar'],
    });
  }

  public error(message, durationMs = 2500) {
    this.snackBar.open(message, 'Close', {
      duration: durationMs,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: ['red-snackbar'],
    });
  }

  public successMS(message, duration: number) {
    this.snackBar.open(message, 'Close', {
      duration: duration,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: ['green-snackbar'],
    });
  }
}
