@import '~bootstrap/dist/css/bootstrap.css';
.sticky-table-header {
  background-color: #f1f1f1;
  color: #0000008a;
  z-index: 1000 !important;
  position: sticky;
}

tr td {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  border: 1px solid black;
  padding: 0px !important;
}

/* table {
  table-layout:fixed;
} */

.overflow-text-css.highlighted {
  background: green;
}

.dynamic-virtual-scroll {
  height: 40vh !important;
}

.highlight{
  background: #42A948; /* green */
}

.link {
  color: black;
  font-weight: bold;
  text-decoration: underline;
}

.td_size{
  min-width:105px;
}

.td_size_comments{
text-align: left;
}

.td_size_projectTitle{
  min-width: 180px;
}

.td-size-productType{
  min-width: 100px;
}

.td_size_shipDate{
  min-width: 80px;
}

.td_size_designer{
  min-width: 80px;
}

.td_size_ofa{
  min-width: 80px;
}

.td_size_app{
  min-width: 135px;
}

.td_size_action{
  min-width: 15px;
}

.link:hover {
  color: black;
  text-decoration: none;
  text-decoration: underline;
}
