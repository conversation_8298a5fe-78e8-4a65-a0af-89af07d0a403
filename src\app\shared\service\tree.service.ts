import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { TreeData } from '../component/tree/tree-data.model';
import { SharedService } from './shared.service';

@Injectable({
  providedIn: 'root'
})
export class TreeFunctionService {
  constructor(private readonly http: HttpClient, private readonly sharedService: SharedService) {}

  // helping function to flatten a json tree object array to flat arrays of objects.
  flatJsonArray(flattenedAray: Array<TreeData>, node: TreeData[]) {
    const array: Array<TreeData> = flattenedAray;
    node.forEach(element => {
      if (element.Children) {
        array.push(element);
        this.flatJsonArray(array, element.Children);
      }
    });
    return array;
  }

  // used to find the max ID present in the tree array.
  findNodeMaxId(node: TreeData[]) {
    if (node.length === 0) {
      return 0;
    }
    const flatArray = this.flatJsonArray([], node);
    const flatArrayWithoutChildren = [];
    flatArray.forEach(element => {
      flatArrayWithoutChildren.push(element.Id);
    });
    const maxId = Math.max(...flatArrayWithoutChildren);
    return maxId;
  }

  findPosition(id: number, data: TreeData[]) {
    for (let i = 0; i < data.length; i += 1) {
      if (id === data[i].Id) {
        return i;
      }
    }
  }

  // finds the parent of the given node using it's Id and using recursion.
  // if found returns an array containing an the parent object and the index of it.
  findFatherNode(id: number, data: TreeData[]) {
    for (let i = 0; i < data.length; i += 1) {
      const currentFather = data[i];
      for (let z = 0; z < currentFather.Children.length; z += 1) {
        if (id === currentFather.Children[z]['Id']) {
          return [currentFather, z];
        }
      }
      for (let z = 0; z < currentFather.Children.length; z += 1) {
        if (id !== currentFather.Children[z]['Id']) {
          const result = this.findFatherNode(id, currentFather.Children);
          if (result !== false) {
            return result;
          }
        }
      }
    }
    return false;
  }

  // this function will convert a structure of P:\Folder1\Folder2 to a structure that is supported by angular tree.
  // this will only work if the a single path. not used at present.
  getJsonArrayFromFilePathString(filePath: string) {
    const filePathArray = filePath.split('\\').filter(char => char !== '');
    const treeObj = new TreeData();
    return this.addDataToArray(treeObj, 0, filePathArray);
  }

  // recursive function to get the json data from such path P:\Folder1\Folder2
  addDataToArray(mainObj: TreeData, index: number, filePathArray: string[]) {
    if (index > filePathArray.length) {
      return;
    }
    if (index === 0) {
      mainObj.Id = index + 1;
      mainObj.Name = filePathArray[index];
      index++;
    }
    if (filePathArray.length !== index) {
      mainObj.Children[0] = new TreeData();
      mainObj.Children[0].Id = index + 1;
      mainObj.Children[0].Name = filePathArray[index];
    }
    index++;

    this.addDataToArray(mainObj.Children[0], index, filePathArray);
    return mainObj;
  }

  // this function is used to get the path of the selectedNode to rootNode
  getPathFromSelectedNodeRootNode(selectedNode: TreeData, tree: TreeData[], initialPath?: string) {
    let path = '';
    let basePathExists = false;
    let obj = selectedNode;
    let oldObj: TreeData;
    do {
      oldObj = obj;
      obj = this.findFatherNode(oldObj.Id, tree);

      if (obj[0]) {
        if (basePathExists) {
          path = obj[0].Name + '\\' + path;
        } else {
          path = obj[0].Name + '\\' + path + '\\' + selectedNode.Name;
          basePathExists = true;
        }
      }
      Object.keys(obj).forEach(key => {
        if (key === '0') {
          obj = obj[0];
        }
      });
    } while (obj);

    return path ? path : initialPath;
  }

  enumerateFolder(basePath: string) {
    return this.http.post(AppConfig.enumPathURL, {
      BasePath: basePath,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations()
    });
  }

  addFolderToSelectedPath(selectedPath: string) {
    return this.http.post(AppConfig.addFolderPathURL, {
      BasePath: selectedPath,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations()
    });
  }

  deleteFolderFromSelectedPath(selectedPath: string) {
    return this.http.post(AppConfig.deletedFolderPathURL, {
      BasePath: selectedPath,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations()
    });
  }

  renameFolderFromSelectedPath(oldPath: string, newPath: string) {
    return this.http.post(AppConfig.renameFolderPathURL, {
      BasePath: oldPath,
      RenamedPath: newPath,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations()
    });
  }
}
