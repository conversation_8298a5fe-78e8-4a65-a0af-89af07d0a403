<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Tape Type</mat-label>
          <input matInput [(ngModel)]="goldStandardTapeWidthFilter.tapeType" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldTapeType)"
            *ngIf="goldStandardTapeWidthFilter.tapeType"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addGoldStandardTapeWidth()">Add New Gold Standard Tape Width</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="goldStandardTapeWidthDataSource"
        (matSortChange)="getGoldStandardTapeWidthSorting($event)"
      >
        <ng-container matColumnDef="tapeType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="60"> Tape Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="60"> {{ element?.tapeType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="width">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Width </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.width }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editGoldStandardTapeWidth(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteGoldStandardTapeWidth(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="goldStandardTapeWidthColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: goldStandardTapeWidthColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!goldStandardTapeWidthDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getGoldStandardTapeWidthPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
