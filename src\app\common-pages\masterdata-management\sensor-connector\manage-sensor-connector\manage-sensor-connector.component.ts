import { Component, OnInit, Inject, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { SensorConnectorsAndTypesMaster, SensorConnectorsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-sensor-connector',
  templateUrl: './manage-sensor-connector.component.html'
})
export class ManageSensorConnectorComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  sensorConnector: SensorConnectorsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageSensorConnectorComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.sensorConnector = data;
    this.sensorConnector.id ? (this.sensorConnector.previousId = this.sensorConnector.id) : (this.sensorConnector.previousId = null);
  }

  ngOnInit() {
    this.sensorConnector = this.sensorConnector.id ? Object.assign({}, this.sensorConnector) : new SensorConnectorsMaster();
    this.sensorConnector.id ? (this.title = 'Update Sensor Connectors') : (this.title = 'Add Sensor Connectors');
  }

  updateSensorConnectors() {
    this.showLoader = true;
    if (this.sensorConnector.previousId) {
      this.subscription.add(
        this.masterDataService.updateSensorConnectors(this.sensorConnector).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addSensorConnectors(this.sensorConnector).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
