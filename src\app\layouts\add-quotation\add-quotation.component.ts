import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Quotation } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { Messages } from 'src/app/shared/constants/messages.constants';
import { Route } from 'src/app/shared/constants/router.constants';
import { TopbarComponent } from '../topbar/topbar.component';
import { CustomerDTO } from './../../admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from './../../admin-pages/new-quotation/summary-sales-order.service';
import { SweetAlertService } from './../../shared/service/sweet-alert.service';
import { AddQuotationService } from './add-quotation.service';

@Component({
  selector: 'sfl-add-quotation',
  templateUrl: './add-quotation.component.html'
})
export class AddQuotationComponent implements OnInit, OnDestroy {
  quoteNumber: string;
  subscription = new Subscription();
  quotation: Quotation;

  constructor(
    public readonly dialogRef: MatDialogRef<TopbarComponent>,
    private readonly router: Router,
    private readonly addQuotationService: AddQuotationService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly salesOrderSummaryService: SalesOrderSummaryService
  ) {}

  ngOnInit() {}

  saveQuotation() {
    this.subscription.add(
      this.addQuotationService.findQuotation(this.quoteNumber).subscribe(res => {
        if (res) {
          this.subscription.add(
            this.addQuotationService.findFromBHX(this.quoteNumber).subscribe(response => {
              if (response) {
                // call bhx with this.quoteNUmber
                this.closeDialog();
                this.router.navigate([Route.APP_ENGG.ccdc], { queryParams: { quotId: response } });
              } else {
                // import from epicor & save in BHX and redirect
                this.findCusotmerDetail(this.quoteNumber);
              }
            })
          );
        } else {
          // create in BHX
          this.subscription.add(
            this.addQuotationService.findFromBHX(this.quoteNumber).subscribe(response => {
              if (response) {
                this.closeDialog();
                this.router.navigate([Route.APP_ENGG.ccdc], { queryParams: { quotId: response } });
              } else {
                this.createNewQuotation(this.quoteNumber);
              }
            })
          );
        }
      })
    );
  }

  saveOrderInfo() {
    this.quotation.activated = true;
    this.quotation.customerClarificationRequired = false;
    this.quotation.externalQuoteRequired = false;
    this.subscription.add(
      this.salesOrderSummaryService.saveOrUpdateQuotation(this.quotation, this.quotation.id).subscribe((res: Quotation) => {
        this.closeDialog();
        this.router.navigate([Route.APP_ENGG.ccdc], { queryParams: { quotId: res.id } });
      })
    );
  }

  async findCusotmerDetail(id) {
    this.subscription.add(
      this.salesOrderSummaryService.getQuotation(id).subscribe(async (response: Quotation) => {
        // if we are able to find the customer info fetch that and save the quote with customer details
        if (response.customerDTO != null) {
          this.quotation = response;
          this.saveOrderInfo();
        } else {
          // take confirmation whether to create quotation without any customer assigned to the quote.
          if (await this.sweetAlertService.createQuotationAlert(Messages.missing_customer_info_sweet_error)) {
            this.createQuoteInBHX(id);
          }
        }
      })
    );
  }

  async createNewQuotation(quoteNumber) {
    if (await this.sweetAlertService.createQuotationAlert(Messages.quotation_sweet_error)) {
      this.createQuoteInBHX(quoteNumber);
    }
  }

  private createQuoteInBHX(quoteNumber) {
    this.quotation = new Quotation();
    this.quotation.customerDTO = new CustomerDTO();
    this.quotation.quotationNumber = quoteNumber;
    this.quotation.activated = true;
    this.subscription.add(
      this.salesOrderSummaryService.saveOrUpdateQuotation(this.quotation, this.quotation.id).subscribe((res: Quotation) => {
        this.closeDialog();
        this.router.navigate([Route.APP_ENGG.ccdc], { queryParams: { quotId: res.id } });
      })
    );
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
