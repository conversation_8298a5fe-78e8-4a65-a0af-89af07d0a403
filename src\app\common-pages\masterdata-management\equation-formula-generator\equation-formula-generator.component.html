<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
    class="sfl-global-spinner-loader"
    [mode]="mode"
    [color]="color"
    [diameter]="spinnerDiameter"
  >
  </mat-progress-spinner>
</div>

<h2 mat-dialog-title>
  {{ title }}
  <hr/>
</h2>
<form class="forms_form" #formulaForm="ngForm" (ngSubmit)="saveFormula()">
  <mat-dialog-content>
    <div class="mb-10 formula-btn-1" fxLayout="row wrap">
      <div fxLayout="column" fxLayoutAlign="start" fxFlex="70%">
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.CLOSURE_LENGTH)"
          >
            Closure Length
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.LEAD_LENGTH)"
          >
            Lead Length
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.SMALL_BAR)"
          >
            Small Bar
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.THERMOSTAT_COUNT)"
          >
            Thermostat Count
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.JUMPER_QTY)"
          >
            Jumper Quantity
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.BIG_BAR)"
          >
            Big Bar
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.JACKET_LENGTH)"
          >
            Jacket Length
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.JUMPER_LEAD_LENGTH)"
          >
            Jumper Lead Length
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.DOUBLE_BAR)"
          >
            Double Bar
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.TAPE_THREAD)"
          >
            Tape Thread
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isOtherBtnEnabled"
            (click)="addExpressionVariable(Formula.LAYER)"
          >
            Layer
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.WELDMEN_LENGTH)"
          >
            Weldment Length
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.WELDMEN_DIAMETER)"
          >
            Weldment Diameter
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.SILICONE_WIDTH)"
          >
            Silicone Width
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isOtherBtnEnabled"
            (click)="addExpressionVariable(Formula.REMOVABLE_LAYER)"
          >
            Removable Layer
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.ROUND_UP)"
          >
            Round Up
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.ROUND_DOWN)"
          >
            Round Down
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpressionVariable(Formula.REMOVABLE_QTY)"
          >
            Removable Qty
          </button>
        </div>
      </div>
      <div fxLayout="column" fxLayoutAlign="end" fxFlex="30%">
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_1)"
          >
            1
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_2)"
          >
            2
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_3)"
          >
            3
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_4)"
          >
            4
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_5)"
          >
            5
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_6)"
          >
            6
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_7)"
          >
            7
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_8)"
          >
            8
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_9)"
          >
            9
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.NUM_0)"
          >
            0
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Numbers.DEC)"
          >
            .
          </button>
          <button
            type="button"
            [disabled]="isLayerBtnEnabled"
            mat-stroked-button
            color="warn"
          >
            ROOT
          </button>
        </div>
        <div class="mb-10">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            (click)="clearExpression()"
            matTooltip="Clear Expression Screen"
          >
            Clear
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            (click)="backspace()"
            matTooltip="Delete Expression (Backspace <-)"
          >
            X
          </button>
        </div>
      </div>
    </div>
    <div class="mb-10" fxLayout="row wrap">
      <div fxLayout="column" fxLayoutAlign="start">
        <div>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Operators.PLUS)"
          >
            +
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Operators.MINUS)"
          >
            -
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Operators.MUL)"
          >
            *
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Operators.DIV)"
          >
            /
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Braces.OP_SquareBraces)"
          >
            [
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Braces.CL_SquareBraces)"
          >
            ]
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Braces.OP_RoundBraces)"
          >
            (
          </button>
          <button
            type="button"
            mat-stroked-button
            color="warn"
            [disabled]="isLayerBtnEnabled"
            (click)="addExpression(Braces.CL_RoundBraces)"
          >
            )
          </button>
        </div>
      </div>
    </div>

    <div class="mb-10" fxLayout="row">
      <mat-form-field>
        <textarea
          matInput
          placeholder="Expression"
          [(ngModel)]="strExpression"
          name="formulaExpression"
          #formulaExpressionInput="ngModel"
          rows="3"
          readonly
          required
          class="formular-text"
        >
        </textarea>
      </mat-form-field>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">
        Cancel
      </button>
    </div>
    <div fxLayoutAlign="end">
      <div class="sfl-add-formula-btn">
        <span matTooltip="Validate Your Expression">
          <button
            type="button"
            mat-stroked-button
            color="warn"
            (click)="validateExpression()"
            [disabled]="!strExpression"
          >
            Validate
          </button>
        </span>
        <span
          matTooltip="In order to add formula to material please validate first"
        >
          <button
            mat-raised-button
            type="submit"
            color="warn"
            [disabled]="!formulaForm.valid || !allowAddFormula"
          >
            Add Formula
          </button>
        </span>
      </div>
    </div>
  </mat-dialog-actions>
</form>
