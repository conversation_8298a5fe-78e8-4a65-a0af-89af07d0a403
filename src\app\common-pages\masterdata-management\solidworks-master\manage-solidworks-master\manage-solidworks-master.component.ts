import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { SnakbarService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { AccessoryControllerMaster, InstallationMethod, PlugMaster, SensorConnectorsAndTypesMaster } from '../../masterdata-management.model';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { SolidWorksBlockMasterDTO } from '../solidworks-master.model';

@Component({
  selector: 'app-manage-solidworks-master',
  templateUrl: './manage-solidworks-master.component.html',
  styleUrls: ['./manage-solidworks-master.component.css']
})
export class ManageSolidworksMasterComponent implements OnInit, OnDestroy {

  subscription = new Subscription();
  showLoader = false;
  title = ''
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  solidWorksBlockMaster: SolidWorksBlockMasterDTO;

  jacketType = [];

  selectedJacketTypes = [];
  selectedPhase = [];
  selectedPlugs = [];
  selectedConnectors = [];
  selectedControlTypes = [];
  selectedApprovalLevels = [];
  selectedProductTypes = [];

  phaseTypes: object = Values.PhaseTypeConst;
  productTypes: object = Values.ProductTypeConst;
  jacketTypes: object = Values.JacketTypeConst;
  plugs: PlugMaster;
  approvalLevels = Values.approvalLevels
  accessoryControllers: AccessoryControllerMaster[];

  checkBoxYesLabel = Values.CheckboxLabels.YES;
  checkBoxNoneLabel = Values.CheckboxLabels.NONE;
  checkBoxNullLabel = Values.CheckboxLabels.NULL;
  RedLightCheckBoxTitle = Values.SolidWorksBlock_CheckBox_Titles.RedLight;
  GreenLightCheckBoxTitle = Values.SolidWorksBlock_CheckBox_Titles.GreenLight;
  AnySensorCheckBoxTitle = Values.SolidWorksBlock_CheckBox_Titles.AnySensor;
  AnyThermostatCheckBoxTitle = Values.SolidWorksBlock_CheckBox_Titles.AnyThermostat;


  installationMethods: InstallationMethod[];
  sensorTypes: SensorConnectorsAndTypesMaster;

  constructor(public readonly dialogRef: MatDialogRef<ManageSolidworksMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly masterDataService: MasterdataManagementService) {
    this.solidWorksBlockMaster = data;
  }

  ngOnInit() {
    this.solidWorksBlockMaster = this.solidWorksBlockMaster.id ? Object.assign({}, this.solidWorksBlockMaster) : new SolidWorksBlockMasterDTO();
    if (this.solidWorksBlockMaster.id) {
      this.title = 'Update SolidWorks Block Master';
      this.selectedJacketTypes = this.solidWorksBlockMaster.elementType ? this.solidWorksBlockMaster.elementType.split(',') : [];
      this.selectedProductTypes = this.solidWorksBlockMaster.productType ? this.solidWorksBlockMaster.productType.split(',') : [];
      this.selectedConnectors = this.solidWorksBlockMaster.connector ? this.solidWorksBlockMaster.connector.split(',') : [];
      this.selectedControlTypes = this.solidWorksBlockMaster.controlType ? this.solidWorksBlockMaster.controlType.split(',') : [];
      this.selectedPlugs = this.solidWorksBlockMaster.plug ? this.solidWorksBlockMaster.plug.split(',') : [];
      this.selectedPhase = this.solidWorksBlockMaster.phase ? this.solidWorksBlockMaster.phase.split(',') : [];
      this.selectedApprovalLevels = this.solidWorksBlockMaster.approvalLevel ? this.solidWorksBlockMaster.approvalLevel.split(',') : [];
      this.solidWorksBlockMaster.solidWorksBlockSensorInformations.forEach(sensor => {
        sensor.connectorsArray = sensor.connector ? sensor.connector.split(",") : []
        sensor.locationArray = sensor.location ? sensor.location.split(",") : []
        sensor.tempTypeArray = sensor.tempType ? sensor.tempType.split(",") : []
        sensor.typeArray = sensor.type ? sensor.type.split(",") : []
      });
      this.solidWorksBlockMaster.solidWorksBlockThermostatInformations.forEach(thermostat => {
        thermostat.thermostatTypeArray = thermostat.thermostatType ? thermostat.thermostatType.split(",") : []
        thermostat.installationMethodArray = thermostat.installationMethod ? thermostat.installationMethod.split(",") : []
      });
    } else {
      this.title = 'Add SolidWorks Block Master';
    }
    this.getControlerTypeMasterData();
    this.getPlugsAndConnectors();
    this.getSensorsTypes();
    this.getInstallationMethods();
  }

  // gets the plug by currently active jacket type, if we switch jacket type from UI it will set plug and connector to null
  getPlugsAndConnectors() {
    this.subscription.add(
      this.masterDataService.getPlugList().subscribe((plugs: PlugMaster) => {
        this.plugs = plugs;
      },
      (error) => {
        if (error.applicationStatusCode === 1230) {
          this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  // gets the available control types
  getControlerTypeMasterData() {
    this.subscription.add(
      this.masterDataService.getAccessoryControllersList().subscribe((accessoryControllers: AccessoryControllerMaster[]) => {
        this.accessoryControllers = accessoryControllers;
      })
    );
  }

  getSensorsTypes() {
    this.subscription.add(
      this.masterDataService.getSensorTypesList().subscribe((sensorTypes: SensorConnectorsAndTypesMaster) => {
        this.sensorTypes = sensorTypes;
      })
    );
  }

  // gets the available installation methods
  getInstallationMethods() {
    this.subscription.add(
      this.masterDataService.getInstallationMethods().subscribe((installationMethods: InstallationMethod[]) => {
        this.installationMethods = installationMethods;
      })
    );
  }


  async updateSolidWorksBlockMaster() {
    this.showLoader = true;
    await this.prepareSolidWorksBlockMasterObject();
    if (this.solidWorksBlockMaster.id) {
      this.subscription.add(
        this.masterDataService.updateSolidWorksBlockMaster(this.solidWorksBlockMaster).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addSolidWorksBlockMaster(this.solidWorksBlockMaster).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  prepareSolidWorksBlockMasterObject(): Promise<boolean> {
    return new Promise(resolve => {
      this.solidWorksBlockMaster.elementType = this.selectedJacketTypes.join();
      this.solidWorksBlockMaster.plug = this.selectedPlugs.join();
      this.solidWorksBlockMaster.connector = this.selectedConnectors.join();
      this.solidWorksBlockMaster.phase = this.selectedPhase.join();
      this.solidWorksBlockMaster.productType = this.selectedProductTypes.join();
      this.solidWorksBlockMaster.controlType = this.selectedControlTypes.join();
      this.solidWorksBlockMaster.approvalLevel = this.selectedApprovalLevels.join();

      // if values are empty string `""` set it to `null`
      // Object.entries get us the array of this.solidWorksBlockMaster then we can iterate over it and get the key value pair as array[0] and array[1] respectively
      Object.entries(this.solidWorksBlockMaster).forEach(property => {
        if (typeof property[1] === 'string' && property[1] === '') {
          this.solidWorksBlockMaster[property[0]] = null;
        }
      });
      resolve();
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  // the method is used to set the specified field will be set to Null, True or False
  setCheckBoxTriStateValues(fieldValue: boolean, field: string) {
    switch (fieldValue) {
      case true: {
        this.checkFieldWhichNeedsToBeUpdated(field, false);
        break;
      }
      case false: {
        this.checkFieldWhichNeedsToBeUpdated(field, null);
        break;
      }
      case null: {
        this.checkFieldWhichNeedsToBeUpdated(field, true);
        break;
      }
    }
  }

  // Takes the field and it's value to be updated with and sets the Null, False or True supplied as `valueToUpdate`
  checkFieldWhichNeedsToBeUpdated(field: string, valueToUpdate: boolean) {
    switch (field) {
      case this.RedLightCheckBoxTitle:
        this.solidWorksBlockMaster.redLight = valueToUpdate;
        break;
      case this.GreenLightCheckBoxTitle:
        this.solidWorksBlockMaster.greenLight = valueToUpdate;
        break;
      case this.AnySensorCheckBoxTitle:
        this.solidWorksBlockMaster.anySensor = valueToUpdate;
        break;
      case this.AnyThermostatCheckBoxTitle:
        this.solidWorksBlockMaster.anyThermostat = valueToUpdate;
        break;
    }
  }

  closeDialog(done: boolean): void {
    this.dialogRef.close(done);
  }

}
