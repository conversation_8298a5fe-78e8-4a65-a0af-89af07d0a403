
import {catchError, map} from 'rxjs/operators';
import { Revision, Units } from '../ccdc-model/ccdc.model';
import { AppConfig } from '../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';

@Injectable()
export class ManageUnitsService {

    constructor(private http: HttpClient) { }

    saveUnit(quotationId: number, units: Units) {
        return this.http.put(AppConfig.SET_UNITS + quotationId, units).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

  getUnit(jacketGroupId: number) {
    return this.http.get(AppConfig.GET_UNITS_JACKET_GROUP + jacketGroupId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

    getUnitByJacketId(jacketId: number) {
        return this.http.get(AppConfig.GET_UNITS_JACKET + jacketId).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

  getUnitByQuotationId(quotationId: number) {
    return this.http.get(AppConfig.GET_UNITS_QUOTE + quotationId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
