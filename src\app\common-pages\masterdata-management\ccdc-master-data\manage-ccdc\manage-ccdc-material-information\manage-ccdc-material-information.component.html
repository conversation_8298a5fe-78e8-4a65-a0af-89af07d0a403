<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Material Information
  <hr />
</h2>
<mat-dialog-content>
  <form class="cust_table" #materialForm="ngForm" (ngSubmit)="addLayer()" novalidate>
    <div fxLayout="column">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxLayout="column wrap" fxFlex="49">
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
          <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="100">
            <mat-select placeholder="Layer" name="layer" [(ngModel)]="materialLayerObject.layerName" required>
              <mat-option *ngFor="let layer of layers" [value]="layer.name">
                {{ layer?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Material Name"
              name="material"
              (selectionChange)="onChangeMaterial($event.value)"
              [disabled]="materialLayerObject.layerName == null"
              [(ngModel)]="materialLayerObject.materialId"
              required
            >
              <mat-option *ngFor="let value of materialLayersList" [value]="value.materialId">
                {{ value?.material }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field
            fxFlex.gt-lg="32"
            fxFlex.gt-md="32"
            fxFlex.gt-sm="16"
            fxFlex.gt-xs="100"
            *ngIf="materialLayerObject.material !== 'Other'"
          >
            <input matInput placeholder="Part Number" name="partnumber" [(ngModel)]="materialLayerObject.partNumber" readonly />
          </mat-form-field>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
          <mat-form-field
            fxFlex.gt-lg="32"
            fxFlex.gt-md="32"
            fxFlex.gt-sm="32"
            fxFlex.gt-xs="100"
            *ngIf="materialLayerObject.material !== 'Other'"
          >
            <input
              *ngIf="tempUnit === '°F'"
              matInput
              placeholder="Max Temp ({{ tempUnit ? tempUnit : '' }})"
              name="maxtempF"
              [(ngModel)]="materialLayerObject.maxTempF"
              readonly
            />
            <input
              *ngIf="tempUnit !== '°F'"
              matInput
              placeholder="Max Temp ({{ tempUnit ? tempUnit : '' }})"
              name="maxtemp"
              [(ngModel)]="materialLayerObject.maxTemp"
              readonly
            />
          </mat-form-field>
          <mat-form-field
            fxFlex.gt-lg="32"
            fxFlex.gt-md="32"
            fxFlex.gt-sm="32"
            fxFlex.gt-xs="100"
            *ngIf="materialLayerObject.material !== 'Other'"
          >
            <input matInput placeholder="Cost / Sq. Ft." name="cost" [(ngModel)]="materialLayerObject.costPerSq" readonly />
          </mat-form-field>
          <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayoutAlign="start" class="mt-10">
          <button mat-raised-button type="submit" [disabled]="materialForm.form.invalid" color="warn">Add Layer</button>
        </div>
      </div>
      <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxLayoutAlign="center center">
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <img class="jumper-product-img" src="{{ imageUrl }}" alt="" />
        </div>
      </div>
    </div>
    </div>
  </form>
  <div class="highlight-mat-table cust_table mt-10">
    <div fxLayout="column">
    <mat-table [dataSource]="materialDataSource" *ngIf="materialDataSource.data.length > 0">
      <ng-container matColumnDef="layerName">
        <mat-header-cell *matHeaderCellDef fxFlex="20"> Layer Name </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="20">{{ element?.layerName }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="material">
        <mat-header-cell *matHeaderCellDef fxFlex="30"> Material </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="30">{{
          element?.material === 'Other'
            ? element?.layerName === 'Facing'
              ? element?.otherFacing
              : element?.layerName === 'Insulation'
              ? element?.otherInsulation
              : element?.otherLiner
            : element?.material
        }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="partNumber">
        <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="15">{{
          element?.otherFacingPartNumber
            ? element?.otherFacingPartNumber
            : element?.otherInsulationPartNumber
            ? element?.otherInsulationPartNumber
            : element?.otherLinerPartNumber
            ? element?.otherLinerPartNumber
            : element?.partNumber
        }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="maxTemp">
        <mat-header-cell *matHeaderCellDef fxFlex="15"> Max Temp ({{ tempUnit ? tempUnit : '' }})</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="15">{{ tempUnit === '°F' ? element?.maxTempF : element?.maxTemp }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="costPerSq">
        <mat-header-cell *matHeaderCellDef fxFlex="10"> Cost / Sq. Ft. </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="10">{{
          element?.otherFacingCost
            ? element?.otherFacingCost
            : element?.otherInsulationCost
            ? element?.otherInsulationCost
            : element?.otherLinerCost
            ? element?.otherLinerCost
            : element?.costPerSq
        }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="action">
        <mat-header-cell *matHeaderCellDef fxFlex="5" fxLayoutAlign="center center"> Action </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="5" fxLayoutAlign="center center">
          <mat-icon class="open-doc" (click)="remove(element)">delete</mat-icon>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="order">
        <mat-header-cell *matHeaderCellDef fxFlex="10" fxLayoutAlign="center center"> Order </mat-header-cell>
        <mat-cell *matCellDef="let element; let i = index" fxFlex="10" fxLayoutAlign="center center">
          <mat-icon class="open-doc" (click)="up(i)" matTooltip="Move Up">keyboard_arrow_up</mat-icon>
          <mat-icon class="open-doc" (click)="down(i)" matTooltip="Move Down">keyboard_arrow_down</mat-icon>
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="materialdisplayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: materialdisplayedColumns"> </mat-row>
    </mat-table>
    <div fxFlex="100" class="mt-10">
      <mat-form-field>
        <textarea [(ngModel)]="ccdcMasterData.materialNotes" name="notes" matInput placeholder="Notes" rows="5"></textarea>
      </mat-form-field>
    </div>
  </div>
  </div>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="space-between">
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="end">
      <button mat-raised-button matStepperNext name="saveandnext">Next</button>
    </div>
  </mat-dialog-actions>
</mat-dialog-actions>
