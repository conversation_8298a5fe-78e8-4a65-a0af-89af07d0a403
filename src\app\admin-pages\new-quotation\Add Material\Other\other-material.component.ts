import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material';
import { AddMaterialComponent } from '../add-material.component';
import { MaterialLayers } from '../../ccdc-model/ccdc.model';

@Component({
    selector: 'sfl-other-material',
    templateUrl: './other-material.component.html'
})
export class OtherMaterialComponent implements OnInit {

    layerName = '';
    materialId = 0;
    materialLayer = new MaterialLayers();
    constructor(
        public dialogRef: MatDialogRef<OtherMaterialComponent>,
        private matDialog: MatDialog,
        @Inject(MAT_DIALOG_DATA) data,
    ) {
        this.materialLayer = data.materialObj;
    }

    ngOnInit() { }

    addOtherMaterial() {
        this.materialLayer.maxTemp = null;
        this.materialLayer.partNumber = null;
        this.materialLayer.costPerSq = null;
        this.materialLayer.inventory = null;
        this.dialogRef.close(this.materialLayer);
    }

    closeDialog() {
        this.dialogRef.close();
    }
}
