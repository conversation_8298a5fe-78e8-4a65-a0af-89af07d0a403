<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr/>
</h2>
<form class="forms_form" #plugMasterForm="ngForm" (ngSubmit)="updateEstEngRelDate()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Product Type"
                      [(ngModel)]="estEngRelDate.prodType"
                      name="prodTypes"
                      #prodTypesInput="ngModel">
            <mat-option *ngFor="let productType of productTypes" [value]="productType.id">
              {{ productType.id }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="prodTypesInput.touched && prodTypesInput.invalid">
          <small class="mat-text-warn" *ngIf="prodTypesInput?.errors?.required">Product Type is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Quote Status" [(ngModel)]="estEngRelDate.quoteStatus" name="quoteStatus"
                      #quoteStatusInput="ngModel" required>
            <mat-option *ngFor="let status of quotStatus" [value]="status.status">
              {{ status.status }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="quoteStatusInput.touched && quoteStatusInput.invalid">
          <small class="mat-text-warn" *ngIf="quoteStatusInput?.errors?.required">Quote Status is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Estimated Eng. Release Date" [(ngModel)]="estEngRelDate.noOfDays" name="noOfDays"
                 #quoteStatusInput="ngModel" required sflIsNumber/>
        </mat-form-field>
        <div *ngIf="quoteStatusInput.touched && quoteStatusInput.invalid">
          <small class="mat-text-warn" *ngIf="quoteStatusInput?.errors?.required">Estimated Eng. Release Date
            Required</small>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!plugMasterForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
