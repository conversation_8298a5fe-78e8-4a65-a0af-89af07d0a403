import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { GenericPageable, LabelConfigMaster, LabelMasterPartNumberFilter } from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { ManageLabelMasterComponent } from './manage-label-master/manage-label-master.component';

@Component({
  selector: 'app-label-master',
  templateUrl: './label-master.component.html',
  styleUrls: ['./label-master.component.css']
})
export class LabelMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Label Master Configuration';
  length: number;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  labelMasterPageable: GenericPageable<LabelConfigMaster>;
  sortOrder = Variable.defaultSortOrderDescending;
  numberOfElements: number;
  filter = [];
  labelMasterDataSource = new MatTableDataSource<LabelConfigMaster>();
  ccdcMaterialMasterMasterColumns = DisplayColumns.Cols.LabelMasterDataColumn;
  showLoader = false;
  pageSizeOptions = Variable.pageSizeOptions;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  subscription = new Subscription();
  filterFieldKeyName = 'labelPartNumber';
  partNumberFilter: LabelMasterPartNumberFilter = new LabelMasterPartNumberFilter();

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) { }

  ngOnInit() {
    this.getLabelMasterData(this.initialPageIndex, this.initialPageSize);
  }

  getLabelMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getLabelConfigMasterData(this.filter, pageable).subscribe(
        (res: GenericPageable<LabelConfigMaster>) => {
          this.labelMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createLabelMasterMaterialsTable(this.labelMasterPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  async addFilter() {
    this.filter = [
      { key: this.filterFieldKeyName, value: !this.partNumberFilter.labelPartNumber ? '' : this.partNumberFilter.labelPartNumber }
    ];
    this.getLabelMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of plug light listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldKeyName,
        value: fieldToClear === this.filterFieldKeyName ? (this.partNumberFilter.labelPartNumber = '') : this.partNumberFilter.labelPartNumber
      }
    ];
    this.getLabelMasterData(this.initialPageIndex, this.pageSize);
  }

  createLabelMasterMaterialsTable(serviceRequestList: GenericPageable<LabelConfigMaster>) {
    this.labelMasterDataSource.data = serviceRequestList.content;
  }

  getLabelMaterialMasterSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getLabelMasterData(this.pageIndex, this.pageSize);
  }

  getLabelMaterialMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getLabelMasterData(this.pageIndex, this.pageSize);
  }

  addLabelMaster() {
    this.editLabelMaster(new LabelConfigMaster());
  }

  editLabelMaster(labelMaster: LabelConfigMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = labelMaster;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-plug-master-model';
    const dialogRef = this.matDialog.open(ManageLabelMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          labelMaster.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getLabelMasterData(this.pageIndex, this.pageSize);
      }
    })
  }

  async deleteLabelMaster(labelMasterId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteLabelConfigurationMaster(labelMasterId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getLabelMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
