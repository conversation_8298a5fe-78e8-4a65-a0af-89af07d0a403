
import {catchError, tap} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { throwError } from 'rxjs';
import { MatDialog } from '@angular/material';
import { Messages, SnakbarService, SharedService } from '..';
import { Router } from '@angular/router';

class CustomError extends Error {​​​​​
  constructor(message: string) {​​​​​
    super(​​​​​message);
    this.stack = null;
  }​​​​​
}​​​​​
@Injectable()
export class AppInterceptor implements HttpInterceptor {
  constructor(
    private snakbarService: SnakbarService,
    private sharedService: SharedService,
    private dialogRef: MatDialog,
    private router: Router
  ) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    if (req.url.includes('localhost:8069')) {
      req = req.clone();
    } else {
      req = req.clone({
        withCredentials: true
      });
    }
    return next
      .handle(req).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          if (event.status === 205) {
            this.snakbarService.success(Messages.Login.fogot_Password_success);
          }
          this.requestSuccess(event);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        if (error.error instanceof Error) {
          // A client-side or network error occurred. Handle it accordingly.
        } else {
          if (error.status === 401) {
            this.dialogRef.closeAll();
            this.sharedService.logout(this.router.url);
            this.snakbarService.error(Messages.error_msg.session_timeout);
            return throwError(error);
          } else if (error.status === 400) {
            if (error.error.title === Messages.error_msg.invalidPassword) {
              this.snakbarService.error(Messages.Login.invalid_login);
            } else if (error.error.title === Messages.error_msg.usernameused) {
              this.snakbarService.error(Messages.Login.user_exits);
            } else if (error.error.errorKey === Messages.error_msg.emailused) {
              this.snakbarService.error(Messages.Login.email_exits);
            } else if (error.error.errorKey === Messages.error_msg.emailnotreg) {
              this.snakbarService.error(Messages.Login.email_not_exists);
            } else {
              this.snakbarService.error(error.error.message);
            }
          } else if (error.status === 404) {
            this.snakbarService.error(Messages.error_msg.nodata);
          } else if (error.status === 500) {
            if (error.error.ExceptionMessage) {
              this.snakbarService.error(error.error.ExceptionMessage);
            }
            else if(error.error.detail){
              this.snakbarService.error(error.error.detail);
            }
            else {
              this.snakbarService.error(Messages.error.error_msg);
            }
          } else if (error.status === 503) {
            this.router.navigate(['/service-unavailable']);
          }
        }
        if (error.status === 0) {
          this.snakbarService.error(Messages.Solid_Work_API_service_error.solidwork_service_error);
          throw new CustomError(Messages.Solid_Work_API_service_error.solidwork_service_error);
        } else if (error.error.ExceptionMessage) {
          throw new CustomError(error.error.ExceptionMessage);
        } else {
          throw new Error(error.message);
        }
      }));
  }
  private requestSuccess(res: HttpResponse<any>) {
    return res;
  }
}
