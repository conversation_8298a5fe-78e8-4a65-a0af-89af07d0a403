import { PipeTransform, Pipe } from '@angular/core';


@Pipe({
    name: 'compare'
})
export class ComparePipe implements PipeTransform {

    transform(value: string, args: any[]): String {
        const _firstArray = args;
        const _secondArray = args;

        for (let i = 0; i < _firstArray.length; i++) {
            for (let j = 0; j < _secondArray.length; j++) {
                if (_firstArray[i] && _secondArray[j]) {
                    if (Array.isArray(_firstArray[i])) {
                        if (_firstArray[i].join(', ') !== _secondArray[j].join(', ')) {
                            return 'lbl-red';
                        }
                    } else {
                        if (_firstArray[i] !== _secondArray[j]) {
                            return 'lbl-red';
                        }
                    }
                }
            }
        }
    }
}
