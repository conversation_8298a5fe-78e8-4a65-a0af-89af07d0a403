import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { GeometryComponent } from '../geometry.component';

@Component({
  selector: 'app-view-jacket-image',
  templateUrl: './view-jacket-image.component.html',
  styleUrls: ['./view-jacket-image.component.css']
})
export class ViewJacketImageComponent implements OnInit {

  imageUrl: string;

  constructor(
    public  readonly dialogRef: MatDialogRef<GeometryComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
    this.imageUrl = data.imageUrl;
   }

  ngOnInit() {
  }

  closeDialog(): void {
    this.dialogRef.close();
}

}
