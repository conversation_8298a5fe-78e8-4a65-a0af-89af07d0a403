
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from '../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../shared/helpers/app.helper';

@Injectable()
export class AddQuotationService {
  constructor(private http: HttpClient) { }

  findQuotation(quoteNumber: string) {
    return this.http.get(AppConfig.QUOTATION_EXIXTS + '/' + quoteNumber).pipe(map(utils.extractData),
      catchError(utils.handleError),);
  }

  findFromBHX(quoteNumber) {
    return this.http.get(AppConfig.QUOTATION + '/quotationNumber/' + quoteNumber).pipe(map(utils.extractData),
      catchError(utils.handleError),);
  }

}
