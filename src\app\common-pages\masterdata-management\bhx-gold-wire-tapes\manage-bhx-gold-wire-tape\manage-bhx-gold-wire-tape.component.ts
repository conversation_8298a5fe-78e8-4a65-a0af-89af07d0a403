import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { BHXGoldWireTapesMaster, AlloyMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';
import { Router } from '@angular/router';
import { SnakbarService } from 'src/app/shared';

@Component({
  selector: 'sfl-manage-bhx-gold-wire-tape',
  templateUrl: './manage-bhx-gold-wire-tape.component.html'
})
export class ManageBhxGoldWireTapeComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  goldWireTape: BHXGoldWireTapesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  types = Values.BHXGoldWireTapeType;
  wireTypes = Values.BHXGoldWireTapeWireType;
  alloys: AlloyMaster[];
  constructor(
    public readonly dialogRef: MatDialogRef<ManageBhxGoldWireTapeComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly snakbarService: SnakbarService,
    private readonly router: Router
  ) {
    this.goldWireTape = data;
  }

  ngOnInit() {
    this.goldWireTape = this.goldWireTape.id ? Object.assign({}, this.goldWireTape) : new BHXGoldWireTapesMaster();
    this.goldWireTape.id ? (this.title = 'Update Gold Wire Tapes') : (this.title = 'Add Gold Wire Tapes');
    this.getAlloys();
  }

  getAlloys() {
    this.subscription.add(
      this.masterDataService.getAlloysList().subscribe(
        (alloys: AlloyMaster[]) => {
          this.alloys = alloys;
          this.showLoader = false;
        },
        error => {
          if (error.applicationStatusCode === 1211) {
            this.snakbarService.error(error.message);
          }
          this.alloys = [];
          this.showLoader = false;
        }
      )
    );
  }

  manageAlloys() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/alloys']);
  }

  updateGoldWireTape() {
    this.showLoader = true;
    if (this.goldWireTape.id) {
      this.subscription.add(
        this.masterDataService.updateGoldWireTapes(this.goldWireTape).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addGoldWireTapes(this.goldWireTape).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
