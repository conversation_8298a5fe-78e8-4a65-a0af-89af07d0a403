import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { WarpsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { SnakbarService } from 'src/app/shared';

@Component({
  selector: 'sfl-manage-warps-master',
  templateUrl: './manage-warps-master.component.html'
})
export class ManageWarpsMasterComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  warp: WarpsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  formData = new FormData();
  constructor(
    public dialogRef: MatDialogRef<ManageWarpsMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) {
    this.warp = data;
  }

  ngOnInit() {
    this.warp = this.warp.id ? Object.assign({}, this.warp) : new WarpsMaster();
    this.warp.id ? (this.title = 'Update Warp') : (this.title = 'Add Warp');
  }

  updateWarp() {
    this.showLoader = true;
    if (this.warp.id) {
      this.subscription.add(
        this.masterDataService.updateWarps(this.warp).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addWarps(this.warp).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
