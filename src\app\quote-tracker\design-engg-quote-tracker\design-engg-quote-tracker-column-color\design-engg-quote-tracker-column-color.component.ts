import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {Subscription} from 'rxjs';
import {Values} from 'src/app/shared/constants/values.constants';
import {DesignEngineeringQuoteTracker, DesignEngQuoteTrackerColumnColor} from '../../quote-tracker.model';
import {QuoteTrackerService} from '../../quote-tracker.service';
import {DesignEnggQuoteTrackerComponent} from '../design-engg-quote-tracker.component';
import {Statuses} from '../../../design-pages/ecr-management/ecr-management.model';
import {SharedService} from '../../../shared';
import {SalesAssociate} from '../../../design-pages/dashboard/dashboard.model';
import {JacketListService} from '../../../design-pages/jacket-list/jacket-list.service';
import {Variable} from '../../../shared/constants/Variable.constants';
import {EditableTrackerFields} from '../../../design-pages/jacket-list/jacket-list.model';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import {DatePipe} from '@angular/common';

@Component({
  selector: 'app-design-engg-quote-tracker-column-color',
  templateUrl: './design-engg-quote-tracker-column-color.component.html',
  styleUrls: ['./design-engg-quote-tracker-column-color.component.css']
})
export class DesignEnggQuoteTrackerColumnColorComponent implements OnInit {
  highlightedColors = Values.highlightColors;
  quoteId: number;
  subscription: Subscription = new Subscription();
  showLoader = false;
  columnList = Values.designQuoteTrackerColumnList;
  value: string;
  statuses: Statuses[];
  designers: SalesAssociate[] = [];
  countries: object = Values.ManufacturingCountries;
  type = Variable.typeDesign;
  trackerFields: EditableTrackerFields = new EditableTrackerFields();
  trackerFieldsForm: FormGroup;
  columnColorList:DesignEngQuoteTrackerColumnColor[];
  selectedColumnColor:string;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly sharedService: SharedService,
    private readonly jacketListService: JacketListService,
    private readonly formBuilder: FormBuilder,
    private readonly datePipe: DatePipe,
    public readonly dialogRef: MatDialogRef<DesignEnggQuoteTrackerComponent>,
  ) {
    this.quoteId = data.quoteId;
    this.value = data.value;
    this.columnColorList = data.data.designEngQuoteTrackerColumnColorList;
    let column: DesignEngQuoteTrackerColumnColor = this.columnColorList.find(data=> data.designEngQuotationColumnName===this.value);
    if(column!==null && column!==undefined){
      this.selectedColumnColor = column.colorValue;
    }
    else{
      this.selectedColumnColor = 'default';
    }
  }

  ngOnInit() {
    this.getDesignQuoteTrackerDefaultValues();
    this.createEditableTrackerFieldForm();
    if(this.value==='designStatusId'){
      this.getQuoteStatuses(false);
    }
    else if(this.value==='approvalStatusId'){
      this.getQuoteStatuses(true);
    }
    else if(this.value==='designerId'){
      this.getAllDesigners();
    }
  }

  selectColumn() {
    this.showLoader = true;
    if(this.selectedColumnColor!==undefined){
      this.subscription.add(
        this.quoteTrackerService.designColumnHighlighting(this.quoteId, this.value, this.selectedColumnColor).subscribe(
          (res: DesignEngineeringQuoteTracker) => {
            if (res) {
              this.dialogRef.close(res);
              this.saveTrackerFields();
            }
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  getDesignQuoteTrackerDefaultValues() {
    this.showLoader = true;
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService
          .getDesignQuoteTrackerDefaultValues(this.type,this.quoteId)
          .subscribe(
            (defaultValues: EditableTrackerFields) => {
              this.trackerFields = defaultValues;
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            },
            () => {
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            }
          )
      );
    });
  }

  createEditableTrackerFieldForm() {
    this.trackerFieldsForm = this.formBuilder.group({
      quotationId: new FormControl(),
      approvalStatusId: new FormControl(),
      dollarAmount: new FormControl(),
      designLocation: new FormControl(),
      quotationStatusId: new FormControl(),
      ofa1Date: new FormControl(),
      submittedDesignDate: new FormControl(),
      approval1Date: new FormControl(),
      assignedDesignerId: new FormControl(),
      designComments: new FormControl(),
      customNoOfDesigns: new FormControl(),
      soRevCount: new FormControl(),
      revisionDate: new FormControl(),
      lastOFADate: new FormControl(),
      engReleaseDate: new FormControl(),
      designQueueDate: new FormControl(),
      patternDesignDate: new FormControl(),
      level1ReviewDate: new FormControl(),
      level1ReviewDateIp: new FormControl(),
      readyForOfaDate: new FormControl(),
      outForApprovalDate: new FormControl(),
      elementAndBomDate: new FormControl(),
      elementAndBomDateIp: new FormControl(),
      finalReviewDate: new FormControl(),
      finalReviewDateIp: new FormControl(),
      vietnamConversionDate: new FormControl(),
      readyForReleaseDate: new FormControl(),
      releasedDate: new FormControl(),
      simulationDate: new FormControl(),
      simulationDateIp: new FormControl()
    });

  }

  getQuoteStatuses(isSpproval:boolean) {
    this.subscription.add(
      this.sharedService
        .getStatuesByTypeOrderByOrderNumber()
        .subscribe((res: Array<Statuses>) => {
          if (res) {
            this.statuses = [];
            for (const designStatus  of res) {
              if (designStatus.type === "both" || designStatus.type === "design") {
                if(isSpproval){
                  if(designStatus.forApproval!==null && designStatus.forApproval==true){
                    this.statuses.push(designStatus);
                  }
                }
                else{
                  this.statuses.push(designStatus);
                }
              }
            }
          }
        })
    );
  }

  getAllDesigners() {
    this.showLoader = true;
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService.getAllDesigners().subscribe(
          (res: Array<SalesAssociate>) => {
            this.designers = res;
            this.showLoader = false;
            resolve();
          },
          () => {
            resolve();
            this.showLoader = false;
          }
        )
      );
    });
  }

  setDefaultValuesForForm() {
    this.trackerFieldsForm.controls.quotationId.patchValue(this.quoteId);
    this.trackerFieldsForm.controls.designLocation.patchValue(
      this.trackerFields.designLocation
    );
    this.trackerFieldsForm.controls.quotationStatusId.patchValue(
      this.trackerFields.quotationStatusId
    );
    this.trackerFieldsForm.controls.approvalStatusId.patchValue(
      this.trackerFields.approvalStatusId
    );
    this.trackerFieldsForm.controls.dollarAmount.patchValue(
      this.trackerFields.dollarAmount
    );
    this.trackerFieldsForm.controls.soRevCount.patchValue(
      this.trackerFields.soRevCount
    );
    this.trackerFieldsForm.controls.ofa1Date.patchValue(
      this.trackerFields.ofa1Date ? new Date(this.trackerFields.ofa1Date) : ""
    );
    this.trackerFieldsForm.controls.submittedDesignDate.patchValue(
      this.trackerFields.submittedDesignDate ? new Date(this.trackerFields.submittedDesignDate) : ""
    );
    this.trackerFieldsForm.controls.revisionDate.patchValue(
      this.trackerFields.revisionDate ? new Date(this.trackerFields.revisionDate) : ""
    );
    this.trackerFieldsForm.controls.lastOFADate.patchValue(
      this.trackerFields.lastOFADate ? new Date(this.trackerFields.lastOFADate) : ""
    );
    this.trackerFieldsForm.controls.engReleaseDate.patchValue(
      this.trackerFields.engReleaseDate ? new Date(this.trackerFields.engReleaseDate) : ""
    );
    this.trackerFieldsForm.controls.approval1Date.patchValue(
      this.trackerFields.approval1Date
        ? new Date(this.trackerFields.approval1Date)
        : ""
    );
    this.trackerFieldsForm.controls.assignedDesignerId.patchValue(
      this.trackerFields.assignedDesignerId
    );
    this.trackerFieldsForm.controls.designComments.patchValue(
      this.trackerFields.designComments
    );
    this.trackerFieldsForm.controls.customNoOfDesigns.patchValue(
      this.trackerFields.customNoOfDesigns
    );
    this.trackerFieldsForm.controls.designQueueDate.patchValue(
      this.trackerFields.designQueueDate
        ? new Date(this.trackerFields.designQueueDate)
        : ""
    );
    this.trackerFieldsForm.controls.patternDesignDate.patchValue(
      this.trackerFields.patternDesignDate
        ? new Date(this.trackerFields.patternDesignDate)
        : ""
    );
    this.trackerFieldsForm.controls.level1ReviewDate.patchValue(
      this.trackerFields.level1ReviewDate
        ? new Date(this.trackerFields.level1ReviewDate)
        : ""
    );
    this.trackerFieldsForm.controls.level1ReviewDateIp.patchValue(
      this.trackerFields.level1ReviewDateIp
        ? new Date(this.trackerFields.level1ReviewDateIp)
        : ""
    );
    this.trackerFieldsForm.controls.readyForOfaDate.patchValue(
      this.trackerFields.readyForOfaDate
        ? new Date(this.trackerFields.readyForOfaDate)
        : ""
    );
    this.trackerFieldsForm.controls.outForApprovalDate.patchValue(
      this.trackerFields.outForApprovalDate
        ? new Date(this.trackerFields.outForApprovalDate)
        : ""
    );
    this.trackerFieldsForm.controls.elementAndBomDate.patchValue(
      this.trackerFields.elementAndBomDate
        ? new Date(this.trackerFields.elementAndBomDate)
        : ""
    );
    this.trackerFieldsForm.controls.elementAndBomDateIp.patchValue(
      this.trackerFields.elementAndBomDateIp
        ? new Date(this.trackerFields.elementAndBomDateIp)
        : ""
    );
    this.trackerFieldsForm.controls.finalReviewDate.patchValue(
      this.trackerFields.finalReviewDate
        ? new Date(this.trackerFields.finalReviewDate)
        : ""
    );
    this.trackerFieldsForm.controls.finalReviewDateIp.patchValue(
      this.trackerFields.finalReviewDateIp
        ? new Date(this.trackerFields.finalReviewDateIp)
        : ""
    );
    this.trackerFieldsForm.controls.vietnamConversionDate.patchValue(
      this.trackerFields.vietnamConversionDate
        ? new Date(this.trackerFields.vietnamConversionDate)
        : ""
    );
    this.trackerFieldsForm.controls.readyForReleaseDate.patchValue(
      this.trackerFields.readyForReleaseDate
        ? new Date(this.trackerFields.readyForReleaseDate)
        : ""
    );
    this.trackerFieldsForm.controls.releasedDate.patchValue(
      this.trackerFields.releasedDate
        ? new Date(this.trackerFields.releasedDate)
        : ""
    );
    this.trackerFieldsForm.controls.simulationDate.patchValue(
      this.trackerFields.simulationDate
        ? new Date(this.trackerFields.simulationDate)
        : ""
    );
    this.trackerFieldsForm.controls.simulationDateIp.patchValue(
      this.trackerFields.simulationDateIp
        ? new Date(this.trackerFields.simulationDateIp)
        : ""
    );
  }

  async saveTrackerFields() {
    this.showLoader = true;
    this.trackerFieldsForm.controls.ofa1Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.ofa1Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.approval1Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.approval1Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.lastOFADate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.lastOFADate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.revisionDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.revisionDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.engReleaseDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.engReleaseDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.submittedDesignDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.submittedDesignDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.designQueueDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.designQueueDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.patternDesignDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.patternDesignDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.level1ReviewDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.level1ReviewDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.level1ReviewDateIp.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.level1ReviewDateIp.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.readyForOfaDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.readyForOfaDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.outForApprovalDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.outForApprovalDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.elementAndBomDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.elementAndBomDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.elementAndBomDateIp.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.elementAndBomDateIp.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.finalReviewDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.finalReviewDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.finalReviewDateIp.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.finalReviewDateIp.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.vietnamConversionDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.vietnamConversionDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.readyForReleaseDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.readyForReleaseDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.releasedDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.releasedDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.simulationDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.simulationDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.simulationDateIp.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.simulationDateIp.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.subscription.add(
      this.jacketListService
        .saveDesignQuoteTrackerFields(this.trackerFieldsForm.value,this.value)
        .subscribe(
          (res: EditableTrackerFields) => {
            this.trackerFields = res;
            this.showLoader = false;
            this.dialogRef.close(res);
          },
          () => {
            this.showLoader = false;
          }
        )
    );
  }

  closeDialog(flag = false): void {
    this.dialogRef.close(flag);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
