import { Component, Input, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { ThermostatInstallationMethod, ThermostatType, ThermostatTypesMaster } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from 'src/app/admin-pages/new-quotation/summary-sales-order.service';
import { Values } from 'src/app/shared/constants/values.constants';
import { MasterdataManagementService } from '../../../masterdata-management.service';
import { SolidWorksBlockThermostatInformationDTO } from '../../solidworks-master.model';

@Component({
  selector: 'sfl-manage-sw-block-thermostat-info',
  templateUrl: './manage-sw-block-thermostat-info.component.html',
  styleUrls: ['./manage-sw-block-thermostat-info.component.css']
})
export class ManageSwBlockThermostatInfoComponent implements OnInit {

  subscription = new Subscription();

  @Input()
  swBlockThermostatInfo: SolidWorksBlockThermostatInformationDTO[];

  @Input()
  anyThermostat: boolean;

  selectedSensorTypes = [];
  selectedLocations = [];
  selectedConnectors = [];
  selectedTempTypes = [];

  selectedSWBlockThermostatInfo = new SolidWorksBlockThermostatInformationDTO();

  thermostatDataSource = new MatTableDataSource<SolidWorksBlockThermostatInformationDTO>();

  OpenOnRise = Values.SolidWorksBlock_CheckBox_Titles.OpenOnRise;
  ManualReset = Values.SolidWorksBlock_CheckBox_Titles.ManualReset;

  thermostatTypes: ThermostatTypesMaster[];
  thermostatInstallationMethods: ThermostatInstallationMethod[];


  SensorsdisplayedColumns = ['thermostatType', 'installationMethod', 'minOpenTemp', 'maxOpenTemp', 'minCloseTemp', 'maxCloseTemp', 'minTolerance', 'maxTolerance', 'openOnRise', 'manualReset', 'action'];


  constructor(private readonly masterDataService: MasterdataManagementService,
    private salesOrderSummaryService: SalesOrderSummaryService,

  ) { }

  ngOnInit() {
    this.thermostatDataSource.data = this.swBlockThermostatInfo;
    this.getThermostatTypes();
    this.getInstallationMethod();
  }

  // used to get the thermostat types
  getThermostatTypes() {
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatTypeList().subscribe(
        (res: ThermostatType[]) => {
          this.thermostatTypes = res;
        }
      )
    );
  }

  // used to get the list of installtion methods
  getInstallationMethod() {
    this.subscription.add(
      this.salesOrderSummaryService.getInstallationMethod().subscribe(
        (installationMethod: ThermostatInstallationMethod[]) => {
          this.thermostatInstallationMethods = installationMethod;
        })
    );
  }

  addToThermostats() {
    this.prepareData();
    this.swBlockThermostatInfo.push(this.selectedSWBlockThermostatInfo);
    this.thermostatDataSource._updateChangeSubscription();
    this.selectedSWBlockThermostatInfo = new SolidWorksBlockThermostatInformationDTO();

  }

  private prepareData() {
    this.selectedSWBlockThermostatInfo.installationMethod = this.selectedSWBlockThermostatInfo.installationMethodArray.join();
    if (this.selectedSWBlockThermostatInfo.installationMethod.length === 0) {
      this.selectedSWBlockThermostatInfo.installationMethod = null;
    }
    this.selectedSWBlockThermostatInfo.thermostatType = this.selectedSWBlockThermostatInfo.thermostatTypeArray.join();
    if (this.selectedSWBlockThermostatInfo.thermostatType.length === 0) {
      this.selectedSWBlockThermostatInfo.thermostatType = null;
    }
  }

  removeFromThermostats(ele) {
    this.thermostatDataSource.data.splice(this.thermostatDataSource.data.indexOf(ele), 1);
    this.thermostatDataSource._updateChangeSubscription();
  }

  setCheckBoxTriStateValues(fieldValue: boolean, field: string) {
    switch (fieldValue) {
      case true: {
        this.checkFieldWhichNeedsToBeUpdated(field, false);
        break;
      }
      case false: {
        this.checkFieldWhichNeedsToBeUpdated(field, null);
        break;
      }
      case null: {
        this.checkFieldWhichNeedsToBeUpdated(field, true);
        break;
      }
    }
  }

  // Takes the field and it's value to be updated with and sets the Null, False or True supplied as `valueToUpdate`
  checkFieldWhichNeedsToBeUpdated(field: string, valueToUpdate: boolean) {
    switch (field) {
      case this.OpenOnRise:
        this.selectedSWBlockThermostatInfo.openOnRise = valueToUpdate;
        break;
      case this.ManualReset:
        this.selectedSWBlockThermostatInfo.manualReset = valueToUpdate;
        break;
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
