import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { SuperAdminNavbarService } from './super-admin-layout-navbar.service';

@Component({
    selector: 'sfl-super-admin-layout-navbar',
    templateUrl: './super-admin-layout-navbar.component.html'
})

export class SuperAdminNavbarComponent {
    constructor(
        public adminNavbarService: SuperAdminNavbarService,
        private router: Router) {
    }

    addMenuItem(): void {
        this.adminNavbarService.add({
            state: 'menu',
            name: 'MENU',
            type: 'sub',
            icon: 'trending_flat',
            children: [
                { state: 'menu', name: 'MENU' },
                { state: 'timeline', name: 'MENU' }
            ]
        });
    }
}
