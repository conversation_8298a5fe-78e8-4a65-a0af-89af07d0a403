
<h2 mat-dialog-title>Import CCDC
  <hr>
</h2>

<div fxLayout="row wrap" fxLayoutAlign="space-between">
  <mat-form-field appearance="outline" fxFlex.gt-lg="23" fxFlex.gt-md="23">
    <mat-select  placeholder="User Name" [(ngModel)]="importFilter.userId.value" name="name">
      <mat-option *ngFor="let user of users" [value]="user?.id">
        {{ user?.firstName }} {{user?.lastName}}
      </mat-option>
  </mat-select>
  </mat-form-field>
<mat-form-field appearance="outline" fxFlex.gt-lg="23" fxFlex.gt-md="23">
  <mat-label>Search Template Name</mat-label>
  <input matInput placeholder="Search Template Name" [(ngModel)]="importFilter.name.value">
</mat-form-field>
<mat-form-field appearance="outline" fxFlex.gt-lg="23" fxFlex.gt-md="23">
  <mat-label>Search Description</mat-label>
  <input matInput placeholder="Search Description" [(ngModel)]="importFilter.description.value">
</mat-form-field>&nbsp;&nbsp;
<div fxLayout="column" class="mt-10">
  <div fxLayout="row wrap">
  <button mat-raised-button type="submit" color="warn" (click)="addFilter()">Search</button>&nbsp;
  <button mat-raised-button type="button" color="warn" (click)="resetFilter()">Reset</button>
</div>
</div>
</div>


<div class="cust_table">
  <mat-table [dataSource]="ccdcMaterialMasterDataSource">
    <ng-container matColumnDef="userId">
      <mat-header-cell *matHeaderCellDef fxFlex="80"> User </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="80">{{element?.userId | getFirstNameLastName:users}}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="name">
      <mat-header-cell *matHeaderCellDef fxFlex="80"> Template Name </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="80">{{element?.name}}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="description">
      <mat-header-cell *matHeaderCellDef fxFlex="80"> Description </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="80">{{element?.description}}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="action" *ngIf="userRole!=='ROLE_SALES'">
      <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="5">       
        <button mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu class="menu" #menu="matMenu">
          <button mat-menu-item (click)="editCCDC(element)">
            <mat-icon>visibility</mat-icon>
            <span>View/Edit</span>
          </button>
          <button mat-menu-item (click)="deleteCcdcTemplateData(element.id)">
            <mat-icon>delete</mat-icon>
            <span>Delete</span>
          </button>
        </mat-menu>
      </mat-cell>
    </ng-container>
    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: displayedColumns;"
    [ngClass]="{highlighted: selection.isSelected(row)}"
      (click)="selectRow(row)"></mat-row>
  </mat-table>
  <div class="no-records" *ngIf="!ccdcMaterialMasterDataSource?.data?.length">
    No data found
  </div>
  <mat-paginator [length]="length" [pageSizeOptions]="pageSizeOptions" [pageSize]="pageSize" [pageIndex]="pageIndex" (page)="getCcdcPagination($event)" showFirstLastButtons>
  </mat-paginator>
</div>
<hr>
<mat-dialog-actions>
  <button mat-raised-button type="submit" color="warn" [disabled] = "!this.selection.selected[0]" (click)="importCCDC()">Import</button>
  <button mat-raised-button type="submit" (click)="closeDialog(false)">Cancel</button>
</mat-dialog-actions>
