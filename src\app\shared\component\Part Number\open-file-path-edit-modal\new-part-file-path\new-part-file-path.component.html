<h2 mat-dialog-title>Add File Path Manually
  <hr>
</h2>
<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field appearance="outline" fxFlex.gt-lg="100" fxFlex.gt-md="100">
      <mat-label>Enter File Path</mat-label>
      <input matInput placeholder="File Path" [(ngModel)]="newPathString" name="filePath">
    </mat-form-field>
  </div>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-raised-button color="warn" type="submit" (click)="save()">Add
    Path</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
