import { PatternService } from './pattern.service';
import { Values } from 'src/app/shared/constants/values.constants';
import { DatePipe } from '@angular/common';
import { PatternDesign, ChecklistPatternDesignDTO, UndoResponse } from './pattern.model';
import { Subscription } from 'rxjs';
import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { forEach } from 'lodash';
import { SweetAlertService } from 'src/app/shared/service/sweet-alert.service';
import { JacketStatus } from '../jacket-list.model';

@Component({
  selector: 'sfl-pattern',
  templateUrl: './pattern.component.html'
})
export class PatternComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  patternDesign = new PatternDesign();
  checklistPatternDesignDTO: ChecklistPatternDesignDTO = new ChecklistPatternDesignDTO();
  jacketList = new Array<number>();
  quotationId: number;
  showLoader: boolean;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  jacketId: number;
  undoResponseDTO: UndoResponse = new UndoResponse();
  JacketStatuses: JacketStatus = new JacketStatus();

  constructor(
    public readonly dialogRef: MatDialogRef<PatternComponent>,
    private readonly datePipe: DatePipe,
    private readonly patternService: PatternService,
    private readonly sweetAlertService: SweetAlertService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketList = data.jacketList;
    this.jacketId = data.jacketId;
    this.quotationId = data.quotationId;
  }

  ngOnInit() {
    this.patternDesign.patternDate = new Date();
    if (this.jacketId) {
      this.getPatternDesignByJacketId(this.jacketId);
    }
    this.checklistPatternDesignDTO.quotationID = this.quotationId;
  }


  getPatternDesignByJacketId(jacketId) {
    this.subscription.add(
      this.patternService.getPatternDesignByJacketId(jacketId).subscribe((res: PatternDesign) => {
        if (res) {
          this.patternDesign = res;
        }
      })
    );
  }

  savePatternDesign() {
    this.showLoader = true;
    this.patternDesign.patternDate = this.datePipe.transform(this.patternDesign.patternDate, Values.dateFormat.format);

    if (this.jacketList && this.jacketList.length > 0) {
      this.jacketList.forEach(ele => {
        const patternDesignConst = Object.assign({}, this.patternDesign);
        patternDesignConst.jacketId = ele;
        patternDesignConst.checked = true;
        this.checklistPatternDesignDTO.patternDesignDTOs.push(patternDesignConst);
      });
    } else {
      if (this.jacketId) {
        this.patternDesign.jacketId = this.jacketId;
        this.checklistPatternDesignDTO.patternDesignDTOs.push(this.patternDesign);
      }
    }
    if (this.checklistPatternDesignDTO.patternDesignDTOs.length > 0) {
      this.subscription.add(
        this.patternService.savePatternDesign(this.checklistPatternDesignDTO).subscribe(
          res => {
            this.checklistPatternDesignDTO = new ChecklistPatternDesignDTO();
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.SAVE, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    } else {
      this.showLoader = false;
    }
  }

  async undoPatternDesign() {
    if (await this.sweetAlertService.warningWhenUndoSignature()) {
      this.subscription.add(
        this.patternService.updatePatternDesign(this.checklistPatternDesignDTO, this.jacketId).subscribe(
          res => {
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.UNDO, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  closeDialog(updated?: string, jacketId?: number): void {
    this.undoResponseDTO.status = updated;
    this.undoResponseDTO.id = jacketId;
    this.dialogRef.close(this.undoResponseDTO);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
