import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {DesignPlanComponent} from './design-plan.component';
import {DesignPlanRoutes} from './design-plan.route';
import {TitleBlockEditorComponent} from './title-block-editor/title-block-editor.component';
import {BOMEditorComponent} from './bom-editor/bom-editor.component';
import {ElementComponent} from './element/element.component';
import {SearchParametersComponent} from './element/search-parameters/search-parameters.component';
import {LapCalcComponent} from './lap-calc/lap-calc.component';
import {AddOperationsComponent} from './bom-editor/add-operations/add-operations.component';
import {AddBOmMaterialsComponent} from './bom-editor/add-materials/add-bom-materials.component';
import {PromptUpdateAllComponent} from './bom-editor/prompt-update-all/prompt-update-all.component';
import {CopyBomComponent} from './bom-editor/copy-bom/copy-bom.component';
import {FinalReviewComponent} from './final-review/final-review.component';
import {FinalAddMaterialComponent} from './final-review/final-add-material/final-add-material.component';
import {FinalAddLabelComponent} from './final-review/final-add-label/final-add-label.component';
import {FinalAddOperationComponent} from './final-review/final-add-operation/final-add-operation.component';
import {ViewImageComponent} from './bom-editor/view-image/view-image.component';
import {DragulaModule, DragulaService} from 'ng2-dragula';
import {InfiniteScrollModule} from 'ngx-infinite-scroll';
import {EditDialogComponent} from './bom-editor/edit-dialog/edit-dialog.component';

@NgModule({
  imports: [RouterModule.forChild(DesignPlanRoutes), SharedModule, DragulaModule, InfiniteScrollModule],
  declarations: [
    DesignPlanComponent,
    BOMEditorComponent,
    ElementComponent,
    SearchParametersComponent,
    LapCalcComponent,
    AddOperationsComponent,
    AddBOmMaterialsComponent,
    PromptUpdateAllComponent,
    CopyBomComponent,
    FinalReviewComponent,
    FinalAddMaterialComponent,
    FinalAddLabelComponent,
    FinalAddOperationComponent,
    ViewImageComponent,
    EditDialogComponent
  ],
  entryComponents: [
    PromptUpdateAllComponent,
    CopyBomComponent,
    FinalAddOperationComponent,
    FinalAddLabelComponent,
    FinalAddMaterialComponent,
    ViewImageComponent
  ],
  providers: [DragulaService],
    exports: [DesignPlanComponent, TitleBlockEditorComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DesignPlanModule {}
