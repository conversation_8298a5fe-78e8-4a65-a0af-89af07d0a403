<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="filter less-peding">
  <mat-card>
    <div fxLayout="row wrap" fxLayoutGap="10px">
      <div class="sub-heder" fxLayoutAlign="start center" fxLayoutGap="10px" fxFlex>

        <span><strong>Quotation # :</strong>{{ newJacketList[0]?.quotationNumber }}</span>
        <span><strong>Customer:</strong> {{ newJacketList[0]?.customerName }}</span>
        <span><strong>SO # :</strong> {{ newJacketList[0]?.salesOrderNumber }}</span>
        <div class="cust_fields">
          <mat-form-field appearance="outline">
            <mat-label>Select Jacket</mat-label>
            <mat-select [(ngModel)]="jacketID" (selectionChange)="OnJacketChange($event.value)">
              <mat-option *ngFor="let jacket of newJacketList" [value]="jacket.id">
                {{ jacket?.partNumber ? jacket?.partNumber + ' - ' : '' }} {{jacket?.name}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div fxLayoutAlign="end center" fxLayoutGap="10px">
        <a href="/view-jacket-reference"  routerLink="/view-jacket-reference" target="_blank">
        <button class="view-ccdc" mat-raised-button color="warn" type="button">
          BOM Reference
        </button>
        </a>
        <div *ngIf="isDesignEng">
          <button class="view-ccdc" mat-raised-button color="warn" type="button" (click)="viewCCDC()">
            View CCDC
          </button>
        </div>
        <button mat-raised-button type="submit" color="warn" id="partnumber" (click)="goBack()">Back to Jacket List</button>
        <button mat-raised-button type="submit" color="warn" (click)="lapCalc()" id="lapcalc" *ngIf="listType === listTypes.element">
          Lap Calc
        </button>
        <button mat-raised-button type="submit" color="warn" id="partnumber" (click)="PartNumber()">Generate P/N</button>
        <button mat-raised-button color="warn" id="opendesignlist" (click)="openDesignlist()">SolidWorks Drawing</button>
      </div>
    </div>
  </mat-card>
</div>
<br />
<div class="demo" fxLayout="row wrap" style="margin-top: -20px;">
  <div fxFlex.gt-lg="10" fxFlex.gt-md="15" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <div class="list">
      <ol class="steps">
        <li class="pending">
          <a class="{{ listType === listTypes.geometry ? 'active' : '' }}" id="geometry" (click)="changeList(listTypes.geometry)"
            >Geometry</a
          >
        </li>
        <li class="pending">
          <a
            class="{{ listType === listTypes.titleblockeditor ? 'active' : '' }}"
            id="titleblock"
            (click)="changeList(listTypes.titleblockeditor)"
            >Title Block Editor</a
          >
        </li>
        <li class="pending">
          <a class="{{ listType === listTypes.element ? 'active' : '' }}" id="element" (click)="changeList(listTypes.element)">Element</a>
        </li>
        <li class="pending">
          <a class="{{ listType === listTypes.bomeditor ? 'active' : '' }}" id="bomeditor" (click)="changeList(listTypes.bomeditor)"
            >BOM Editor</a
          >
        </li>
        <li class="pending no-border">
          <a class="{{ listType === listTypes.finalReview ? 'active' : '' }}" id="finalReview" (click)="changeList(listTypes.finalReview)">
            Final Review
          </a>
        </li>
      </ol>
    </div>
  </div>
  <div fxFlex.gt-lg="{{ sizeLG }}" fxFlex.gt-md="{{ sizeMD }}" fxFlex.gt-sm="{{ sizeSM }}" fxFlex.gt-xs="{{ sizeXS }}">
    <div *ngIf="listType === listTypes.geometry" class="less-peding">
      <sfl-geometry
        [jacketID]="jacketID"
        [jacketGroups]="jacketGroup"
        [isDocumentsListOpen]="documentslist"
        [quotEntryMethod]="jacketListInfo?.entryMethod"
      ></sfl-geometry>
    </div>
    <div *ngIf="listType === listTypes.element" class="less-peding">
      <sfl-element [jacketID]="jacketID"></sfl-element>
    </div>
    <div *ngIf="listType === listTypes.bomeditor" class="less-peding">
      <sfl-bom-editor [jacketID]="jacketID" [revisionId]="jacketListInfo?.revisionId"></sfl-bom-editor>
    </div>
    <div *ngIf="listType === listTypes.titleblockeditor" class="less-peding">
      <sfl-title-block-editor [jacketID]="jacketID"></sfl-title-block-editor>
    </div>
    <div *ngIf="listType === listTypes.finalReview" class="less-peding">
      <sfl-final-review
        [jacketID]="jacketID"
        [salesOrderNumber]="jacketListInfo?.salesOrderNumber"
        [quotationNo]="jacketListInfo?.quotationNo"
      ></sfl-final-review>
    </div>
  </div>

  <div fxLayout="row">
  <div
    *ngIf="documentslist"
    fxFlex.gt-lg="{{ 100 - sizeLG - 10 }} "
    fxFlex.gt-md="{{ 100 - sizeMD - 35 }} "
    fxFlex.gt-sm="{{ 100 - sizeSM }} "
  >
    <mat-card class="fileModal">
      <div *ngIf="fileName" fxLayout="row" class="filename">
        <mat-icon>insert_drive_file</mat-icon>
        <h4 class="open-doc" (click)="openFile(fileName)">{{ fileName }}</h4>
        <a>
          <div class="open-doc" fxLayoutAlign="end">
            <mat-icon>search</mat-icon>
          </div>
        </a>
        <hr />
      </div>
      <div *ngIf="!fileName" fxLayoutAlign="center" class="filename">
        <h4>No part file available</h4>
      </div>
    </mat-card>
  </div>
  </div>
</div>
