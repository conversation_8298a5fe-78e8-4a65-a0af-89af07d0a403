<h2 mat-dialog-title fxFlex>
    Vietnam Conversion
    <hr>
</h2>
<mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayout="column" fxFlex="59">
            <div fxLayout="row wrap" fxLayoutAlign=" start space-between">
                <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <input matInput placeholder="Sales Order Number">
                </mat-form-field>&nbsp;&nbsp;&nbsp;
                <div fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
                    <button mat-raised-button color="warn" type="submit">Add Part</button>
                </div>
                <div fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
                    <button mat-raised-button type="submit">Remove Part</button>
                </div>
            </div>
            <div>
                <mat-table [dataSource]="partdataSource">
                    <ng-container matColumnDef="uspn">
                        <mat-header-cell *matHeaderCellDef fxFlex="12"> U.S. P/N. </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="12">{{element.uspn}}</mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="vietnampn">
                        <mat-header-cell *matHeaderCellDef fxFlex="12"> Vietnam P/N </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="12">{{element.vietnampn}}</mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="rev">
                        <mat-header-cell *matHeaderCellDef fxFlex="5"> Rev </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="5">{{element.rev}}
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="getdet">
                        <mat-header-cell *matHeaderCellDef fxFlex="9"> Get Details </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="9">{{element.getdet}}
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="createusv">
                        <mat-header-cell *matHeaderCellDef fxFlex="10"> Create U.S V </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="10">{{element.createusv}}
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="createviet">
                        <mat-header-cell *matHeaderCellDef fxFlex="14"> Create Vietnam V </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="14">{{element.createviet}}
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="unglobalize">
                        <mat-header-cell *matHeaderCellDef fxFlex="10"> Un-Globalize </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="10">{{element.unglobalize}}
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="uploadbom">
                        <mat-header-cell *matHeaderCellDef fxFlex="14"> Upload BoM/BoO </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="14">{{element.uploadbom}}
                        </mat-cell>
                    </ng-container>
                    <ng-container matColumnDef="uploadlabels">
                        <mat-header-cell *matHeaderCellDef fxFlex="14"> Upload Labels </mat-header-cell>
                        <mat-cell *matCellDef="let element" fxFlex="14">{{element.uploadlabels}}
                        </mat-cell>
                    </ng-container>
                    <mat-header-row *matHeaderRowDef="partdisplayedColumns "></mat-header-row>
                    <mat-row *matRowDef="let row; columns: partdisplayedColumns; "></mat-row>
                </mat-table>
            </div>
        </div>
        <div fxLayout="column" fxFlex="39" class="highlight-mat-table">
            <h2 class="font-md">Operations</h2>
            <mat-table [dataSource]="oprdataSource">
                <ng-container matColumnDef="opr">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Opr. </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{element.opr}}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="des">
                    <mat-header-cell *matHeaderCellDef fxFlex="40"> Description </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="40">{{element.des}}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="opr2">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Opr </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{element.opr2}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="prod">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Prod </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{element.prod}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="setup">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Setup </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{element.setup}}
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="oprdisplayedColumns "></mat-header-row>
                <mat-row *matRowDef="let row; columns: oprdisplayedColumns; "></mat-row>
            </mat-table>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <h2 class="font-md" fxflex>Materials</h2>
                <div fxLayoutAlign="end center">
                    <mat-icon class="open-doc" matTooltip="Add Materials">add</mat-icon>
                </div>
            </div>
            <mat-table [dataSource]="matdataSource">
                <ng-container matColumnDef="part">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Part. </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{element.part}}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="des">
                    <mat-header-cell *matHeaderCellDef fxFlex="60"> Description </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="60">{{element.des}}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="qty">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Qty </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{element.qty}}
                    </mat-cell>
                </ng-container>
                <ng-container matColumnDef="action">
                    <mat-header-cell *matHeaderCellDef fxFlex="10"> Action </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="10">
                        <mat-icon class="open-doc" matTooltip="Delete Materials">delete</mat-icon>
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="matdisplayedColumns "></mat-header-row>
                <mat-row *matRowDef="let row; columns: matdisplayedColumns; "></mat-row>
            </mat-table>
            <h2 class="font-md">Labels</h2>
            <mat-table [dataSource]="matdataSource2">
                <ng-container matColumnDef="part">
                    <mat-header-cell *matHeaderCellDef fxFlex="20"> Part. </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="20">{{element.part}}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="des">
                    <mat-header-cell *matHeaderCellDef fxFlex="60"> Description </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="60">{{element.des}}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="qty">
                    <mat-header-cell *matHeaderCellDef fxFlex="20"> Qty </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="20">{{element.qty}}
                    </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="matdisplayedColumns2"></mat-header-row>
                <mat-row *matRowDef="let row; columns: matdisplayedColumns2; "></mat-row>
            </mat-table>
        </div>
    </div>
</mat-dialog-content>
<hr>
<mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveCustomer()">Save</button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>