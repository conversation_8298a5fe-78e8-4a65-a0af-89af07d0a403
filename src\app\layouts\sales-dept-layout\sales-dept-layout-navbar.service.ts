import { Injectable } from '@angular/core';

export interface BadgeItem {
  type: string;
  value: string;
}

export interface ChildrenItems {
  state: string;
  name: string;
  type: string;
}

export interface Menu {
  state: string;
  name: string;
  type: string;
  icon: string;
  badge?: BadgeItem[];
  children?: ChildrenItems[];
}

const MENUITEMS = [
  {
    state: 'sales/dashboard',
    name: 'Sales',
    type: 'link',
    icon: 'card_travel'
  },
  {
    state: 'sales/dashboard/rfq-submission-form',
    name: 'RFQ Form',
    type: 'link',
    icon: 'description'
  },
  {
    state: 'sales/dashboard/order-submission-form',
    name: 'Order Form',
    type: 'link',
    icon: 'description'
  }
];

@Injectable({
  providedIn: 'root'
})
export class SalesDeptLayoutNavbarService {
  sidePanelOpened: boolean;
  getAll(): Menu[] {
    return MENUITEMS;
  }

  add(menu) {
    MENUITEMS.push(menu);
  }
}
