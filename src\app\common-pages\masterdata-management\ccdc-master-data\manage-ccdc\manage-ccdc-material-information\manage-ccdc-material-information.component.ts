import { Component, Input, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { OtherMaterialComponent } from 'src/app/admin-pages/new-quotation/Add Material/Other/other-material.component';
import { MaterialLayers } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from 'src/app/admin-pages/new-quotation/summary-sales-order.service';
import { SnakbarService, SweetAlertService } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { environment } from 'src/environments/environment';
import { CcdcTemplateDTO, MaterialInfoTemplateDTO } from '../manage-ccdc.model';

@Component({
  selector: 'app-manage-ccdc-material-information',
  templateUrl: './manage-ccdc-material-information.component.html',
  styleUrls: ['./manage-ccdc-material-information.component.css']
})
export class ManageCcdcMaterialInformationComponent implements OnInit {
  @Input("ccdcMasterData")
  ccdcMasterData: CcdcTemplateDTO;
  @Input("materialInfoDto")
  materialInfoDto: Array<MaterialInfoTemplateDTO>;
  subscription: Subscription = new Subscription();
  materialLayerObject: MaterialInfoTemplateDTO;
  selectedMaterialLayerObject: MaterialInfoTemplateDTO;
  materialLayersList: MaterialInfoTemplateDTO[];
  materialLayerObjectList: MaterialInfoTemplateDTO[];

  imageUrl = '..//..//..//assets//images//ft-logo.png';
  materialdisplayedColumns = ['layerName', 'material', 'partNumber', 'maxTemp', 'costPerSq', 'action', 'order'];
  materialDataSource = new MatTableDataSource<MaterialInfoTemplateDTO>();

  layers = [
    { layerId: 1, name: 'Facing', value: 'Facing' },
    { layerId: 2, name: 'Insulation', value: 'Insulation' },
    { layerId: 3, name: 'Liner', value: 'Liner' }
  ];
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  constructor(
    private salesOrderSummaryService: SalesOrderSummaryService,
    private matDialog: MatDialog,
    private sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
  ) { }

  ngOnInit() {
    this.materialLayerObjectList = new Array<MaterialInfoTemplateDTO>();
    this.materialLayerObject = new MaterialInfoTemplateDTO();
    this.materialDataSource = new MatTableDataSource<MaterialInfoTemplateDTO>();
    this.getMaterialLayersList();
    this.setMaterialLayers();
  }

  getMaterialLayersList() {
    this.showLoader = true;
    this.salesOrderSummaryService.getMaterialLayer().subscribe(
      (res: MaterialInfoTemplateDTO[]) => {
        this.materialLayersList = res;
        this.showLoader = false;
      },
      (error) => {
        this.showLoader = false;
        if (error.applicationStatusCode === 1226) {
          this.snakbarService.error(error.message);
        }
      }
    );
  }

  // used to handle the change of materials
  onChangeMaterial(data) {
    this.setMaterialLayerInfo(data);
  }

  // used to set the material layer info
  setMaterialLayerInfo(data) {
    this.selectedMaterialLayerObject = this.materialLayersList.find(s => s.materialId === data);
    this.materialLayerObject.material = this.selectedMaterialLayerObject.material;
    if (this.selectedMaterialLayerObject.material === Values.Other) {
      this.addOtherMaterial();
    } else {
      this.materialLayerObject.partNumber = this.selectedMaterialLayerObject.partNumber;
      this.materialLayerObject.material = this.selectedMaterialLayerObject.material;
      this.materialLayerObject.maxTempF = this.selectedMaterialLayerObject.maxTempF;
      this.materialLayerObject.maxTemp = this.selectedMaterialLayerObject.maxTemp;
      this.materialLayerObject.costPerSq = this.selectedMaterialLayerObject.costPerSq;
      this.materialLayerObject.inventory = this.selectedMaterialLayerObject.inventory;
    }
    if (this.selectedMaterialLayerObject.imageUrl) {
      this.imageUrl = environment.IMAGES_URL + this.selectedMaterialLayerObject.imageUrl;
    } else {
      this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
    }
  }

  // used to add new layer
  addLayer() {
    this.materialLayerObjectList.push(this.materialLayerObject);
    this.materialLayerObject = new MaterialInfoTemplateDTO();
    this.materialDataSource.data = this.materialLayerObjectList;
    this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
    this.setMaterialIndex();
  }


  // used to remove the material from the list
  async remove(element) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.materialDataSource.data.splice(this.materialDataSource.data.indexOf(element), 1);
      this.materialDataSource._updateChangeSubscription();
      this.setMaterialIndex();
    }
  }

  // used to handle the Other Material option shows modal
  addOtherMaterial() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { materialObj: this.materialLayerObject };
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(OtherMaterialComponent, matDataConfig)
      .afterClosed()
      .subscribe((result: MaterialLayers) => {
        if (result) {
          this.materialLayerObjectList.push(result);
          this.setMaterialIndex();
          this.materialLayerObject = new MaterialLayers();
          this.materialDataSource.data = this.materialLayerObjectList;
        } else {
          this.materialLayerObject = new MaterialLayers();
        }
      });
  }

  setMaterialLayers() {
    this.materialLayerObjectList = this.materialInfoDto.sort((a, b) => {
      const index1 = a.materialInfoIndex;
      const index2 = b.materialInfoIndex;
      if (index1 > index2) return 1;
      if (index2 > index1) return -1;
      return 0;
    });
    this.materialDataSource.data = this.materialInfoDto;
  }

  // used to handle material list movement towards up side
  up(i) {
    if (i > 0) {
      const new_index = i - 1;
      this.materialDataSource.data.splice(new_index, 0, this.materialDataSource.data.splice(i, 1)[0]);
      this.materialDataSource._updateChangeSubscription();
      this.setMaterialIndex();
    }
  }

  private setMaterialIndex() {
    for (let i = 0; i < this.materialLayerObjectList.length; i++) {
      this.materialLayerObjectList[i].materialInfoIndex = i;
    }
  }

  // used to handle material list movement towards down side
  down(i) {
    if (i < this.materialDataSource.data.length) {
      const new_index = i + 1;
      this.materialDataSource.data.splice(new_index, 0, this.materialDataSource.data.splice(i, 1)[0]);
      this.materialDataSource._updateChangeSubscription();
      this.setMaterialIndex();
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
