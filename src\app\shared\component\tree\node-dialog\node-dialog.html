<div mat-dialog-content>
  <mat-form-field>
    <input matInput placeholder="Node Name" [(ngModel)]="data.Name" />
  </mat-form-field>
</div>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="end">
    <button mat-raised-button type="submit" (click)="onNoClick()">No</button>
  </div>
  <div fxLayoutAlign="start">
    <button mat-raised-button type="submit" [disabled]="!(data.Name)" [mat-dialog-close]="{nodeName: data.Name}" color="warn">
      {{data.Component}}
    </button>
  </div>
</mat-dialog-actions>
