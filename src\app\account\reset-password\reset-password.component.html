<section class="user">
    <div class="user_options-container">
        <mat-card>
            <mat-card-content>
                <div class="user_options-text">
                    <div class="user_options-unregistered">
                        <div class="user_unregistered-title">
                            <img src="../../../assets/images/logo.png" alt="logo">
                        </div>
                        <p class="user_unregistered-text">Enter your new password to change it.</p>
                        <div class="mat-badge-warn" *ngIf="isError">
                            {{message}}
                        </div>
                    </div>
                </div>
            </mat-card-content>
            <div class="user_options-forms" id="user_options-forms">
                <mat-card>
                    <mat-card-content>
                        <div class="user_forms-login">
                            <h2 class="forms_title">Reset Password</h2>
                            <form class="forms_form" #resetPasswordForm="ngForm"
                                (ngSubmit)="resetPassword(resetPasswordForm)">
                                <mat-form-field class="forms_fieldset">
                                    <input matInput type="password" placeholder="New Password" name="newpasswordInput"
                                        autofocus [(ngModel)]="resetPasswordModel.newPassword"
                                        #newpasswordInput="ngModel" required newpassword minlength="4" maxlength="50">
                                </mat-form-field>
                                <div *ngIf="newpasswordInput.touched && newpasswordInput.invalid">
                                    <small class="mat-text-warn" *ngIf="newpasswordInput?.errors.required">Password is
                                        required.</small>
                                </div>
                                <div *ngIf="newpasswordInput?.errors?.minlength">
                                    <small class="mat-text-warn">Password must be at least 4 characters long.</small>
                                </div>
                                <mat-form-field>
                                    <input matInput type="password" placeholder="Confirm password"
                                        name="conpasswordInput" [(ngModel)]="confirmPassword"
                                        #conpasswordInput="ngModel" placeholder="Confirm Password"
                                        appEqualvalidate="newpasswordInput" required maxlength="50">
                                </mat-form-field>
                                <div *ngIf="conpasswordInput.touched && conpasswordInput.invalid">
                                    <small class="mat-text-warn" *ngIf="conpasswordInput?.errors.required">Confirm
                                        password is required.</small>
                                </div>
                                <sfl-error-messages [control]="conpasswordInput"></sfl-error-messages>
                                <div class="forms_buttons">
                                    <button type="button" class="forms_buttons-forgot" [routerLink]="['/login']">Access
                                        your account?
                                    </button>
                                    <button mat-raised-button type="submit" color="warn"
                                        [disabled]="resetPasswordForm.form.invalid">Change Password</button>
                                </div>
                            </form>
                        </div>
                    </mat-card-content>
                </mat-card>
            </div>
        </mat-card>
    </div>
</section>