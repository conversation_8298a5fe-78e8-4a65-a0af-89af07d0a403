import {Component, OnDestroy, OnInit} from '@angular/core';
import {MatDialogRef, MatTableDataSource} from '@angular/material';
import {Router} from '@angular/router';
import {Subscription} from 'rxjs';
import {SearchMfgPartService} from './search-mfg-part-service';
import {SearchMfgPartModel} from './search-mfg-part-model';
import {Variable} from '../../../../shared/constants/Variable.constants';
import {TopbarComponent} from '../../../../layouts';
import {SweetAlertService} from '../../../../shared';

@Component({
  selector: 'sfl-search-mfg-part',
  templateUrl: './search-mfg-part-component.html'
})
export class SearchMfgPartComponent implements OnInit, OnDestroy {
  partNumber: string;
  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  mfgParts = new MatTableDataSource<SearchMfgPartModel>();
  searchMFGPartsColumns = ['part-number', 'name','uom', 'desc', 'part-desc', 'company'];

  constructor(
    public readonly dialogRef: MatDialogRef<TopbarComponent>,
    private readonly router: Router,
    private readonly sweetAlertService: SweetAlertService,
    private readonly searchEcrService: SearchMfgPartService
  ) {
  }

  ngOnInit() {
  }

  searchEcr() {
    return new Promise(resolve => {

      this.showLoader = true;
      this.searchEcrService.searchMFGPartNumber(this.partNumber.trim()).subscribe(
        (searchEcrModels: SearchMfgPartModel[]) => {
          this.mfgParts.data = searchEcrModels;
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
      resolve();
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
