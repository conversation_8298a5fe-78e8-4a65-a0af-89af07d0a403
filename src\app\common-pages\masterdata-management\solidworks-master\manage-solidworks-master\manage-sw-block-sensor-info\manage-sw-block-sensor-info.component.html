<div fxLayout="row wrap" fxLayoutAlign="space-between">
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <mat-select placeholder="Type" name="sensorType" [(ngModel)]="selectedSWBlockSensorInfo.typeArray" #sensorTypeSelect="ngModel" multiple>
      <mat-option *ngFor="let sensor of sensorTypes" [value]="sensor.id">
        {{ sensor?.id }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <mat-select
      placeholder="Location"
      name="sensorLocation"
      [(ngModel)]="selectedSWBlockSensorInfo.locationArray"
      #sensorLocationSelect="ngModel"
      multiple
    >
      <mat-option *ngFor="let location of sensorLocations" [value]="location.id">
        {{ location?.value }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <mat-select
      placeholder="Sensor Connector"
      name="sensorConnector"
      [(ngModel)]="selectedSWBlockSensorInfo.connectorsArray"
      #sensorConnectorSelect="ngModel"
      multiple
    >
      <mat-option *ngFor="let connector of sensorConnectors" [value]="connector?.id">
        {{ connector?.id }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input matInput placeholder="Min Length" name="minLength" [(ngModel)]="selectedSWBlockSensorInfo.minLength" #minLength="ngModel" />
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input matInput placeholder="Max Length" name="maxLength" [(ngModel)]="selectedSWBlockSensorInfo.maxLength" #maxLength="ngModel" />
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <mat-select
      placeholder="Temp Type"
      name="sensorTempType"
      [(ngModel)]="selectedSWBlockSensorInfo.tempTypeArray"
      #sensorTempTypeSelect="ngModel"
      multiple
    >
      <mat-option *ngFor="let type of tempTypes" [value]="type.id">
        {{ type?.id }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <div fxLayoutAlign="end" class="mb-10">
    <button
      mat-raised-button
      [disabled]="anySensor === true || anySensor === false"
      mat-raised-button
      color="warn"
      type="button"
      (click)="addToSensor()"
    >
      Add
    </button>
  </div>
</div>
<div class="highlight-mat-table cust_table mt-10">
  <mat-table [dataSource]="sensorsDataSource">
    <ng-container matColumnDef="type">
      <mat-header-cell *matHeaderCellDef fxFlex="20"> Type </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="20">{{ element?.typeArray }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="location">
      <mat-header-cell *matHeaderCellDef fxFlex="35"> Location </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="35">{{ element?.locationArray }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="connector">
      <mat-header-cell *matHeaderCellDef fxFlex="15"> Connector </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="15">{{ element?.connectorsArray }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="minLength">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Min Length</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.minLength }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="maxLength">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Max Length</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.maxLength }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="tempType">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Temp Type</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.tempTypeArray }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="action">
      <mat-header-cell *matHeaderCellDef fxFlex="10"> Action </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">
        <mat-icon class="open-doc" (click)="removeFromSensor(element)">delete</mat-icon>
      </mat-cell>
    </ng-container>
    <mat-header-row *matHeaderRowDef="SensorsdisplayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: SensorsdisplayedColumns"></mat-row>
  </mat-table>
</div>
