<!-- #enddocregion label -->
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<h2 mat-dialog-title>
  Application Information
  <mat-icon
    *ngIf="showReloadButton"
    class="open-doc sfl-pull-right"
    [matTooltip]="outDatedViewErrorMessage"
    color="warn"
    matTooltipClass="sfl-formula-tooltip"
    (click)="reloadPage()"
    id="refresh"
  >
    cached
  </mat-icon>
  <hr />
</h2>
<mat-dialog-content>
  <form #appInfoForm="ngForm" role="form">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select placeholder="Element Type" [(ngModel)]="applicationInfoDto.jacketType" #jacketTypeSelect="ngModel" name="jacketType">
          <mat-option *ngFor="let jacket of jacketTypes" [value]="jacket.id">
            {{ jacket.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Voltage" name="voltage" [(ngModel)]="applicationInfoDto.voltage" sflIsNumber />
        <div matSuffix matTooltip="Voltage">V</div>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select placeholder="Select Phase" [(ngModel)]="applicationInfoDto.phase" name="phase">
          <mat-option *ngFor="let phase of phaseTypes" [value]="phase.id">
            {{ phase.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>


      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input
          matInput
          placeholder="Operating Temp ({{ tempUnit ? tempUnit : '' }})"
          name="operatingTemp"
          [(ngModel)]="applicationInfoDto.operatingTemp"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input
          matInput
          placeholder="Max Exposure Temp ({{ tempUnit ? tempUnit : '' }})"
          name="maxExposureTemp"
          [(ngModel)]="applicationInfoDto.maxExposureTemp"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input
          matInput
          placeholder="Min Ambient Temp ({{ tempUnit ? tempUnit : '' }})"
          name="minExposureTemp"
          [(ngModel)]="applicationInfoDto.minAmbientTemp"
        />
      </mat-form-field>


      <mat-form-field fxFlex.gt-lg="31" fxFlex.gt-md="30" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Heat up From°" name="heatupFrom" [(ngModel)]="applicationInfoDto.heatupFrom" />
      </mat-form-field>
      <mat-icon>settings_ethernet</mat-icon>
      <mat-form-field fxFlex.gt-lg="31" fxFlex.gt-md="30" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="To°" [(ngModel)]="applicationInfoDto.heatupTo" name="heatupTo" />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="31" fxFlex.gt-md="30" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="In" name="heatupIn" [(ngModel)]="applicationInfoDto.heatupIn" />
        <div matSuffix>Hours</div>
      </mat-form-field>


      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input
          matInput
          placeholder="Pipe Thickness ({{ measureUnit ? measureUnit : '' }})"
          name="pipeThickness"
          [(ngModel)]="applicationInfoDto.pipeThickness"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select placeholder="Content Motion" name="contentMotion" [(ngModel)]="applicationInfoDto.contentMotion">
          <mat-option *ngFor="let motion of contentMotions" ngDefaultControl [value]="motion.id" name="content">
            {{ motion.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input
          matInput
          placeholder="Flowing at rate of"
          name="contentMotionFlowingRate"
          [(ngModel)]="applicationInfoDto.contentMotionFlowingRate"
        />
      </mat-form-field>


      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select
          placeholder="Pipe Material"
          name="materialId"
          (selectionChange)="onPipeMaterialChange($event.value)"
          [(ngModel)]="applicationInfoDto.materialId"
        >
          <mat-option *ngFor="let pipe of pipeMaterial" [value]="pipe.id">
            {{ pipe?.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherMaterial">
        <input
          matInput
          placeholder="Other Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})"
          name="materialHeat"
          [(ngModel)]="applicationInfoDto.otherMaterialHeat"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherMaterial">
        <input matInput placeholder="Other Density (lb/ft3)" name="materialDensity" [(ngModel)]="applicationInfoDto.otherMaterialDensity" />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherMaterial">
        <input
          matInput
          placeholder="Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})"
          name="materialHeat"
          [(ngModel)]="applicationInfoDto.materialHeat"
          readonly
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherMaterial">
        <input matInput placeholder="Density (lb/ft3)" name="materialDensity" [(ngModel)]="applicationInfoDto.materialDensity" readonly />
      </mat-form-field>


      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select
          placeholder="Pipe Contents"
          name="contenId"
          (selectionChange)="onPipeContentChange($event.value)"
          [(ngModel)]="applicationInfoDto.contentId"
        >
          <mat-option *ngFor="let content of pipeContent" [value]="content.id">
            {{ content?.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherContent">
        <input
          matInput
          placeholder="Other Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})"
          name="contentHeat"
          [(ngModel)]="applicationInfoDto.otherContentHeat"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherContent">
        <input matInput placeholder="Other Density (lb/ft3)" name="density" [(ngModel)]="applicationInfoDto.otherContentDensity" />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherContent">
        <input
          matInput
          placeholder="Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})"
          name="contentHeat"
          [(ngModel)]="applicationInfoDto.contentHeat"
          readonly
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherContent">
        <input matInput placeholder="Density (lb/ft3)" name="density" [(ngModel)]="applicationInfoDto.contentDensity" readonly />
      </mat-form-field>


      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select
          placeholder="Control Type"
          name="controlType"
          [(ngModel)]="applicationInfoDto.controlType"
          (selectionChange)="onControlTypeChanged($event.value)"
        >
          <mat-option *ngFor="let control of accessoryControllers" [value]="control.name">
            {{ control?.name }}
          </mat-option>
          <mat-option value="Other"> Other </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field *ngIf="isOther" fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Other Control Type" name="s" [(ngModel)]="applicationInfoDto.otherControlType" type="text" />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Watt Density" name="wattdesnsity" [(ngModel)]="applicationInfoDto.wattDensity" type="text" />
      </mat-form-field>


      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select
          placeholder="Fit Type"
          name="fitType"
          (selectionChange)="onFitTypeChange($event.value)"
          [(ngModel)]="applicationInfoDto.fitTypeEnum"
        >
          <mat-option *ngFor="let fitType of fitTypes" [value]="fitType.id">
            {{ fitType?.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    <div fxFlex="100">
      <mat-form-field>
        <textarea
          matInput
          placeholder="Notes"
          rows="5"
          name="notes"
          [(ngModel)]="applicationInfoDto.notes"
          #notesInput="ngModel"
        ></textarea>
      </mat-form-field>
    </div>
  </div>

  </form>
</mat-dialog-content>
<hr />
<br />
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="end">
    <button mat-raised-button matStepperNext name="saveandnext">Next</button>
  </div>
</mat-dialog-actions>

