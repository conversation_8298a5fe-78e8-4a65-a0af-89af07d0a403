import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ConfirmGroupingChangeComponent } from './confirm-grouping-change.component';
import { MasterDataModule } from '../../masterdata-management.module';
import { RouterTestingModule } from '@angular/router/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

let component: ConfirmGroupingChangeComponent;
let fixture: ComponentFixture<ConfirmGroupingChangeComponent>;

describe('ConfirmGroupingChangeComponent', () => {
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [MasterDataModule, RouterTestingModule, BrowserAnimationsModule],
      declarations: [ConfirmGroupingChangeComponent]
    })
      .compileComponents()
      .then(() => {
        fixture = TestBed.createComponent(ConfirmGroupingChangeComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
      });
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ConfirmGroupingChangeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  confirmComponentTest();
});

function confirmComponentTest() {
  it('should have title on ngOnInit', () => {
    expect(component.title).toBe('Attention! Confirm Grouping Change', 'should have title on ngOnInit');
  });
}
