import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { PowerCordConnectorMaster } from '../../masterdata-management.model';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-power-cord-connector',
  templateUrl: './manage-power-cord-connector.component.html'
})
export class ManagePowerCordConnectorComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  powerCordConnector: PowerCordConnectorMaster;
  _data: PowerCordConnectorMaster;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public dialogRef: MatDialogRef<ManagePowerCordConnectorComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this._data = data;
  }

  ngOnInit() {
    this.powerCordConnector = this._data.id ? Object.assign({}, this._data) : new PowerCordConnectorMaster();
    this._data.id ? (this.title = 'Update Power Cord Connector') : (this.title = 'Add Power Cord Connector');
  }

  updatePowerCordConnector() {
    this.showLoader = true;
    if (this._data.id) {
      this.subscription.add(
        this.masterDataService.updatePowerCordConnector(this.powerCordConnector).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addPowerCordConnector(this.powerCordConnector).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
