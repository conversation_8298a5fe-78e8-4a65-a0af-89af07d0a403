<div class="row g-4 mx-2">
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Quotation Status</h3>
      <mat-card-content>
        <div>
          <p>Update or add Quotation Status related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageQuotStatus()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Accessory</h3>
      <mat-card-content>
        <div>
          <p>Update or add Accessory related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageAccessory()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Accessory Controllers</h3>
      <mat-card-content>
        <div>
          <p>Update or add Accessory Controllers related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageAccessoryController()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Material</h3>
      <mat-card-content>
        <div>
          <p>Update or add Material related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageMaterial()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Material Properties</h3>
      <mat-card-content>
        <div>
          <p>Update or add Material Properties related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageMaterialProperties()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Closure Material</h3>
      <mat-card-content>
        <div>
          <p>Update or add Closure Material related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageClosureMaterial()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Plug</h3>
      <mat-card-content>
        <div>
          <p>Update or add Plug related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePlug()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Thermostat List</h3>
      <mat-card-content>
        <div>
          <p>Update or add Thermostat List related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageThermostat()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Features</h3>
      <mat-card-content>
        <div>
          <p>Update or add Features related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageFeatures()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Product Group</h3>
      <mat-card-content>
        <div>
          <p>Update or add Product Group related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageProductGroupCoverPage()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>

  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Sleeving Type</h3>
      <mat-card-content>
        <div>
          <p>Update or add Sleeving Type related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageSleevingTypes()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Strain Relief</h3>
      <mat-card-content>
        <div>
          <p>Update or add Strain Relief related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageStrainReliefs()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Plug Light</h3>
      <mat-card-content>
        <div>
          <p>Update or add Plug Light related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePlugLights()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Departments</h3>
      <mat-card-content>
        <div>
          <p>Update or add Departments related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageDepartments()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Sensor Connectors</h3>
      <mat-card-content>
        <div>
          <p>Update or add Sensor Connectors related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageSensorConnectors()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Power Cord Materials</h3>
      <mat-card-content>
        <div>
          <p>Update or add Power Cord Materials related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePowerCordMaterial()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Power Cord Voltages</h3>
      <mat-card-content>
        <div>
          <p>Update or add Power Cord Voltages related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePowerCordVoltages()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Power Cord AMPS</h3>
      <mat-card-content>
        <div>
          <p>Update or add Power Cord AMPS related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePowerCordAmps()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Power Cord Options</h3>
      <mat-card-content>
        <div>
          <p>Update or add Power Cord Options related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePowerCordOptions()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Sensor Types</h3>
      <mat-card-content>
        <div>
          <p>Update or add Sensor Types related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageSensorTypes()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Thermostat Types</h3>
      <mat-card-content>
        <div>
          <p>Update or add Thermostat Types related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageThermostatTypes()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Lead Types</h3>
      <mat-card-content>
        <div>
          <p>Update or add Lead Types related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageLeadTypes()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="currency-master-data w-100 h-100 m-0">
      <h3>Currency Master Data</h3>
      <mat-card-content>
        <div>
          <p>Update or add Currency related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageCurrency()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="ccdc-master-data w-100 h-100 m-0">
      <h3>CCDC Master Data</h3>
      <mat-card-content>
        <div>
          <p>Update or add CCDC related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageCCDC()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="part-number-master-data w-100 h-100 m-0">
      <h3>Part Number Master Data</h3>
      <mat-card-content>
        <div>
          <p>Update or add Part Number related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePartNumber()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="part-number-master-data w-100 h-100 m-0">
      <h3>Labor / Burden rates Master Data</h3>
      <mat-card-content>
        <div>
          <p>Update or add Labor / Burden rates related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageLabor()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="blank-cards col-xl-3 col-md-4 col-sm-6 col-12 p-2"></div>
  <div class="blank-cards col-xl-3 col-md-4 col-sm-6 col-12 p-2"></div>
</div>
