import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { PowerCordOptionsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'sfl-manage-power-cord-options',
  templateUrl: './manage-power-cord-options.component.html'
})
export class ManagePowerCordOptionsComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  powerCordOptions: PowerCordOptionsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManagePowerCordOptionsComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.powerCordOptions = data;
  }

  ngOnInit() {
    this.powerCordOptions = this.powerCordOptions.id ? Object.assign({}, this.powerCordOptions) : new PowerCordOptionsMaster();
    this.powerCordOptions.id ? (this.title = 'Update Power Cord Options') : (this.title = 'Add Power Cord Options');
  }

  updatePowerCordOptions() {
    this.showLoader = true;
    this.powerCordOptions.powerCordConstantId = this.powerCordOptions.powerCordConstantId.toUpperCase();
    if (this.powerCordOptions.id) {
      this.subscription.add(
        this.masterDataService.updatePowerCordOptions(this.powerCordOptions).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addPowerCordOptions(this.powerCordOptions).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
