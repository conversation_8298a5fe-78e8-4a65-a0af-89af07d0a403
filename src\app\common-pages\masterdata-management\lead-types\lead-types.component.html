<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Name</mat-label>
          <input matInput [(ngModel)]="leadTypeFilter.leadName" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldLeadName)"
            *ngIf="leadTypeFilter.leadName"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Part Number</mat-label>
          <input matInput [(ngModel)]="leadTypeFilter.partNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldPartNumber)"
            *ngIf="leadTypeFilter.partNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addLeadType()">Add New Lead Type</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="leadTypeMasterDataSource"
        (matSortChange)="getLeadTypeMasterSorting($event)"
      >
        <ng-container matColumnDef="leadName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Lead Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30"> {{ element?.leadName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.partNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxTemp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Max Temp </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.maxTemp }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxVolts">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Max Volts </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.maxVolts }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="costPerFoot">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cost Per Foot </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.costPerFoot }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editLeadType(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteLeadType(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="leadTypeMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: leadTypeMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!leadTypeMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getLeadTypeMasterPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
