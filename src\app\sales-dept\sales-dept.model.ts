import { CustomerDTO } from '../admin-pages/new-quotation/ccdc-model/ccdc.model';

export class Document {
  constructor(
    public activated?: boolean,
    public filePath?: string,
    public id?: number,
    public name?: string,
    public quotationId?: number,
    public fileData?: File
  ) {
    this.activated = true;
  }
}


export class Quotation {
  constructor(
    public id?: number,
    public quotationNumber?: number,
    public salesOrderNumber?: number,
    public quotationStatusId?: number,
    public quotationStatusName?: string,
    public activated?: boolean,
    public measurementUnit?: string,
    public tempUnit?: string,
    public projectName?: string,
    public entryDate?: string,
    public customerDTO?: CustomerDTO,
    public entryMethod?: string,
    public manufacturedIn?: string,
    public externalQuoteRequired: boolean = false,
    public customerClarificationRequired: boolean = false,
    public dateAppStarted?: string,
    public quoteCompletedDate?: string,
    public salesAssociateId?: number,
    public shipDate?: string,
    public projectTitle?: string,
    public designLocation?: string,
    public ofa1Date?: string,
    public approval1Date?: string,
    public ofa2Date?: string,
    public approval2Date?: string,
    public assignedDesignerId?: number,
    public dollarAmount?: number,
    public designComments?: string,
    public noOfCustomRevision?: number
  ) {}
}

export class AccountManager {
  public id?: number;
  public name?: string;
}
