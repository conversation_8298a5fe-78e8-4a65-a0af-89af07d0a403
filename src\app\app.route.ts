import {Routes} from '@angular/router';
import {
  ViewJacketReferenceComponent
} from './design-pages/design-plan/view-jacket-reference/view-jacket-reference.component';
import {
  AdminLayoutComponent,
  DesignEngLayoutComponent,
  ErrorComponent,
  OutsideLayoutComponent,
  ServiceUnavailableComponent,
  SuperAdminLayoutComponent,
  UnauthorizedPageComponent
} from './layouts';
import {MasterDataLayoutComponent} from './layouts/master-data-layout/master-data-layout.component';
import {QuoteTrackerLayoutComponent} from './layouts/quote-tracker-layout/quote-tracker-layout.component';
import {SalesDeptLayoutComponent} from './layouts/sales-dept-layout/sales-dept-layout.component';
import {Role} from './shared';
import {AuthGuardService, NonAuthGuardService} from './shared/service/auth-admin-guard.service';


export const AppRoutes: Routes = [
  {
    path: '',
    component: OutsideLayoutComponent,
    canActivate: [NonAuthGuardService],
    loadChildren: './account/account.module#AccountModule'
  },
  {path: 'view-jacket-reference', component: ViewJacketReferenceComponent},
  /* ...App Admin Pages... */
  {
    path: 'app-eng',
    component: AdminLayoutComponent,
    canActivate: [AuthGuardService],
    data: {roles: [Role.ADMIN_ROLE, Role.DESIGN_ROLE, Role.ENG_ROLE, Role.SALES_ROLE]},
    children: [
      {
        path: 'dashboard',
        loadChildren: './admin-pages/dashboard/dashboard.module#DashboardModule'
      },
      {
        path: 'ccdc',
        loadChildren: './admin-pages/new-quotation/summary-sales-order.component.module#SummarySalesOrderModule',
      },
      {
        path: 'quotation-comparison',
        loadChildren: './admin-pages/quotation-comparison/quotation-comparison.module#QuotationComparisonModule'
      },
      {
        path: 'ccdc/print',
        loadChildren: './common-pages/view-ccdc/view-ccdc.module#ViewccdcModule'
      }
    ]
  },
  /* ...Design Admin Pages... */
  {
    path: 'design-eng',
    component: DesignEngLayoutComponent,
    canActivate: [AuthGuardService],
    data: {roles: [Role.ADMIN_ROLE, Role.DESIGN_ROLE, Role.ENG_ROLE, Role.SALES_ROLE]},
    children: [
      {
        path: 'dashboard',
        loadChildren: './design-pages/dashboard/dashboard.module#DashboardModule'
      },
      {
        path: 'jacket-list',
        loadChildren: './design-pages/jacket-list/jacket-list.module#JacketListModule'
      },
      {
        path: 'design-plan',
        loadChildren: './design-pages/design-plan/design-plan.component.module#DesignPlanModule'
      },
      {
        path: 'ecr-management',
        loadChildren: './design-pages/ecr-management/ecr-management.module#ECRManagementModule'
      }
    ]
  },

  /* ...Super Admin Pages... */
  {
    path: 'super-admin',
    component: SuperAdminLayoutComponent,
    canActivate: [AuthGuardService],
    data: {roles: [Role.ADMIN_ROLE, Role.DESIGN_ROLE, Role.ENG_ROLE]},
    children: [
      {
        path: 'users',
        loadChildren: './common-pages/users/users.module#UsersModule'
      }
    ]
  },
  /* ...Masterdata Management... */
  {
    path: 'master-data',
    component: MasterDataLayoutComponent,
    canActivate: [AuthGuardService],
    children: [
      {
        path: 'management',
        loadChildren: './common-pages/masterdata-management/masterdata-management.module#MasterDataModule'
      }
    ]
  },
  /* ...Quotation Tracker... */
  {
    path: 'quote-tracker',
    component: QuoteTrackerLayoutComponent,
    canActivate: [AuthGuardService],
    children: [
      {
        path: 'dashboard',
        loadChildren: './quote-tracker/quote-tracker.module#QuoteTrackerModule'
      }
    ]
  },
  /* ...Sales Department... */
  {
    path: 'sales',
    component: SalesDeptLayoutComponent,
    canActivate: [AuthGuardService],
    children: [
      {
        path: 'dashboard',
        loadChildren: './sales-dept/sales-dept.module#SalesDeptModule'
      }
    ]
  },
  {
    path: 'service-unavailable',
    component: ServiceUnavailableComponent
  },
  {
    path: 'unauthorized',
    component: UnauthorizedPageComponent
  },
  {
    path: '**',
    component: ErrorComponent
  }
];
