import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { DepartmentsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-departments',
  templateUrl: './manage-departments.component.html'
})
export class ManageDepartmentsComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  departments: DepartmentsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageDepartmentsComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.departments = data;
  }

  ngOnInit() {
    this.departments = this.departments.id ? Object.assign({}, this.departments) : new DepartmentsMaster();
    this.departments.id ? (this.title = 'Update Departments') : (this.title = 'Add Departments');
  }

  updateDepartment() {
    this.showLoader = true;
    if (this.departments.id) {
      this.subscription.add(
        this.masterDataService.updateDepartment(this.departments).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addDepartment(this.departments).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
