<h2 mat-dialog-title>Add
  <hr>
</h2>
<form #materialForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-form-field class="example-full-width">
      <input
        type="text"
        placeholder="Select Material"
        aria-label="Select Material"
        matInput
        [matAutocomplete]="auto"
        [formControl]="materialControl"
      />
      <mat-hint>Enter text to find Materials</mat-hint>
    </mat-form-field>
    <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn" placeholder="Select" name="select"
                      (optionSelected)="onSelectionChanges($event.option.value)" required>
      <mat-option *ngFor="let data of materialObservable$ | async;" [value]="data">
        {{ data?.partNumber }},
        {{ data?.description }}
      </mat-option>
      <mat-option value="other">Other</mat-option>
    </mat-autocomplete>

    <ng-container *ngIf="selection !== 'other' ">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Part Number" [(ngModel)]="masterDataDetails.partNumber" name="partNumber"
                 autocomplete="off" readonly>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Rel Op" [(ngModel)]="masterDataDetails.relOpr" name="relOpr"
                 autocomplete="off">
        </mat-form-field>
        <mat-form-field>
          <input matInput placeholder="Description" [(ngModel)]="masterDataDetails.description" name="description"
                 autocomplete="off" readonly>
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Qty" [(ngModel)]="masterDataDetails.qty" name="qty" autocomplete="off">
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="UOM" [(ngModel)]="masterDataDetails.uom" name="uom" autocomplete="off" readonly>
        </mat-form-field>
      </div>
    </ng-container>
    <ng-container *ngIf="selection === 'other' ">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Part Number" [(ngModel)]="masterDataDetails.partNumber" name="partNumber"
                 autocomplete="off">
        </mat-form-field>
        <mat-form-field>
          <input matInput placeholder="Description" [(ngModel)]="masterDataDetails.description" name="description"
                 autocomplete="off">
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Rel Op" [(ngModel)]="masterDataDetails.relOpr" name="relOpr"
                 autocomplete="off">
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Qty" [(ngModel)]="masterDataDetails.qty" name="qty" autocomplete="off">
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="UOM" [(ngModel)]="masterDataDetails.uom" name="uom" autocomplete="off">
        </mat-form-field>
      </div>
    </ng-container>
  </mat-dialog-content>
  <hr>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveData()" [disabled]="materialForm.invalid">Add
      Material
    </button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</form>
