<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #thermostatForm="ngForm" (ngSubmit)="updateThermostatList()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number"
            [(ngModel)]="thermostatList.partNumber"
            name="partnumber"
            #partnumberInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partnumberInput.touched && partnumberInput.invalid">
          <small class="mat-text-warn" *ngIf="partnumberInput?.errors?.required">Part number is required.</small>
          <small class="mat-text-warn" *ngIf="partnumberInput?.errors?.whitespace && !partnumberInput?.errors?.required"
            >Invalid part number.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Amps" [(ngModel)]="thermostatList.amps" name="amps" #ampsInput="ngModel" required/>
        </mat-form-field>
        <div *ngIf="ampsInput.touched && ampsInput.invalid">
          <small class="mat-text-warn" *ngIf="ampsInput?.errors?.required">Amps is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Close Temp"
            [(ngModel)]="thermostatList.closeTemp"
            name="closeTemp"
            #closeTempInput="ngModel"
            (change)="convertTemperature('celcius', 'closeTemp')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="closeTempInput.touched && closeTempInput.invalid">
          <small class="mat-text-warn" *ngIf="closeTempInput?.errors?.required">Close temperature is required.</small>
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Close Temp F"
            [(ngModel)]="thermostatList.closeTempF"
            name="closeTempF"
            #closeTempFInput="ngModel"
            (change)="convertTemperature('fahrenheit', 'closeTemp')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="closeTempFInput.touched && closeTempFInput.invalid">
          <small class="mat-text-warn" *ngIf="closeTempFInput?.errors?.required">Close temperature fahrenheit is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Close Tolerance C"
            [(ngModel)]="thermostatList.closeTolC"
            name="closeTolC"
            #closeTolCInput="ngModel"
            (change)="convertTemperature('celcius', 'closeTolC')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="closeTolCInput.touched && closeTolCInput.invalid">
          <small class="mat-text-warn" *ngIf="closeTolCInput?.errors?.required">Close tolerance C is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Close Tolerance F"
            [(ngModel)]="thermostatList.closeTolF"
            name="closeTolF"
            #closeTolFInput="ngModel"
            (change)="convertTemperature('fahrenheit', 'closeTolC')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="closeTolFInput.touched && closeTolFInput.invalid">
          <small class="mat-text-warn" *ngIf="closeTolFInput?.errors?.required">Close tolerance fahrenheit is required.</small>
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="openOnRise" color="warn" [(ngModel)]="thermostatList.openOnRise">Open On Rise</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="manualReset" color="warn" [(ngModel)]="thermostatList.manualReset">Manual Reset</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="adjustable" color="warn" [(ngModel)]="thermostatList.adjustable">Adjustable</mat-checkbox>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Cost" [(ngModel)]="thermostatList.cost" name="cost" #costInput="ngModel" required sflIsDecimal />
        </mat-form-field>
        <div *ngIf="costInput.touched && costInput.invalid">
          <small class="mat-text-warn" *ngIf="costInput?.errors?.required">Cost is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Open Temperature"
            [(ngModel)]="thermostatList.openTemp"
            name="openTemp"
            #openTempInput="ngModel"
            (change)="convertTemperature('celcius', 'openTemp')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="openTempInput.touched && openTempInput.invalid">
          <small class="mat-text-warn" *ngIf="openTempInput?.errors?.required">Open Temperature is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Open Temperature Fahrenheit"
            [(ngModel)]="thermostatList.openTempF"
            name="openTempF"
            #openTempFInput="ngModel"
            (change)="convertTemperature('fahrenheit', 'openTemp')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="openTempFInput.touched && openTempFInput.invalid">
          <small class="mat-text-warn" *ngIf="openTempFInput?.errors?.required">Open temperature fahrenheit is required.</small>
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Manufacturer"
            [(ngModel)]="thermostatList.manufacturer"
            name="manufacturer"
            #manufacturerInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="manufacturerInput.touched && manufacturerInput.invalid">
          <small class="mat-text-warn" *ngIf="manufacturerInput?.errors?.required">Manufacturer is required.</small>
          <small class="mat-text-warn" *ngIf="manufacturerInput?.errors?.whitespace && !manufacturerInput?.errors?.required">
            Invalid manufacturer.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Tolerance"
            [(ngModel)]="thermostatList.tolerance"
            name="tolerance"
            #toleranceInput="ngModel"
            (change)="convertTemperature('celcius', 'tolerance')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="toleranceInput.touched && toleranceInput.invalid">
          <small class="mat-text-warn" *ngIf="toleranceInput?.errors?.required">Tolerance is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Tolerance F"
            [(ngModel)]="thermostatList.toleranceF"
            name="toleranceF"
            #toleranceFInput="ngModel"
            (change)="convertTemperature('fahremheit', 'tolerance')"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="toleranceInput.touched && toleranceInput.invalid">
          <small class="mat-text-warn" *ngIf="toleranceInput?.errors?.required">Tolerance fahrenheit is required.</small>
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Type" [(ngModel)]="thermostatList.type" name="typeInput" #typeInput="ngModel" required>
            <mat-option *ngFor="let type of thermostatTypes" [value]="type.id">
              {{ type.id }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="typeInput.touched && typeInput.invalid">
          <small class="mat-text-warn" *ngIf="typeInput?.errors.required">Thermostat Type is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Volts" [(ngModel)]="thermostatList.volts" name="volts" #voltsInput="ngModel" required/>
        </mat-form-field>
        <div *ngIf="voltsInput.touched && voltsInput.invalid">
          <small class="mat-text-warn" *ngIf="voltsInput?.errors?.required">Volts is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="thermostatList.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!thermostatForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
