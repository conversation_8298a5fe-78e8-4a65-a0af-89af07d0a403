import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'app-new-part-file-path',
  templateUrl: './new-part-file-path.component.html',
  styleUrls: ['./new-part-file-path.component.css']
})
export class NewPartFilePathComponent implements OnInit {
  newPathString: string;
  selectedPath = ''

  constructor(@Inject(MAT_DIALOG_DATA) data, public readonly dialogRef: MatDialogRef<NewPartFilePathComponent>,) {
    this.newPathString = data.newPathString
    this.selectedPath = data.selectedPath
   }

  ngOnInit() {
  }

  closeDialog(): void {
    this.dialogRef.close(this.newPathString);
  }

  save() {
    this.closeDialog();
  }
}
