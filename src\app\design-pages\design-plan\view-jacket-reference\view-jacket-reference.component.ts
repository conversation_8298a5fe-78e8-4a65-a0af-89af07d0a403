import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatSort, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { FinalDesignReviewDTOOLD, FinalDesignReviewJacketRefernce } from '../final-review/final-review.model';
import { FinalReviewService } from '../final-review/final-review.service';



@Component({
  selector: 'app-view-jacket-reference',
  templateUrl: './view-jacket-reference.component.html',
  styleUrls: ['./view-jacket-reference.component.css']
})
export class ViewJacketReferenceComponent implements OnInit, OnDestroy {

  subscription: Subscription = new Subscription();
  finalReviewDTO: FinalDesignReviewJacketRefernce = new FinalDesignReviewJacketRefernce();
  partNumber: string;
  showLoader = false;
  partInfoLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  operationsDataSourceUSA = new MatTableDataSource();
  operationsColumns = DisplayColumns.Cols.OperationsFinalReviewColumnJacketReference;
  materialsDataSourceUSA = new MatTableDataSource();
  materialsColumns = DisplayColumns.Cols.MaterialsColumnJacketRefernce;
  labelsDataSourceUSA = new MatTableDataSource();
  labelsColumns = DisplayColumns.Cols.LabelsColumnJacketReference;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private readonly finalReviewService: FinalReviewService,
    private readonly snakBarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) { }

  ngOnInit() {
    this.materialsDataSourceUSA.sort = this.sort;
  }

  pollEpicor() {
    this.showLoader = true;
    this.subscription.add(
      this.finalReviewService.pollJacketDetailsById(this.partNumber).subscribe(
        (res:FinalDesignReviewJacketRefernce) => {
          if (res) {
            this.finalReviewDTO = res;
            this.showLoader = false;
            this.createMatTable(this.finalReviewDTO);
            this.snakBarService.success(Messages.JacketReference.success_poll_epicore);
          } else {
            this.sweetAlertService.partNumberNotExistsWarning();
            this.showLoader = false;
          }
        },
        () => {
          this.showLoader = false;
         }
      )
    );
  }

  createMatTable(serviceRequestList: FinalDesignReviewDTOOLD) {
    this.operationsDataSourceUSA.data = serviceRequestList.operations;
    this.materialsDataSourceUSA.data = serviceRequestList.materials;
    this.labelsDataSourceUSA.data = serviceRequestList.labels;
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
