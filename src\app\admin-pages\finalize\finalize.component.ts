import { SelectionModel } from '@angular/cdk/collections';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatCheckboxChange, MatDialog, MatDialogConfig, MatSort, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { RepeatJacketDTO } from 'src/app/design-pages/jacket-list/jacket-list.model';
import { JacketListService } from 'src/app/design-pages/jacket-list/jacket-list.service';
import { Messages, SharedService, SnakbarService, SweetAlertService } from 'src/app/shared';
import { AddNewJackets } from 'src/app/shared/component/geometry/geometry.model';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { finalizeInformationChangedField, Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { PartNumberComponent } from '../../shared/component/Part Number/part-number.component';
import { JacketAccessoriesList } from '../accessories/accessories.model';
import { AddLeadtimeComponent } from './add-leadtime/add-leadtime.component';
import { AdditonalAddedValueModalComponent } from './additonal-added-value-modal/additonal-added-value-modal.component';
import { AddLeadTime, CurrencyDTO, SendToDesignConfigurationDTO } from './finalize.model';
import { FinalizeService } from './finalize.service';
import { ManufacturedInModalComponent } from './manufactured-in-modal/manufactured-in-modal.component';
import { MassUpdateJacketModalComponent } from './mass-update-jacket-modal/mass-update-jacket-modal.component';

@Component({
  selector: 'sfl-finalize',
  templateUrl: './finalize.component.html',
})
export class FinalizeComponent implements OnInit, OnDestroy {
  displayedColumns = [
    'select',
    'quantity',
    'jacketRepeat',
    'name',
    'customerPN',
    'briskheatPN',
    'description',
    'laborHours',
    'materialCost',
    'usCost',
    'listPrice',
    'discount',
    'netPrice',
    'extendedNetPrice',
    'margin',
    'grossMargin',
    'madeIn',
    'massUpdated'
  ];
  jacketList: JacketAccessoriesList[];
  jacket: AddNewJackets[];
  jacketDataSource = new MatTableDataSource<JacketAccessoriesList>();
  jacketDataSourceVietnam = new MatTableDataSource<JacketAccessoriesList>();
  jacketDataSourceCosta = new MatTableDataSource<JacketAccessoriesList>();
  isNoDataFound = this.jacketDataSource.connect().pipe(map(data => data.length === 0));

  subscription: Subscription = new Subscription();
  private _revisionId: number;
  private _soNumber: string;
  _manufacturedIn: string;
  manufacturedInPresent = false;
  totalExtendedNetPrice = 0;
  totalGrossMargin = 0;
  vietnamTotalExtendedNetPrice = 0;
  vietnamTotalGrossMargin = 0;
  costaRicaTotalExtendedNetPrice = 0;
  costaRicaTotalGrossMargin = 0;
  success: boolean;
  madein: string;
  madeInArray: string;
  usaCountry = Values.MadeInCountryUSA;
  vietnamCountry = Values.MadeInCountryVietnam;
  costaRicaCountry = Values.MadeInCountryCostaRica;
  discountValue: number;
  costaRicadiscountValue: number;
  currency: CurrencyDTO;
  currencyId: number;
  leadTime: number;
  optJacketList: AddNewJackets[];
  isDesignEng: boolean;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  changedFields = finalizeInformationChangedField;
  selection = new SelectionModel<JacketAccessoriesList>(true, []);
  madeInNewArray = [
    {name: 'USA', value: 'Usa'},
    {name: 'VIETNAM', value: 'Vietnam'},
    {name: 'Costa Rica', value: 'Costa Rica'}
  ];

  @Input() quotId;
  @ViewChild(MatSort) sort: MatSort;
  @Input() quoteNumber: number;
  allMadeIn: any;
  allMadeInUsa = 'Usa';
  newAllMadeIn = ['Usa'];
  newAllMadeInVietnam = ['Vietnam'];
  newAllMadeInCostaRica = ['Costa Rica'];
  tempVietnamTotalExtendedNetPrice: number;

  @Input()
  set revisionId(id) {
    this._revisionId = id;
    this.getManufacturedInByQuoteId();
  }

  get revisionId() {
    return this._revisionId;
  }

  @Input()
  set soNumber(salesOrderNumber) {
    this._soNumber = salesOrderNumber === null ? '' : salesOrderNumber;
  }

  get soNumber() {
    return this._soNumber;
  }

  constructor(
    private readonly matDialog: MatDialog,
    private readonly finalizeService: FinalizeService,
    private readonly snakbarService: SnakbarService,
    private readonly sharedService: SharedService,
    private readonly jacketListService: JacketListService,
    private readonly sweetAlertService: SweetAlertService,
  ) {
  }

  ngOnInit() {
    this.jacketDataSource.sort = this.sort;
    this.jacketList = new Array<JacketAccessoriesList>();
    this.optJacketList = new Array<AddNewJackets>();
    this.success = false;
    this.getLeadTime();
    this.getCurrency();
  }


  getManufacturedInByQuoteId() {
    this.finalizeService
      .getManufacturedInByQuotationId(this.quotId)
      .toPromise()
      .then((sendToDesignConfigurationDTO: SendToDesignConfigurationDTO) => {
        if (sendToDesignConfigurationDTO && sendToDesignConfigurationDTO.manufacturedIn) {
          this._manufacturedIn = sendToDesignConfigurationDTO.manufacturedIn;
          this.madein = sendToDesignConfigurationDTO.manufacturedIn;
          this.manufacturedInPresent = true;
        } else {
          this.madein = 'Usa';
          this.manufacturedInPresent = false;
          this._manufacturedIn = '';
        }
        this.getFinalizeDetailsNew();
      });
  }

  getCurrency() {
    this.subscription.add(
      this.finalizeService.getAllCurrency().subscribe((res: CurrencyDTO) => {
        if (res) {
          this.currency = res;
          this.currencyId = this.currency[0].id;
        }
      })
    );
  }

  getLeadTime() {
    this.subscription.add(
      this.finalizeService.getLeadTime(this.quotId).subscribe((res: AddLeadTime) => {
        if (res) {
          this.leadTime = res.leadTime;
        }
      })
    );
  }

  markJacketAsRepeat(id, event) {
    this.showLoader = true;
    const repeatJacketDTO = new RepeatJacketDTO();
    repeatJacketDTO.jacketId = id;
    repeatJacketDTO.repeat = event.checked;
    this.subscription.add(
      this.jacketListService.markJacketAsRepeat(repeatJacketDTO).subscribe(
        error => {
          this.showLoader = false;
        }
      ));
  }

  getFinalizeDetails(revisionId: number, manufacturedIn: string) {
    this.showLoader = true;
    this.subscription.add(
      this.finalizeService.getFinalizeDetails(revisionId, manufacturedIn).subscribe(
        (res: JacketAccessoriesList[]) => {
          if (res.length > 0) {
            this.calculateTotalExtendedNetPriceAndGrossMargin(res, [],[]);
            this.jacketDataSource.data = res;
          }
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  calculateTotalExtendedNetPriceAndGrossMargin(jacketAccessories: JacketAccessoriesList[], jacketAccessoriesAll: JacketAccessoriesList[],jacketAccessoriesAllCostaRica : JacketAccessoriesList[]) {
    this.totalExtendedNetPrice = 0;
    this.totalGrossMargin = 0;
    this.vietnamTotalExtendedNetPrice = 0;
    this.vietnamTotalGrossMargin = 0;
    this.costaRicaTotalExtendedNetPrice = 0;
    this.costaRicaTotalGrossMargin = 0;
    let tempVietnamTotalExtendedNetPrice: number = 0;
    let tempVietnamTotalGrossMargin: number = 0;
    let tempCostaRicaTotalExtendedNetPrice: number = 0;
    let tempCostaRicaTotalGrossMargin: number = 0;

    if (jacketAccessories.length > 0) {
      for (const jacketAccss of jacketAccessories) {
        if (jacketAccss.revisionAccessoryId) {
          tempVietnamTotalExtendedNetPrice += jacketAccss.extendedNetPrice;
          tempVietnamTotalGrossMargin += jacketAccss.grossMargin;
        }
        if (jacketAccss.madeIn === 'Usa' && jacketAccss.revisionAccessoryId == null) {
          this.totalExtendedNetPrice += jacketAccss.extendedNetPrice;
          this.totalGrossMargin += jacketAccss.grossMargin;
        } else if(jacketAccss.madeIn === 'Vietnam' && jacketAccss.revisionAccessoryId == null){
          this.vietnamTotalExtendedNetPrice += jacketAccss.vietnamExtendedNetPrice;
          this.vietnamTotalGrossMargin += jacketAccss.vietnamGrossMargin;
        }
        else if(jacketAccss.madeIn === 'Costa Rica' && jacketAccss.revisionAccessoryId == null){
          this.costaRicaTotalExtendedNetPrice += jacketAccss.costaRicaExtendedNetPrice;
          this.costaRicaTotalGrossMargin += jacketAccss.costaRicaGrossMargin;
        }
      }

      this.vietnamTotalExtendedNetPrice += tempVietnamTotalExtendedNetPrice;
      this.vietnamTotalGrossMargin += tempVietnamTotalGrossMargin;
      this.totalExtendedNetPrice += tempVietnamTotalExtendedNetPrice;
      this.totalGrossMargin += tempVietnamTotalGrossMargin;
      this.costaRicaTotalExtendedNetPrice += tempCostaRicaTotalExtendedNetPrice;
      this.costaRicaTotalGrossMargin += tempCostaRicaTotalGrossMargin;
    }

    if (jacketAccessoriesAll.length > 0) {
      for (const jacketAccss of jacketAccessoriesAll) {
        this.vietnamTotalExtendedNetPrice += jacketAccss.vietnamExtendedNetPrice;
        this.vietnamTotalGrossMargin += jacketAccss.vietnamGrossMargin;
      }
    }

    if (jacketAccessoriesAllCostaRica.length > 0) {
      for (const jacketAccss of jacketAccessoriesAllCostaRica) {
        this.costaRicaTotalExtendedNetPrice += jacketAccss.costaRicaExtendedNetPrice
        this.costaRicaTotalGrossMargin += jacketAccss.costaRicaGrossMargin
      }
    }
  }

  partNumber() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: this.quotId};
    matDataConfig.width = PopupSize.size.popup_xxlg;
    this.matDialog.open(PartNumberComponent, matDataConfig).afterClosed().subscribe(res => {
      this.getFinalizeDetailsNew();
    });
  }

  sendToDesign() {
    if (!this._manufacturedIn) {
      const matDataConfig = new MatDialogConfig();
      matDataConfig.width = PopupSize.size.popup_xmd;
      matDataConfig.data = {quotationId: this.quotId};
      this.matDialog
        .open(ManufacturedInModalComponent, matDataConfig)
        .afterClosed()
        .subscribe((res: SendToDesignConfigurationDTO) => {
          if (res) {
            this.madein = res.manufacturedIn;
            this._manufacturedIn = res.manufacturedIn;
            this.manufacturedInPresent = true;
            this.getFinalizeDetails(this.revisionId, this._manufacturedIn);
            this.subscription.add(
              this.finalizeService.sendToDesign(this.quotId).subscribe(() => {
                this.success = true;
              })
            );
          }
        });
    }
  }

  downloadQuoteExcel() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: this.quotId, leadTime: this.leadTime};
    matDataConfig.width = PopupSize.size.popup_sm;
    this.matDialog
      .open(AddLeadtimeComponent, matDataConfig)
      .afterClosed()
      .subscribe(
        res => {
          this.leadTime = res.leadTime;
          if (res) {
            let fileName = '';
            this.subscription.add(
              this.finalizeService.downloadQuoteExcel(this.quotId).subscribe(
                success => {
                  fileName = this.sharedService.getProjectName()
                    ? 'Q' + this.quoteNumber + '-' + this.sharedService.getProjectName()
                    : 'Q' + this.quoteNumber;
                  this.downloadExcel(success, fileName + '.xlsx');
                },
                err => {
                  this.snakbarService.error(Messages.error.excel_error_msg);
                }
              )
            );
          }
        },
        err => {
          this.snakbarService.error(Messages.error.excel_error_msg);
        }
      );
  }

  downloadSOExcel() {
    if (this.madeInArray === 'All') {
      this.allMadeIn = this.madeInNewArray.map(x => x.value);
    } else {
      this.allMadeIn = [this.madeInArray];
    }
    if (this.madeInArray === 'Usa') {
      this.subscription.add(
        this.finalizeService.downloadSOExcelFinalize({
          discount: this.discountValue,
          costaRicaDiscount: this.costaRicadiscountValue,
          revisionId: this._revisionId,
          madeIn: this.newAllMadeIn,
          currencyId: this.currencyId
        }).subscribe(success => {
          this.downloadExcel(success, 'SOSheet-' + this._soNumber + '.xlsx');
        })
      );
    } else if (this.madeInArray === 'Vietnam') {
      this.subscription.add(
        this.finalizeService.downloadSOExcelFinalize({
          discount: this.discountValue,
          revisionId: this._revisionId,
          madeIn: this.newAllMadeInVietnam,
          currencyId: this.currencyId
        }).subscribe(success => {
          this.downloadExcel(success, 'SOSheet-' + this._soNumber + '.xlsx');
        })
      );

    } else if (this.madeInArray === 'All') {
      this.subscription.add(
        this.finalizeService.downloadSOExcelFinalize({
          discount: this.discountValue,
          costaRicaDiscount: this.costaRicadiscountValue,
          revisionId: this._revisionId,
          madeIn: this.allMadeIn,
          currencyId: this.currencyId
        }).subscribe(success => {
          this.downloadExcel(success, 'SOSheet-' + this._soNumber + '.xlsx');
        })
      );
    } else {
      this.subscription.add(
        this.finalizeService.downloadSOExcelFinalize({
          discount: this.discountValue,
          costaRicaDiscount: this.costaRicadiscountValue,
          revisionId: this._revisionId,
          madeIn: this.newAllMadeInCostaRica,
          currencyId: this.currencyId
        }).subscribe(success => {
          this.downloadExcel(success, 'SOSheet-' + this._soNumber + '.xlsx');
        })
      );
    }
  }


  downloadExcel(response, sheetName: string) {
    const dwldLink = document.createElement('a');
    const url = URL.createObjectURL(response);
    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('target', '_blank');
    dwldLink.setAttribute('download', sheetName);
    dwldLink.style.visibility = 'hidden';
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    // selecting just the jackets, so need to minus the accesories from all Finalize Accessories.
    const numRows =
      this.jacketDataSource.data.length -
      this.jacketDataSource.data.filter((jacket: JacketAccessoriesList) => jacket.revisionAccessoryId).length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.jacketDataSource.data.forEach(row => {
        if (row.jacketId) {
          this.selection.select(row);
        }
      });
  }

  onSelectedJacket(event: MatCheckboxChange, row: JacketAccessoriesList) {
    if (event && row.jacketId) {
      this.selection.toggle(row);
    }
  }

  updateQty(element: JacketAccessoriesList, changedField?: string) {
    this.showLoader = true;
    element.finalizeInformationChangedField = changedField ? changedField : 'NONE';
    return new Promise(async (resolve) => {
      this.subscription.add(
        this.finalizeService.updateQty(element).subscribe(
          (res: JacketAccessoriesList) => {
            if (res) {
              const index = this.jacketDataSource.data.indexOf(element);
              this.jacketDataSource.data[index] = res;
              this.jacketDataSource._updateChangeSubscription();
              this.calculateTotalExtendedNetPriceAndGrossMargin(this.jacketDataSource.data, [], []);
              this.showLoader = false;
              this.selection.clear();
            }
          },
          error => {
            if (error.applicationStatusCode === 1217) {
              this.snakbarService.error(error.message);
            }
            this.showLoader = false;
            this.selection.clear();
          }
        )
      ).add(()=>{
        this.applyVietnamDiscount();
        resolve();
      });
    })
  }

  massUpdateJacketCosts() {
    this.showLoader = true;
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = this.selection.selected;
    matDataConfig.width = PopupSize.size.popup_xmd;
    this.matDialog
      .open(MassUpdateJacketModalComponent, matDataConfig)
      .afterClosed()
      .subscribe(
        (res: JacketAccessoriesList[]) => {
          if (res) {
            this.setJacketByIndex(res);
          }
          this.calculateTotalExtendedNetPriceAndGrossMargin(this.jacketDataSource.data, [],[]);
          this.selection.clear();
          this.jacketDataSource._updateChangeSubscription();
          this.showLoader = false;
        },
        error => {
          this.selection.clear();
          this.showLoader = false;
        }
      );
  }

  openAddedValuePopUp(element: JacketAccessoriesList) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = element;
    matDataConfig.width = PopupSize.size.popup_xmd;
    this.matDialog
      .open(AdditonalAddedValueModalComponent, matDataConfig)
      .afterClosed()
      .subscribe(() => {
        this.selection.clear();
      });
  }

  resetSelectedJacketLines() {
    this.showLoader = true;
    const selectedJackets = this.selection.selected;
    this.subscription.add(
      this.finalizeService.resetSelectedJacketLines(selectedJackets).subscribe(
        (res: JacketAccessoriesList[]) => {
          if (res) {
            this.setJacketByIndex(res);
          }
          this.calculateTotalExtendedNetPriceAndGrossMargin(this.jacketDataSource.data, [],[]);
          this.showLoader = false;
          this.selection.clear();
          this.jacketDataSource._updateChangeSubscription();
        },
        error => {
          this.showLoader = false;
          this.selection.clear();
        }
      )
    );
  }

  deleteSelectedJacket() {
    this.showLoader = true;
    this.subscription.add(this.finalizeService.deleteSelectedJacketLines(this.selection.selected).subscribe(() => {
        this.getFinalizeDetails(this._revisionId, this.madein);
        this.jacketDataSource.data = this.jacketList;
        this.showLoader = false;
        this.selection.clear();
        this.snakbarService.success(
          Messages.JACKET_DELETE.jacket_delete
        );
      },
      error => {
        this.showLoader = false;
        this.selection.clear();
      }
    ));
  }

  async confirmDelete() {
    if (await this.sweetAlertService.deleteAlert()) {
      this.deleteSelectedJacket();
    }
  }

  onMadeInChange(value) {
    this.applyVietnamDiscount();
    this.selection.clear();
  }

  getFinalizeDetailsNew() {
    this.showLoader = true;
    let countrySearch = ['Usa'];
    if(this.manufacturedInPresent && this.manufacturedInPresent===true && this.manufacturedInPresent!==null && this.manufacturedInPresent!==undefined && this._manufacturedIn && this._manufacturedIn!==null && this._manufacturedIn!==undefined){
      countrySearch = [this._manufacturedIn];
    }
    this.subscription.add(
      this.finalizeService
        .applyVietnamDiscount({
          discount: this.discountValue,
          revisionId: this._revisionId,
          madeIn: countrySearch,
          currencyId: this.currencyId
        })
        .subscribe(
          (res) => {
            if (res.length > 0) {
              this.calculateTotalExtendedNetPriceAndGrossMargin(res[0], [],[]);
              this.jacketDataSource.data = res[0];
              this.madeInArray=countrySearch[0];
            } else {
              this.jacketDataSource.data = [];
            }
            this.showLoader = false;
            this.discountValue = null;
            this.selection.clear();
          },
          error => {
            this.showLoader = false;
            this.selection.clear();
          }
        )
    );
  }

  applyVietnamDiscount() {
    this.showLoader = true;
    if (this.madeInArray === 'All') {
      this.allMadeIn = this.madeInNewArray.map(x => x.value);
    } else {
      this.allMadeIn = [this.madeInArray];
    }
    if(this.allMadeIn===null || this.allMadeIn===undefined || this.allMadeIn==''){
      this.allMadeIn = ['USA'];
    }
    this.subscription.add(
      this.finalizeService
        .applyVietnamDiscount({
          discount: this.discountValue,
          costaRicaDiscount: this.costaRicadiscountValue,
          revisionId: this._revisionId,
          madeIn: this.allMadeIn,
          currencyId: this.currencyId
        })
        .subscribe(
          (res) => {
            if (res.length > 0) {
              this.jacketDataSource.data = res[0];
            } else {
              this.jacketDataSource.data = [];
            }
            if (this.madeInArray === 'All') {
              this.jacketDataSourceVietnam.data = res[1];
              this.jacketDataSourceCosta = res[2];
            }
            if (res.length == 1) {
              this.calculateTotalExtendedNetPriceAndGrossMargin(res[0], [],[]);
            } else if (res.length == 2) {
              this.calculateTotalExtendedNetPriceAndGrossMargin(res[0], res[1],res[2]);
            }
            else if (res.length == 3) {
              this.calculateTotalExtendedNetPriceAndGrossMargin(res[0], res[1],res[2]);
            }
            this.showLoader = false;
            this.discountValue = null;
            this.selection.clear();
          },
          error => {
            this.showLoader = false;
            this.selection.clear();
          }
        )
    );
  }

  applyVietnamDiscountOnSingleJacket(element: JacketAccessoriesList) {
    this.showLoader = true;
    const discount = element.madeIn.toLowerCase() === 'usa' ? element.discount : element.vietnamDiscount;
    this.subscription.add(
      this.finalizeService
        .updateSingleJacketVietnamDiscount({
          discount: discount,
          revisionId: element.revisionId,
          madeIn: element.madeIn,
          jacketId: element.jacketId
        })
        .subscribe(
          (res: JacketAccessoriesList) => {
            const index = this.jacketDataSource.data.indexOf(element);
            this.jacketDataSource.data[index] = res;
            this.jacketDataSource._updateChangeSubscription();
            this.calculateTotalExtendedNetPriceAndGrossMargin(this.jacketDataSource.data, [],[]);
            this.showLoader = false;
            this.selection.clear();
          },
          error => {
            if (error.applicationStatusCode === 1217) {
              this.snakbarService.error(error.message);
            }
            this.showLoader = false;
            this.selection.clear();
          }
        )
    );
  }

  resetFinalize() {
    this.showLoader = true;
    if (this.madeInArray === 'All') {
      this.allMadeIn = this.madeInNewArray.map(x => x.value);
    } else {
      this.allMadeIn = [this.madeInArray];
    }
    if(this.allMadeIn===null || this.allMadeIn===undefined || this.allMadeIn==''){
      this.allMadeIn = ['USA'];
    }
    this.subscription.add(
      this.finalizeService.resetFinalize(this._revisionId, this.allMadeIn).subscribe(
        (res) => {
          if (res.length > 0) {
            this.jacketDataSource.data = res[0];
          } else {
            this.jacketDataSource.data = [];
          }
          if (this.madeInArray === 'All') {
            this.jacketDataSourceVietnam.data = res[1];
            this.jacketDataSourceCosta = res[2];
          }
          if (res.length == 1) {
            this.calculateTotalExtendedNetPriceAndGrossMargin(res[0], [],[]);
          } else if (res.length == 2) {
            this.calculateTotalExtendedNetPriceAndGrossMargin(res[0], res[1],res[2]);
          }
          else if (res.length == 3) {
            this.calculateTotalExtendedNetPriceAndGrossMargin(res[0], res[1],res[2]);
          }
          this.showLoader = false;
          this.selection.clear();
        },
        error => {
          this.showLoader = false;
          this.selection.clear();
        }
      )
    );
  }

  setJacketByIndex(res) {
    res.forEach(jacket => {
      const index = this.jacketDataSource.data.indexOf(
        this.jacketDataSource.data.find(jacketList => jacketList.jacketId === jacket.jacketId)
      );
      this.jacketDataSource.data[index] = jacket;
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
