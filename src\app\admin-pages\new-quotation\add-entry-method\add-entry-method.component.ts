import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { SalesOrderSummaryService } from '../summary-sales-order.service';
import { NgForm } from '@angular/forms';
import { SweetAlertService } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';

@Component({
  selector: 'sfl-add-entry-method',
  templateUrl: './add-entry-method.component.html'
})
export class AddEntryMethodComponent implements OnInit, OnDestroy {
  quotationId: number;
  entryMethod: string;
  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public entryMethodDialogRef: MatDialogRef<AddEntryMethodComponent>,
    private salesOrderSummaryService: SalesOrderSummaryService,
    @Inject(MAT_DIALOG_DATA) data,
    private sweetAlertService: SweetAlertService
  ) {
    this.quotationId = data.quotationId;
    this.entryMethod = data.entryMethod;
  }

  ngOnInit() {}

  // used to handle the entry method change
  async changeEntryMethod(entryMethodForm: NgForm, value) {
    if ((await this.sweetAlertService.warningWhenEntryMethodChange()) && this.entryMethod) {
      this.showLoader = true;
      this.subscription.add(
        this.salesOrderSummaryService.changeEntryMethod(this.quotationId, this.entryMethod).subscribe(
          success => {
            const obj = { selectedEntryMethod: this.entryMethod, mode: value === 'save' ? 0 : 1 };
            entryMethodForm.reset();
            this.showLoader = false;
            this.entryMethodDialogRef.close(obj);
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  closeDialog() {
    this.entryMethodDialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
