
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';



@Injectable({ providedIn: 'root' })
export class FinalReviewService {

    constructor(private http: HttpClient) { }

    saveFinalReview(review) {
        return this.http.post(AppConfig.FINAL_REVIEW_SAVE, review).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

    updateFinalReview(review, jacketId) {
      return this.http.delete(AppConfig.FINAL_REVIEW_UPDATE + jacketId, review).pipe(
          map(utils.extractData),
          catchError(utils.handleError),);
  }

    getFinalReviewByJacketId(jacketId) {
        return this.http.get(AppConfig.FINAL_REVIEW_SAVE + '/jacket/' + jacketId).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }
}
