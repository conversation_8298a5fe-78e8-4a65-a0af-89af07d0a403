@import '~bootstrap/dist/css/bootstrap.css';
.sticky-table-header {
  background-color: #f1f1f1;
  color: #0000008a;
  z-index: 1000 !important;
  position: sticky;
}

tr td {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid black;
  padding: 0px !important;
}

.link {
  color: black;
  font-weight: bold;
  text-decoration: underline;
}

.text-left {
  text-align: left !important;
}

.link:hover {
  color: black;
  text-decoration: none;
  text-decoration: underline;
}

.notes-cell, .sqt-link-cell {
  position: relative;
  min-height: 40px;
  text-align: left;

  .notes-content, .sqt-link-content {
    min-height: 40px;
    padding-inline: 4px;
    word-break: break-word;
    white-space: normal;
    cursor: pointer;
    text-align: left;

    .empty-cell {
      color: #999;
      font-style: italic;
    }
  }

  .notes-editor, .sqt-link-editor {
    width: 100%;

    .notes-textarea, .sqt-link-textarea {
      width: 100%;
      min-height: 40px;
      padding-inline: 4px;
      border: none;
      outline: none;
      resize: none;
      font-family: inherit;
      font-size: inherit;
      background-color: #f9f9f9;
      border-radius: 2px;
      box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
      overflow: hidden;
      text-align: left;
    }

    .auto-grow {
      height: auto;
    }
  }
}

.text-wrap {
  white-space: normal !important;
  word-break: break-word;
}

.formatted-text {
  margin: 0;
  padding-inline: 4px;
  background: transparent;
  border: none;
  font-family: inherit;
  font-size: inherit;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  color: inherit;
  text-align: left;

  a.link {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    text-align: left;
  }
}
