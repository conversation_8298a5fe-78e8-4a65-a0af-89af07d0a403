import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { ThermostatMaster, ThermostatMasterPageable, ThermostatFilter, GenericPageable } from '../masterdata-management.model';
import { MatTableDataSource, MatSort, MatPaginator, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageThermostatListComponent } from './manage-thermostat-list/manage-thermostat-list.component';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-thermostat-list',
  templateUrl: './thermostat-list.component.html'
})
export class ThermostatListComponent implements OnInit, OnDestroy {
  pageTitle = 'Thermostat Master';
  thermostatMaster: ThermostatMaster;
  thermostatMasterPageable: GenericPageable<ThermostatMaster>;
  thermostatMasterDataSource = new MatTableDataSource<ThermostatMaster>();
  thermostatMasterMasterColumns = DisplayColumns.Cols.ThermostatMaster;
  thermostatFilter: ThermostatFilter = new ThermostatFilter();
  dataSource = new MatTableDataSource<ThermostatMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldPartNumber = Values.FilterFields.partNumber;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getThermostatMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new thermostat
  addThermostatMaster() {
    this.editThermostatMaster(new ThermostatMaster());
  }

  // used to add filter to thermostat listing
  async addFilter() {
    this.filter =
      this.thermostatFilter.partNumber === '' ? [] : [{ key: this.filterFieldPartNumber, value: this.thermostatFilter.partNumber }];
    this.getThermostatMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of thermostat listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber,
        value: fieldToClear === this.filterFieldPartNumber ? (this.thermostatFilter.partNumber = '') : this.thermostatFilter.partNumber
      }
    ];
    this.getThermostatMasterData(this.initialPageIndex, this.pageSize);
  }

  getThermostatMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getThermostatsList(this.filter, pageable).subscribe(
        (res: GenericPageable<ThermostatMaster>) => {
          this.thermostatMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createThermostatTable(this.thermostatMasterPageable);
          this.showLoader = false;
        },
        error => {
          if (error.applicationStatusCode === 1235) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false;
          this.thermostatMasterDataSource.data = [];
        }
      )
    );
  }

  createThermostatTable(serviceRequestList: GenericPageable<ThermostatMaster>) {
    this.thermostatMasterDataSource.data = serviceRequestList.content;
  }

  getThermostatMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getThermostatMasterData(this.pageIndex, this.pageSize);
  }

  getThermostatMasterSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getThermostatMasterData(this.pageIndex, this.pageSize);
  }

  async deleteThermostatMaster(thermostatId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteThermostatsList(thermostatId).subscribe(
        () => {
          this.snakbarService.success('Thermostat' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getThermostatMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }
  editThermostatMaster(thermostat: ThermostatMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = thermostat;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-thermostat-master-model';
    const dialogRef = this.matDialog.open(ManageThermostatListComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          thermostat.thermostatListId
            ? 'Thermostat' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Thermostat' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getThermostatMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
