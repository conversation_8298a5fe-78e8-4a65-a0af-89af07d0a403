import { <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { Component } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { DesignPlanComponent } from '../../design-plan.component';
import { WireSelector, SearchParameter, WireSelectorResult } from '../element.model';
import { ElementService } from '../element.service';
import { SnakbarService, Messages } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-search-parameters',
  templateUrl: './search-parameters.component.html'
})
export class SearchParametersComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  elementName: string;
  isTapOrDualWire: boolean;
  wireSelector: WireSelector;
  wireSelectorResult: WireSelectorResult[];
  productType: string;

  showLoader: boolean;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    private readonly dialogRef: MatDialogRef<DesignPlanComponent>,
    private readonly elementService: ElementService,
    private readonly snakbarService: SnakbarService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.elementName = data.elementName;
    this.wireSelector = data.wireInfo;
    this.productType = this.wireSelector.basicInfoDTO.productType;
  }

  ngOnInit() {
    this.wireSelector.searchParameterDTO = new SearchParameter();
    if (this.elementName.toLowerCase().includes('tape') || this.elementName.toLowerCase().includes('dual')) {
      this.isTapOrDualWire = true;
    }
    if (this.wireSelector.elementInfoDTO.wireType.toLowerCase().includes('grounded')) {
      this.wireSelector.searchParameterDTO.picksStart = 6;
      this.wireSelector.searchParameterDTO.picksEnd = 9;
    } else {
      this.wireSelector.searchParameterDTO.picksStart = 6;
      this.wireSelector.searchParameterDTO.picksEnd = 12;
    }
    // we don't need to consider tpi and tpi picks for search if product type is silicone
    if (this.productType && this.productType.toUpperCase() === Values.productTypeSilicone) {
      this.wireSelector.searchParameterDTO.tpiStart = 0;
      this.wireSelector.searchParameterDTO.tpiEnd = 0;
      this.wireSelector.searchParameterDTO.tpiPicksStart = 0;
      this.wireSelector.searchParameterDTO.tpiPicksEnd = 0;
    }
  }

  searchWire() {
    this.showLoader = true;
    this.subscription.add(
      this.elementService.searchWire(this.wireSelector).subscribe(
        (res: WireSelectorResult[]) => {
          if (res.length > 0) {
            this.showLoader = false;
            this.dialogRef.close(res);
          } else {
            this.showLoader = false;
            this.snakbarService.error(Messages.error_msg.nodata);
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
