<div fxLayout="column" fxLayoutAlign="space-between">
  <div class="sfl-loading" *ngIf="showLoader">
    <mat-progress-spinner
      class="sfl-global-spinner-loader"
      [mode]="mode"
      [color]="color"
      [diameter]="spinnerDiameter">
    </mat-progress-spinner>
  </div>

  <div class="eco-report flex-container">
    <div class="less-peding cust_fields">
      <form class="fields" #ecoForm="ngForm">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-xs="100">
          <mat-card class="mb-10">
            <strong class="pb-15">ECO Details</strong>
            <div class="mt-10" fxLayout="row" fxLayoutGap="20px">
              <mat-form-field appearance="outline">
                <mat-label>ECR Number</mat-label>
                <input matInput placeholder="ECR #" [(ngModel)]="_ecrDto.ecrNo" name="ecrNumber" (change)="updateEcr()"
                       sflIsDecimal>
              </mat-form-field>
              <mat-form-field appearance="outline">
                <mat-label>ECO Creator</mat-label>
                <mat-select placeholder="Creator" [(ngModel)]="ecoDto.ecoCreator" name="creator"
                            (ngModelChange)="updateEco()">
                  <mat-option *ngFor="let creator of users" [value]="creator?.id">
                    {{ creator.firstName }} {{ creator.lastName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field appearance="outline">
                <mat-label>Creation Date</mat-label>
                <input matInput [matDatepicker]="date" [max]="maxDate" placeholder="Date" name="ecoDate"
                       [(ngModel)]="ecoDto.creationDate" #date="ngModel" autocomplete="off" [disabled]="true">
                <mat-datepicker-toggle matSuffix [for]="date"></mat-datepicker-toggle>
                <mat-datepicker #date></mat-datepicker>
              </mat-form-field>
              <mat-form-field appearance="outline">
                <mat-label>Change Type</mat-label>
                <mat-select placeholder="Creator" [(ngModel)]="ecoDto.changeType" name="changeType"
                            (ngModelChange)="updateEco()">
                  <mat-option *ngFor="let type of changeTypes" [value]="type">
                    {{ type }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </mat-card>
          <!-- Tabs -->

          <div>
            <mat-card>
              <mat-tab-group mat-stretch-tabs class="eco-matGroup" (selectedTabChange)="switchingTab($event)">
                <mat-tab label="Cover Page">
                  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5"
                       *ngFor="let approval of ecoWorkFlowData['Approvals Required']; let i = index;">
                    <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
                      <strong class="pb-15" *ngIf="i == 0">{{ approval.title }}</strong>
                      <mat-checkbox name="eco-checkbox" color="warn" class="critical-check"
                                    [(ngModel)]="approval.status" [attr.name]="'approv'+i"
                                    (ngModelChange)="updateEcoWorkFlow(approval)"> {{ approval.description }}
                      </mat-checkbox>
                    </div>
                  </div>
                  <div fxFlex fxLayout="row" fxLayoutGap="20px">
                    <div fxLayout="column" fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-xs="50">
                      <strong class="pb-15">Description of Change</strong>
                      <div fxLayout="row wrap" class="mb-10 cust_fields">
                        <textarea name="eco-checkbox" matInput placeholder="Description of change"
                                  [(ngModel)]="ecrDto.description" name="descriptionOfChange" rows="6"
                                  class="sfl-textarea" (change)="updateEcr()"></textarea>
                      </div>
                    </div>
                    <div fxLayout="column" fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-xs="50">
                      <div class="cust_fields">
                        <div fxFlex fxLayout="row wrap">
                          <strong class="pb-15" fxFlex>Part Number(s)</strong>
                          <button class="add-new-icon-button" mat-icon-button fxFlexAlign="end" color="warn"
                                  (click)="addPartNumber()" matTooltip="Add Part Number" matTooltipPosition="above">
                            <mat-icon>add</mat-icon>
                          </button>
                        </div>
                      </div>
                      <div class="mb-10 cust_fields sfl-mat-list mat-list-selected-pn">
                        <div fxLayout="row wrap" class="ecr-pn-sync">
                          <div fxFlex fxLayout="row wrap">
                            <h4 fxFlex style="font-weight: bold">Part Numbers</h4>
                            <span style="font-weight: bold" fxFlex="5">US</span>
                            <span style="font-weight: bold" fxFlex="5">VN</span>
                            <span style="font-weight: bold" fxFlex="5">CR</span>
                            <span style="font-weight: bold" fxFlex="5">US-V</span>
                            <span style="font-weight: bold" fxFlex="5">US-R</span>
                            <span fxFlex="4"></span>
                            <span fxFlex="5"></span>
                            <span fxFlex="5"></span>
                            <span fxFlex="5"></span>
                            <span fxFlex="5"></span>
                          </div>
                          <hr>
                        </div>
                        <div fxLayout="row wrap" class="ecr-pn-sync"
                             *ngFor="let pnObj of ecrDto.ecrPartNumbers;let i = index">
                          <div fxFlex fxLayout="row wrap">
                            <h4 fxFlex>{{ pnObj?.partNumber }}</h4>
                            <div fxFlex="30" *ngIf="pnObj.syncStatus">
                            <span class="bom-sync-status"
                                [ngClass]="(pnObj.syncStatus.toLowerCase().includes('failed') ? 'sync-error' : 'bom-sync-status')">{{ pnObj.syncStatus }}</span>
                            </div>
                            <div fxFlex="5" class="open-doc">
                              <mat-icon (click)="getECRPartSyncStatus(pnObj)">refresh</mat-icon>
                            </div>
                            <mat-checkbox fxFlex="5" color="warn"
                                          matTooltip="Part number: {{ pnObj?.partNumber }} , On-Hold Response Code: {{ pnObj?.usHoldReasonCode }}, On-Hold Reason: {{ pnObj?.usHoldComments }} "
                                          name="onhold" [(ngModel)]="pnObj.usOnHold" readonly="true"
                                          [disabled]="true"></mat-checkbox>
                            <mat-checkbox fxFlex="5" color="warn"
                                          matTooltip="Part number: {{ pnObj?.partNumber }} , On-Hold Response Code: {{ pnObj?.vietnamHoldReasonCode }}, On-Hold Reason: {{ pnObj?.vietnamHoldComments }} "
                                          name="onhold" [(ngModel)]="pnObj.vietnamOnHold" readonly="true"
                                          [disabled]="true"></mat-checkbox>
                            <mat-checkbox fxFlex="5" color="warn"
                                          matTooltip="Part number: {{ pnObj?.partNumber }} , On-Hold Response Code: {{ pnObj?.costaRicaHoldReasonCode }}, On-Hold Reason: {{ pnObj?.costaRicaHoldComments }} "
                                          name="onhold" [(ngModel)]="pnObj.costaRicaOnHold" readonly="true"
                                          [disabled]="true"></mat-checkbox>
                            <mat-checkbox fxFlex="5" color="warn"
                                          matTooltip="Part number: {{ pnObj?.partNumber }} , On-Hold Response Code: {{ pnObj?.usVietnamHoldReasonCode }}, On-Hold Reason: {{ pnObj?.usVietnamHoldComments }} "
                                          name="onhold" [(ngModel)]="pnObj.usVietnamOnHold" readonly="true"
                                          [disabled]="true"></mat-checkbox>
                            <mat-checkbox fxFlex="5" color="warn"
                                          matTooltip="Part number: {{ pnObj?.partNumber }} , On-Hold Response Code: {{ pnObj?.usCostaRicaHoldReasonCode }}, On-Hold Reason: {{ pnObj?.usCostaRicaHoldComments }} "
                                          name="onhold" [(ngModel)]="pnObj.usCostaRicaOnHold" readonly="true"
                                          [disabled]="true"></mat-checkbox>
                            <button fxFlex="10" class="custom-button" color="warn" (click)="syncOnhold(pnObj,'OnHold')">
                              Set On-Hold
                            </button>
                            <button fxFlex="10" class="custom-button" color="warn"
                                    (click)="syncOnhold(pnObj,'OnHoldClear')">Remove Hold
                            </button>
                            <a (click)="confirmPartNumberAndAttachment(pnObj.id, 'partNumber')">
                              <div class="open-doc" fxLayoutAlign="end">
                                <mat-icon>delete</mat-icon>
                              </div>
                            </a>
                          </div>
                          <hr>
                        </div>
                      </div>
                    </div>
                    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-xs="40">
                      <div class="cust_fields">
                        <div fxFlex fxLayout="row wrap">
                          <strong class="pb-15" fxFlex>Attachment(s)</strong>
                          <button class="add-new-icon-button" mat-icon-button fxFlexAlign="end" color="warn"
                                  (click)="addAttachment(ecrDto.id)" matTooltip="Add Attachment"
                                  matTooltipPosition="above">
                            <mat-icon>add</mat-icon>
                          </button>
                        </div>
                      </div>
                      <div class="cust_fields sfl-mat-list mat-list-selected-pn">
                        <div fxLayout="row wrap" class="filename"
                             *ngFor="let file of ecrDto.ecrAttachments;let i = index">
                          <mat-icon>insert_drive_file</mat-icon>
                          <h4 fxFlex class="open-doc" (click)="openDoc(file)">{{ file?.fileName }}</h4>
                          <a (click)="confirmPartNumberAndAttachment(file.id, 'attachment')">
                            <div class="open-doc" fxLayoutAlign="end">
                              <mat-icon>delete</mat-icon>
                            </div>
                          </a>
                          <hr>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div fxFlex fxLayout="row">
                    <div class="eco-checks mb-10" fxLayoutGap="20px">
                      <mat-checkbox color="warn" [(ngModel)]="ecoDto.openSo" name="openSo"
                                    (ngModelChange)="updateEco()">Open So
                      </mat-checkbox>
                      <mat-checkbox color="warn" [(ngModel)]="ecoDto.openJob" name="openJob"
                                    (ngModelChange)="updateEco()">Open Job(s)
                      </mat-checkbox>
                    </div>
                  </div>
                  <!-- PQP Information -->

                  <div class="pqp-info" *ngIf="ecoDto.criticalPart && ecoPqpDetailsAvailable">


                    <div fxLayout="column" fxFlex.gt-lg="10" fxFlex.gt-md="10">
                      <strong class="pb-15">PQP Details</strong>
                    </div>
                  </div>


                  <div fxLayout="row wrap" fxLayoutGap="20px" class="mb-10">

                    <div fxLayout="column" fxLayoutGap="10px" fxFlex.gt-lg="40" fxFlex.gt-md="40">
                      <div fxLayout="column" fxFlex.gt-lg="10" fxFlex.gt-md="10">
                        <strong class="pb-15">Comments</strong>
                        <div fxLayout="row wrap" class="mb-10 cust_fields">
                          <textarea matInput placeholder="Comments" [(ngModel)]="pqpDto.pqpComment" name="pqpComments"
                                    rows="6" class="sfl-textarea" (change)="updatePqp()"></textarea>
                        </div>
                      </div>
                    </div>
                  </div>

                </mat-tab>
                <mat-tab label="Work Flow">
                  <div *ngIf="currentTab == 'workFlow'">
                    <sfl-eco-workflow [ecoDto]="ecoDto" [_users]="users"></sfl-eco-workflow>
                  </div>
                </mat-tab>
                <mat-tab label="Design Activity">
                  <div *ngIf="currentTab == 'designActivity'">
                    <sfl-eco-design-activity [ecoDto]="ecoDto" [_users]="users"></sfl-eco-design-activity>
                  </div>
                </mat-tab>
                <mat-tab label="Title Block Editor">
                  <div *ngIf="currentTab == 'titleBlock'">
                    <mat-form-field style="margin-top: 20px">
                      <mat-label>Select Part Number</mat-label>
                      <mat-select placeholder="partNumber" name="partNumber" [(ngModel)]="partNumber"
                                  (selectionChange)="getFinalReviewECR()"
                      >
                        <mat-option *ngFor="let partNumber of partNumberList" [value]="partNumber?.partNumber">
                          {{ partNumber?.partNumber }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                    <sfl-title-block-editor *ngIf="jacketId!==undefined && jacketId!==null"
                                            [jacketID]="jacketId"></sfl-title-block-editor>
                  </div>
                </mat-tab>
                <mat-tab label="Final Review">
                  <div *ngIf="currentTab == 'finalReview'">
                    <sfl-final-ecr-review [ecoDto]="ecoDto"></sfl-final-ecr-review>
                  </div>
                </mat-tab>
                <mat-tab label="Costing">
                  <div *ngIf="currentTab == 'costing'">
                    <sfl-eco-costing [ecoDto]="ecoDto" [_users]="users"></sfl-eco-costing>
                  </div>
                </mat-tab>
              </mat-tab-group>
            </mat-card>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
