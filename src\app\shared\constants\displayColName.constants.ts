export const DisplayColumns = {
  Cols: {
    UserDisplayColumns: ['firstName', 'email', 'login', 'activated', 'authorities', 'country', 'initialName', 'action'],
    DashboardQuotationList: [
      'quotationNumber',
      'salesOrderNumber',
      'quotationStatus',
      'revision',
      'salesAssociate',
      'customerName',
      'design',
      'actions'
    ],
    DesignQuotationList: [
      'quotationNumber',
      'salesOrderNumber',
      'projectTitle',
      'quotationStatus',
      'activeRevision',
      'salesAssociate',
      'customerName',
      'design',
      'shipDate',
      'dollarAmount'
    ],
    thermostatsdisplayedColumns: [
      'openTemp',
      'tolerance',
      'amps',
      'type',
      'partNumber',
      'cost',
      'openOnRise',
      'manualReset',
      'volts',
      'thermostatType',
      'installationMethod',
      'usaStock',
      'vietnamStock',
      'action'
    ],
    CostReport: ['partNumber', 'list', 'net', 'material', 'labor', 'burden', 'subcnt', 'mtlBurd', 'total', 'margin'],
    GoldReportQuotationColumn: ['salesOrderNumberQuotation', 'customerName', 'shipBy', 'quotationStatus', 'reportDate'],
    GoldReportColumn: ['salesOrderNumberGoldReport', 'partNumber', 'heaterman', 'revision', 'element', 'quantity', 'golden'],
    ECRManagement: ['ecrNo', 'ecrStatusId', 'ecrDate', 'submitted', 'requestor', 'review', 'partNumber', 'description', 'action'],
    ECRPartManagement: ['partNo', 'syncStatus', 'refresh', 'usSync', 'vietnamSync', 'costaRicaSync', 'usvSync', 'uscSync', 'setOnHold','clearHold','action'],
    QuotationStatusMaster: ['status', 'defaultHidden', 'type','rowColor','appEngRowColor','forApproval', 'isObsolete', 'orderNumber', 'action'],
    AccessoryMaster: ['partNumber', 'description', 'controllerName', 'listPrice', 'usCost', 'isObsolete', 'action'],
    MaterialMaster: ['material', 'partNumber', 'costPerSq', 'imageUrl', 'inventory', 'maxTemp', 'maxTempF', 'identifier','isObsolete', 'action'],
    PlugMaster: [
      'plugName',
      'plugCost',
      'partNumber',
      'jacketType',
      'clothCe',
      'clothUl',
      'imageUrl',
      'jumperPartNumber',
      'maxAmps',
      'maxVolts',
      'isObsolete',
      'action'
    ],
    ClosureMaterialMaster: ['name', 'partNumber1', 'partNumber2', 'ul p/n', 'costPerSq', 'imageUrl', 'maxTemp', 'maxTempF', 'isObsolete', 'action'],
    MaterialProperty: ['name', 'density', 'materialType', 'specificHeat', 'isObsolete', 'action'],
    ThermostatMaster: [
      'partNumber',
      'amps',
      'closeTemp',
      'closeTempF',
      'closeTolC',
      'closeTolF',
      'cost',
      'manualReset',
      'manufacturer',
      'openOnRise',
      'openTemp',
      'openTempF',
      'adjustable',
      'tolerance',
      'toleranceF',
      'type',
      'volts',
      'isObsolete',
      'action'
    ],
    FeaturesMaster: ['name'],
    AccessoryController: ['name', 'isObsolete', 'action'],
    ProductGroupCoverPage: ['name'],
    ProductTypeCoverPage: ['name'], // Legacy alias for backward compatibility
    PQPFamily: ['name', 'isObsolete', 'action'],
    PowerCordConnector: ['name', 'partNumber', 'price', 'value', 'action'],
    SleevingTypesAndStrainReliefsCols: ['name', 'partNumber', 'cost', 'clothCE', 'clothUL', 'isObsolete', 'action'],
    PlugLightsCols: ['name', 'partNumber', 'cost', 'type', 'clothCE', 'clothUL', 'isObsolete', 'action'],
    GoldStandardTapeWidthMaster: ['tapeType', 'width', 'isObsolete', 'action'],
    BHXGoldWireTapesMasterCols: ['alloyName', 'type', 'tpiStr', 'picks', 'wireType', 'isObsolete', 'action'],
    AlloyMasterCols: [
      'alloyName',
      'ohmsPerFoot',
      'maxTempC',
      'gndPartNumber',
      'fgPartNumber',
      'samPartNumber',
      'tpignPartNumber',
      'tpifgPartNumber',
      'tpisamPartNumber',
      'tpiWperIn',
      'msm',
      'msb',
      'isObsolete',
      'action'
    ],
    HeatingTapesMasterCols: [
      'tapeType',
      'tapePartNumber',
      'sequenceNumber',
      'width',
      'ohmsPerFt',
      'alloyName',
      'wireType',
      'strands',
      'tpi',
      'picks',
      'warps',
      'isDualWireTape',
      'isObsolete',
      'action'
    ],
    DepartmentsCols: ['name', 'active', 'action'],
    ECRStatusesCols: ['status', 'active', 'action'],
    BHXMaterialMasterCols: [
      'action',
      'partNumber',
      'grouping',
      'description',
      'siliconOpr',
      'clothOpr',
      'inseparableOpr',
      'qty',
      'formula',
      'operationName',
      'sequence',
      'opNumber',
      'prodHrs',
      'setupHrs',
      'uom',
      'blocked',
      'customer',
      'customerAbbreviation',
      'elementType',
      'wireType',
      'ce',
      'ul',
      'manualResetThermostat',
      'privateLabel',
      'layered',
      'maxDiameter',
      'minDiameter',
      'closure',
      'plug',
      'connector',
      'sensConn',
      'sleeving',
      'thermostat',
      'maxLength',
      'minLength',
      'greenLight',
      'minJumpers',
      'sensorType',
      'productType',
      'minVolts',
      'maxVolts',
      'minAmps',
      'maxAmps',
      'minLead',
      'maxLead',
      'minTemp',
      'maxTemp',
      'phase',
      'installationMethod',
      'strainRelief',
      'hazardous',
      'appType',
      'controller',
      'leadTypes',
      'deleted'
    ],
    SensorConnectorsAndTypes: ['id', 'isObsolete', 'action'],
    SensorControlTypes: ['value', 'isObsolete', 'action'],
    PowerCordMaterialsCols: ['powerCordMaterialId', 'value', 'price', 'isObsolete', 'action'],
    PowerCordOptionsCols: ['powerCordConstantId', 'value', 'isObsolete', 'action'],
    WarpsMasterCols: ['tapeWidth', 'warp', 'isObsolete', 'action'],
    estEngRelDateMasterCols: ['prodType', 'quoteStatus', 'noOfDays', 'action'],
    ThermostatTypesMasterCols: ['id', 'isObsolete', 'action'],
    LaborMasterCols: ['labor', 'burden', 'country', 'materialOH', 'isObsolete', 'action'],
    PowerCordVoltage: ['value', 'isObsolete', 'action'],
    LeadTypeMaster: ['leadName', 'partNumber', 'maxTemp', 'maxVolts', 'costPerFoot', 'isObsolete', 'action'],
    OperationsFinalReviewColumn: ['sequence', 'operation', 'opNumber', 'setupHours', 'prodHours', 'action'],
    OperationsFinalReviewColumnJacketReference: ['sequence', 'operation', 'opNumber', 'setupHours', 'prodHours'],
    MaterialsColumn: ['sequence', 'partNumber', 'description', 'quantity', 'uom', 'relOp', 'action'],
    MaterialsColumnJacketRefernce: ['sequence', 'partNumber', 'description', 'quantity', 'uom', 'relOp'],
    LabelsColumn: [
      'number',
      'format',
      'volts',
      'watts',
      'amps',
      'phase',
      'size',
      'width',
      'length',
      'csv',
      'csa',
      'mhlv',
      'mhla',
      'tempRange',
      'lowT',
      'highT',
      'modelNumber',
      'open1',
      'open2',
      'open3',
      'partNumber',
      // 'type',
      'action'
    ],
    LabelsColumnJacketReference: [
      'number',
      'format',
      'volts',
      'watts',
      'amps',
      'phase',
      'size',
      'width',
      'length',
      'csv',
      'csa',
      'mhlv',
      'mhla',
      'tempRange',
      'lowT',
      'highT',
      'modelNumber',
      'open1',
      'open2',
      'open3',
      'partNumber'
      // 'type',
    ],
    LabelEntryColumns: [
      'update',
      'image',
      'toSync',
      'labelPartNumber',
      'format',
      'volts',
      'watts',
      'amps',
      'phase',
      'size',
      'modelNumber',
      'open1',
      'open2',
      'open3',
      'width',
      'length',
      'csv',
      'csa',
      'mhlv',
      'mhla',
      'tempRange',
      'lowT',
      'highT',
    ],
    SoInDesignColumn: ['soInDesign', 'noOfDesign', 'dollarValue'],
    SoInDesignDetailedColumn: ['classification', 'noOfDesign', 'dollarValue'],
    QuoteCompletedColumn: ['classification', 'noOfDesign', 'dollarValue'],
    QuoteCompletedDetailedColumn: ['salesOrderNumber', 'quotationNumber', 'noOfDesigns', 'dollarValue'],
    AppEngineeringQuoteTrackerDashboardColumn: [
      'customerName',
      'projectTitle',
      'quotationNumber',
      'dateSubmittedToApp',
      'appStartedDate',
      'appCompletedDate',
      'productType',
      'extQuoteRequired',
      'assignedAppEngineerId',
      'accountManagerId',
      'oemRequiringSimulation',
      'noOfDesigns',
      'customerClarificationRequired',
      'notes',
      'sqtLink',
      'currentStatusComment'
    ],
    DesignEngineeringQuoteTrackerDashboardColumn: [
      'projectTitle',
      'soNumber',
      'customerName',
      'designLocation',
      'shipDate',
      'folderSubmittedDate',
      'designStatusId',
      'ofa1Date',
      'approval1Date',
      'ofa2Date',
      'approval2Date',
      'salesRepId',
      'productType',
      'designerId',
      'noOfDesigns',
      'dollarAmount',
      'designComments',
      'noOfRevisions',
      'noOfDaysOFA1',
      'noOfDaysOFA2',
      'totalDaysOFA',
      'appEnggAssigned'
    ],
    CurrencyMasterDataColumn: [
      'name',
      'abbreviation',
      'conversionRateWrtUSD',
      'obsolete',
      'symbol',
      'action'
    ],

    CcdcMasterDataColumn: [
      'user',
      'templateName',
      'description',
      'action'
    ],

    EcoLineMasterDataColumns: [
      'feature',
      'revisionId',
      'buttedRevisionId',
      'action'
    ],

    PartNumberMasterDataColumn: [
      'facing',
      'insulation',
      'liner',
      'number',
      'pattern',
      'action'
    ],

    LabelMasterDataColumn: [
      'labelPartNumber',
      'format',
      'volts',
      'watts',
      'amps',
      'phase',
      'size',
      'width',
      'length',
      'csv',
      'csa',
      'mhlv',
      'mhla',
      'tempRange',
      'lowT',
      'highT',
      'modelNumber',
      'imageUrl',
      'open1',
      'open2',
      'open3',
      'action'
    ],

    SolidWorksMasterDataColumn: [
      'usaFilePath',
      'vietnamFilePath',
      'action'
    ]
  }
};
