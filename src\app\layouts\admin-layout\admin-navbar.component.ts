import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SharedService } from 'src/app/shared';
import { AuditCountResponseDTO, Types } from '../design-eng-layout/design-eng-layout.model';

import { AdminNavbarService } from './admin-navbar.service';

@Component({
  selector: 'sfl-admin-navbar',
  templateUrl: './admin-navbar.component.html',
  styleUrls: ['./admin-navbar.scss']
})

export class AdminNavbarComponent implements OnInit, OnDestroy {
  topFiveQuotation: AuditCountResponseDTO[];
  type = Types;
  showLoader: boolean;
  subscription = new Subscription();
  constructor(
    public readonly adminNavbarService: AdminNavbarService,
    public readonly sharedService: SharedService,
    private router: Router
  ) {
  }

  ngOnInit(): void {
    this.getTopFiveQuotationApp();
  }

  getTopFiveQuotationApp() {
    const userId = this.sharedService.getUserId();
    this.showLoader = true;
    this.subscription.add(
      this.adminNavbarService.getTopFiveSo(userId, this.type.APP).subscribe(
        (res) => {
          this.topFiveQuotation = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  addMenuItem(): void {
    this.adminNavbarService.add({
      state: 'menu',
      name: 'MENU',
      type: 'sub',
      icon: 'trending_flat',
      children: [
        { state: 'menu', name: 'MENU' },
        { state: 'timeline', name: 'MENU' }
      ]
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
