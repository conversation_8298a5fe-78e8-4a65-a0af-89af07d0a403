<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #ecoLineMaterialForm="ngForm" (ngSubmit)="updateEcoLineMaster()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Feature"
            [(ngModel)]="ecoLineRevisionMaster.feature"
            name="ecoLineMasterFeature"
            #featureName="ngModel"
            disabled
            sflNoWhiteSpaces
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Non Butted Revision Number"
            [(ngModel)]="ecoLineRevisionMaster.revisionNumber"
            name="revisionNumber"
            #revisionNumberInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Butted Revision Number"
            [(ngModel)]="ecoLineRevisionMaster.buttedRevisionId"
            name="buttedRevisionId"
            #buttedRevisionIdInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!ecoLineMaterialForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
