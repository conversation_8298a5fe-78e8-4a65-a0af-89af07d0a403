<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Workflow Information
  <mat-icon
    *ngIf="showReloadButton"
    class="open-doc sfl-pull-right"
    [matTooltip]="outDatedViewErrorMessage"
    color="warn"
    matTooltipClass="sfl-formula-tooltip"
    (click)="reloadPage()"
    id="refresh"
  >
    cached
  </mat-icon>
  <hr/>
</h2>
<form #workflowForm="ngForm">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="48" fxFlex.gt-md="57" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Approval Level" name="approvalLevel" [(ngModel)]="workFlow.approvalLevel">
              <mat-option *ngFor="let level of approvalLevels | orderBy:'name'" [value]="level.value">
                {{ level.name }}
              </mat-option>
              <mat-option>None</mat-option>
            </mat-select>
          </mat-form-field>

          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <input matInput placeholder="Entry Date" [(ngModel)]="workFlow.entryDate" name="entryDate"
                     autocomplete="off" readonly/>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <input matInput placeholder="App Enggineer Started" [(ngModel)]="workFlow.dateAppStarted"
                     name="appEnggStarted" readonly/>
            </mat-form-field>
          </div>
          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <input matInput placeholder="Project Name" [(ngModel)]="workFlow.projectName" name="projectName"
                     autocomplete="off"/>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <input
                matInput
                placeholder="App Enggineer Completed"
                [(ngModel)]="workFlow.dateAppCompleted"
                name="appEnggCompleted"
                readonly
              />
            </mat-form-field>
          </div>
          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <mat-select
                placeholder="App Enggineer Assigned"
                name="appEnggAssigned"
                [(ngModel)]="workFlow.assignedAppEngineerId"
                name="AppEnggAssigned"
                (selectionChange)="appEnggAssigned()"
              >
                <mat-option *ngFor="let engg of salesassociates | orderBy:'firstName'"
                            [value]="engg?.id"> {{ engg?.firstName }} {{ engg?.lastName }}
                </mat-option>
                <mat-option>None</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div fxLayout="d-flex flex-column">
            <div>
              <mat-checkbox color="warn" [(ngModel)]="workFlow.externalQuoteRequired" name="externalQuoteRequired"
                            disabled>
                External Quote Required
              </mat-checkbox>
            </div>
            <div>
              <mat-checkbox color="warn" [(ngModel)]="workFlow.customerClarificationRequired"
                            name="customerClarificationRequired" disabled>
                Customer Clarification Required
              </mat-checkbox>
            </div>
          </div>
          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <mat-select placeholder="Product Type" name="productType" [(ngModel)]="workFlow.productType">
                <mat-option *ngFor="let product of productTypes | orderBy:'value'" [value]="product?.id">
                  {{ product?.value }}
                </mat-option>
                <mat-option>None</mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <mat-select placeholder="Account Manager" name="accountManager" [(ngModel)]="workFlow.accountMgrId"
                          name="accountManager">
                <mat-option *ngFor="let manager of accountManagers | orderBy:'firstName'"
                            [value]="manager?.id"> {{ manager?.firstName }}  {{ manager?.lastName }}
                </mat-option>
                <mat-option>None</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div fxFlex.gt-lg="48" fxFlex.gt-md="33" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div fxLayout="row wrap">
            <div class="col">
              <label class="lbl">Approval Format:</label>
              <div class="mt-10" *ngFor="let approvalFormat of approvalFormatList; let i = index">
                <mat-checkbox
                  color="warn"
                  name="approvalFormat + {{ i }}"
                  id="{{ approvalFormat.approvalLevel }}"
                  [(ngModel)]="approvalFormat.selected"
                  value="{{ approvalFormat.approvalLevel }}"
                >{{ approvalFormat.name }}
                </mat-checkbox
                >
              </div>
            </div>
            <div class="col mr-10">
              <label class="lbl">Markings:</label>
              <div class="mt-10" *ngFor="let markings of markingsList; let i = index">
                <mat-checkbox
                  color="warn"
                  name="markings + {{ i }}"
                  id="{{ markings.markings }}"
                  [(ngModel)]="markings.selected"
                  value="{{ markings.markings }}"
                >{{ markings.name }}
                </mat-checkbox
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div fxFlex="100">
        <mat-form-field>
          <textarea matInput placeholder="Notes" rows="3" [(ngModel)]="workFlow.notes" name="notes"></textarea>
        </mat-form-field>
        <mat-form-field>
        <textarea
          matInput
          placeholder="Current Status"
          rows="3"
          [(ngModel)]="workFlow.currentStatusComment"
          name="currentStatusComment"
        ></textarea>
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>
  <hr/>
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button color="warn" type="submit" (click)="saveWorkFlow(workflowForm, 'save')" name="save">
        Save
      </button>
      <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" (click)="saveWorkFlow(workflowForm, 'saveandnext')" name="saveandnext">
        Save And Next
      </button>
    </div>
  </mat-dialog-actions>
</form>
