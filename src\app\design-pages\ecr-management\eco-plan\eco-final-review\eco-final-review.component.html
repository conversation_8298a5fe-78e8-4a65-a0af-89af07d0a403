<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="30" fxFlex.gt-md="30">
    <mat-form-field appearance="outline">
      <mat-select placeholder="Reviewed By" [(ngModel)]="finalReviewDto.reviewedBy" name="finalRevReviewedBy" (ngModelChange)="updateFinalReview(true)">
        <mat-option *ngFor="let user of users" [value]="user.id">
          {{user.firstName}} {{user.lastName}}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div fxLayout="column" fxFlex.gt-lg="30" fxFlex.gt-md="30">
    <mat-form-field appearance="outline">
      <mat-label>Date</mat-label>
      <input matInput [matDatepicker]="finalRevReviewedByByDateM" [max]="maxDate" placeholder="Date" name="finalRevReviewedByByDateM" [(ngModel)]="finalReviewDto.reviewDate" #finalRevReviewedByByDateM="ngModel" autocomplete="off" readonly (ngModelChange)="updateFinalReview()">
      <mat-datepicker-toggle matSuffix [for]="finalRevReviewedByByDateM"></mat-datepicker-toggle>
      <mat-datepicker #finalRevReviewedByByDateM></mat-datepicker>
    </mat-form-field>
  </div>
</div>

<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.elementDesignReviewed" name="eleDesReviewed" (ngModelChange)="updateFinalReview()"> Element Design Reviewed </mat-checkbox>
  </div>                      
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.tsDoesNotInterfere" name="tsDosntInt" (ngModelChange)="updateFinalReview()"> T/s Does Not Interfere (or N/A) </mat-checkbox>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.bomReviewed" name="bomRevFinalRev" (ngModelChange)="updateFinalReview()"> BoM / BoO Reviewed </mat-checkbox>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.labelReviewed" name="lblRev" (ngModelChange)="updateFinalReview()"> Label Reviewed </mat-checkbox>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.partsFullyActiveInEpicore" name="partsActiveEpicor" (ngModelChange)="updateFinalReview()"> Parts Fully Active in Epicor </mat-checkbox>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.designReleasedToPatterns" name="designRelToPattern" (ngModelChange)="updateFinalReview()"> Design(s) Released to Patterns </mat-checkbox>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.heaterManualVerified" name="heatManVarified" (ngModelChange)="updateFinalReview()"> Heater-Manual Varified (or N/A) </mat-checkbox>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.engineeringCostSpreadsheet" name="engCostSpredSheet" (ngModelChange)="updateFinalReview()"> Engineering Cost Spreadsheet </mat-checkbox>
  </div>
</div>
<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
    <mat-checkbox color="warn" class="critical-check" [(ngModel)]="finalReviewDto.customerPdfUpdated" name="custPdf" (ngModelChange)="updateFinalReview()"> Customer PDF Updated (or N/A) </mat-checkbox>
  </div>
</div>

<div fxLayout="row wrap" fxLayoutAlign="center center" class="pb-5">
  <div fxLayout="column" fxFlex.gt-lg="60" fxFlex.gt-md="60">
    <strong class="pb-15">Comments</strong>
    <textarea matInput placeholder="Comments" [(ngModel)]="finalReviewDto.comment" name="finalReviewComments" rows="6" class="sfl-textarea" (change)="updateFinalReview()"></textarea>
  </div>
</div>