<!-- So In Design Main Content for Grid -->
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter"></mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <ng-container *ngIf="!showDetailed">
      <div fxLayout="row wrap" class="mb-10 cust_fields">
        <div fxFlex fxLayoutAlign="start center">
          <mat-card-title
            >{{ soInDesignTitle }} - Count <strong>{{ quotesCountsByDate?.soInDesign }}</strong></mat-card-title
          >
        </div>
        <!-- Date filter section -->
        <form #soInDesignFilterForm="ngForm">
          <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
            <mat-form-field appearance="outline" fxFlex.gt-lg="29" fxFlex.gt-md="29">
              <mat-label>From Date</mat-label>
              <input
                matInput
                (click)="fromdate.open()"
                [matDatepicker]="fromdate"
                placeholder="From Date"
                [(ngModel)]="fromDate"
                name="fromDate"
                autocomplete="off"
                [max]="toDate"
                required
              />
              <mat-datepicker-toggle matSuffix [for]="fromdate"></mat-datepicker-toggle>
              <mat-datepicker #fromdate></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-lg="29" fxFlex.gt-md="29">
              <mat-label>To Date</mat-label>
              <input
                matInput
                (click)="todate.open()"
                [matDatepicker]="todate"
                placeholder="To Date"
                [(ngModel)]="toDate"
                name="toDate"
                autocomplete="off"
                required
              />
              <mat-datepicker-toggle matSuffix [for]="todate"></mat-datepicker-toggle>
              <mat-datepicker #todate></mat-datepicker>
            </mat-form-field>
            <div fxFlex.gt-lg="15" fxFlex.gt-md="15">
              <button mat-raised-button color="warn" (click)="searchSOIndesignAndCounters()" [disabled]="soInDesignFilterForm.invalid">
                Search
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- SO in Design Table View -->
      <div class="cust_table">
        <mat-table mat-table [dataSource]="soInDesignDataSource">
          <ng-container matColumnDef="soInDesign">
            <mat-header-cell *matHeaderCellDef fxFlex="35"> Categories </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.soInDesign | uppercase }} </mat-cell>
          </ng-container>
          <ng-container matColumnDef="noOfDesign">
            <mat-header-cell *matHeaderCellDef fxFlex="25"> Number Of Design </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.noOfDesign }} </mat-cell>
          </ng-container>
          <ng-container matColumnDef="dollarValue">
            <mat-header-cell *matHeaderCellDef fxFlex="25"> Dollar Value($) </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.dollarValue | currency: 'USD':'symbol':'1.2-2' }} </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="soInDesignColumns"></mat-header-row>
          <mat-row
            *matRowDef="let row; columns: soInDesignColumns"
            [ngClass]="row.soInDesign === 'Total' ? 'highlight-total open-doc' : 'open-doc'"
            (click)="openSoDetailed(row)"
          ></mat-row>
        </mat-table>
      </div>
      <div class="no-records" *ngIf="!soInDesignDataSource?.data?.length">No data found</div>
    </ng-container>

    <!-- So In Design Detailed Grid Starts -->
    <ng-container *ngIf="showDetailed">
      <button mat-raised-button color="warn" (click)="toggleDetailedView()">Back</button>
      <sfl-so-in-design-detailed
        [_dataSource]="detailedSoInDesignDataSource"
        [_fromDate]="fromDate"
        [_toDate]="toDate"
      ></sfl-so-in-design-detailed>
    </ng-container>
    <!-- So In Design Detailed Grid Ends -->
  </mat-card>
</div>
