import { Component, Input, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { SensorService } from 'src/app/admin-pages/new-quotation/Add Sensors/add-sensors.service';
import { Values } from 'src/app/shared/constants/values.constants';
import { SensorConnectorsAndTypesMaster, SensorConnectorsMaster, ThermostatTypesMaster } from '../../../masterdata-management.model';
import { MasterdataManagementService } from '../../../masterdata-management.service';
import { SolidWorksBlockSensorInformationDTO } from '../../solidworks-master.model';

@Component({
  selector: 'sfl-manage-sw-block-sensor-info',
  templateUrl: './manage-sw-block-sensor-info.component.html',
  styleUrls: ['./manage-sw-block-sensor-info.component.css']
})
export class ManageSwBlockSensorInfoComponent implements OnInit {
  subscription = new Subscription();

  @Input()
  swBlockSensorsInfo: SolidWorksBlockSensorInformationDTO[];

  @Input()
  anySensor: boolean;

  selectedSensorTypes = [];
  selectedLocations = [];
  selectedConnectors = [];
  selectedTempTypes = [];

  selectedSWBlockSensorInfo = new SolidWorksBlockSensorInformationDTO();

  sensorsDataSource = new MatTableDataSource<SolidWorksBlockSensorInformationDTO>();

  sensorTypes: SensorConnectorsAndTypesMaster;
  tempTypes: ThermostatTypesMaster[] = [];
  sensorLocations: object = Values.SensorLocation;
  sensorConnectors: SensorConnectorsMaster[];


  SensorsdisplayedColumns = ['type', 'location', 'connector', 'minLength', 'maxLength', 'tempType', 'action'];


  constructor(private readonly masterDataService: MasterdataManagementService,
    public sensorService: SensorService,
  ) { }

  ngOnInit() {
    this.sensorsDataSource.data = this.swBlockSensorsInfo;
    this.getSensorsConnectors();
    this.getSensorsTypes();
    this.getThermostatTypes();
  }

  getSensorsTypes() {
    this.subscription.add(
      this.masterDataService.getSensorTypesList().subscribe((sensorTypes: SensorConnectorsAndTypesMaster) => {
        this.sensorTypes = sensorTypes;
      })
    );
  }

  getSensorsConnectors() {
    this.subscription.add(
      this.sensorService.getSensorConnectorsList().subscribe(
        (sensorConnector: SensorConnectorsMaster[]) => {
          if (sensorConnector) {
            this.sensorConnectors = sensorConnector;
          }
        },
        error => {
          this.sensorConnectors = [];
        }
      )
    );
  }

  // used to get listing of thermostat types
  getThermostatTypes() {
    this.subscription.add(
      this.sensorService.getThermostatTypeList().subscribe(
        (res: ThermostatTypesMaster[]) => {
          this.tempTypes = res;
        }
      )
    );
  }

  addToSensor() {
    this.prepareData();
    this.swBlockSensorsInfo.push(this.selectedSWBlockSensorInfo);
    this.sensorsDataSource._updateChangeSubscription();
    this.selectedSWBlockSensorInfo = new SolidWorksBlockSensorInformationDTO();

  }

  private prepareData() {
    this.selectedSWBlockSensorInfo.type = this.selectedSWBlockSensorInfo.typeArray.join();
    if (this.selectedSWBlockSensorInfo.type.length === 0) {
      this.selectedSWBlockSensorInfo.type = null;
    }
    this.selectedSWBlockSensorInfo.connector = this.selectedSWBlockSensorInfo.connectorsArray.join();
    if (this.selectedSWBlockSensorInfo.connector.length === 0) {
      this.selectedSWBlockSensorInfo.connector = null;
    }
    this.selectedSWBlockSensorInfo.tempType = this.selectedSWBlockSensorInfo.tempTypeArray.join();
    if (this.selectedSWBlockSensorInfo.tempType.length === 0) {
      this.selectedSWBlockSensorInfo.tempType = null;
    }
    this.selectedSWBlockSensorInfo.location = this.selectedSWBlockSensorInfo.locationArray.join();
    if (this.selectedSWBlockSensorInfo.location.length === 0) {
      this.selectedSWBlockSensorInfo.location = null;
    }
  }

  removeFromSensor(ele) {
    this.sensorsDataSource.data.splice(this.sensorsDataSource.data.indexOf(ele), 1);
    this.sensorsDataSource._updateChangeSubscription();
  }


}
