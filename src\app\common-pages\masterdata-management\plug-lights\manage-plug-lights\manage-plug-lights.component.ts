import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { PlugLightsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-manage-plug-lights',
  templateUrl: './manage-plug-lights.component.html'
})
export class ManagePlugLightsComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  plugLights: PlugLightsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  plugLightType = Values.PlugLightsTypes;
  constructor(
    public readonly dialogRef: MatDialogRef<ManagePlugLightsComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.plugLights = Object.assign({}, data);
    data.clothCE === 'true' || data.clothCE === true ? (this.plugLights.clothCE = true) : (this.plugLights.clothCE = false);
    data.clothUL === 'true' || data.clothUL === true ? (this.plugLights.clothUL = true) : (this.plugLights.clothUL = false);
  }

  ngOnInit() {
    this.plugLights = this.plugLights.id ? Object.assign({}, this.plugLights) : new PlugLightsMaster();
    this.plugLights.id ? (this.title = 'Update Plug Light') : (this.title = 'Add Plug Light');
  }

  updatePlugLight() {
    this.showLoader = true;
    if (this.plugLights.id) {
      this.subscription.add(
        this.masterDataService.updatePlugLight(this.plugLights).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addPlugLight(this.plugLights).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
