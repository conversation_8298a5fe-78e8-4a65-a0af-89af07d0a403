import { <PERSON>MM<PERSON>, ENTER, SPACE } from '@angular/cdk/keycodes';
import { HttpResponse } from '@angular/common/http';
import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { MatChipInputEvent } from '@angular/material/chips';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { APIRESULT } from '../../design-pages/jacket-list/jacket-list.model';
import { JacketListService } from '../../design-pages/jacket-list/jacket-list.service';
import { Customer, SalesAssociate, Status } from '../dashboard/dashboard.model';
import { AddApplicationComponent } from '../new-quotation/Add Applicaton/add-application.component';
import { AddMaterialComponent } from '../new-quotation/Add Material/add-material.component';
import { AddNoteComponent } from '../new-quotation/Add Note/add-note.component';
import { AddPluginComponent } from '../new-quotation/Add Plugin/add-plugin.component';
import { AddSensorsComponent } from '../new-quotation/Add Sensors/add-sensors.component';
import { AddWorkflowComponent } from '../new-quotation/Add Workflow/add-workflow.component';
import { AddQuotationComponent } from './../../layouts/add-quotation/add-quotation.component';
import { deepCopyFunction, SharedService, SnakbarService, SweetAlertService } from './../../shared';
import { Messages } from './../../shared/constants/messages.constants';
import { PopupSize } from './../../shared/constants/popupsize.constants';
import { AccessoriesComponent } from './../accessories/accessories.component';
import { AddApplicationService } from './Add Applicaton/add-application.service';
import { AddClosureComponent } from './Add Closure/add-closure.component';
import { AddClosureService } from './Add Closure/add-closure.service';
import { SensorService } from './Add Sensors/add-sensors.service';
import { AddThermostatsComponent } from './Add Thermostats/add-thermostats.component';
import { AddEntryMethodComponent } from './add-entry-method/add-entry-method.component';
import {
  AccountManager,
  ApplicationInfo,
  CcdcConfigurationsHolderDTO,
  CcdcTemplateExportDTO,
  CcdcWorkflow,
  ClosureInformation,
  ClosureMaterial,
  CustomerDTO,
  Document,
  JacketGroup,
  JumperPlug,
  LeadPlug,
  Material,
  MaterialInfoReq,
  MaterialLayers,
  Notes,
  PluggingInformation,
  Quotation,
  Revision,
  SensorInformation,
  SensorInformationObject,
  ThermostatInfo,
  ThermostatList,
  Units
} from './ccdc-model/ccdc.model';
import { ExportCcdcModalComponent } from './export-ccdc-modal/export-ccdc-modal.component';
import { ImportCcdcModalComponent } from './import-ccdc-modal/import-ccdc-modal.component';
import { ManageJacketComponent } from './manage-jacketgroups/manage-jacket-groups.component';
import { JacketGroupService } from './manage-jacketgroups/manage-jacket-groups.service';
import { ManageRevisionsComponent } from './manage-revisions/manage-revisions.component';
import { ManageRevisionsService } from './manage-revisions/manage-revisions.service';
import { ManageUnitsComponent } from './manage-units/manage-units.component';
import { ManageUnitsService } from './manage-units/manage-units.service';
import { SalesOrderSummaryService } from './summary-sales-order.service';

@Component({
  selector: 'sfl-summary-sales-order',
  templateUrl: './summary-sales-order.component.html'
})
export class SummarySalesOrderComponent implements OnInit, OnDestroy {
  quotID: number;
  epiCoreId: number;
  quotation: Quotation;
  material: MaterialLayers[];
  appInfo: ApplicationInfo;
  pipeMaterial: Material[] = [];
  workflow: CcdcWorkflow;
  pluggingInformation: PluggingInformation;
  subscription = new Subscription();
  jacketGroupId: number;
  revisionId: number;
  noteInfo: Notes;
  closureInfo: ClosureInformation;
  materials: ClosureMaterial[];
  closureMaterial: ClosureMaterial;
  thermostatInfo: ThermostatInfo;
  materialData: MaterialInfoReq;
  sensorInfoObject: SensorInformationObject;
  onlyrevision: boolean;
  document: Document;
  quotationStatuses: Status[];
  jacketGroup: JacketGroup[] = [];
  revisionList: Revision[];
  measureUnit: string;
  tempUnit: string;
  onlyComparison: boolean;
  value: string;
  filedata: File;
  filename: string;
  listType = 'customer';
  ccdcExportTemplateData = new CcdcTemplateExportDTO();
  documentslist = false;
  submenu: boolean;
  id: number;
  menuChange: boolean;
  expFeature: boolean;
  isUploading = false;
  isValidFileSize = false;
  jacketTypes = Values.JacketTypeConst;
  phaseTypes = Values.PhaseTypeConst;
  contentMotions = Values.ContentMotionsConst;
  productTypes = Values.ProductTypeConst;
  fitTypes = Values.FitTypeConst;

  displayedColumns = ['id', 'fileName', 'url'];
  documentDataSource = new MatTableDataSource<Document>();

  materialdisplayedColumns = ['layerName', 'material', 'partNumber', 'maxTemp', 'costPerSq'];
  materialDataSource = new MatTableDataSource<MaterialLayers>();

  SensorsdisplayedColumns = ['sensorType', 'sensorLocation', 'sensorConnector', 'sensorLeadLength', 'sensorTempType'];
  sensorsDataSource = new MatTableDataSource<SensorInformation>();

  thermostatdisplayedColumns = [
    'thermostatType',
    'installationMethod',
    'openTemp',
    'partNumber',
    'cost',
    'manualReset',
    'openOnRise',
    'usaStock',
    'vietnamStock'
  ];
  thermostatdataSource = new MatTableDataSource<ThermostatList>();

  sizeLG = 90;
  sizeMD = 85;
  sizeSM = 49;
  sizeXS = 100;
  emailChips = [];
  selectable = true;
  removable = true;
  addOnBlur = true;
  readonly separatorKeysCodes: number[] = [ENTER, COMMA, SPACE];

  isRevisionActive: boolean;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  @ViewChild(AccessoriesComponent) accessory: AccessoriesComponent;
  salesassociates: SalesAssociate[];
  accountManagers: AccountManager[];

  constructor(
    private matDialog: MatDialog,
    private salesOrderSummaryService: SalesOrderSummaryService,
    private snakbarService: SnakbarService,
    private addApplicationService: AddApplicationService,
    private addClosureService: AddClosureService,
    private sensorService: SensorService,
    private sharedService: SharedService,
    private activatedRoute: ActivatedRoute,
    private jacketGroupService: JacketGroupService,
    private manageRevisionsService: ManageRevisionsService,
    private sweetAlertService: SweetAlertService,
    private manageUnitService: ManageUnitsService,
    private titleService: Title,
    private readonly jacketListService: JacketListService,
  ) {
  }

  level = [
    {name: 'Level  1', value: 'Level1'},
    {name: 'Level 2', value: 'Level2'},
    {name: 'Level 3', value: 'Level3'}
  ];

  jacket = [
    {id: 1, name: 'Default', measurementUnit: 'CM', tempUnit: '°C'},
    {id: 2, name: 'Ground', measurementUnit: 'MM', tempUnit: '°F'},
    {id: 3, name: 'Non-Ground', measurementUnit: 'IN', tempUnit: '°C'}
  ];

  rev = [
    {name: 'A', value: 'A'},
    {name: 'B', value: 'B'},
    {name: 'C', value: 'C'}
  ];

  async ngOnInit() {
    this.titleService.setTitle('CCDC Info - App Eng');
    this.quotation = new Quotation();
    this.quotation.customerDTO = new CustomerDTO();
    this.workflow = new CcdcWorkflow();
    this.documentslist = false;
    this.menuChange = true;
    this.onlyrevision = true;
    this.expFeature = true;
    this.value = Messages.Quotation.upload_message;
    this.closureInfo = new ClosureInformation();
    this.closureMaterial = new ClosureMaterial();
    this.material = new Array<MaterialLayers>();
    this.onlyComparison = true;
    this.pluggingInformation = new PluggingInformation();
    this.pluggingInformation.jumperPlugDTO = new JumperPlug();
    this.pluggingInformation.leadPlugDTO = new LeadPlug();
    this.quotation = new Quotation();
    this.quotation.customerDTO = new Customer();
    this.revisionList = new Array<Revision>();
    this.document = new Document();
    this.sensorInfoObject = new SensorInformationObject();

    this.activatedRoute.queryParams.subscribe(params => {
      if (params['quotId']) {
        this.quotID = params['quotId'];
        this.getQuotDetails();
        this.getAllRevisionsByQuotationId();
        this.getDocumentListyByQuoteId(this.quotID);
      }
    });
    this.sharedService.isCurrentRevisionActive.subscribe(res => (this.isRevisionActive = res));
    this.getSaleAssociate();
    this.getAccountManagerMaster();
  }

  openFileInExplorer(link) {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.openFileInExplorer(link).subscribe(
        (res: APIRESULT) => {
          if (res.success) {
            this.showLoader = false;
          } else {
            this.showLoader = false;
            this.snakbarService.error(Messages.Title_Block.error_msg);
          }
        },
        error => {
          this.snakbarService.error(Messages.Solid_Work.not_install);
          this.showLoader = false;
        }
      )
    );
  }

  callAllCcdcApis() {
    this.getMaterialByJacketGroupId();
    this.getMaterialList();
    this.getApplicationInfo();
    this.getNotesByJacketGroupId();
    this.getClosureMaterialList();
    this.getThermostatByJacketGroupId();
    this.getWorkFlowByJacketGroupId();
    this.getPluggingInformationByJacketGroupId();
    this.getSensorsListByJacketGroupId();
    this.getMeasurementUnit();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.measureUnit = res.measurementUnit;
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  // used to get all the available revisions using the QuotationId
  getAllRevisionsByQuotationId() {
    if (this.quotID) {
      this.showLoader = true;
      this.subscription.add(
        this.manageRevisionsService.getRevisionsByQuotationId(this.quotID).subscribe(
          (res: Revision[]) => {
            this.revisionList = res;
            this.revisionId = this.revisionList[0].id;
            this.onRevisionChanged(this.revisionId);
            this.showLoader = false;
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  // when user changes the revision from the dropdown need to get relavent jacket group data for the page
  onRevisionChanged(revisionId) {
    this.showLoader = true;
    this.subscription.add(
      this.jacketGroupService.getJacketGroupsByRevisionId(revisionId).subscribe(
        (res: JacketGroup[]) => {
          this.jacketGroup = res;
          this.revisionId = revisionId;
          this.jacketGroupId = this.jacketGroup[0].id;
          this.onJacketGroupChanged(this.jacketGroupId);
          if (this.revisionList.find(revision => revision.id === revisionId).activeRevision) {
            if (!this.sharedService.getJacketGroup(revisionId)) {
              this.sharedService.setJacketGroup(revisionId, this.jacketGroup[0].id);
            }
            this.sharedService.setCurrentRevisionActive(true);
          } else {
            this.sharedService.setCurrentRevisionActive(false);
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  onJacketGroupChanged(value) {
    const jacketGroup = this.jacketGroup.find(data => data.id === value);
    this.jacketGroupId = jacketGroup.id;
    if (this.listType === 'ccdc') {
      this.callAllCcdcApis();
    }
  }

  getSensorsListByJacketGroupId() {
    this.subscription.add(
      this.sensorService.getSensorList(this.jacketGroupId).subscribe((res: SensorInformationObject) => {
        if (res.sensorsInformationDTOList.length > 0 || res.notes) {
          this.sensorInfoObject = res;
          this.sensorsDataSource.data = this.sensorInfoObject.sensorsInformationDTOList;
        } else {
          this.sensorInfoObject = null;
        }
      })
    );
  }

  getClosureMaterialList() {
    this.subscription.add(
      this.addClosureService.getClosureMaterialList().subscribe((res: ClosureMaterial[]) => {
        this.materials = res;
        this.getClosureInfo();
      })
    );
  }

  getClosureInfo() {
    this.subscription.add(
      this.addClosureService.getClosureInfoByJacketGroup(this.jacketGroupId).subscribe((res: ClosureInformation) => {
        this.closureInfo = res;
        if (res) {
          if (this.materials.length > 0) {
            if (this.closureInfo.closureMaterialId) {
              this.closureMaterial = this.materials.find(x => x.id === this.closureInfo.closureMaterialId);
            }
          }
        }
      })
    );
  }

  // used to get the quotes details by quote id like the customer details
  getQuotDetails() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getQUotById(this.quotID).subscribe(
        async (res: Quotation) => {
          this.quotation = res;
          this.quotationStatuses = await this.sharedService.getAllStatusByType('app', this.quotation.quotationStatusId);
          // convert email string to array in order to show it in mat-chips
          this.emailChips = this.quotation.customerDTO.email ? this.quotation.customerDTO.email.split(',') : [];
          this.sharedService.setProjectName(res.projectTitle);
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  getWorkFlowByJacketGroupId() {
    this.subscription.add(
      this.salesOrderSummaryService.getWorkFlowByJacketGroupId(this.jacketGroupId).subscribe((res: CcdcWorkflow) => {
        this.workflow = res;
      })
    );
  }

  // uses the jacket group id to get the application info data
  getApplicationInfo() {
    this.showLoader = true;
    this.subscription.add(
      this.addApplicationService.getApplicationInfoByJacketGroup(this.jacketGroupId).subscribe(
        (res: ApplicationInfo) => {
          this.appInfo = res;
          if (this.appInfo) {
            if (this.appInfo.contentMotion) {
              this.appInfo.contentMotion = this.contentMotions.find(e => e.id === this.appInfo.contentMotion).value;
            }
            if (this.appInfo.jacketType) {
              this.appInfo.jacketType = this.jacketTypes.find(e => e.id === this.appInfo.jacketType).value;
            }
            if (this.appInfo.phase) {
              this.appInfo.phase = this.phaseTypes.find(e => e.id === this.appInfo.phase).value;
            }
            if (this.appInfo.fitTypeEnum) {
              this.appInfo.fitTypeEnum = this.fitTypes.find(e => e.id === this.appInfo.fitTypeEnum).value;
            }
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to get the materials data using the jacket group id
  getMaterialByJacketGroupId() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getAllMaterial(this.jacketGroupId).subscribe(
        (res: MaterialInfoReq) => {
          if (res.materialInfoDTOList.length > 0 || res.notes) {
            this.materialData = res;
            this.materialDataSource.data = this.materialData.materialInfoDTOList;
          } else {
            this.materialData = null;
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  getNotesByJacketGroupId() {
    this.subscription.add(
      this.salesOrderSummaryService.getNotesByJacketGroupId(this.jacketGroupId).subscribe((res: Notes) => {
        this.noteInfo = res;
      })
    );
  }

  getThermostatByJacketGroupId() {
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatByJacketGroupId(this.jacketGroupId).subscribe((res: ThermostatInfo) => {
        if (res.thermostatInformationDTOList.length > 0 || res.notes) {
          this.thermostatInfo = res;
          this.thermostatdataSource = new MatTableDataSource<ThermostatList>(this.thermostatInfo.thermostatInformationDTOList);
        } else {
          this.thermostatInfo = null;
        }
      })
    );
  }

  // used to get the available material list
  getMaterialList() {
    this.showLoader = true;
    this.subscription.add(
      this.addApplicationService.getMaterialList().subscribe(
        (res: Material[]) => {
          this.pipeMaterial = res;
          this.getApplicationInfo();
          this.showLoader = false;
        },
        (error) => {
          this.showLoader = false;
          if (error.applicationStatusCode === 1225) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  saveOrderInfo() {
    this.quotation.activated = true;
    // convert email chips array to string as we are saving it as string in db
    this.quotation.customerDTO.email = this.emailChips.toString();
    this.subscription.add(
      this.salesOrderSummaryService.saveOrUpdateQuotation(this.quotation, this.quotation.id).subscribe(() => {
        this.changeList('ccdc');
      })
    );
  }

  getMaterial(jgId) {
    this.subscription.add(
      this.salesOrderSummaryService.getAllMaterial(jgId).subscribe(
        (res: HttpResponse<MaterialLayers[]>) => this.onSuccess(res),
        () => this.snakbarService.error(Messages.Quotation.quotation_list_error)
      )
    );
  }

  private onSuccess(data) {
    this.material = data;
    this.materialDataSource = new MatTableDataSource<MaterialLayers>(this.material);
  }

  findCusotmerDetail(id) {
    this.subscription.add(
      this.salesOrderSummaryService.getQuotation(id).subscribe((response: Quotation) => {
        if (response.customerDTO != null) {
          this.quotation = response;
          this.quotation.quotationStatusId = this.quotationStatuses[0].id;
        } else {
          this.quotation = new Quotation();
          this.quotation.customerDTO = new CustomerDTO();
          this.quotation.quotationNumber = id;
        }
      })
    );
  }

  getPluggingInformationByJacketGroupId() {
    this.subscription.add(
      this.salesOrderSummaryService.getPluggingInformationByJacketGroupId(this.jacketGroupId).subscribe((res: PluggingInformation) => {
        this.pluggingInformation = res;
      })
    );
  }

  readUrl(event) {
    if (event.target.files && event.target.files[0]) {
      this.isValidFileSize = false;
      this.filedata = event.target.files[0];
      this.filename = this.filedata.name;
      this.value = this.filename;
      this.document.quotationId = this.quotation.id;
    } else {
      this.filedata = null;
      this.value = Messages.Quotation.upload_message;
    }
  }

  uploadFile() {
    if (this.filedata.size > 10485760) {
      this.isValidFileSize = true;
      this.filedata = null;
      this.filename = null;
    } else {
      this.isUploading = true;
      this.isValidFileSize = false;
      const formData = new FormData();
      formData.append('file', this.filedata);
      formData.append('document', JSON.stringify(this.document));
      this.salesOrderSummaryService.uploadDocument(formData).subscribe(() => {
        this.document = new Document();
        this.filedata = null;
        this.filename = '';
        this.value = Messages.Quotation.upload_message;
        this.getDocumentListyByQuoteId(this.quotation.id);
      });
    }
  }

  // gets the uploaded documents using the quote id
  getDocumentListyByQuoteId(id) {
    this.changeList('customer');
    this.showLoader = true;
    this.salesOrderSummaryService.getDocumentListyByQuoteId(id).subscribe(
      (res: Document[]) => {
        this.documentDataSource.data = res;
        this.isUploading = false;
        this.showLoader = false;
      },
      () => (this.showLoader = false)
    );
  }

  expandSubmenu() {
    this.submenu = !this.submenu;
  }

  expandFeature() {
    this.expFeature = !this.expFeature;
  }

  manageUnits() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {
      quotationId: this.quotID === undefined ? 0 : this.quotID,
      jacketGroupId: this.jacketGroupId,
      revId: this.revisionId
    };
    matDataConfig.width = PopupSize.size.popup_xmd;
    this.matDialog
      .open(ManageUnitsComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.data) {
          this.getMeasurementUnit();
          if (res.mode) {
            this.addEntryMethod();
          }
        }
      });
  }

  addEntryMethod() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: this.quotation.id, entryMethod: this.quotation.entryMethod};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(AddEntryMethodComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.selectedEntryMethod) {
          this.quotation.entryMethod = res.selectedEntryMethod;
          if (res.mode) {
            this.addWorkflow();
          }
        }
      });
  }

  addWorkflow() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {
      jacketGroupId: this.jacketGroupId,
      quotationId: this.quotation.quotationNumber,
      quoteId: this.quotation.id
    };
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(AddWorkflowComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.data) {
          this.workflow = res.data;
          this.getWorkFlowByJacketGroupId();
          if (res.mode) {
            this.addApplicationInfo();
          }
        }
      });
  }

  addApplicationInfo() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {jacketGroupId: this.jacketGroupId};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(AddApplicationComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.data) {
          this.appInfo = res.data;
          if (this.appInfo.contentMotion) {
            this.appInfo.contentMotion = this.contentMotions.find(e => e.id === this.appInfo.contentMotion).value;
          }
          if (this.appInfo.jacketType) {
            this.appInfo.jacketType = this.jacketTypes.find(e => e.id === this.appInfo.jacketType).value;
          }
          if (this.appInfo.phase) {
            this.appInfo.phase = this.phaseTypes.find(e => e.id === this.appInfo.phase).value;
          }
          if (this.appInfo.fitTypeEnum) {
            this.appInfo.fitTypeEnum = this.fitTypes.find(e => e.id === this.appInfo.fitTypeEnum).value;
          }
          if (res.mode) {
            this.addPlugin();
          }
        }
      });
  }

  addMaterial() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {jacketGroupId: this.jacketGroupId};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(AddMaterialComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        this.getMaterialByJacketGroupId();
        if (res.mode) {
          this.addClosure();
        }
      });

  }

  addClosure() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {jacketGroupId: this.jacketGroupId};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(AddClosureComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.data) {
          this.closureInfo = res.data;
          if (this.materials.length > 0) {
            if (this.closureInfo.closureMaterialId) {
              this.closureMaterial = this.materials.find(x => x.id === this.closureInfo.closureMaterialId);
            }
          }
          if (res.mode) {
            this.addSensors();
          }
        }
      });
  }

  addPlugin() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {jacketGroupId: this.jacketGroupId, jacketType: this.appInfo ? this.appInfo.jacketType : null};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(AddPluginComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.data) {
          this.getPluggingInformationByJacketGroupId();
          if (res.mode) {
            this.addMaterial();
          }
        }
      });
  }

  addSensors() {
    const matDataConfig = new MatDialogConfig();
    const appInfoControlType = this.appInfo && this.appInfo.controlType ? this.appInfo.controlType : '';
    matDataConfig.data = {jacketGroupId: this.jacketGroupId, controlType: appInfoControlType};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(AddSensorsComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.data.sensorsInformationDTOList.length > 0) {
          this.getSensorsListByJacketGroupId();
          if (res.mode) {
            this.addThermostat();
          }
        } else {
          this.getSensorsListByJacketGroupId();
        }
      });
  }

  addThermostat() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {jacketGroupId: this.jacketGroupId};
    this.matDialog
      .open(AddThermostatsComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res.data) {
          this.getThermostatByJacketGroupId();
          if (res.mode) {
            this.addNote();
          }
        }
      });
  }

  addNote() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {jacketGroupId: this.jacketGroupId};
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(AddNoteComponent, matDataConfig)
      .afterClosed()
      .subscribe(result => {
        if (result) {
          this.noteInfo = result;
        }
      });
  }

  openAddQuo(): void {
    this.matDialog.open(AddQuotationComponent, {
      width: PopupSize.size.popup_md
    });
  }

  manageJacketGroup(): void {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {
      quotationId: this.quotID === undefined ? 0 : this.quotID,
      revisionId: this.revisionId === undefined ? 0 : this.revisionId
    };
    matDataConfig.width = PopupSize.size.popup_xmd;
    this.matDialog
      .open(ManageJacketComponent, matDataConfig)
      .afterClosed()
      .subscribe(() => {
        if (this.listType === 'accessories') {
          this.accessory.getAllJacketsAccessories(this.revisionId);
        }
        this.onRevisionChanged(this.revisionId);
      });
  }

  manageImportCcdc(): void {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {jacketGroupId: this.jacketGroupId};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(ImportCcdcModalComponent, matDataConfig)
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          this.callAllCcdcApis();
        }
      });
  }

  manageExportCcdc(): void {
    const matDataConfig = new MatDialogConfig();
    const ccdcTemplateExport = this.prepareCcdcTemplateExportDTO();
    matDataConfig.data = ccdcTemplateExport;
    matDataConfig.width = PopupSize.size.popup_xmd;
    this.matDialog
      .open(ExportCcdcModalComponent, matDataConfig);
  }


  private prepareCcdcTemplateExportDTO() {
    const ccdcExportData = new CcdcConfigurationsHolderDTO();
    const ccdcTemplateExport = new CcdcTemplateExportDTO();
    ccdcTemplateExport.userId = this.sharedService.getUserId();
    ccdcTemplateExport.measurementUnit = this.measureUnit;
    ccdcTemplateExport.temperatureUnit = this.tempUnit;
    if (this.noteInfo !== null) {
      ccdcTemplateExport.notes = this.noteInfo.notes;
    }
    ccdcExportData.ccdcApplicationInfoDTO = this.appInfo ? deepCopyFunction(this.appInfo) : new ApplicationInfo();
    if (ccdcExportData.ccdcApplicationInfoDTO.contentMotion) {
      ccdcExportData.ccdcApplicationInfoDTO.contentMotion = this.contentMotions.find(e => e.value === ccdcExportData.ccdcApplicationInfoDTO.contentMotion).id;
    }
    if (ccdcExportData.ccdcApplicationInfoDTO.jacketType) {
      ccdcExportData.ccdcApplicationInfoDTO.jacketType = this.jacketTypes.find(e => e.value === ccdcExportData.ccdcApplicationInfoDTO.jacketType).id;
    }
    if (ccdcExportData.ccdcApplicationInfoDTO.phase) {
      ccdcExportData.ccdcApplicationInfoDTO.phase = this.phaseTypes.find(e => e.value === ccdcExportData.ccdcApplicationInfoDTO.phase).id;
    }

    if (ccdcExportData.ccdcApplicationInfoDTO.fitTypeEnum) {
      ccdcExportData.ccdcApplicationInfoDTO.fitTypeEnum = this.fitTypes.find(e => e.value === ccdcExportData.ccdcApplicationInfoDTO.fitTypeEnum).id;
    }
    ccdcExportData.closureDTO = this.closureInfo ? this.closureInfo : new ClosureInformation();
    ccdcExportData.pluggingInformationDTO = this.pluggingInformation ? this.pluggingInformation : new PluggingInformation();
    if (!ccdcExportData.pluggingInformationDTO.leadPlugDTO) {
      ccdcExportData.pluggingInformationDTO.leadPlugDTO = new LeadPlug();
    }

    if (!ccdcExportData.pluggingInformationDTO.jumperPlugDTO) {
      ccdcExportData.pluggingInformationDTO.jumperPlugDTO = new JumperPlug();
    }

    if (this.sensorInfoObject) {
      ccdcExportData.sensorsInformationRequestDto = this.sensorInfoObject;
      ccdcTemplateExport.sensorNotes = this.sensorInfoObject.notes;
    } else {
      ccdcExportData.sensorsInformationRequestDto = new SensorInformationObject();
    }

    if (this.thermostatInfo) {
      ccdcExportData.thermostatInformationRequestDto = this.thermostatInfo;
      ccdcTemplateExport.thermostatNotes = this.thermostatInfo.notes;
    } else {
      ccdcExportData.thermostatInformationRequestDto = new ThermostatInfo();
    }

    if (this.materialData) {
      ccdcExportData.materialInfoDTOS = this.materialData;
      ccdcTemplateExport.materialNotes = this.materialData.notes;
    } else {
      ccdcExportData.materialInfoDTOS = new MaterialInfoReq();
    }

    ccdcTemplateExport.ccdcConfigurationsHolderDTO = ccdcExportData;
    return ccdcTemplateExport;
  }

  manageRevision(): void {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: this.quotID === undefined ? 0 : this.quotID};
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(ManageRevisionsComponent, matDataConfig)
      .afterClosed()
      .subscribe(() => this.getAllRevisionsByQuotationId());
  }

  changeList(listType) {
    this.listType = listType;
    this.submenu = false;
    if (this.listType === 'ccdc') {
      this.menuChange = true;
      this.onlyrevision = true;
      this.onlyComparison = true;
      this.callAllCcdcApis();
    } else if (this.listType === 'customer') {
      this.menuChange = true;
      this.onlyrevision = true;
      this.onlyComparison = true;
    } else if (this.listType === 'geometry' || this.listType === 'accessories') {
      this.menuChange = false;
      this.onlyrevision = true;
      this.onlyComparison = true;
    }
  }

  openDoclist() {
    this.documentslist = !this.documentslist;
    if (!this.documentslist) {
      this.sizeLG = 90;
      this.sizeMD = 85;
      this.sizeSM = 49;
      this.sizeXS = 100;
    } else {
      this.sizeLG = 69;
      this.sizeMD = 65;
      this.sizeSM = 50;
      this.sizeXS = 100;
    }
  }

  openDoc(fileId) {
    this.subscription.add(
      this.salesOrderSummaryService.getDocument(fileId).subscribe(success => {
        const url = URL.createObjectURL(success);
        window.open(url, '_target');
      })
    );
  }

  deleteFile(fileId) {
    this.subscription.add(
      this.salesOrderSummaryService.deleteDocument(fileId).subscribe(() => {
        this.documentDataSource.data.splice(this.documentDataSource.data.map(x => x.id).indexOf(fileId), 1);
      })
    );
  }

  async confirmDelete(fileId) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.deleteFile(fileId);
    }
  }

  isNumberKey(event) {
    const charCode = event.which ? event.which : event.keyCode;
    return !(charCode > 31 && (charCode < 48 || charCode > 57));
  }

  add(event: MatChipInputEvent): void {
    const input = event.input;
    const value = event.value;
    if ((value || '').trim()) {
      this.emailChips.push(value.trim());
    }
    // Reset the input value once its converted to chips
    if (input) {
      input.value = '';
    }
  }

  remove(email): void {
    const index = this.emailChips.indexOf(email);
    if (index >= 0) {
      this.emailChips.splice(index, 1);
    }
  }

  // used to get sales associates list
  getSaleAssociate() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.salesOrderSummaryService.getSalesAssociate(false).subscribe(
        (res: Array<SalesAssociate>) => {
          this.salesassociates = res;
          this.showLoader = false;
          resolve();
        },
        () => {
          this.showLoader = false;
        }
      );
    });
  }

  // used to get all the account managers
  getAccountManagerMaster() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.salesOrderSummaryService.getAccountManagers().subscribe(
        (res: AccountManager[]) => {
          this.accountManagers = res;
          this.showLoader = false;
          resolve();
        },
        () => {
          this.showLoader = false;
        }
      );
    });
  }

  // update cust clarification required flag/ based on the status selected
  updateQuoteStatus(quoteStatusId: number) {
    const statusSelected = this.quotationStatuses.find(status => status.id === quoteStatusId).status;
    if (statusSelected === Values.QuoteStatus_Customer_Clarification_Required) {
      this.quotation.customerClarificationRequired = true;
    }
    if (statusSelected === Values.QuoteStatus_External_Quote_Required) {
      this.quotation.externalQuoteRequired = true;
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
