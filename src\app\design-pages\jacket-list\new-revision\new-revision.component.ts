import { Subscription } from 'rxjs';
import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { JacketListComponent } from '../jacket-list.component';
import { NewRevisonService } from './new-revision.service';
import { UpdateDirResponse, NewRevision } from './new-revision.model';
import { SnakbarService, Messages } from 'src/app/shared';

@Component({
  selector: 'sfl-new-revision',
  templateUrl: './new-revision.component.html'
})
export class NewRevisionComponent implements OnInit, OnDestroy {
  subscription = new Subscription();

  jacketId: number;
  fixPath: string;
  partNumber: string;
  currentRevision: string;
  newRevision: string;
  revision: NewRevision;
  selectedPartFilePath: boolean;

  constructor(
    private newRevisionService: NewRevisonService,
    public dialogRef: MatDialogRef<JacketListComponent>,
    private snakbarService: SnakbarService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketId = data.jacketId;
    this.fixPath = data.fixPath;
    this.partNumber = data.partNumber;
    this.currentRevision = data.currentRevision;
    this.selectedPartFilePath = data.selectedPartFilePath;
  }

  ngOnInit() {
    this.revision = new NewRevision();
  }

  updateNewRevisionAndDirectory() {
    if (this.currentRevision === this.newRevision.toUpperCase()) {
      this.snakbarService.error(Messages.new_Revision.same_revision);
    } else {
      const data = {
        partNumber: this.partNumber,
        newRevision: this.newRevision.toUpperCase(),
        oldRevision: this.currentRevision,
        fixPath: this.fixPath,
        CustomPath: this.selectedPartFilePath
      };
      this.subscription.add(
        this.newRevisionService.updateDirectory(data).subscribe((res: UpdateDirResponse) => {
          if (res.success) {
            this.snakbarService.success(res.message);
            this.updateNewRevisionBHXOnly();
          } else {
            this.snakbarService.error(res.message);
          }
        })
      );
    }
  }

  updateNewRevisionBHXOnly() {
    if (this.currentRevision === this.newRevision.toUpperCase()) {
      this.snakbarService.error(Messages.new_Revision.same_revision);
    } else {
      this.revision.jacketId = this.jacketId;
      this.revision.revisionName = this.currentRevision;
      this.revision.newRevisionName = this.newRevision.toUpperCase();
      this.subscription.add(
        this.newRevisionService.updateRevisionBHX(this.revision).subscribe(res => {
          if (res) {
            this.dialogRef.close(this.newRevision.toUpperCase());
          }
        })
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
