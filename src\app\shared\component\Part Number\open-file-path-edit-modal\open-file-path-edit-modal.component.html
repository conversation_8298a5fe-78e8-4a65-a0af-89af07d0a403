<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<h2 mat-dialog-title>
  Part File Path Add/Update
  <hr />
</h2>
<mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <button mat-raised-button type="button" (click)="addPath()" color="warn">Enter File Path Manually</button>
  </mat-dialog-actions><br><br>

  <p class="rootPN-message">
    You can Add / Update Part File Path Here
    <span *ngIf="existingFilePathOfJacket"> (Manually Selected Existing File Path {{ existingFilePathOfJacket }} )</span>
  </p>
  <div fxLayoutAlign="space-between" *ngIf="selectedNode">
    <div fxLayoutAlign="end">
      <h2>Current New Path to be selected {{ selectedPath }}</h2>
    </div>
    <div class="mt-10" fxLayoutAlign="start">
      <span class="sfl-item-position"
        ><sfl-add-node (addedNode)="addChildNode($event)" [isTop]="false" [currentNode]="selectedNode"></sfl-add-node
      ></span>
    </div>
  </div>
  <cdk-virtual-scroll-viewport class="virtual-scroll-container" itemSize="18">
    <ng-container *cdkVirtualFor="let node of dataSource">
      <div class="mat-tree-node" [style.padding-left]="node.level * 24 + 'px'" [style.padding-bottom]="node.level * 5 + 'px'">
        <button (click)="onToggle(node)" [attr.aria-label]="'toggle ' + node.Name">
          <mat-icon class="mat-icon">
          {{ treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
          </mat-icon>
        </button>
        <mat-icon
          [ngStyle]="{ color: node?.item === selectedNode?.item ? '#F44336' : 'grey' }"
          class="sfl-type-icon mt-5"
          [attr.aria-label]="node.type + 'icon'"
        >
          {{ 'folder' }}
        </mat-icon>

     <span class="nodeName mr-10 mt-10">
       {{ node?.item }}
      </span>
      </div>
    </ng-container>
  </cdk-virtual-scroll-viewport>
</mat-dialog-content>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="end">
    <button mat-raised-button type="button" (click)="closeDialog(null)">No</button>
  </div>
  <div fxLayoutAlign="start">
    <button mat-raised-button type="button" (click)="saveFilePathForJacket()" color="warn">Yes</button>
  </div>
</mat-dialog-actions>
