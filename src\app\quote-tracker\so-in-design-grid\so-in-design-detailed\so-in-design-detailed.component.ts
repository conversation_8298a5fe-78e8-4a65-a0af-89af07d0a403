import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { Variable } from '../../../shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MatTableDataSource } from '@angular/material';
import { DisplayColumns } from '../../../shared/constants/displayColName.constants';
import { SoInDesign, SoInDesignDetailed } from '../../quote-tracker.model';
import { QuoteTrackerService } from '../../quote-tracker.service';
import { DatePipe } from '@angular/common';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-so-in-design-detailed',
  templateUrl: './so-in-design-detailed.component.html'
})
export class SoInDesignDetailedComponent implements OnInit, OnDestroy {
  soInDesignTitle = 'SO in Design Detailed';
  totalNoOfDesign: number;
  totalDollarValue: number;

  subscription = new Subscription();

  @Input() _dataSource: SoInDesign;
  @Input() _fromDate: string;
  @Input() _toDate: string;

  soInDesignDetailedDataSource = new MatTableDataSource<SoInDesignDetailed>();
  soInDesignDetailedColumns = DisplayColumns.Cols.SoInDesignDetailedColumn;

  showLoader = false;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(private readonly quoteTrackerService: QuoteTrackerService, private readonly datePipe: DatePipe) {}

  ngOnInit() {
    this.soInDesignTitle = 'SO in Design in ' + this._dataSource.soInDesign;
    this.totalNoOfDesign = this._dataSource.noOfDesign;
    this.totalDollarValue = this._dataSource.dollarValue;
    this.getDetailedSOInDesignByType(this._dataSource.soInDesign.toLowerCase(), this._fromDate, this._toDate);
  }

  // used to get the details of SO based on the provided type
  getDetailedSOInDesignByType(category: string, fromDate: string, toDate: string) {
    this.showLoader = true;
    const filter = {
      category: category.toUpperCase(),
      startDate: this.datePipe.transform(fromDate, Values.dateFormat.formatHyphen),
      endDate: this.datePipe.transform(toDate, Values.dateFormat.formatHyphen)
    };
    this.subscription.add(
      this.quoteTrackerService.getSoInDesignByType(filter).subscribe(
        (res: SoInDesignDetailed[]) => {
          this.soInDesignDetailedDataSource.data = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
