import { Component, EventEmitter, Output, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { FormControl } from '@angular/forms';
import { map, startWith } from 'rxjs/operators';

import * as screenfull from 'screenfull';
import { MatDialog } from '@angular/material';

import { SharedService } from '../../shared';
import { Route } from 'src/app/shared/constants/router.constants';

@Component({
  selector: 'sfl-super-admin-topbar',
  templateUrl: './super-admin-topbar.component.html'
})
export class SuperAdminTopbarComponent implements OnInit {
  @Output() toggleSidenav = new EventEmitter<void>();

  myControl = new FormControl();
  filteredOptions: Observable<Quotations[]>;
  filter: boolean;
  isDesignAdmin: boolean;
  isAppAdmin: boolean;
  isFullScreen: boolean;

  constructor(private router: Router, private sharedService: SharedService, private matDialog: MatDialog) {}

  ngOnInit() {}

  displayFn(company?: Quotations): string | undefined {
    return company ? company.name : undefined;
  }

  gotolist() {
    this.router.navigate([this.sharedService.getRole() + '/jacket-list']);
  }

  fullScreenToggle(): void {
    if (screenfull.enabled) {
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    }
  }

  profile() {
    this.router.navigate([this.sharedService.getRole() + '/profile']);
  }

  changePassword() {
    this.router.navigate([this.sharedService.getRole() + '/change-password']);
  }

  switchToAppAdmin() {
    this.router.navigate(['/app-eng/dashboard']);
  }

  switchToDesign() {
    this.router.navigate(['/design-eng/dashboard']);
  }

  logout() {
    this.sharedService.logout(null);
  }

  search() {
    this.router.navigate([this.sharedService.getRole() + '/jacket-list']);
    this.filter = false;
  }

  switchToMasterdataManagement() {
    this.router.navigate([Route.MASTERDATA.management]);
  }

  // used to switch to the quote tracker screen
  switchToQuoteTracker() {
    this.router.navigate([Route.QUOTE_TRACKER.dashboard]);
  }
}

export interface Quotations {
  name: string;
  value: string;
}
