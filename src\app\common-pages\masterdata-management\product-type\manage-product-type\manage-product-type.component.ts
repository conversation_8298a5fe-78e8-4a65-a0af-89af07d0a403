import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { ProductTypeCoverPageMaster } from '../../masterdata-management.model';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'app-manage-product-type',
  templateUrl: './manage-product-type.component.html',
  styleUrls: ['./manage-product-type.component.css'],
})
export class ManageProductTypeComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  productTypes: ProductTypeCoverPageMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(public readonly dialogRef: MatDialogRef<ManageProductTypeComponent>, @Inject(MAT_DIALOG_DATA) data, private readonly masterDataService: MasterdataManagementService) {
    this.productTypes = data;
  }

  ngOnInit() {
    this.productTypes = this.productTypes.id ? Object.assign({}, this.productTypes) : new ProductTypeCoverPageMaster();
    this.productTypes.id ? (this.title = 'Update Product Type') : (this.title = 'Add Product Type');
  }

  updateProductType() {
    this.showLoader = true;
    if (this.productTypes.id) {
      this.subscription.add(
        this.masterDataService.updateProductType(this.productTypes).subscribe(
          (res) => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          (error) => {
            this.showLoader = false;
          },
        ),
      );
    } else {
      this.subscription.add(
        this.masterDataService.addProductType(this.productTypes).subscribe(
          (res) => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          (error) => {
            this.showLoader = false;
          },
        ),
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
