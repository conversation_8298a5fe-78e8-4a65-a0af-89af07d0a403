export class GenericPageable<T> {
  constructor(
    public content?: T[],
    public totalElements?: number,
    public totalPages?: number,
    public number?: number,
    public numberOfElements?: number
  ) { }
}

export class QuotationStatusMaster {
  constructor(public id?: number, public rowColor?: string, public appEngRowColor?: string, public status?: string, public type?: string, public isObsolete: boolean = false, public orderNumber?: number, public defaultHidden: boolean = false,public forApproval: boolean = false) { }
}
export const QuotationStatusMasterPageable = new GenericPageable<QuotationStatusMaster>();

export class QuotationStatusFilter {
  constructor(public status?: string) { }
}

export class AccessoryMaster {
  constructor(
    public id?: number,
    public controllerName?: string,
    public description?: string,
    public listPrice?: number,
    public partNumber?: string,
    public usCost?: number,
    public isObsolete: boolean = false
  ) { }
}

export class EcoLineMaster {
  constructor(
    public id?: number,
    public feature?: string,
    public revisionNumber?: number,
    public buttedRevisionId?: number
  ) { }
}

export const AccessoryMasterPageable = new GenericPageable<AccessoryMaster>();

export class AccessoryFilter {
  constructor(public partNumber: string = '', public controllerName: string = '') { }
}

export class MaterialMaster {
  constructor(
    public costPerSq?: number,
    public imageUrl?: string,
    public inventory?: string,
    public material?: string,
    public materialId?: number,
    public maxTemp?: number,
    public identifier?: string,
    public maxTempF?: number,
    public partNumber?: string,
    public isObsolete: boolean = false
  ) { }
}

export const MaterialMasterPageable = new GenericPageable<MaterialMaster>();

export class MaterialFilter {
  constructor(public partNumber: string = '', public material: string = '') { }
}

export class PlugMaster {
  constructor(
    public id?: number,
    public clothCe: boolean = false,
    public clothUl: boolean = false,
    public imageUrl?: string,
    public jumperPartNumber?: string,
    public jacketType?: string,
    public maxAmps?: string,
    public maxVolts?: string,
    public partNumber?: string,
    public plugCost?: number,
    public plugName?: string,
    public isObsolete: boolean = false
  ) { }
}

export class CurrencyMaster {
  constructor(
    public abbreviation?: string,
    public conversionRateWrtUSD?: string,
    public id?: number,
    public name?: string,
    public obsolete: boolean = false,
    public symbol?: string
  ) { }
}

export class CcdcMaster {
  constructor(
    public id?: number,
    public name?: string,
    public description?: string,
  ) { }
}


export const PlugMasterPageable = new GenericPageable<PlugMaster>();

export class PlugMasterFilter {
  constructor(public partNumber: string = '', public plugName: string = '') { }
}

export class ClosureMaterialMaster {
  constructor(
    public id?: number,
    public costPerSq?: number,
    public fastenerCode?: string,
    public imageUrl?: string,
    public maxTemp?: number,
    public maxTempF?: number,
    public name?: string,
    public partNumber1?: string,
    public partNumber2?: string,
    public isObsolete: boolean = false
  ) { }
}

export const ClosureMaterialMasterPageable = new GenericPageable<ClosureMaterialMaster>();

export class ClosureMaterialFilter {
  constructor(public partNumber1: string = '', public name: string = '') { }
}

export class MaterialPropertiesMaster {
  constructor(
    public id?: number,
    public density?: number,
    public materialType?: string,
    public name?: string,
    public specificHeat?: number,
    public isObsolete: boolean = false
  ) { }
}

export const MaterialPropertiesMasterPageable = new GenericPageable<MaterialPropertiesMaster>();

export class MaterialPropertiesFilter {
  constructor(public name?: string) { }
}

export class ThermostatMaster {
  constructor(
    public thermostatListId?: number,
    public adjustable: boolean = false,
    public amps?: number,
    public closeTemp?: number,
    public closeTempF?: number,
    public closeTolC?: number,
    public closeTolF?: number,
    public cost?: number,
    public manualReset: boolean = false,
    public manufacturer?: string,
    public openOnRise: boolean = false,
    public openTemp?: number,
    public openTempF?: number,
    public partNumber?: string,
    public tolerance?: number,
    public toleranceF?: number,
    public type?: string,
    public volts?: number,
    public isObsolete: boolean = false
  ) { }
}

export const ThermostatMasterPageable = new GenericPageable<ThermostatMaster>();

export class ThermostatFilter {
  constructor(public partNumber?: string) { }
}

export class FeaturesMaster {
  constructor(public id?: number, public name?: string) { }
}

export const FeaturesMasterPageable = new GenericPageable<FeaturesMaster>();

export class FeaturesFilter {
  constructor(public name?: string) { }
}

export class AccessoryControllerMaster {
  constructor(public id?: number, public name?: string, public isObsolete: boolean = false) { }
}

export const AccessoryControllerMasterPageable = new GenericPageable<AccessoryControllerMaster>();

export class AccessoryControllerFilter {
  constructor(public name?: string) { }
}

export class ProductTypeCoverPageMaster {
  constructor(public id?: number, public name?: string, public isObsolete: boolean = false) { }
}

export const ProductTypeCoverPageMasterPageable = new GenericPageable<ProductTypeCoverPageMaster>();

export class ProductFilter {
  constructor(public name?: string) { }
}

export class PQPFamilyMaster {
  constructor(public id?: number, public name?: string, public ecoPqpId?: number, public isObsolete: boolean = false) { }
}

export const PQPFamilyMasterPageable = new GenericPageable<PQPFamilyMaster>();

export class PQPFamilyFilter {
  constructor(public name?: string) { }
}

export class PowerCordConnectorMaster {
  constructor(public id?: number, public name?: string, public partNumber?: string, public price?: number, public value?: number) { }
}

export const PowerCordConnectorPageable = new GenericPageable<PowerCordConnectorMaster>();

export class PowerCordConnectorFilter {
  constructor(public partNumber?: string) { }
}

export class SleevingTypesAndStrainReliefsMaster {
  constructor(
    public id?: number,
    public name?: string,
    public partNumber?: string,
    public cost?: number,
    public clothCE: boolean = false,
    public clothUL: boolean = false,
    public isObsolete: boolean = false
  ) { }
}

export const SleevingTypesAndStrainReliefsPageable = new GenericPageable<SleevingTypesAndStrainReliefsMaster>();

export class SleevingTypesAndStrainReliefsFilter {
  constructor(public partNumber: string = '', public name: string = '') { }
}

export class PlugLightsMaster {
  constructor(
    public id?: number,
    public name?: string,
    public partNumber?: string,
    public cost?: number,
    public type?: string,
    public clothCE: boolean = false,
    public clothUL: boolean = false,
    public isObsolete: boolean = false
  ) { }
}
export const PlugLightsPageable = new GenericPageable<PlugLightsMaster>();

export class PlugLightsFilter {
  constructor(public partNumber: string = '', public name: string = '') { }
}

export class GoldStandardTapeWidthMaster {
  constructor(public id?: number, public tapeType?: string, public width?: number, public isObsolete: boolean = false) { }
}
export const GoldStandardTapeWidthPageable = new GenericPageable<GoldStandardTapeWidthMaster>();

export class GoldStandardTapeWidthFilter {
  constructor(public tapeType?: string) { }
}

export class BHXGoldWireTapesMaster {
  constructor(
    public id?: number,
    public alloyName?: string,
    public type?: string,
    public tpiStr?: number,
    public picks?: number,
    public wireType?: string,
    public isObsolete: boolean = false
  ) { }
}
export const BHXGoldWireTapesPageable = new GenericPageable<BHXGoldWireTapesMaster>();

export class BHXGoldWireTapesFilter {
  constructor(public alloyName?: string) { }
}

export class AlloyMaster {
  constructor(
    public id?: number,
    public alloyName?: string,
    public ohmsPerFoot?: number,
    public maxTempC?: number,
    public gndPartNumber?: string,
    public fgPartNumber?: string,
    public samPartNumber?: string,
    public tpignPartNumber?: string,
    public tpifgPartNumber?: string,
    public tpisamPartNumber?: string,
    public tpiWperIn?: number,
    public msm?: number,
    public msb?: number,
    public isObsolete: boolean = false
  ) { }
}
export const AlloyPageable = new GenericPageable<AlloyMaster>();

export class AlloyFilter {
  constructor(public gndPartNumber: string = '', public alloyName: string = '') { }
}

export class HeatingTapesMaster {
  constructor(
    public id?: number,
    public tapeType?: string,
    public tapePartNumber?: string,
    public sequenceNumber?: number,
    public width?: number,
    public ohmsPerFt?: number,
    public alloyName?: string,
    public wireType?: string,
    public strands?: number,
    public tpi?: number,
    public dualWireTape?: boolean,
    public picks?: number,
    public warps?: number,
    public isObsolete: boolean = false
  ) { }
}
export const HeatingTapesPageable = new GenericPageable<HeatingTapesMaster>();

export class HeatingTapesFilter {
  constructor(public tapePartNumber: string = '', public alloyName: string = '', public width: string = '', public tapeType: string = '') { }
}
export class TapePartNumberInfoDTO {
  constructor(public tapePartNumber: string, public sequenceNumber: number) { }
}
export class DepartmentsMaster {
  constructor(public id?: number, public name?: string, public active: boolean = false) { }
}
export const DepartmentsPageable = new GenericPageable<DepartmentsMaster>();

export class DepartmentsFilter {
  constructor(public name?: string) { }
}

export class ECRStatusesMaster {
  constructor(public id?: number, public status?: string, public active: boolean = false) { }
}
export const ECRStatusesPageable = new GenericPageable<DepartmentsMaster>();

export class ECRStatusesFilter {
  constructor(public status?: string) { }
}

export class BHXMaterialMaster {
  constructor(
    public id?: number,
    public grouping?: number,
    public partNumber?: string,
    public description?: string,
    public relOpr?: number,
    public qty?: number,
    public formula?: string,
    public qtyIdentity?: string,
    public operationName?: string,
    public sequence?: string,
    public opNumber?: number,
    public prodHrs?: number,
    public setupHrs?: number,
    public uom?: string,
    public blocked: boolean = false,
    public labelEntryRequired: boolean = false,
    public customer?: string,
    public customerAbbreviation?: string,
    public elementType?: string,
    public wireType?: string,
    public ce: boolean = null,
    public ul: boolean = null,
    public manualResetThermostat: boolean = null,
    public privateLabel: boolean = null,
    public layered?: string,
    public maxDiameter?: number,
    public minDiameter?: number,
    public closure?: string,
    public plug?: string,
    public connector?: string,
    public sensConn?: string,
    public sleeving?: string,
    public thermostat: boolean = null,
    public maxLength?: number,
    public minLength?: number,
    public greenLight?: string,
    public minJumpers?: string,
    public jacketType: string = null,
    public sensorType?: string,
    public type?: string,
    public allFieldsOptionChecked?: boolean,
    public productType?: string,
    public leadType?: string,
    public minVolts?: number,
    public maxVolts?: number,
    public minAmps?: number,
    public maxAmps?: number,
    public minLead?: number,
    public maxLead?: number,
    public minTemp?: number,
    public maxTemp?: number,
    public phase?: string,
    public phaseValue?: string,
    public installationMethod?: string,
    public strainRelief?: string,
    public hazardous: boolean = null,
    public appType?: string,
    public controller?: string,
    public siliconOpr?: number,
    public clothOpr?: number,
    public inseparableOpr?: number,
    public deleted: boolean = false
  ) { }
}
export const BHXMaterialPageable = new GenericPageable<BHXMaterialMaster>();

export class BHXMaterialFilter {
  constructor(public partNumber?: string) { }
}

export class SensorConnectorsAndTypesMaster {
  constructor(public id?: string, public value?: string) { }
}
export const SensorConnectorsAndTypesPageable = new GenericPageable<SensorConnectorsAndTypesMaster>();

export class SensorConnectorsAndTypesFilter {
  constructor(public id?: string) { }
}

export class PowerCordMaterialsMaster {
  constructor(
    public id?: number,
    public powerCordMaterialId?: string,
    public value?: string,
    public price?: number,
    public isObsolete: boolean = false
  ) { }
}
export const PowerCordMaterialsPageable = new GenericPageable<PowerCordMaterialsMaster>();

export class PowerCordMaterialsFilter {
  constructor(public value?: string) { }
}

export class PowerCordVoltagesMaster {
  constructor(public id?: number, public value?: string, public isObsolete: boolean = false) { }
}
export const PowerCordVoltagesPageable = new GenericPageable<PowerCordVoltagesMaster>();

export class PowerCordVoltagesFilter {
  constructor(public value?: string) { }
}

export class PowerCordAmpsMaster {
  constructor(public id?: number, public value?: string, public isObsolete: boolean = false) { }
}
export const PowerCordAmpsPageable = new GenericPageable<PowerCordAmpsMaster>();

export class PowerCordAmpsFilter {
  constructor(public value?: string) { }
}

export class PowerCordOptionsMaster {
  constructor(public id?: number, public powerCordConstantId?: string, public value?: string, public isObsolete: boolean = false) { }
}
export const PowerCordOptionsPageable = new GenericPageable<PowerCordOptionsMaster>();

export class PowerCordOptionsFilter {
  constructor(public value?: string) { }
}

export class WarpsMaster {
  constructor(public id?: number, public tapeWidth?: number, public warp?: number, public isObsolete: boolean = false) { }
}
export class EstEngRelDateMaster {
  constructor(public id?: number, public prodType?: string, public quoteStatus?: string,public noOfDays: number = 0) { }
}
export const WarpsPageable = new GenericPageable<WarpsMaster>();

export class WarpsFilter {
  constructor(public minTapeWidth?: number, public maxTapeWidth?: number) { }
}

export class LaborMaster {
  constructor(public id?: string, public labor?: number, public burden?: number, public country?: string, public materialOH?: number, public isObsolete: boolean = false) { }
}
export class ThermostatTypesMaster {
  constructor(public id?: string, public previousId?: string, public isObsolete: boolean = false) { }
}
export const ThermostatTypesPageable = new GenericPageable<ThermostatTypesMaster>();

export class ThermostatTypesFilter {
  constructor(public id?: string) { }
}

export class SensorTypesMaster {
  constructor(public id?: string, public previousId?: string, public isObsolete: boolean = false) { }
}
export const SensorTypesPageable = new GenericPageable<SensorTypesMaster>();

export class SensorTypesFilter {
  constructor(public id?: string) { }
}

export class SensorConnectorsMaster {
  constructor(public id?: string, public previousId?: string, public isObsolete: boolean = false) { }
}
export const SensorConnectorsPageable = new GenericPageable<SensorConnectorsMaster>();

export class SensorConnectorsFilter {
  constructor(public value?: string) { }
}

export class LeadTypesMaster {
  public id?: number;
  public leadName?: string;
  public partNumber?: string;
  public maxTemp?: number;
  public maxVolts?: number;
  public costPerFoot?: number;
  constructor(public isObsolete: boolean = false) { }
}

export const LeadTypesMasterPageable = new GenericPageable<LeadTypesMaster>();

export class LeadTypesMasterFilter {
  constructor(public partNumber: string = '', public leadName: string = '') { }
}

export class InstallationMethod {
  id?: number;
  methodName?: string;
}

export class StrainRelief {
  constructor(public id?: number, public name?: string, public partNumber?: string, public cost?: number) { }
}


export class Grouping {
  public dataType = 'Number';
  public key = 'grouping';
  public operator = 'Eq';
  public value?: number;
}

export class PartNumber {
  public dataType = 'String';
  public key = 'partNumber';
  public operator = 'Li';
  public value?: string;
}

export class Type {
  public dataType = 'String';
  public key = 'type';
  public operator = 'In';
  public value?: string;
}

export class AppType {
  public dataType = 'String';
  public key = 'appType';
  public operator = 'In';
  public value?: string;
}

export class Description {
  public dataType = 'String';
  public key = 'description';
  public operator = 'Li';
  public value?: string;
}

export class Formula {
  public dataType = 'String';
  public key = 'formula';
  public operator = 'Li';
  public value?: string;
}

export class RelOp {
  public dataType = 'Number';
  public key = 'relOpr';
  public operator = 'Eq';
  public value?: number;
}
export class siliconeRelOp {
  public dataType = 'Number';
  public key = 'siliconOpr';
  public operator = 'Eq';
  public value?: number;
}
export class clothRelOp {
  public dataType = 'Number';
  public key = 'clothOpr';
  public operator = 'Eq';
  public value?: number;
}
export class inseparableRelOp {
  public dataType = 'Number';
  public key = 'inseparableOpr';
  public operator = 'Eq';
  public value?: number;
}

export class UOM {
  public dataType = 'String';
  public key = 'uom';
  public operator = 'Li';
  public value?: string;
}

export class Quantity {
  public dataType = 'Number';
  public key = 'qty';
  public operator = 'Eq';
  public value?: number;
}

export class Customer {
  public dataType = 'String';
  public key = 'customer';
  public operator = 'Li';
  public value?: string;
}

export class CE {
  public dataType = 'Boolean';
  public key = 'ce';
  public operator = 'Eq';
  public value?: boolean = null;
}

export class UL {
  public dataType = 'Boolean';
  public key = 'ul';
  public operator = 'Eq';
  public value?: boolean = null;
}

export class Hazardous {
  public dataType = 'Boolean';
  public key = 'hazardous';
  public operator = 'Eq';
  public value?: boolean = null;
}

export class ManualReset {
  public dataType = 'Boolean';
  public key = 'manualResetThermostat';
  public operator = 'Eq';
  public value?: boolean = null;
}


export class Blocked {
  public dataType = 'Boolean';
  public key = 'blocked';
  public operator = 'Eq';
  public value?: boolean = false;
}

export class Deleted {
  public dataType = 'Boolean';
  public key = 'deleted';
  public operator = 'Eq';
  public value?: boolean = false;
}

export class PrivateLabel {
  public dataType = 'Boolean';
  public key = 'privateLabel';
  public operator = 'Eq';
  public value?: boolean = null;
}

export class ProductType {
  public dataType = 'String';
  public key = 'productType';
  public operator = 'In';
  public value?: string;
}

export class JacketType {
  public dataType = 'String';
  public key = 'jacketType';
  public operator = 'In';
  public value?: string;
}

export class MinVolatge {
  public dataType = 'Number';
  public key = 'minVolts';
  public operator = 'Eq';
  public value?: number;
}

export class MaxVolatge {
  public dataType = 'Number';
  public key = 'maxVolts';
  public operator = 'Eq';
  public value?: number;
}

export class MinAmperage {
  public dataType = 'Number';
  public key = 'minAmps';
  public operator = 'Eq';
  public value?: number;
}

export class MaxAmperage {
  public dataType = 'Number';
  public key = 'maxAmps';
  public operator = 'Eq';
  public value?: number;
}

export class MinLeadLength {
  public dataType = 'Number';
  public key = 'minLead';
  public operator = 'Eq';
  public value?: number;
}

export class MaxLeadLength {
  public dataType = 'Number';
  public key = 'maxLead';
  public operator = 'Eq';
  public value?: number;
}

export class MinTemprature {
  public dataType = 'Number';
  public key = 'minTemp';
  public operator = 'Eq';
  public value?: number;
}

export class MaxTemprature {
  public dataType = 'Number';
  public key = 'maxTemp';
  public operator = 'Eq';
  public value?: number;
}

export class Phase {
  public dataType = 'String';
  public key = 'phase';
  public operator = 'In';
  public value?: string;
}

export class InstallMethod {
  public dataType = 'String';
  public key = 'installationMethod';
  public operator = 'In';
  public value?: string;
}

export class Strain_Relief {
  public dataType = 'String';
  public key = 'strainRelief';
  public operator = 'In';
  public value?: string;
}

export class Controller {
  public dataType = 'String';
  public key = 'controller';
  public operator = 'In';
  public value?: string;
}

export class MaxDiameter {
  public dataType = 'Number';
  public key = 'maxDiameter';
  public operator = 'Eq';
  public value?: number;
}

export class MinDiameter {
  public dataType = 'Number';
  public key = 'minDiameter';
  public operator = 'Eq';
  public value?: number;
}

export class ElementType {
  public dataType = 'String';
  public key = 'elementType';
  public operator = 'In';
  public value?: string;
}

export class WireType {
  public dataType = 'String';
  public key = 'wireType';
  public operator = 'In';
  public value?: string;
}

export class SensorConnector {
  constructor(
    public dataType = 'String',
    public key = 'sensConn',
    public operator = 'In',
    public value?: string,
  ) { }
}

export class SensorType {
  public dataType = 'String';
  public key = 'sensorType';
  public operator = 'In';
  public value?: string;
}

export class Sleeving {
  public dataType = 'String';
  public key = 'sleeving';
  public operator = 'In';
  public value?: string;
}

export class MaxLength {
  public dataType = 'Number';
  public key = 'maxLength';
  public operator = 'Eq';
  public value?: number;
}

export class MinLength {
  public dataType = 'Number';
  public key = 'minLength';
  public operator = 'Eq';
  public value?: number;
}

export class MinJumper {
  public dataType = 'Number';
  public key = 'minJumpers';
  public operator = 'Eq';
  public value?: number;
}

export class Thermostat {
  public dataType = 'Boolean';
  public key = 'thermostat';
  public operator = 'Eq';
  public value: boolean = null;
}

export class ClosureMaterial {
  public dataType = 'String';
  public key = 'closure';
  public operator = 'In';
  public value?: string;
}

export class Plug {
  public dataType = 'String';
  public key = 'plug';
  public operator = 'In';
  public value?: string;
}

export class Layered {
  public dataType = 'String';
  public key = 'layered';
  public operator = 'In';
  public value?: string;
}

export class Connector {
  public dataType = 'String';
  public key = 'connector';
  public operator = 'In';
  public value?: string;
}

export class LeadType {
  public dataType = 'String';
  public key = 'leadType';
  public operator = 'In';
  public value?: string;
}

export class GreenLight {
  public dataType = 'String';
  public key = 'greenLight';
  public operator = 'In';
  public value?: string;
}

export class Operation {
  public dataType = 'String';
  public key = 'operationName';
  public operator = 'Li';
  public value?: string;
}

export class Sequence {
  public dataType = 'String';
  public key = 'sequence';
  public operator = 'Li';
  public value?: string;
}

export class OperationNumber {
  public dataType = 'Number';
  public key = 'sequence';
  public operator = 'Eq';
  public value?: number;
}

export class ProductionHours {
  public dataType = 'Number';
  public key = 'prodHrs';
  public operator = 'Eq';
  public value?: number;
}

export class SetupHours {
  public dataType = 'Number';
  public key = 'setupHrs';
  public operator = 'Eq';
  public value?: number;
}

export class BhxMatetrialFilter {
  public grouping?: Grouping = new Grouping();
  public partNumber?: PartNumber = new PartNumber();
  public type?: Type = new Type();
  public appType?: AppType = new AppType();
  public description: Description = new Description();
  public relOp?: RelOp = new RelOp();
  public siliconRelOp?: siliconeRelOp = new siliconeRelOp();
  public clothRelOp?: clothRelOp = new clothRelOp();
  public inseparableRelOp?: inseparableRelOp = new inseparableRelOp();
  public formula?: Formula = new Formula();
  public uom?: UOM = new UOM();
  public quantity?: Quantity = new Quantity();
  public customer?: Customer = new Customer();
  public ce?: CE = new CE();
  public ul?: UL = new UL();
  public hazardous?: Hazardous = new Hazardous();
  public manualResetThermostat?: ManualReset = new ManualReset();
  public privateLabel?: PrivateLabel = new PrivateLabel();
  public productType?: ProductType = new ProductType();
  public minVolts?: MinVolatge = new MinVolatge();
  public maxVolts?: MaxVolatge = new MaxVolatge();
  public minAmps?: MinAmperage = new MinAmperage();
  public maxAmps?: MaxAmperage = new MaxAmperage();
  public minLead?: MinLeadLength = new MinLeadLength();
  public maxLead?: MaxLeadLength = new MaxLeadLength();
  public minTemp?: MinTemprature = new MinTemprature();
  public maxTemp?: MaxTemprature = new MaxTemprature();
  public phase?: Phase = new Phase();
  public jacketType?: JacketType = new JacketType();
  public installationMethod?: InstallMethod = new InstallMethod();
  public strainRelief?: Strain_Relief = new Strain_Relief();
  public controller?: Controller = new Controller();
  public maxDiameter?: MaxDiameter = new MaxDiameter();
  public minDiameter?: MinDiameter = new MinDiameter();
  public elementType?: ElementType = new ElementType();
  public wireType?: WireType = new WireType();
  public blocked?: Blocked = new Blocked();
  public deleted?: Deleted = new Deleted();
  public sensConn: SensorConnector = new SensorConnector();
  public sensorType?: SensorType = new SensorType();
  public sleeving?: Sleeving = new Sleeving();
  public maxLength?: MaxLength = new MaxLength();
  public minLength?: MinLength = new MinLength();
  public layered?: Layered = new Layered();
  public minJumpers?: MinJumper = new MinJumper();
  public thermostat?: Thermostat = new Thermostat();
  public closure?: ClosureMaterial = new ClosureMaterial();
  public plug?: Plug = new Plug();
  public connector?: Connector = new Connector();
  public leadType?: LeadType = new LeadType();
  public greenLight?: GreenLight = new GreenLight();
  public operationName?: Operation = new Operation();
  public sequence?: Sequence = new Sequence();
  public prodHrs?: ProductionHours = new ProductionHours();
  public setupHrs?: SetupHours = new SetupHours();
}

export class LabelMasterPartNumberFilter {
  constructor(public labelPartNumber: string = '') { }
}
export class LabelConfigMaster {
  constructor(
    public amps = '',
    public csa = '',
    public csv = '',
    public format = '',
    public highT = '',
    public id?: number,
    public labelPartNumber = '',
    public length = '',
    public lowT = '',
    public mhla = '',
    public mhlv = '',
    public modelNumber = '',
    public open1 = '',
    public open2 = '',
    public open3 = '',
    public phase = '',
    public size = '',
    public tempRange = '',
    public volts = '',
    public watts = '',
    public width = '',
    public imageUrl = '',
  ) { }

}
