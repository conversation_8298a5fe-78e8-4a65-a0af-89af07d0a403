import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { SnakbarService } from 'src/app/shared';
import { JacketProductType, OperationsMasterData } from '../../bom-editor/bom-editor.model';
import { BomEditorService } from '../../bom-editor/bom-editor.service';

@Component({
  selector: 'sfl-final-add-label',
  templateUrl: './final-add-label.component.html'
})
export class FinalAddLabelComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  jacketId: number;
  groupingId: number;
  selection: string;
  type: string;
  productType: string;
  masterDataList: OperationsMasterData[];
  masterDataDetails: OperationsMasterData;

  constructor(
    public dialogRef: MatDialogRef<FinalAddLabelComponent>,
    private snakbarService: SnakbarService,
    private bomeditorService: BomEditorService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketId = data.jacketId;
    this.groupingId = data.groupingId;
    this.type = data.type;
  }

  ngOnInit() {
    this.masterDataDetails = new OperationsMasterData();
    this.getProductType();
  }

  // used to get the product type of a jacket by jacket id
  getProductType() {
    this.subscription.add(
      this.bomeditorService.getProductTypeByJacketId(this.jacketId).subscribe((typeObject: JacketProductType) => {
        this.productType = typeObject.productType;
        this.getAllLabels();
      })
    );
  }

  getAllLabels() {
    this.subscription.add(
      this.bomeditorService
        .getAllMaterialByGroupId(this.productType, this.groupingId, this.type)
        .subscribe((res: OperationsMasterData[]) => {
          if (res) {
            this.masterDataList = res;
          }
        })
    );
  }

  onSelectionChanges(index) {
    index !== 'other' ? (this.masterDataDetails = this.masterDataList[index]) : (this.masterDataDetails = {});
  }

  saveData() {
    this.masterDataDetails.jacketId = this.jacketId;
    // this.masterDataDetails.id = null;
    this.masterDataDetails.type = this.type;
    this.masterDataDetails.relOperation = this.masterDataDetails.relOpr;
    this.dialogRef.close(this.masterDataDetails);
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
