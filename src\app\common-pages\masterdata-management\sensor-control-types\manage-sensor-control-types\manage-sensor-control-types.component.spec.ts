import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageSensorControlTypesComponent } from './manage-sensor-control-types.component';

describe('ManageSensorControlTypesComponent', () => {
  let component: ManageSensorControlTypesComponent;
  let fixture: ComponentFixture<ManageSensorControlTypesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageSensorControlTypesComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageSensorControlTypesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
