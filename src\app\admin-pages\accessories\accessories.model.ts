export class JacketList {
  constructor(
    public jacketControlType?: string,
    public otherJacketControlType?: string,
    public jacketGroupId?: number,
    public jacketGroupName?: string,
    public totalAmps?: number,
    public totalDiscount?: number,
    public totalExtendedNetPrice?: number,
    public totalFactoryCost?: number,
    public totalJacketCount?: number,
    public totalLaborHours?: number,
    public totalListPrice?: number,
    public totalMargin?: number,
    public totalNetPrice?: number,
    public totalSystemLength?: number,
    public totalMaterialCost?: number,
    public totalWatts?: number,
    public jacketAccessoryList?: JacketAccessoriesList[]
  ) {}
}

export class JacketAccessoriesList {
  constructor(
    public amps?: number,
    public controlType?: string,
    public otherControlType?: string,
    public discount?: number,
    public appliedDiscount?: number,
    public vietnamDiscount?: number,
    public extendedNetPrice?: number,
    public vietnamExtendedNetPrice?: number,
    public costaRicaExtendedNetPrice?: number,
    public factoryCost?: number,
    public id?: number,
    public jacketRepeat: boolean = false,
    public jacketId?: number,
    public revisionAccessoryId?: number,
    public revisionId?: number,
    public laborHours?: number,
    public addedLaborHours = 0,
    public listPrice?: number,
    public addedListPrice = 0,
    public margin?: number,
    public vietnamMargin?: number,
    public costaRicaMargin?: number,
    public materialCost?: number,
    public usMaterialCost?: number,
    public vietnamMaterialCost?: number,
    public costaRicaMaterialCost?: number,
    public addedMaterialCost = 0,
    public addedCost = 0,
    public name?: string,
    public netPrice?: number,
    public vietnamNetPrice?: number,
    public costaRicaNetPrice?: number,
    public systemLength?: number,
    public watts?: number,
    public grossMargin?: number,
    public vietnamGrossMargin?: number,
    public costaRicaGrossMargin?: number,
    public description?: string,
    public quantity?: number,
    public customerPN?: string,
    public partNumber?: string,
    public madeIn?: string,
    public manualUpdated?: boolean,
    public massUpdated?: boolean,
    public finalizeInformationChangedField = 'NONE'
  ) {}
}
