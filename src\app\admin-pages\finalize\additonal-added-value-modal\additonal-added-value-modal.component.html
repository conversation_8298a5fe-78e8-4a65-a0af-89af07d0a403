<div fxLayout="row">
  <div fxFlex>
    <h2 mat-dialog-title>Added Values Summary
      <hr>
    </h2>
  </div>
</div>
<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <h1>Total Added Labor Hours : </h1>
    </div>
    <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
    <h1>{{jacket?.addedLaborHours}}</h1>
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <h1>Total Added Material Cost:</h1>
    </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
    <h1>{{jacket?.addedMaterialCost}}</h1>
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <h1>Total Added Cost : </h1>
    </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
    <h1>{{jacket?.addedCost}}</h1>
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <h1>Total Added List Price : </h1>
    </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
    <h1>{{jacket?.addedListPrice}}</h1>
    </div>
  </div>
</mat-dialog-content>
<hr>
<mat-dialog-actions>
  <button mat-raised-button type="button" (click)="closeDialog()">Close</button>
</mat-dialog-actions>