
import {catchError, map} from 'rxjs/operators';
import { ApplicationInfo } from './../ccdc-model/ccdc.model';
import { utils } from './../../../shared/helpers/app.helper';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class AddApplicationService {

    constructor(private http: HttpClient) { }

    getMaterialList() {
        return this.http.get(AppConfig.GET_MATERIALS_LIST).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    saveApplicationInfo(appInfo: ApplicationInfo) {
        return this.http.post(AppConfig.APPLICATION_INFO, appInfo).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    saveOrupdateApplicationInfo(appInfo: ApplicationInfo, id: number) {
        if (id) {
            return this.http.put(AppConfig.APPLICATION_INFO, appInfo).pipe(map(utils.extractData),
            catchError(utils.handleError),);
        } else {
            return this.http.post(AppConfig.APPLICATION_INFO, appInfo).pipe(map(utils.extractData),
                catchError(utils.handleError),);
        }
    }

    getApplicationInfoByJacketGroup(id: number) {
        return this.http.get(AppConfig.APPLICATION_INFO + '/jacketGroup/' + id).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }
}
