import { Component, OnInit, Inject, On<PERSON><PERSON>roy } from '@angular/core';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogConfig, MatDialog } from '@angular/material';
import { Subscription } from 'rxjs';
import { MaterialMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { AddAttachmentComponent } from 'src/app/design-pages/ecr-management/add-attachment/add-attachment.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { SnakbarService } from 'src/app/shared';
import { Messages } from 'src/app/shared';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'sfl-manage-material',
  templateUrl: './manage-material.component.html'
})
export class ManageMaterialComponent implements OnInit, OnD<PERSON>roy {
  subscription = new Subscription();
  showLoader = false;
  material: MaterialMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  formData = new FormData();
  imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  constructor(
    public readonly dialogRef: MatDialogRef<ManageMaterialComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) {
    this.material = data;
  }
  ngOnInit() {
    this.material = this.material.materialId ? Object.assign({}, this.material) : new MaterialMaster();
    this.material.materialId ? (this.title = 'Update Material') : (this.title = 'Add Material');
    this.material.imageUrl
      ? (this.imageUrl = environment.IMAGES_URL + this.material.imageUrl)
      : (this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE);
  }

  updateMaterial() {
    this.showLoader = true;
    this.formData.append('materialDTO', JSON.stringify(this.material));
    this.subscription.add(
      this.masterDataService.addMaterial(this.formData).subscribe(
        () => {
          this.showLoader = false;
          this.dialogRef.close(true);
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  addAttachment() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-upload-material-file-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      if (uploadedAttachment && uploadedAttachment.type.match(/image\/*/)) {
        const reader = new FileReader();
        reader.readAsDataURL(uploadedAttachment);
        reader.onload = _event => {
          this.imageUrl = reader.result.toString();
          this.material.imageUrl = reader.result.toString();
        };
        this.formData.append('file', uploadedAttachment, uploadedAttachment.name);
      } else if (uploadedAttachment) {
        this.snakbarService.error(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Only_Image_Type_Allowed);
      }
    });
  }

  removeAttachment() {
    this.formData.delete('file');
    this.material.imageUrl = '';
    this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  // takes temperature type and sends to master data service method for conversion based on the type [celcius <-> fahrenheit]
  convertTeperature(temperatureType: string): void {
    temperatureType === 'celcius'
      ? (this.material.maxTempF = this.masterDataService.convertTemperature(this.material.maxTemp, temperatureType))
      : (this.material.maxTemp = this.masterDataService.convertTemperature(this.material.maxTempF, temperatureType));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
