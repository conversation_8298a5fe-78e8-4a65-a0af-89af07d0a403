<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Notes
  <hr />
</h2>
<form #notesForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-form-field fxFlex="100">
      <textarea matInput placeholder="Notes" name="note" rows="10" [(ngModel)]="note.notes" #noteInput="ngModel"></textarea>
    </mat-form-field>
  </mat-dialog-content>
  <hr />
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveNote(notesForm)" [disabled]="notesForm.form.invalid" name="save">
      Save
    </button>
    <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
  </mat-dialog-actions>
</form>
