
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';


import { SharedService } from 'src/app/shared';

@Injectable({ providedIn: 'root' })
export class NewRevisonService {
  constructor(private http: HttpClient, private readonly sharedService: SharedService) {}

  updateDirectory(data) {
    return this.http.post(AppConfig.NEW_REVISON_UPDATE_DIR, {
      partNumber: data.partNumber,
      newRevision: data.newRevision,
      oldRevision: data.oldRevision,
      fixPath: data.fixPath,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations(),
      CustomPath: data.CustomPath
    });
  }

  updateRevisionBHX(data) {
    return this.http.put(AppConfig.UPDATE_REVISION, data).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
}
