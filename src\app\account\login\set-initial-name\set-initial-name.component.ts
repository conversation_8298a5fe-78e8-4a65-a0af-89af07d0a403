import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { AuthenticationService } from '../login.service';
import { Subscription } from 'rxjs';
import { UserInitialNameDto } from '../../account.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SnakbarService, Messages } from 'src/app/shared';

@Component({
  selector: 'sfl-set-initial-name',
  templateUrl: './set-initial-name.component.html'
})
export class SetInitialNameComponent implements OnInit, OnDestroy {

  userDto: UserInitialNameDto = new UserInitialNameDto();
  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(private dialogRef: MatDialogRef<SetInitialNameComponent>, private loginService: AuthenticationService, @Inject(MAT_DIALOG_DATA) data, private snakbarService: SnakbarService) {
    this.userDto.id = data.userId;
  }

  ngOnInit() {
  }

  setInitialName() {
    if (this.userDto.initialName && this.userDto.initialName.trim().length > 0) {
    this.showLoader = true;
      this.subscription.add(this.loginService.setInitialName(this.userDto).subscribe(res => {
        this.showLoader = false;
        this.dialogRef.close(true);
      }, error => {
        this.showLoader = false;
        this.snakbarService.error(Messages.Initial_Name.TryAgain);
      }));
    } else {
      this.snakbarService.error(Messages.Initial_Name.Invalid);
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
