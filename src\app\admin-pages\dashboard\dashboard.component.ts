import {ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {MatDialog, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {QuotationPageable, QuotationSearchFilter, SalesAssociate, Status, StatusCount} from './dashboard.model';
import {DisplayColumns} from './../../shared/constants/displayColName.constants';
import {DashboardService} from './dashboard.service';
import {SharedService, SnakbarService, SweetAlertService} from '../../shared';
import {Subscription} from 'rxjs';
import {Values} from 'src/app/shared/constants/values.constants';
import {map} from 'rxjs/operators';
import {Title} from '@angular/platform-browser';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {CopyQuotationComponent} from './copy-quotation/copy-quotation.component';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';

@Component({
  selector: 'sfl-dashboard',
  templateUrl: './dashboard.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardComponent implements OnInit, OnDestroy {
  noOfRecord: number = Values.pageSize;
  salesassociate: SalesAssociate[];
  statuscount: StatusCount;
  status: Status[];
  quotationPageable: QuotationPageable = new QuotationPageable();

  displayedColumns = DisplayColumns.Cols.DashboardQuotationList;
  quotationSearchFilter = new QuotationSearchFilter();
  isError = false;
  subscription = new Subscription();

  totalPages: number;
  showLoader = false;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.itemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.itemsPerPage;
  length: number;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortBy;
  dataSource = new MatTableDataSource();
  isNoDataFound = this.dataSource.connect().pipe(map(data => data.length === 0));
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  userRole:string;
  @ViewChild(MatPaginator) paginator = MatPaginator;
  @ViewChild(MatSort) sort = new MatSort();

  constructor(
    private readonly dashboardService: DashboardService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly sharedService: SharedService,
    private readonly titleService: Title,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) {}

  async ngOnInit() {
    this.titleService.setTitle('Dashboard - App Eng');
    this.statuscount = new StatusCount();
    this.onLoadData();
    this.userRole = this.sharedService.getRole();
    this.status = await this.sharedService.getAllStatus();
    this.status = this.status.filter(
      status => (status.type === Values.ApplicationEng || status.type === Values.Both) && status.status !== Values.Archived
    );
    this.subscription.add(
      this.sharedService.setQuotationList(new QuotationSearchFilter()) // if user leaves the page, all the search param should be blank or when user re-visit the page user would get the previously search result.
    );
    this.getAdvSearch();
  }

  async onLoadData() {
    this.countStatus();
    await this.getSaleAssociate();
  }

  getAdvSearch() {
    if (this.sharedService.refreshResult) {
      this.subscription.add(
        this.sharedService.refreshResult.subscribe((res: QuotationSearchFilter) => {
          if (res.customerName || res.quotationNumber || res.salesAssociateName || res.status) {
            this.quotationSearchFilter = res;
            this.searchQuotations(this.initialPageIndex, this.pageSize);
          } else {
            this.quotationSearchFilter = new QuotationSearchFilter(); // if global search is reset search object should be reset as this.quotationSearchFilter is global object. // Fetching quotations without any filter applied.
            this.searchQuotations(this.initialPageIndex, this.pageSize);
          }
        })
      );
    }
  }

  importAndroidData(){
    this.snakbarService.successMS('Mobile app data import is in progress! It will take 5 to 10 minutes to complete',5000);
    this.dashboardService.importMobileData().subscribe((res) =>{
      this.snakbarService.successMS('Mobile app data import is completed',5000);
    },error => {
      this.snakbarService.error('There some issue in importing the data, might dont have any data to pull',5000);
    });
  }

  getPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.searchQuotations(this.pageIndex, this.pageSize);
  }

  getSorting(event) {
    this.sortField = event.active;
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    this.searchQuotations(this.initialPageIndex, this.pageSize);
  }

  async addFilter() {
    this.quotationSearchFilter.quotationNumber =
      this.quotationSearchFilter.quotationNumber === '' ? undefined : this.quotationSearchFilter.quotationNumber;
    this.quotationSearchFilter.customerName =
      this.quotationSearchFilter.customerName === '' ? undefined : this.quotationSearchFilter.customerName;
    this.searchQuotations(this.initialPageIndex, this.pageSize);
  }

  searchQuotations(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, sort: this.sortField + ',' + this.sortOrder, direction: this.sortOrder };
    this.showLoader = true;
    this.subscription.add(
      this.dashboardService.searchQuotations(this.quotationSearchFilter, pageable).subscribe((res: QuotationPageable) => {
        if (res) {
          this.quotationPageable = res;
          this.length = res.totalElements;
          this.totalPages = res.totalPages;
          this.pageIndex = res.number;
          this.createMatTable(this.quotationPageable);
          this.showLoader = false;
        } else {
          this.dataSource.data = [];
          this.showLoader = false;
        }
      })
    );
  }

  createMatTable(serviceRequestList: QuotationPageable) {
    this.dataSource.data = serviceRequestList.content;
  }

  getSaleAssociate() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.dashboardService.getSalesAssociate(false).subscribe(
        (res: Array<SalesAssociate>) => {
          this.salesassociate = res;
          this.showLoader = false;
          resolve();
        },
        () => (this.showLoader = false)
      );
    });
  }

  countStatus() {
    this.dashboardService.countStatus().subscribe((res: StatusCount) => {
      this.statuscount = res;
    });
  }

  async deleteQuotations(id) {
    if (await this.sweetAlertService.archiveAlert()) {
      this.dashboardService.deleteQuotations(id).subscribe(() => {
        this.updateQuotationsList();
      });
    }
  }

  async resetFilter() {
    this.quotationSearchFilter = new QuotationSearchFilter();
    this.searchQuotations(this.initialPageIndex, this.pageSize);
  }

  updateQuotationsList() {
    this.searchQuotations(this.initialPageIndex, this.pageSize);
  }

  openCopyQuotation(quotationIds: number): void {
    this.showLoader = true;
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { quotationId: quotationIds };
    matDataConfig.width = PopupSize.size.popup_xmd;
    const dialogRef = this.matDialog.open(CopyQuotationComponent, matDataConfig)
    dialogRef.afterClosed()
    .subscribe(() => {
      this.updateQuotationsList();
      this.showLoader = false;
    });
    this.showLoader = false;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
