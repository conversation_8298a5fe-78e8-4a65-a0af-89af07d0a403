import {Component, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import {Mat<PERSON><PERSON>og, MatDialogConfig} from '@angular/material';
import {Subscription} from 'rxjs';
import {Messages, SharedService, SnakbarService, SweetAlertService} from 'src/app/shared';
import {DMTLogComponent} from 'src/app/shared/component/dml-log/dmt-log.component';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {Values} from '../../../shared/constants/values.constants';
import {TitleBlockHighlighterDTO} from '../title-block-editor/title-block-editor.model';
import {
  AMPS,
  BasicInfo,
  Element,
  ElementInfo,
  ElementType,
  Reliability,
  SearchParameter,
  TapeInformation,
  TargetingResult,
  WireInformation,
  WireSelector,
  WireSelectorResult
} from './element.model';
import {ElementService} from './element.service';
import {SearchParametersComponent} from './search-parameters/search-parameters.component';
import {Units} from '../../../admin-pages/new-quotation/ccdc-model/ccdc.model';
import {ManageUnitsService} from '../../../admin-pages/new-quotation/manage-units/manage-units.service';

@Component({
  selector: 'sfl-element',
  templateUrl: './element.component.html'
})
export class ElementComponent implements OnInit, OnDestroy {
  private _JacketID: number;
  subscription = new Subscription();
  notApplicable = 'N/A';
  elementsType: ElementType[];
  elementName: string;
  elementNumber = 1;
  wireSelector: WireSelector;
  basicInfo: BasicInfo;
  tempUnit: string;
  phaseTypes: object = Values.elementPhaseTypeConst;
  wireSelectorResult: WireSelectorResult[];
  targetingResult: TargetingResult;
  reliability: Reliability;
  wireInformation: WireInformation;
  tapeInformation: TapeInformation;
  elementChoiceSelected: boolean[];
  showLoader: boolean;
  elementObject: Element;
  wireType = [];
  syncStatus: string;
  isSyncSuccess = true;
  loading = false;
  syncRes: string;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  manuallyAddTapePartNumber = false;
  isTapePartNumberAvailable = false;
  allowSelectElement = false;

  get jacketID() {
    return this._JacketID;
  }

  @Input()
  set jacketID(val) {
    this._JacketID = val;
    this.getJacketElement(this._JacketID);
    this.checkElementLastSyncStatusByJacketId();
  }

  titleBlockHighlighter: TitleBlockHighlighterDTO = new TitleBlockHighlighterDTO();

  constructor(
    private matDialog: MatDialog,
    private elementService: ElementService,
    private sharedService: SharedService,
    private snakbarService: SnakbarService,
    private manageUnitService: ManageUnitsService,
    private sweetAlertService: SweetAlertService
  ) {
  }

  ngOnInit() {
    this.wireSelectorResult = new Array<WireSelectorResult>();
    this.targetingResult = new TargetingResult();
    this.reliability = new Reliability();
    this.wireInformation = new WireInformation();
    this.tapeInformation = new TapeInformation();
    this.wireSelector = new WireSelector();
    this.wireSelector.elementInfoDTO = new ElementInfo();
    this.wireSelector.basicInfoDTO = new BasicInfo();
    this.basicInfo = new BasicInfo();
    this.elementsType = Values.ElementType;
    this.elementObject = new Element();
    this.getMeasurementUnit();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnitByJacketId(this.jacketID).subscribe(
        (res: Units) => {
          if (res) {
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  changeElement(elementNumber: number) {
    this.elementNumber = elementNumber;
    this.getJacketElement(this._JacketID);
    this.checkElementLastSyncStatusByJacketId();
  }

  getJacketElement(jacketId) {
    this.showLoader = true;
    this.wireSelectorResult = new Array<WireSelectorResult>();
    this.elementService.getJacketElementJacket(jacketId, this.elementNumber).subscribe((res: WireSelector) => {
      if (res.basicInfoDTO && res.elementInfoDTO) {
        this.wireSelector.basicInfoDTO = res.basicInfoDTO;
        if (
          this.wireSelector.basicInfoDTO.productType &&
          this.wireSelector.basicInfoDTO.productType.toUpperCase() === Values.productTypeSilicone
        ) {
          this.wireType = Values.wireTypeSiliconeConst;
        } else {
          this.wireType = Values.wireTypeConst;
        }
        this.wireSelector.elementInfoDTO = res.elementInfoDTO;
        if(this.wireSelector.elementInfoDTO.standard === undefined || this.wireSelector.elementInfoDTO.standard===null || this.wireSelector.elementInfoDTO.standard===''){
          this.wireSelector.elementInfoDTO.standard = 'std';
        }
        this.targetingResult = res.targetingResult;
        this.wireSelector.tajima = res.tajima;
        this.wireSelector.goldStandard = res.goldStandard;
        this.wireSelector.inVietnam = res.inVietnam;
        this.reliability = res.reliability;
        this.tapeInformation = res.tapeInformation;
        this.setOldTapePartNumber();
        this.wireInformation = res.wireInformation;
        this.elementObject.id = res.id;
        this.elementObject.tgtOhms = res.tgtOhms;
        this.elementObject.maxOhms = res.maxOhms;
        this.elementObject.minOhms = res.minOhms;
        this.isTapePartNumberAvailable = true;
        // we already have the wire/ tape info selected so we need to make the select element button clickable on initialization
        this.allowSelectElement = true;
      } else {
        this.emptyResultDTOs();
        this.getBasicInfo();
        this.allowSelectElement = false;
      }
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    });
  }

  // provides a modal where user can add/ update additional parameters before searching for the wire/ tape
  openSearchParameters() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.data = {elementName: this.wireSelector.elementInfoDTO.elementType, wireInfo: this.wireSelector};
    this.matDialog
      .open(SearchParametersComponent, matDataConfig)
      .afterClosed()
      .subscribe(
        result => {
          if (result) {
            // we need to set the Targeting, Reliability, Tape info and wire info to nul when user serches from the change parameter modal as we did in normal search function
            this.allowSelectElement = false;
            this.targetingResult = new TargetingResult();
            this.reliability = new Reliability();
            this.wireInformation = new WireInformation();
            this.tapeInformation = new TapeInformation();
            this.elementObject.tgtOhms = null;
            this.elementObject.minOhms = null;
            this.elementObject.maxOhms = null;
            this.wireSelectorResult = result;
            this.elementChoiceSelected = new Array<boolean>(this.wireSelectorResult.length);
          }
        },
        () => {
          this.emptyObject();
        }
      );
  }

  emptyObject() {
    this.wireSelectorResult = [];
    this.targetingResult = new TargetingResult();
    this.reliability = new Reliability();
    this.wireInformation = new WireInformation();
    this.tapeInformation = new TapeInformation();
  }

  // used to search the wire/ tapes from the users input/ preferences made on the element selection
  searchWire() {
    this.allowSelectElement = false;
    this.targetingResult = new TargetingResult();
    this.reliability = new Reliability();
    this.wireInformation = new WireInformation();
    this.tapeInformation = new TapeInformation();
    this.elementObject.tgtOhms = null;
    this.elementObject.minOhms = null;
    this.elementObject.maxOhms = null;
    this.showLoader = true;
    this.wireSelector.searchParameterDTO = new SearchParameter();
    if (this.wireSelector.tajima === true) {
      this.wireSelector.searchParameterDTO.tpiStart = 0;
      this.wireSelector.searchParameterDTO.tpiEnd = 0;
    }
    // this checks if the elementType is Tape/ Dual wire tape and the tape width is less than 0.5 then hide the TPI elements from elements choices as the min width to use TAPI is 0.5
    if (this.wireSelector.elementInfoDTO.elementType !== Values.ElementTypes.WireType && this.wireSelector.elementInfoDTO.tapeWidth < 0.5) {
      this.wireSelector.searchParameterDTO.tpiStart = 0;
      this.wireSelector.searchParameterDTO.tpiEnd = 0;
    }
    if (this.wireSelector.elementInfoDTO.wireType.toLowerCase().includes('grounded')) {
      this.wireSelector.searchParameterDTO.picksStart = 6;
      this.wireSelector.searchParameterDTO.picksEnd = 9;
    } else {
      this.wireSelector.searchParameterDTO.picksStart = 6;
      this.wireSelector.searchParameterDTO.picksEnd = 12;
    }
    if (this.wireSelector.elementInfoDTO.elementType === Values.ElementDualWireTape) {
      this.wireSelector.elementInfoDTO.dualWireTape = true;
    } else if (this.wireSelector.elementInfoDTO.elementType === Values.ElementTape) {
      this.wireSelector.elementInfoDTO.dualWireTape = false;
    }
    this.subscription.add(
      this.elementService.searchWire(this.wireSelector).subscribe(
        (res: WireSelectorResult[]) => {
          if (res.length > 0) {
            this.wireSelectorResult = res;
            this.elementChoiceSelected = new Array<boolean>(this.wireSelectorResult.length);
            this.showLoader = false;
          } else {
            this.showLoader = false;
            this.emptyObject();
            this.snakbarService.error(Messages.error_msg.nodata);
          }
        },
        () => {
          this.emptyObject();
          this.showLoader = false;
        }
      )
    );
  }

  // gets the basic info for element
  getBasicInfo() {
    this.subscription.add(
      this.elementService.getBasicInfo(this._JacketID).subscribe((res: BasicInfo) => {
        if (res) {
          this.wireSelector.basicInfoDTO = res;
          if (
            this.wireSelector.basicInfoDTO.productType &&
            this.wireSelector.basicInfoDTO.productType.toUpperCase() === Values.productTypeSilicone
          ) {
            this.wireType = Values.wireTypeSiliconeConst;
          } else {
            this.wireType = Values.wireTypeConst;
          }
          if (res.jacketType === Values.jacketTypeNonGrounded) {
            this.wireSelector.elementInfoDTO.wireType = Values.wireTypeFiberglass;
          } else if (res.jacketType === Values.jacketTypeNonGroundedSamox) {
            this.wireSelector.elementInfoDTO.wireType = Values.wireTypeSamox;
          } else {
            this.wireSelector.elementInfoDTO.wireType = res.jacketType;
          }
        }
      })
    );
  }

  getAmps(field = '') {
    if (this.wireSelector.basicInfoDTO.volts && this.wireSelector.basicInfoDTO.watts) {
      this.subscription.add(
        this.elementService
          .getAMPS(this.wireSelector.basicInfoDTO.volts, this.wireSelector.basicInfoDTO.watts, this.wireSelector.basicInfoDTO.phase)
          .subscribe((res: AMPS) => {
            this.wireSelector.basicInfoDTO.amps = res.amps;
          })
      );
    }
    // update flag for provided field
    if (field === 'volts') {
      // highlightedCommon DTO
      this.titleBlockHighlighter.highlightedCommon.volts = true;
      this.titleBlockHighlighter.highlightedCommon.amps = true;
    } else if (field === 'watts') {
      // highlightedCommon DTO
      this.titleBlockHighlighter.highlightedCommon.watts = true;
      this.titleBlockHighlighter.highlightedCommon.amps = true;
    }
  }

  async getReliabilityTargetingWireInformation(i) {
    for (let count = 0; count < this.elementChoiceSelected.length; count++) {
      if (this.elementChoiceSelected[count] === true) {
        this.elementChoiceSelected[count] = false;
      }
    }
    this.elementChoiceSelected[i] = true;
    this.targetingResult = this.wireSelectorResult[i].targetingResultDTO;
    this.reliability = this.wireSelectorResult[i].reliabilityDTO;
    this.wireInformation = this.wireSelectorResult[i].wireInformationDTO;
    this.tapeInformation = this.wireSelectorResult[i].tapeInformation;
    this.elementObject.selectedElement = i;
    this.elementObject.searchParameterValue = this.wireSelectorResult[i].elementChoiceDTO.searchParameterValue;
    this.elementObject.searchParameterName = this.wireSelectorResult[i].elementChoiceDTO.searchParameterName;
    this.elementObject.maxOhms = null;
    this.elementObject.minOhms = null;
    this.elementObject.tgtOhms = null;
    this.manuallyAddTapePartNumber = false;
    if (this.tapeInformation) {
      if (!this.tapeInformation.partNumber) {
        this.elementObject.oldTapePartNumber = null;
        this.elementObject.tapePartNumber = null;
        this.isTapePartNumberAvailable = false;
      } else {
        this.elementObject.oldTapePartNumber = this.tapeInformation.partNumber;
        this.isTapePartNumberAvailable = true;
      }
    }
    this.allowSelectElement = true;
  }

  setOldTapePartNumber() {
    if (this.tapeInformation.partNumber) {
      this.elementObject.oldTapePartNumber = this.tapeInformation.partNumber;
    }
  }

  async saveElement() {
    if (!this.wireInformation.inEpiCore) {
      this.sweetAlertService.checkInEpicor();
    } else {
      if (this.tapeInformation && this.tapeInformation.partNumber && !this.tapeInformation.partNumber.match(Values.Tape_Part_Number_Regex)) {
        this.snakbarService.error(Messages.error.tape_invalid_partnumber);
      } else {
        this.syncStatus = null;
        this.loading = false;
        Object.assign(
          this.elementObject,
          this.wireSelector.basicInfoDTO,
          this.wireSelector.searchParameterDTO,
          this.wireSelector.elementInfoDTO,
          this.targetingResult,
          this.reliability,
          this.wireInformation,
          this.tapeInformation
        );
        this.elementObject.elementNumber = this.elementNumber;
        this.elementObject.tajima = this.wireSelector.tajima;
        this.elementObject.goldStandard = this.wireSelector.goldStandard;
        this.elementObject.inVietnam = this.wireSelector.inVietnam;
        this.elementObject.inCostaRica = this.wireSelector.inCostaRica;
        this.setTapeInfoPartNumber();
        this.setWireInfoPartnumber();
        this.elementObject.jacketId = this._JacketID;
        if (this.elementObject.elementType.includes(Values.ElementTypes.Tape)) {
          this.elementObject.syncToEpicore = await this.sweetAlertService.confirmSyncElementToEpicor();
          this.elementObject.syncStatus = this.elementObject.syncToEpicore
            ? Messages.Bom_Element.In_progress
            : null;
          // as we have Tape selection from user we need to set the Highlight title block picks and OHMS/ft to be set true
          this.titleBlockHighlighter.highlightedElement.pick1 = true;
          this.titleBlockHighlighter.highlightedElement.ohmPerfT1 = true;
        } else if (this.elementObject.elementType === Values.ElementTypes.WireType) {
          this.elementObject.tapePartNumber = null;
          this.elementObject.syncStatus = null;
        }
        // we will always have wire info so set the Highlight title block wirePart1_1 and wireOhmsfT1 to true
        this.titleBlockHighlighter.highlightedElement.wirePart1_1 = true;
        this.titleBlockHighlighter.highlightedElement.wireOhmsfT1 = true;
        // we will also need to set TGT OHMS, MIN OHMS and MAX OHMS on selection of Element
        this.titleBlockHighlighter.highlightedElement.tgtOhms1 = true;
        this.titleBlockHighlighter.highlightedElement.maxOhms1 = true;
        this.titleBlockHighlighter.highlightedElement.minOhms1 = true;
        // we will also need to set Total TGT OHMS, Total MIN OHMS and Total MAX OHMS on selection of Element
        this.titleBlockHighlighter.highlightedElement.totMaxOhms = true;
        this.titleBlockHighlighter.highlightedElement.totMinOhms = true;
        this.titleBlockHighlighter.highlightedElement.totTgtOhms = true;
        // setting up the product type in the element object
        this.elementObject.productType = this.wireSelector.basicInfoDTO.productType;
        this.showLoader = true;
        this.subscription.add(
          this.elementService.saveElement(this.elementObject).subscribe(
            (res: Element) => {
              if (res) {
                this.elementObject = res;
                if (this.tapeInformation) {
                  this.tapeInformation.partNumber = this.elementObject.tapePartNumber;
                }
                this.setSyncStatus();
                this.snakbarService.success(Messages.element_Selection.success_select_element);
                this.checkIfTapeOrDualWireTape();
                this.isTapePartNumberAvailable = true;
                this.wireSelector.basicInfoDTO.volts = this.elementObject.volts;
                this.wireSelector.basicInfoDTO.watts = this.wireSelector.basicInfoDTO.watts;
                this.titleBlockHighlighter.jacketIds = [this._JacketID];
                // after user selects element we need to find if any of the highlightedElement fields have been updated if so we need to call hightlight Element API to hightlight element in title block
                // call the updateTitleBlockHighlightDTO API
                this.subscription.add(
                  this.sharedService.highlightTitleBlockFields(this.titleBlockHighlighter).subscribe(
                    () => {
                      this.titleBlockHighlighter = new TitleBlockHighlighterDTO();
                      this.showLoader = false;
                    },
                    () => {
                      this.showLoader = false;
                    }
                  )
                );
              }
              this.showLoader = false;
            },
            error => {
              this.showLoader = false;
              this.loading = false;
              this.syncStatus = null;
              if (this.elementObject.oldTapePartNumber) {
                this.tapeInformation.partNumber = this.elementObject.oldTapePartNumber;
              } else {
                this.tapeInformation.partNumber = '';
              }
              this.elementObject.tapePartNumber = null;
            }
          )
        );
      }
    }
  }

  setTapeInfoPartNumber() {
    if (this.tapeInformation && this.tapeInformation.partNumber) {
      this.elementObject.tapePartNumber = this.tapeInformation.partNumber;
    }
  }

  setWireInfoPartnumber() {
    if (this.wireInformation && this.wireInformation.partNumber) {
      this.elementObject.wirePartNumber = this.wireInformation.partNumber;
    }
  }

  setSyncStatus() {
    if (this.elementObject.syncStatus === Messages.Bom_Element.In_progress) {
      this.syncStatus = Messages.Bom_Element.sync_In_progress;
      this.loading = true;
      this.isSyncSuccess = true;
    }
  }

  checkIfTapeOrDualWireTape() {
    if (
      (this.elementObject.elementType === Values.ElementTypes.Tape ||
        this.elementObject.elementType === Values.ElementTypes.DualWireTape) &&
      this.elementObject.tapePartNumber
    ) {
      this.elementObject.oldTapePartNumber = this.tapeInformation.partNumber;
      this.elementObject.tapePartNumber = null;
    }
  }

  checkElementLastSyncStatusByJacketId() {
    this.isSyncSuccess = true;
    this.syncStatus = null;
    this.loading = false;
    this.subscription.add(
      this.elementService.checkElementLastSyncStatusByJacketId(this._JacketID, this.elementNumber).subscribe((res: string) => {
        if (res) {
          this.syncRes = res;
          if (res === Messages.Bom_Element.In_progress) {
            this.syncStatus = Messages.Bom_Element.sync_In_progress;
            this.loading = true;
          } else {
            this.syncStatus = res;
            this.isSyncSuccess = res.includes(Messages.Bom_Element.sync_failed) ? false : true;
          }
        }
      })
    );
  }

  clearElement() {
    this.showLoader = true;
    this.subscription.add(this.elementService.deleteElementById(this._JacketID, this.elementNumber).subscribe(() => {
      this.getJacketElement(this._JacketID);
      this.showLoader = false;
      this.snakbarService.success(Messages.element_Selection.success_clear_element);
    }, error => {
      this.showLoader = false;
    }));
  }

  retrySync() {
    this.loading = true;
    this.subscription.add(
      this.elementService.retrySync(this.elementObject.id).subscribe((res) => {
        if (res) {
          this.snakbarService.success(res.status);
          this.loading = false;
          this.checkElementLastSyncStatusByJacketId();
        }
      })
    );
  }

  emptyFields() {
    this.wireSelector.elementInfoDTO.noOfTapes = 0;
    this.wireSelector.elementInfoDTO.tapeWidth = 0;
    this.wireSelector.elementInfoDTO.totalLength = 0;
    this.wireSelector.elementInfoDTO.wireType = undefined;
    this.tajimaWireElementTypeUpdated();
  }

  emptyResultDTOs() {
    this.wireSelector.basicInfoDTO = new BasicInfo();
    this.wireSelector.elementInfoDTO = new ElementInfo();
    this.targetingResult = new TargetingResult();
    this.reliability = new Reliability();
    this.tapeInformation = new TapeInformation();
    this.wireInformation = new WireInformation();
    this.elementObject = new Element();
  }

  openDMTLog() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_lg;
    matDataConfig.data = {elementId: this.elementObject.id, fromElement: true};
    this.matDialog
      .open(DMTLogComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        this.checkElementLastSyncStatusByJacketId();
      });
  }

  // handles when Tajima wire check box is checked or unchecked also when element type is updated.
  tajimaWireElementTypeUpdated() {
    this.titleBlockHighlighter.highlightedElement.elementType = true;
  }

  // handles when wire type field is updated.
  wireTypeUpdated() {
    this.titleBlockHighlighter.highlightedElement.construct1 = true;
  }

  // handles when number of tapes field is updated.
  noOfTapesUpdated() {
    this.titleBlockHighlighter.highlightedElement.number1 = true;
  }

  // handles when total element length field is updated.
  elementTotalLengthUpdated() {
    this.titleBlockHighlighter.highlightedElement.length1 = true;
  }

  // handles when tape width element field is updated.
  tapeWidthUpdated() {
    this.titleBlockHighlighter.highlightedElement.width1 = true;
    this.titleBlockHighlighter.highlightedElement.warp1 = true;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
