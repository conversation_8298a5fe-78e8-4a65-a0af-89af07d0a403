import { ApplicationInfoTemplateDTO, ClosureTemplateDTO, MaterialInfoTemplateDTO, PluggingInformationTemplateDTO, SensorsInformationTemplateDTO, ThermostatInformationTemplateDTO } from 'src/app/common-pages/masterdata-management/ccdc-master-data/manage-ccdc/manage-ccdc.model';

export class JacketGroup {
  constructor(
    public id?: number,
    public name?: string,
    public measurementUnit?: string,
    public tempUnit?: string,
    public quotationId?: number,
    public revisionId?: number
  ) {
  }
}

export class CcdcTemplateDTO {
  constructor(
    public id?: number,
    public name?: string,
    public measurementUnit?: string,
    public temperatureUnit?: string,
    public userId?: number,
    public description?: string,
    public notes?: string,
    public sensorNotes?: string,
    public thermostatNotes?: string,
    public materialNotes?: string,
    public applicationInfoTemplateDTO: ApplicationInfoTemplateDTO = new ApplicationInfoTemplateDTO(),
    public closureTemplateDTO: ClosureTemplateDTO = new ClosureTemplateDTO(),
    public materialInfoTemplateDTOS = new Array<MaterialInfoTemplateDTO>(),
    public pluggingInformationTemplateDTO: PluggingInformationTemplateDTO = new PluggingInformationTemplateDTO(),
    public sensorsInformationTemplateDTOS?: SensorsInformationTemplateDTO[],
    public thermostatInformationTemplateDTOS?: ThermostatInformationTemplateDTO,
  ) {

  }
}

export interface CopyQuotationData {
  quotationId: number;
}

export class Quotation {
  constructor(
    public id?: number,
    public quotationNumber?: number,
    public salesOrderNumber?: number | string,
    public quotationStatusId?: number,
    public quotationStatusName?: string,
    public activated?: boolean,
    public measurementUnit?: string,
    public tempUnit?: string,
    public projectName?: string,
    public entryDate?: string,
    public customerDTO?: CustomerDTO,
    public entryMethod?: string,
    public manufacturedIn?: string,
    public externalQuoteRequired: boolean = false,
    public customerClarificationRequired: boolean = false,
    public dateAppStarted?: string,
    public quoteCompletedDate?: string,
    public salesAssociateId?: number,
    public shipDate?: string,
    public projectTitle?: string,
    public designLocation?: string,
    public ofa1Date?: string,
    public approval1Date?: string,
    public ofa2Date?: string,
    public approval2Date?: string,
    public assignedDesignerId?: number,
    public dollarAmount?: number,
    public designComments?: string,
    public noOfCustomRevision?: number
  ) {
  }
}

export class CustomerDTO {
  constructor(
    public id?: number,
    public salesOrderNo?: string,
    public name?: string,
    public code?: string,
    public engCustAbrev?: string,
    public contact?: string,
    public phoneNumber?: string,
    public faxNumber?: string,
    public addressLine1?: string,
    public addressLine2?: string,
    public email?: string,
    public sqtLink?: string,
    public notes?: string
  ) {
  }
}

export class SalesOrderFormDTO {
  constructor(
    public quotationId?: number,
    public quotationNumber?: string,
    public salesOrderNumber?: string,
    public customerDTO?: CustomerDTO,
    public projectTitle?: string,
    public folderSubmittedDate?: string,
    public salesAssociateId?: number,
    public assignedAppEngineerId?: number,
    public designComments?: string,
    public sendCopyOfResponse?: boolean,
    public dollarAmount?: number,
    public shipDate?: string
  ) {
  }
}


export class MaterialInfo {
  constructor(
    public id?: number,
    public jacketGroupId?: number,
    public materialId?: number,
    public layerName?: string,
    public costPerSq?: number,
    public inventory?: string,
    public maxTemp?: string,
    public maxTempF?: string,
    public partNumber?: string,
    public otherFacing?: string,
    public otherFacingPartNumber?: string,
    public otherFacingCost?: string,
    public otherLiner?: string,
    public otherLinerPartNumber?: string,
    public otherLinerCost?: string,
    public otherInsulation?: string,
    public otherInsulationPartNumber?: string,
    public otherInsulationCost?: string,
    public materialInfoIndex?: number,
    public material?: string
  ) {
  }
}

export class MaterialInfoReq {
  constructor(public materialInfoDTOList?: MaterialInfo[], public notes?: string) {
  }
}

export class MaterialLayers {
  constructor(
    public costPerSq?: number,
    public materialId?: number,
    public imageUrl?: string,
    public inventory?: string,
    public layerName?: string,
    public material?: string,
    public maxTemp?: string,
    public maxTempF?: string,
    public partNumber?: string,
    public identifier?: string,
    public otherFacing?: string,
    public otherFacingPartNumber?: string,
    public otherFacingCost?: string,
    public otherLiner?: string,
    public otherLinerPartNumber?: string,
    public otherLinerCost?: string,
    public otherInsulation?: string,
    public otherInsulationPartNumber?: string,
    public otherInsulationCost?: string,
    public jacketGroupId?: number
  ) {
  }
}

export class ApplicationInfo {
  constructor(
    public id?: number,
    public notes?: string,
    public jacketType?: string,
    public voltage?: number,
    public phase?: string,
    public heatupFrom?: number,
    public heatupTo?: number,
    public heatupIn?: number,
    public operatingTemp?: number,
    public maxExposureTemp?: number,
    public minAmbientTemp?: number,
    public pipeThickness?: number,
    public contentMotion?: string,
    public contentMotionFlowingRate?: number,
    public jacketGroupId?: number,
    public contentId?: number,
    public materialId?: number,
    public contentName?: string,
    public materialName?: string,
    public materialHeat?: number,
    public materialDensity?: number,
    public contentHeat?: number,
    public contentDensity?: number,
    public controlType?: string,
    public productType?: string,
    public otherMaterialHeat?: number,
    public otherMaterialDensity?: number,
    public otherContentHeat?: number,
    public otherContentDensity?: number,
    public materialSpecificHeat?: number,
    public contentSpecificHeat?: number,
    public otherControlType?: string,
    public wattDensity?: number,
    public fitTypeEnum?: string,
    public minDutyCycle?: number,
    public maxDutyCycle?: number,
  ) {
  }
}

export class Material {
  constructor(
    public id?: number,
    public name?: string,
    public density?: number,
    public specificHeat?: number,
    public materialType?: string
  ) {
  }
}

export class Notes {
  constructor(public id?: number, public jacketGroupId?: number, public notes?: string) {
  }
}

export class Plug {
  constructor(
    public id?: number,
    public plugName?: string,
    public imageUrl?: string,
    public plugCost?: string,
    public maxAmps?: string,
    public maxVolts?: string,
    public partNumber?: string,
    public jumperPartNumber?: string
  ) {
  }
}

export class JumperPlug {
  constructor(
    public id?: number,
    public jumperLength?: number,
    public quantity?: number,
    public plugId?: number,
    public plugName?: string
  ) {
  }
}

export class LeadPlug {
  constructor(public id?: number, public leadLength?: number, public plugId?: number, public plugName?: string, public partNumber?: string,) {
  }
}

export class PluggingInformation {
  constructor(
    public id?: number,
    public jacketGroupId?: number,
    public notes?: string,
    public greenLightName?: string,
    public greenLightId?: number,
    public otherGreenLight?: string,
    public otherGreenLightPartNumber?: string,
    public otherGreenLightCost?: number,
    public redLightName?: string,
    public redLightId?: number,
    public otherRedLight?: string,
    public otherRedLightPartNumber?: string,
    public otherRedLightCost?: number,
    public sleevingTypeId?: number,
    public sleevingTypeName?: string,
    public otherSleevingType?: string,
    public otherSleevingTypePartNumber?: string,
    public otherSleevingTypeCost?: number,
    public strainReliefId?: number,
    public strainReliefName?: string,
    public otherStrainRelief?: string,
    public otherStrainReliefPartNumber?: string,
    public otherStrainReliefCost?: number,
    public otherPlug?: string,
    public otherPlugPartNumber?: string,
    public otherPlugCost?: number,
    public otherConnector?: string,
    public otherConnectorPartNumber?: string,
    public otherConnectorCost?: number,
    public jumperPlugDTO?: JumperPlug,
    public leadPlugDTO?: LeadPlug,
    public leadTypeDTO?: LeadType
  ) {
  }
}

export class ClosureInformation {
  constructor(
    public id?: number,
    public extendedFlap?: boolean,
    public estSurfaceTemp?: number,
    public closureMaterialId?: number,
    public jacketGroupId?: number,
    public notes?: string,
    public cat5Tunnel?: boolean,
    public otherClosure?: string,
    public otherClosurePartNumber?: string,
    public otherClosureCost?: number,
    public closureMaterialImageUrl?: string,
    public closureMaterialCostPerSq?: number,
    public closureMaterialMaxTemp?: string,
    public closureMaterialName?: string
  ) {
  }
}

export class ClosureMaterial {
  constructor(
    public id?: number,
    public name?: string,
    public maxTemp?: string,
    public maxTempF?: string,
    public costPerSq?: number,
    public imageUrl?: string
  ) {
  }
}

export class ThermostatType {
  constructor(public id?: string) {
  }
}

export class OtherThermostatType {
  constructor(public id?: string) {
  }
}

export class ThermostatList {
  constructor(
    public amps?: string,
    public tempUnit?: string,
    public closeTemp?: string,
    public closeTempF?: string,
    public cost?: number,
    public id?: number,
    public jacketGroupId?: number,
    public thermostatListId?: number,
    public manualReset?: boolean,
    public openOnRise?: boolean,
    public openTemp?: string,
    public openTempF?: string,
    public partNumber?: string,
    public tolerance?: number,
    public toleranceF?: number,
    public type?: string,
    public volts?: string,
    public thermostatType: ThermostatType = new ThermostatType(),
    public otherThermostatType: OtherThermostatType = new OtherThermostatType(),
    public otherThermostatPartNumber?: string,
    public otherThermostatCost?: string,
    public installationMethodDTO?: ThermostatInstallationMethod,
    public vietnamStock?: number,
    public usaStock?: number
  ) {
  }
}

export class ThermostatInfo {
  constructor(public thermostatInformationDTOList?: ThermostatList[], public notes?: string) {
  }
}

export class SensorType {
  constructor(public id?: string) {
  }
}

export class SensorConnector {
  constructor(public id?: string) {
  }
}

export class SensorInformation {
  constructor(
    public id?: number,
    public jacketGroupId?: number,
    public sensorConnector: SensorConnector = new SensorConnector(),
    public sensorLeadLength?: string,
    public sensorLocation?: string,
    public sensorType: SensorType = new SensorType(),
    public sensorTempType?: string,
    public otherSensorType?: string,
    public otherSensorLocation?: string,
    public otherSensorConnector?: string
  ) {
    this.id = null;
  }
}

export class SensorInformationObject {
  constructor(public notes?: string, public sensorsInformationDTOList?: SensorInformation[]) {
  }
}

export class ViewCCDCJacketGroup {
  constructor(
    public id?: number,
    public name?: string,
    public measurementUnit?: string,
    public tempUnit?: string,
    public quotationId?: number,
    public revisionId?: number,
    public ccdcWorkflow?: CcdcWorkflow,
    public appInfo?: ApplicationInfo,
    public materialDatas?: MaterialInfoReq,
    public closureInfo?: ClosureInformation,
    public closureMaterial?: ClosureMaterial,
    public sensorInfoObjects?: SensorInformationObject,
    public thermostatInfos?: ThermostatInfo,
    public pluggingInformation?: PluggingInformation,
    public notes?: Notes
  ) {
    this.ccdcWorkflow = new CcdcWorkflow();
    this.appInfo = new ApplicationInfo();
    this.pluggingInformation = new PluggingInformation();
    this.closureInfo = new ClosureInformation();
    this.closureMaterial = new ClosureMaterial();
    this.sensorInfoObjects = new SensorInformationObject();
    this.thermostatInfos = new ThermostatInfo();
    this.notes = new Notes();
  }
}

export class CcdcWorkflow {
  constructor(
    public id?: number,
    public approvalLevel?: string,
    public entryDate?: string,
    public shipDate?: string,
    public notes?: string,
    public markingList: Markings[] = [],
    public approvalFormatList: ApprovalFormat[] = [],
    public jacketGroupId?: number,
    public measurementUnit?: string,
    public tempUnit?: string,
    public projectName?: string,
    public productType?: string,
    public accountMgrId?: number,
    public assignedAppEngineerId?: number,
    public externalQuoteRequired: boolean = false,
    public customerClarificationRequired: boolean = false,
    public dateAppStarted?: string,
    public currentStatusComment?: string,
    public dateAppCompleted?: string,
    public quoteId?: number,
    public sqtFolderLink?: string
  ) {
  }
}

export class ApprovalFormat {
  constructor(public id?: number, public approvalLevel?: string, public selected?: boolean, public name?: string) {
  }
}

export class Markings {
  constructor(public id?: number, public markings?: string, public selected?: boolean, public name?: string) {
  }
}

export class Revision {
  constructor(
    public id?: number,
    public createdByUser?: string,
    public revisionName?: string,
    public createdOn?: string,
    public quotationId?: number,
    public activeRevision?: boolean
  ) {
  }
}

export class Document {
  constructor(
    public activated?: boolean,
    public filePath?: string,
    public id?: number,
    public name?: string,
    public quotationId?: number,
    public fileData?: File
  ) {
    this.activated = true;
  }
}

export class Units {
  constructor(public measurementUnit?: string, public tempUnit?: string, public revisionId?: number) {
  }
}

export class SleevingType {
  constructor(public id?: number, public name?: string, public partNumber?: string, public cost?: number) {
  }
}

export class StrainRelief {
  constructor(public id?: number, public name?: string, public partNumber?: string, public cost?: number) {
  }
}

export class RedLight {
  constructor(public id?: number, public name?: string, public partNumber?: string, public cost?: number) {
  }
}

export class GreenLight {
  constructor(public id?: number, public name?: string, public partNumber?: string, public cost?: number) {
  }
}

export class LeadType {
  constructor(
    public id?: number,
    public partNumber?: string,
    public leadName?: string,
    public maxTemp?: number,
    public maxVolts?: number,
    public costPerFoot?: number
  ) {
  }
}

export class ThermostatTypesMaster {
  constructor(public id?: string, public previousId?: string) {
  }
}

export class ThermostatInstallationMethod {
  constructor(public id?: number, public methodName?: string) {
  }
}

export class AccountManager {
  public id?: number;
  public name?: string;
}

export class QuoteDefaults {
  public entryDate?: string;
  public accountMgrId?: number;
  public assignedAppEngineerId?: number;
  public dateAppStarted?: string;
  public dateAppCompleted?: string;
  public externalQuoteRequired?: boolean;
  public customerClarificationRequired?: boolean;
  public currentStatusComment?: string;
  public projectTitle?: string;
  public sqtFolderLink?: string;
}

export class CcdcTemplateExportDTO {
  constructor(
    public ccdcConfigurationsHolderDTO?: CcdcConfigurationsHolderDTO,
    public description?: string,
    public measurementUnit?: string,
    public temperatureUnit?: string,
    public notes?: string,
    public sensorNotes?: string,
    public thermostatNotes?: string,
    public materialNotes?: string,
    public name?: string,
    public userId?: number
  ) {
  }
}

export class CcdcConfigurationsHolderDTO {
  constructor(
    public ccdcApplicationInfoDTO: ApplicationInfo = new ApplicationInfo(),
    public closureDTO: ClosureInformation = new ClosureInformation(),
    public materialInfoDTOS: MaterialInfoReq = new MaterialInfoReq(),
    public pluggingInformationDTO: PluggingInformation = new PluggingInformation(),
    public sensorsInformationRequestDto: SensorInformationObject = new SensorInformationObject(),
    public thermostatInformationRequestDto: ThermostatInfo = new ThermostatInfo()
  ) {
  }
}
