<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Filter BHX Material
  <hr />
</h2>
<form class="forms_form" #BHXMaterialForm="ngForm">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select   placeholder="Grouping"
          [(ngModel)]="bhxMaterialMaster.grouping.value"
          name="grouping"
          #groupingInput="ngModel"
          >
            <mat-option *ngFor="let group of groupings" [value]="group?.id">
              {{ group?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Part Number" [(ngModel)]="bhxMaterialMaster.partNumber.value" name="partnumber" #partnumberInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="App Type" [(ngModel)]="selectedJacketTypes" name="jacketType" #jacketTypeInput="ngModel" multiple>
            <mat-option *ngFor="let jacketType of jacketTypes" [value]="jacketType?.id">
              {{ jacketType?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-form-field>
          <textarea matInput placeholder="Description" rows="3"  #descriptionInput="ngModel"
          [(ngModel)]="bhxMaterialMaster.description.value" name="description">
          </textarea>
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Cloth Operation" [(ngModel)]="bhxMaterialMaster.clothRelOp.value"
          name="clothRelOp"
          #clothRelOpInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Silicone Operation" [(ngModel)]="bhxMaterialMaster.siliconRelOp.value"
          name="siliconRelOp"
          siliconRelOpInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Inseparable Operation" [(ngModel)]="bhxMaterialMaster.inseparableRelOp.value"
          name="inseparableRelOp"
          #inseparableRelOpInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="UOM" [(ngModel)]="bhxMaterialMaster.uom.value" name="uom" #uomInput="ngModel">
            <mat-option *ngFor="let uom of uomMaster" [value]="uom">
              {{ uom }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Quantity" [disabled]="disQty" (change)="checkQuantityAndFormula()" [(ngModel)]="bhxMaterialMaster.quantity.value"
          name="qty"
          #qtyInput="ngModel"/>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Customer" [(ngModel)]="bhxMaterialMaster.customer.value" name="customer" #customerInput="ngModel" />
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="start" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox
          name="clothCE"
          color="warn"
          [indeterminate]="bhxMaterialMaster?.ce.value === null"
          [ngModel]="bhxMaterialMaster?.ce.value === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterialMaster?.ce.value, ClothCECheckBoxTitle)"
        >
          Cloth CE - {{ bhxMaterialMaster?.ce.value === true ? checkBoxYesLabel : bhxMaterialMaster?.ce.value === false ? checkBoxNoLabel : checkBoxNullLabel }}
        </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox
        name="clothUL"
        color="warn"
        [indeterminate]="bhxMaterialMaster?.ul.value === null"
        [ngModel]="bhxMaterialMaster?.ul.value === true"
        (ngModelChange)="setCheckBoxTriStateValues(bhxMaterialMaster?.ul.value, ClothULCheckBoxTitle)"
      >
        Cloth UL  - {{ bhxMaterialMaster?.ul.value === true ? checkBoxYesLabel : bhxMaterialMaster?.ul.value === false ? checkBoxNoLabel : checkBoxNullLabel }}
      </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox
          name="hazardous"
          color="warn"
          [indeterminate]="bhxMaterialMaster?.hazardous.value === null"
          [ngModel]="bhxMaterialMaster?.hazardous.value === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterialMaster?.hazardous.value, HazardousCheckBoxTitle)"
        >
          Hazardous - {{ bhxMaterialMaster?.hazardous.value === true ? checkBoxYesLabel : bhxMaterialMaster?.hazardous.value === false ? checkBoxNoLabel : checkBoxNullLabel }}

        </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox
        name="manualreset"
        color="warn"
        [indeterminate]="bhxMaterialMaster?.manualResetThermostat.value === null"
        [ngModel]="bhxMaterialMaster?.manualResetThermostat.value === true"
        (ngModelChange)="setCheckBoxTriStateValues(bhxMaterialMaster?.manualResetThermostat.value, ManualResetTSCheckBox)"
      >
        Manual Reset T/S - {{ bhxMaterialMaster?.manualResetThermostat.value === true ? checkBoxYesLabel : bhxMaterialMaster?.manualResetThermostat.value === false ? checkBoxNoLabel : checkBoxNullLabel }}
      </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox
        name="privateLabel"
        color="warn"
        [indeterminate]="bhxMaterialMaster?.privateLabel.value === null"
        [ngModel]="bhxMaterialMaster?.privateLabel.value === true"
        (ngModelChange)="setCheckBoxTriStateValues(bhxMaterialMaster?.privateLabel.value, PrivateLabelCheckBox)"
      >
       Private Label - {{ bhxMaterialMaster?.privateLabel.value === true ? checkBoxYesLabel : bhxMaterialMaster?.privateLabel.value === false ? checkBoxNoLabel : checkBoxNullLabel }}
      </mat-checkbox>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex.gt-lg="21" fxFlex.gt-md="21" fxFlex.gt-sm="21" fxFlex.gt-xs="100">
        <mat-slide-toggle name="blocked" color="warn" (ngModelChange)="setDualStateCheckBoxesValue(bhxMaterialMaster?.blocked.value, BlockedDualStateValue)" [(ngModel)]="bhxMaterialMaster?.blocked.value"> Blocked </mat-slide-toggle>
        <mat-slide-toggle name="deleted" color="warn" (ngModelChange)="setDualStateCheckBoxesValue(bhxMaterialMaster?.deleted.value, DeletedDualStateValue)" [(ngModel)]="bhxMaterialMaster?.deleted.value"> Deleted </mat-slide-toggle>
      </div>

    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Product Type" [(ngModel)]="selectedProductTypes" name="productType" #productTypeInput="ngModel" multiple>
            <mat-option *ngFor="let prodType of productTypes" [value]="prodType?.id">
              {{ prodType?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Minmum Voltage" [(ngModel)]="bhxMaterialMaster.minVolts.value" name="minVolt" #minVoltInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Maximum Voltage" [(ngModel)]="bhxMaterialMaster.maxVolts.value" name="maxVolt" #maxVoltInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Minimum Amperage" [(ngModel)]="bhxMaterialMaster?.minAmps.value" name="minAmp" #minAmpInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Maximum Amperage" [(ngModel)]="bhxMaterialMaster?.maxAmps.value" name="maxAmp" #maxAmpInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Min Lead Length" [(ngModel)]="bhxMaterialMaster.minLead.value" name="minLead" #minLeadInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Max Lead Length" [(ngModel)]="bhxMaterialMaster.maxLead.value" name="maxLead" #maxLeadInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Minimum Temperature" [(ngModel)]="bhxMaterialMaster.minTemp.value" name="minTemp" #minTempInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Maximum Temperature" [(ngModel)]="bhxMaterialMaster.maxTemp.value" name="maxTemp" #maxTempInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Phase" name="phase" [(ngModel)]="selectedPhase" #phaseSelect="ngModel" multiple>
            <mat-option *ngFor="let phase of phaseTypes" [value]="phase?.id">
              {{ phase?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Install Method" [(ngModel)]="selectedInstallationMethods"
          name="installMethod"
          #installMethodInput="ngModel"
          multiple>
          <mat-option *ngFor="let installMethod of installationMethods" [value]="installMethod?.methodName">
            {{ installMethod?.methodName }}
          </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Strain Relief" [(ngModel)]="selectedStrainReliefs"
          name="strainRelief"
          #strainReliefInput="ngModel" multiple>
          <mat-option *ngFor="let strainRelief of strainReliefs" [value]="strainRelief?.name">
            {{ strainRelief?.name }}
          </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Controller" [(ngModel)]="selectedController" name="controller" #controllerInput="ngModel" multiple>
            <mat-option *ngFor="let controller of accessoryControllers" [value]="controller?.name">
              {{ controller?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="76" fxFlex.gt-md="72" fxFlex.gt-sm="60" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Click Add Formula" [(ngModel)]="strFormula" name="formula" #formulaInput="ngModel"/>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="27" fxFlex.gt-sm="40" fxFlex.gt-xs="100" class="sfl-add-formula-btn">
        <button type="button" class="formula-btn-1" mat-stroked-button color="warn" (click)="addFormula()" [disabled]="disFormulaBtn">
          Add Formula
        </button>
        <button type="button" mat-stroked-button color="warn" (click)="clearFormula()" [disabled]="disFormulaBtn">Clear Formula</button>
      </div>
    </div>

    <!-- Switching view based on the grouping selection -->
    <!-- grouping 1 is for Label -->
    <mat-card sfl-bhx-material-card label-grp>
      <mat-card-subtitle>Grouping Label</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutGap="30px">
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Max Diameter" [(ngModel)]="bhxMaterialMaster.maxDiameter.value"
            name="maxDiameter"
            #maxDiameterInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Min Diameter" [(ngModel)]="bhxMaterialMaster.minDiameter.value"
            name="minDiameter"
            #minDiameterInput="ngModel" />
          </mat-form-field>
        </div>
      </div>
    </mat-card>
    <!-- grouping 2 is for Element Sensor -->
    <mat-card sfl-bhx-material-card element-sensor-grp>
      <mat-card-subtitle>Grouping Element Sensor</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Element Type" [(ngModel)]="elementType" name="elementType" #elementTypeInput="ngModel" multiple>
              <mat-option *ngFor="let elementType of elementTypes" [value]="elementType?.id">
                {{ elementType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Wire Type" [(ngModel)]="wireType" name="wireType" #wireTypeInput="ngModel" multiple>
              <mat-option *ngFor="let wireType of wireTypes" [value]="wireType?.id">
                {{ wireType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Connector" [(ngModel)]="sensorCon" name="sensConn" #sensConnInput="ngModel" multiple>
              <mat-option *ngFor="let sensConn of sensorsConnectors" [value]="sensConn?.id">
                {{ sensConn?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Type"  [(ngModel)]="sensorType" name="sensorType" #sensorTypeInput="ngModel" multiple>
              <mat-option *ngFor="let sensorType of sensorTypes" [value]="sensorType?.id">
                {{ sensorType?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sleeving" [(ngModel)]="sleeving" name="sleeving" #sleevingInput="ngModel" multiple>
              <mat-option *ngFor="let sleeving of sleevingTypes" [value]="sleeving?.name">
                {{ sleeving?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div fxLayout="row wrap" fxLayoutGap="30px">
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Max Length ({{ measureUnit ? measureUnit : '' }})"
            [(ngModel)]="bhxMaterialMaster?.maxLength.value"
            name="maxLength"
            #maxLengthInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Min Length"
            [(ngModel)]="bhxMaterialMaster?.minLength.value"
            name="minLength"
            #minLengthInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number"  placeholder="Min Jumper"
            [(ngModel)]="bhxMaterialMaster?.minJumpers.value"
            name="minJumpers"
            #minJumpersInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-checkbox name="thermostat" color="warn"  [indeterminate]="bhxMaterialMaster?.thermostat.value === null"
          [ngModel]="bhxMaterialMaster?.thermostat.value === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterialMaster?.thermostat.value, ThermostatCheckBoxTitle)">
            Thermostat {{
              bhxMaterialMaster?.thermostat.value === true ? checkBoxYesLabel : bhxMaterialMaster?.thermostat.value === false ? checkBoxNoLabel : checkBoxNullLabel
            }}
          </mat-checkbox>
        </div>
      </div>
    </mat-card>
    <!-- grouping 3 is for Facing Liner Closure -->
    <mat-card class="sfl-bhx-material-card facing-liner-grp">
      <mat-card-subtitle>Grouping Facing Liner Closure</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Layered (Material)" [(ngModel)]="selectedLayeredMaterial" name="layered" multiple>
              <mat-option *ngFor="let material of materialMaster" [value]="material?.material">
                {{ material?.material }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Connector" [(ngModel)]= "sensConn" #sensorConn = "ngModel" name="sensConn" multiple>
              <mat-option *ngFor="let sensConn of sensorsConnectors" [value]="sensConn?.id">
                {{ sensConn?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Max Diameter"
            [(ngModel)]="bhxMaterialMaster?.maxDiameter.value"
            name="maxDiameter"
            #maxDiameterInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Min Diameter"
            [(ngModel)]="bhxMaterialMaster?.minDiameter.value"
            name="minDiameter"
            #minDiameterInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Closure Material" [(ngModel)]="selectedClosuredMaterial"
            name="closure"
            #closureInput="ngModel"
            multiple>
            <mat-option *ngFor="let closure of closureMaterial" [value]="closure?.name">
              {{ closure?.name }}
            </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </mat-card>
    <!-- grouping 4 is for Wire Plugging -->
    <mat-card class="sfl-bhx-material-card wire-plugging-grp">
      <mat-card-subtitle>Grouping Wire Plugging</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Element Type" [(ngModel)]="elementType" name="elementType" #elementTypeInput="ngModel" multiple>
              <mat-option *ngFor="let elementType of elementTypes" [value]="elementType?.id">
                {{ elementType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Wire Type" [(ngModel)]="wireType" name="wireType" #wireTypeInput="ngModel" multiple>
              <mat-option *ngFor="let wireType of wireTypes" [value]="wireType?.id">
                {{ wireType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Plug" [(ngModel)]="selectedPlugs"
            name="plug"
            #plugInput="ngModel"
            [disabled]="noPlug"
            (ngModelChange)="changePlugData($event)" multiple>
            <mat-option *ngFor="let plug of plugs" [value]="plug?.plugName">
              {{ plug?.plugName }} {{ plug?.jacketType ? '- ' + plug?.jacketType : '' }}
            </mat-option>
              <mat-option value="none">None</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select [(ngModel)]="selectedConnectors"
            placeholder="Connector"
            name="connector"
            #connectorInput="ngModel" multiple>
            <mat-option *ngFor="let plug of plugs" [value]="plug?.plugName">
              {{ plug?.plugName }} {{ plug?.jacketType ? '- ' + plug?.jacketType : '' }}
            </mat-option>
              <mat-option value="none">None</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Connector" [(ngModel)]="sensConn" name="sensConn" #sensConnInput="ngModel" multiple>
              <mat-option *ngFor="let sensConn of sensorsConnectors" [value]="sensConn?.id">
                {{ sensConn?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sleeving" [(ngModel)]="selectedSleeving" name="sleeving" #sleevingInput="ngModel" multiple>
              <mat-option *ngFor="let sleeving of sleevingTypes" [value]="sleeving?.name">
                {{ sleeving?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Lead Type" name="leadType" [(ngModel)]="selectedLeadTypes" multiple>
              <mat-option *ngFor="let leadType of leadTypes" [value]="leadType?.leadName">{{ leadType?.leadName }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Green Light"  [(ngModel)]="selectedGreenLights" name="greenLight" #greenLightInput="ngModel" multiple>
              <mat-option *ngFor="let greenLight of greenLights" [value]="greenLight?.name">
                {{ greenLight?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
      </div>
    </mat-card>
    <!-- grouping 5 is for Operation -->
    <mat-card class="sfl-bhx-material-card operation-grp">
      <mat-card-subtitle>Grouping Operation</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput placeholder="Operation" [(ngModel)]="bhxMaterialMaster?.operationName.value"
            name="operationName"
            #operationNameInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput placeholder="Sequence" [(ngModel)]="bhxMaterialMaster?.sequence.value" name="sequence" #sequenceInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Operation Number"
            [(ngModel)]="bhxMaterialMaster?.operationName.value"
            name="opNumber"
            #opNumberInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Production Hours"
            [(ngModel)]="bhxMaterialMaster?.prodHrs.value"
            name="prodHrs"
            #prodHrsInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Setup Hours"
            [(ngModel)]="bhxMaterialMaster?.setupHrs.value"
            name="setupHrs"
            #setupHrsInput="ngModel" />
          </mat-form-field>
        </div>
      </div>
    </mat-card>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" (click)="onSubmit()" color="warn">Filter</button>

    </div>
  </mat-dialog-actions>
</form>
