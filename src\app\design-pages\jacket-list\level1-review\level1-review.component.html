<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Level 1 Review
  <hr />
</h2>

<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <input
        matInput
        [matDatepicker]="date"
        placeholder="Date"
        name="levelOneReviewDate"
        [(ngModel)]="levelOneReview.levelOneReviewDate"
        #levelOneReviewDateDate="ngModel"
        autocomplete="off"
      />
      <mat-datepicker-toggle matSuffix [for]="date"></mat-datepicker-toggle>
      <mat-datepicker #date></mat-datepicker>
    </mat-form-field>
  </div>
  <div fxLayout="column wrap" fxLayoutAlign="space-between">
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="geometryPattern" [(ngModel)]="levelOneReview.geometryPattern" #geometryPatternCheckBox="ngModel"
        >Geometry / Pattern</mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox
        color="warn"
        name="layeredConstruction"
        [(ngModel)]="levelOneReview.layeredConstruction"
        #layeredConstructionCheckBox="ngModel"
        >Layered Construction</mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox
        color="warn"
        name="wattageCalculation"
        [(ngModel)]="levelOneReview.wattageCalculation"
        #wattageCalculationCheckBox="ngModel"
        >Wattage Calculation</mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="wirringDiagram" [(ngModel)]="levelOneReview.wirringDiagram" #wirringDiagramCheckBox="ngModel"
        >Wirring Diagram</mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="stepJacketModels" [(ngModel)]="levelOneReview.stepJacketModels" #stepJacketModelsCheckBox="ngModel"
        >STEP / Jacket Models</mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox
        color="warn"
        name="checkedEngineering"
        [(ngModel)]="levelOneReview.checkedEngineering"
        #checkedEngineeringCheckBox="ngModel"
        >Checked Eng. Knowlede Database for Customer Specific Info</mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="verifiedUL" [(ngModel)]="levelOneReview.verifiedUL" #verifiedULCheckBox="ngModel"
        >Verified UL Compliance E208441 (Comment on Exceptions)</mat-checkbox
      >
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100">
      <input matInput placeholder="Comments" name="comments" [(ngModel)]="levelOneReview.comments" #commentsCheckBox="ngModel" />
    </mat-form-field>
  </div>
</mat-dialog-content>
<hr />
<mat-dialog-actions>
  <button mat-raised-button color="warn" type="submit" (click)="saveLevelReview()">Save</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button type="submit" (click)="undoLevelOne()">Undo Signature</button>
</mat-dialog-actions>
