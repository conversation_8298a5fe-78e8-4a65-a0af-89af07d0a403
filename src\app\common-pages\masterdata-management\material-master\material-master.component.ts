import { Compo<PERSON>, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { MaterialMaster, MaterialMasterPageable, MaterialFilter, GenericPageable } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageMaterialComponent } from './manage-material/manage-material.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-material-master',
  templateUrl: './material-master.component.html'
})
export class MaterialMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Material Master';
  material: MaterialMaster;
  materialPageable: GenericPageable<MaterialMaster>;
  materialMasterDataSource = new MatTableDataSource<MaterialMaster>();
  materialMasterColumns = DisplayColumns.Cols.MaterialMaster;

  dataSource = new MatTableDataSource<MaterialMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  materialFilter: MaterialFilter = new MaterialFilter();
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  filterFieldMaterial = Values.FilterFields.material;
  filterFieldPartNumber = Values.FilterFields.partNumber;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
    private readonly matDialog: MatDialog
  ) {}

  ngOnInit() {
    this.getMaterialMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new material
  addMaterial() {
    this.editMaterialMaster(new MaterialMaster());
  }

  // used to add filter to material listing
  addFilter() {
    this.filter = [
      { key: this.filterFieldPartNumber, value: !this.materialFilter.partNumber ? '' : this.materialFilter.partNumber },
      { key: this.filterFieldMaterial, value: !this.materialFilter.material ? '' : this.materialFilter.material }
    ];
    this.getMaterialMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to material listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber,
        value: fieldToClear === this.filterFieldPartNumber ? (this.materialFilter.partNumber = '') : this.materialFilter.partNumber
      },
      {
        key: this.filterFieldMaterial,
        value: fieldToClear === this.filterFieldMaterial ? (this.materialFilter.material = '') : this.materialFilter.material
      }
    ];
    this.getMaterialMasterData(this.initialPageIndex, this.pageSize);
  }

  getMaterialMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getMaterialLayer(this.filter, pageable).subscribe(
        (res: GenericPageable<MaterialMaster>) => {
          this.materialPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createMaterialTable(this.materialPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.materialMasterDataSource.data = [];
          if (error.applicationStatusCode === 1226) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  createMaterialTable(serviceRequestList: GenericPageable<MaterialMaster>) {
    this.materialMasterDataSource.data = serviceRequestList.content;
  }

  getMaterialMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getMaterialMasterData(this.pageIndex, this.pageSize);
  }

  getMaterialMasterSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getMaterialMasterData(this.pageIndex, this.pageSize);
  }

  editMaterialMaster(material: MaterialMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = material;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-material-master-model';
    const dialogRef = this.matDialog.open(ManageMaterialComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          material.materialId
            ? 'Material' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Material' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getMaterialMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  async deleteMaterialMaster(materialId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteMaterial(materialId).subscribe(
        () => {
          this.snakbarService.success('Material' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getMaterialMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
