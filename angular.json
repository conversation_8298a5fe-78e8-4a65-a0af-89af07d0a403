{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"briskheat": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "./node_modules/perfect-scrollbar/css/perfect-scrollbar.css", "src/assets/styles/app.scss"], "scripts": ["./node_modules/jquery/dist/jquery.min.js"]}, "configurations": {"prod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "stage": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}, "dev": {"optimization": true, "outputHashing": "all", "sourceMap": true, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "briskheat:build"}, "configurations": {"production": {"browserTarget": "briskheat:build:production"}, "stage": {"browserTarget": "briskheat:build:stage"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "briskheat:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "./node_modules/perfect-scrollbar/css/perfect-scrollbar.css", "src/assets/styles/app.scss"], "scripts": ["./node_modules/jquery/dist/jquery.min.js"], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "briskheat-e2e": {"root": "e2e/", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "briskheat:serve"}, "configurations": {"production": {"devServerTarget": "briskheat:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "briskheat"}