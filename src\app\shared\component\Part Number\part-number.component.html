<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="cust-popup">
  <div fxLayout="row">
    <h2 mat-dialog-title fxFlex>Generate Part Number</h2>
    <h2 *ngIf="isPartError" class="error-msg">
      Part number could not be generated.<a class="open-doc" (click)="ErrorList()">CLICK HERE</a>
    </h2>
    &nbsp;&nbsp;
    <mat-form-field fxFlex.gt-lg="15" fxFlex.gt-md="15">
      <mat-select placeholder="Made In" [(ngModel)]="madeIn" (selectionChange)="selectMadeIn($event.value)">
        <mat-option value="USA"> USA </mat-option>
        <mat-option value="VIETNAM"> VIETNAM </mat-option>
        <mat-option value="Costa Rica"> Costa Rica </mat-option>
      </mat-select>
    </mat-form-field>&nbsp;&nbsp;
    <mat-form-field fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <mat-select placeholder="Jacket Group" (selectionChange)="onJacketGroupChange($event.value)" [(ngModel)]="jgId">
        <mat-option *ngFor="let jacketgroup of jacketGroups" [value]="jacketgroup.id">
          {{ jacketgroup?.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>&nbsp;&nbsp;
  </div>
  <hr />
  <mat-dialog-content class="cust_table text-wrap">
    <div class="mt-10"></div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="80" fxFlex.gt-md="80" class="highlight-mat-table">
        <mat-table [dataSource]="dataSource">
          <ng-container matColumnDef="select">
            <mat-header-cell *matHeaderCellDef fxFlex="8">
              <mat-checkbox color="warn" (change)="$event ? masterToggle() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </mat-header-cell>
            <mat-cell *matCellDef="let row" fxFlex="10">
              <mat-checkbox color="warn" (click)="$event.stopPropagation()" [checked]="selection.isSelected(row)"
                (change)="onSelectedJacket($event, row)">
              </mat-checkbox>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="nonStandardPartNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="10">Non Standard PN#</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10">
              <span *ngIf="element?.nonStandardPartNumber" class="material-icons">
                done</span>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="jacketName">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              Jacket Name
            </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10">{{
              element.name
              }}</mat-cell>
          </ng-container>
          <ng-container matColumnDef="briskHeatPN">
            <mat-header-cell *matHeaderCellDef fxFlex="20">
              Part Number
            </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20">
              <div *ngIf="element?.nonStandardPartNumber; else elseBlock">
                <input class="quantity sfl-part-no" matInput placeholder="Part Number" [(ngModel)]="element.partNumber"
                  (focus)="copyJacketOldValues(element)" [ngStyle]="{
                    width: element?.fieldWidth,
                    'max-width': '170px'
                  }" (keyup)="setPartSeqTextFieldWidth(element)" (change)="updatePartNumber(element)" />
              </div>
              <ng-template #elseBlock>
                <input class="quantity sfl-part-no" matInput placeholder="Part Number"
                  [(ngModel)]="element.tempPartNumber" [ngStyle]="{
                    width: element?.fieldWidth,
                    'max-width': '170px'
                  }" (keyup)="setPartSeqTextFieldWidth(element)" (change)="updatePartNumber(element)"
                  [disabled]="element?.repeat" />
                <input class="quantity sfl-sequ-number" placeholder="Seq. Number" matInput
                  [(ngModel)]="element.tempSequenceNumber" (focus)="copyJacketOldValues(element)"
                  (change)="updatePartNumber(element)" [disabled]="element?.repeat" />
              </ng-template>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="path">
            <mat-header-cell *matHeaderCellDef fxFlex="30">
              Path
            </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="30">
              <span *ngIf="element?.partNumber" matTooltip="Click to open and edit/create part file path"
                (click)="openFilePathEditModalWithSingleElement(element)" class="open-doc material-icons" [ngStyle]="{
                  color: element?.selectedPartFilePath ? '#F44336' : 'black'
                }">
                folder_open
              </span>
              <a class="link open-doc text-warp" (click)="openFile(element)" matTooltip="{{ element.partFilePath }}">{{
                element.partFilePath }}</a>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="reference">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              Reference
            </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10">{{
              element?.reference
              }}</mat-cell>
          </ng-container>
          <ng-container matColumnDef="bhc">
            <mat-header-cell *matHeaderCellDef fxFlex="5">
              BHC
            </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="5">
              <mat-checkbox color="warn" [(ngModel)]="element.bhc" name="bhc" [disabled]="
                  element?.partNumber === null ||
                  element?.nonStandardPartNumber ||
                  element?.repeat
                " (change)="updateBHCPartNumber(element)"></mat-checkbox>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="gcode">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              G-Code
            </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10" matTooltip="{{ element?.gCode }}">{{ element?.gCode }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mcode">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              M-Code
            </mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10" matTooltip="{{ element?.mCode }}">{{ element?.mCode }}
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
        </mat-table>
        <div class="no-records" *ngIf="isNoDataFound | async">
          No data found
        </div>
      </div>
      <div fxFlex.gt-lg="19" fxFlex.gt-md="19">
        <form>
          <div fxLayout="column wrap">
            <div class="mt-10">
              <mat-checkbox color="warn" [(ngModel)]="jacketPartInfo.ccdcParts.ul" name="UL" disabled>UL</mat-checkbox>
            </div>
            <mat-form-field>
              <input matInput placeholder="Customer Code" name="customerCode" [(ngModel)]="jacketPartInfo.customerCode"
                readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Eng. Customer Abrev" name="customerAbbrev"
                [(ngModel)]="jacketPartInfo.customerAbbrev" readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Volts" name="volts" [(ngModel)]="jacketPartInfo.ccdcParts.voltage"
                readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Phase" name="phase" [(ngModel)]="jacketPartInfo.ccdcParts.phase" readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Fastener" name="fastener" [(ngModel)]="jacketPartInfo.ccdcParts.fastenerType"
                readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Liner" name="liner" [(ngModel)]="jacketPartInfo.ccdcParts.linerType"
                readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Facing" name="facing" [(ngModel)]="jacketPartInfo.ccdcParts.facingType"
                readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Insulation" name="insulation"
                [(ngModel)]="jacketPartInfo.ccdcParts.insulationType" readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Jacket Type" name="jacketType"
                [(ngModel)]="jacketPartInfo.ccdcParts.jacketType" readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="T/S(s)" name="thermostatType"
                [(ngModel)]="jacketPartInfo.ccdcParts.thermostatType" readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Sensor(s)" name="sensorType"
                [(ngModel)]="jacketPartInfo.ccdcParts.sensorType" readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Control Type" name="controlType"
                [(ngModel)]="jacketPartInfo.ccdcParts.controlType" readonly />
            </mat-form-field>
            <mat-form-field>
              <input matInput placeholder="Root P/N" name="rootPN" [(ngModel)]="jacketPartInfo.rootPN"
                [disabled]="dataSource.data.length === 0" (change)="updatePartNumbersFromRootPN(false)" />
            </mat-form-field>
          </div>
        </form>
      </div>
    </div>
  </mat-dialog-content>
  <hr />
  <div fxLayout="row" fxLayoutAlign="space-between">
    <p *ngIf="apiRespnseError" class="error-msg mt-10">
      {{ serviceErrorMessage }}
    </p>
    <p *ngIf="solidWorksNotInstalled" class="open-doc error-msg mt-10">
      Solidwork api service is not running on your computer
    </p>
  </div>
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="end">
      <div *ngIf="pathsFoundInDriveAndNotInBHX" class="sfl-item-position mt-5">
        <mat-icon matTooltip="Click to check file path generation response" (click)="openPathGenerationResponse()">info
        </mat-icon>
      </div>
      <div class="close-button" fxLayoutAlign="start">
        <button mat-raised-button type="submit" (click)="closeDialog()">
          Confirm
        </button>
      </div>
      <button mat-raised-button color="warn" type="submit" (click)="checkNonStarndardPN(true)"
        [disabled]="!selection.hasValue()">
        Generate Non Standard P/N
      </button>
      <button mat-raised-button color="warn" type="submit" (click)="resetPartNumber()"
        [disabled]="!selection.hasValue()">
        Reset Part Number
      </button>
      <button mat-raised-button color="warn" type="submit" (click)="resetRoot()" [disabled]="!selection.hasValue()">
        Reset Root
      </button>
      <button mat-raised-button color="warn" type="submit" (click)="generatePartNumber()"
        [disabled]="!selection.hasValue()">
        Regenerate Part Number
      </button>
      <button mat-raised-button type="submit" color="warn" [disabled]="notAllPartNumber || dataSource.data.length === 0"
        (click)="generateFile()">
        Generate File
      </button>
      <button mat-raised-button type="submit" color="warn" [disabled]="!selection.hasValue()"
        (click)="resetPath(selection.selected)">
        Reset File Paths
      </button>
    </div>
  </mat-dialog-actions>
</div>
