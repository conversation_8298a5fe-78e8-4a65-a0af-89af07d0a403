import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {Subscription} from 'rxjs';
import {BOMEditorComponent} from 'src/app/design-pages/design-plan/bom-editor/bom-editor.component';
import {BomEditorService} from 'src/app/design-pages/design-plan/bom-editor/bom-editor.service';
import {ElementService} from 'src/app/design-pages/design-plan/element/element.service';
import {Messages} from 'src/app/shared';
import {Variable} from '../../constants/Variable.constants';

@Component({
  selector: 'sfl-dmt-log',
  templateUrl: './dmt-log.component.html'
})
export class DMTLogComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  dmtResMap = new Map<string, string>();
  jacketId: number;
  elementId: number;
  type: string;
  fromElement: boolean;
  finalReviewScreen: boolean;
  title: string;
  fileType:string
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public dialogRef: MatDialogRef<BOMEditorComponent>,
    private bomeditorService: BomEditorService,
    private elementService: ElementService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketId = data.jacketId;
    this.elementId = data.elementId;
    this.type = data.epicorType;
    this.fromElement = data.fromElement;
    this.finalReviewScreen = data.finalReviewScreen;
    this.fileType= data.fileType;
  }

  ngOnInit() {
    if (this.fromElement) {
      this.getElementsDMTLogs();
      this.title = Messages.dmtLogTitle.element;
    } else if (this.finalReviewScreen) {
      this.getDMTLogsFinalReviewScreen();
      this.title = Messages.dmtLogTitle.finalReviewScreen;
    } else {
      this.getDMTLogs();
      this.title = Messages.dmtLogTitle.bom;
    }
  }

  getDMTLogs() {
    let fileType = this.fileType ? this.fileType : 'All';
    this.showLoader = true;
    this.subscription.add(
      this.bomeditorService.getDMTLogResponseV1(this.jacketId, this.type,fileType).subscribe(
        (res: Map<string, string>) => {
          this.dmtResMap = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  getElementsDMTLogs() {
    this.showLoader = true;
    this.subscription.add(
      this.elementService.getElementDMTLogResponseByJacketId(this.elementId).subscribe(
        (res: Map<string, string>) => {
          this.dmtResMap = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  getDMTLogsFinalReviewScreen() {
    this.subscription.add(
      this.elementService.getDMTLogsFinalReview(this.jacketId, this.type).subscribe((res: Map<string, string>) => {
        this.dmtResMap = res;
      })
    );
  }

  refreshDMTLog() {
    if (this.fromElement) {
      this.getElementsDMTLogs();
    } else if (this.finalReviewScreen) {
      this.getDMTLogsFinalReviewScreen();
    } else {
      this.getDMTLogs();
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
