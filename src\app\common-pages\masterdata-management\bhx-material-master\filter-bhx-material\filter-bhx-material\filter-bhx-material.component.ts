import {Component, Inject, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialog, MatDialogConfig, MatDialogRef, MatTableDataSource} from '@angular/material';
import {Subscription} from 'rxjs';
import {Messages, SharedService, SnakbarService} from 'src/app/shared';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';
import {Values} from 'src/app/shared/constants/values.constants';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {
  EquationFormulaGeneratorComponent
} from '../../../equation-formula-generator/equation-formula-generator.component';
import {
  AccessoryControllerMaster,
  BHXMaterialMaster,
  BhxMatetrialFilter,
  Blocked,
  CE,
  ClosureMaterialMaster,
  Deleted,
  Description,
  Hazardous,
  InstallationMethod,
  LeadTypesMaster,
  ManualReset,
  MaterialMaster,
  PlugMaster,
  PrivateLabel,
  SensorConnectorsAndTypesMaster,
  SleevingTypesAndStrainReliefsMaster,
  StrainRelief,
  Thermostat,
  UL
} from '../../../masterdata-management.model';
import {MasterdataManagementService} from '../../../masterdata-management.service';

@Component({
  selector: 'sfl-app-filter-bhx-material',
  templateUrl: './filter-bhx-material.component.html'
})
export class FilterBhxMaterialComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  bhxMaterial: BHXMaterialMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  measureUnit: string;
  groupings = Values.GroupingsBHXMaterial;
  uomMaster = Values.UOMBHXMaterial;
  elementTypes = Values.BHXMaterialElementType;
  elementType = [];
  wireTypes = Values.wireTypesBHXMaterialMaster;
  wireType = [];
  jacketTypes: object = Values.JacketTypeConst;
  jacketType = [];
  targetCountryTypes = Values.TargetCountryTypes;
  connectors = Values.Connectors;
  greenLights = [];
  operations = Values.Operations;
  sensConn = [];
  sensorType = [];
  types = [];
  selectedInstallationMethods = [];
  selectedStrainReliefs = [];
  selectedController = [];
  selectedLayeredMaterial = [];
  selectedClosuredMaterial = [];
  selectedSleeving = [];
  selectedGreenLights = [];
  selectedPlugs = [];
  selectedConnectors = [];
  selectedPhase = [];
  selectedJacketTypes = [];
  selectedProductTypes = [];
  accessoryControllers: AccessoryControllerMaster[];
  leadTypes: LeadTypesMaster[];
  sleeving: any;
  selectedLeadTypes = [];
  sensorTypes: SensorConnectorsAndTypesMaster;
  sleevingTypes: SleevingTypesAndStrainReliefsMaster;
  plugs: PlugMaster;
  closureMaterial: ClosureMaterialMaster;
  installationMethods: InstallationMethod[];
  strainReliefs: StrainRelief[];
  sensorsConnectors: SensorConnectorsAndTypesMaster;
  currentGrouping: number;
  strFormula = '';
  noConnector = false;
  noPlug = false;
  disFormulaBtn = false;
  disQty = false;
  materialMaster: MaterialMaster;
  description: Description;
  filter = [];
  sensorCon: string;
  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  bhxMaterialMaster = new BhxMatetrialFilter();
  productTypes: object = Values.ProductTypeConst;
  phaseTypes: object = Values.PhaseTypeConst;
  checkBoxYesLabel = Values.CheckboxLabels.YES;
  checkBoxNoLabel = Values.CheckboxLabels.NO;
  bhxMaterialPageable: BhxMatetrialFilter;
  bhxMaterialMasterDataSource = new MatTableDataSource<BHXMaterialMaster>();
  numberOfElements: number;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  BlockedDualStateValue = Values.BHXMaterial_DualState_CheckBoxes_Value.Blocked;
  DeletedDualStateValue = Values.BHXMaterial_DualState_CheckBoxes_Value.Deleted;
  checkBoxNullLabel = Values.CheckboxLabels.NULL;
  ClothCECheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.ClothCE;
  ClothULCheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.ClothUL;
  HazardousCheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.Hazardous;
  ThermostatCheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.Thermostat;
  ManualResetTSCheckBox = Values.BHXMaterial_CheckBox_Titles.ManualReset;
  PrivateLabelCheckBox = Values.BHXMaterial_CheckBox_Titles.PrivateLabel;
  filterArr = [];

  constructor(
    public readonly dialogRef: MatDialogRef<FilterBhxMaterialComponent>,
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sharedService: SharedService,
    private readonly snakbarService: SnakbarService,
    @Inject(MAT_DIALOG_DATA) data,
  ) {
  }
  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  ngOnInit() {
    this.getMaterialList();
    this.getSensorsConnectors();
    this.getSensorsTypes();
    this.getSleevingTypes();
    this.getPlugsAndConnectors();
    this.getClosureMaterials();
    this.getInstallationMethods();
    this.getStrainReliefs();
    this.getControlerTypeMasterData();
    this.getPluginMasterData();
    this.getLeadTypesMasterData();
    this.measureUnit = 'IN';
  }


  filterBhxMaterialTable(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getBHXMaterialsMaster(this.filter, pageable).subscribe((res) => {

      })
    );
  }

  getMaterialList() {
    this.subscription.add(
      this.masterDataService.getMaterialsList().subscribe((materialList: MaterialMaster) => {
        this.materialMaster = materialList;
      })
    );
  }


  getSensorsConnectors() {
    this.subscription.add(
      this.masterDataService.getSensorConnectorsList().subscribe((sensorsConnectors: SensorConnectorsAndTypesMaster) => {
        this.sensorsConnectors = sensorsConnectors;
        this.sensorCon = this.sensorsConnectors.value;
      })
    );
  }

  getSensorsTypes() {
    this.subscription.add(
      this.masterDataService.getSensorTypesList().subscribe((sensorTypes: SensorConnectorsAndTypesMaster) => {
        this.sensorTypes = sensorTypes;
      })
    );
  }

  getSleevingTypes() {
    this.subscription.add(
      this.masterDataService.getSleevingTypesList().subscribe((sleevingTypes: SleevingTypesAndStrainReliefsMaster) => {
        this.sleevingTypes = sleevingTypes;
      })
    );
  }

  getPlugsAndConnectors() {
    this.subscription.add(
      this.masterDataService.getPlugList().subscribe((plugs: PlugMaster) => {
        this.plugs = plugs;
      },
      (error) => {
        if (error.applicationStatusCode === 1230) {
          this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  getClosureMaterials() {
    this.subscription.add(
      this.masterDataService.getClosureMaterial().subscribe((closureMaterial: ClosureMaterialMaster) => {
        this.closureMaterial = closureMaterial;
      },
      error => {
        if (error.applicationStatusCode === 1215) {
          this.snakbarService.error(error.message);
        }
      }
      )
    );
  }

  getInstallationMethods() {
    this.subscription.add(
      this.masterDataService.getInstallationMethods().subscribe((installationMethods: InstallationMethod[]) => {
        this.installationMethods = installationMethods;
      })
    );
  }

  getStrainReliefs() {
    this.subscription.add(
      this.masterDataService.getStrainReliefs().subscribe((strainReliefs: StrainRelief[]) => {
        this.strainReliefs = strainReliefs;
      },
      (error) => {
        if (error.applicationStatusCode === 1234) {
          this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  changePlugData(plugName: String[]) {
    if (plugName.length && !plugName.includes('none')) {
      this.noPlug = false;
      this.noConnector = true;
    } else {
      this.selectedPlugs = [];
      this.noPlug = false;
      this.noConnector = false;
    }
  }

  getControlerTypeMasterData() {
    this.subscription.add(
      this.masterDataService.getAccessoryControllersList().subscribe((accessoryControllers: AccessoryControllerMaster[]) => {
        this.accessoryControllers = accessoryControllers;
      })
    );
  }
  // gets the available plugging light master data [we need only green lights for BHX material]
  getPluginMasterData() {
    this.subscription.add(
      this.masterDataService.getPluginMasterData().subscribe((plugLights: any) => {
        this.greenLights = plugLights.greenLights;
      })
    );
  }

  // gets the Lead type master data
  getLeadTypesMasterData() {
    this.subscription.add(
      this.masterDataService.getLeadTypeMaster().subscribe((leadTypes: LeadTypesMaster[]) => {
        this.leadTypes = leadTypes;
      })
    );
  }

  setCheckBoxTriStateValues(fieldValue: boolean, field: string) {
    switch (fieldValue) {
      case true: {
        this.checkFieldWhichNeedsToBeUpdated(field, false);
        break;
      }
      case false: {
        this.checkFieldWhichNeedsToBeUpdated(field, null);
        break;
      }
      case null: {
        this.checkFieldWhichNeedsToBeUpdated(field, true);
        break;
      }
    }
  }

  setDualStateCheckBoxesValue(fieldValue: boolean, field: string) {
    switch (fieldValue) {
      case true: {
        this.checkDualStateCheckBoxFields(field, false);
        break;
      }
      case false: {
        this.checkDualStateCheckBoxFields(field, true);
        break;
      }
    }
  }

  checkFilterExist(filterArr, key: string) {
    const propertyIndex = filterArr.findIndex(val => val.key === key);
    if (propertyIndex > -1) {
      this.filterArr.splice(propertyIndex, 1);
    }
  }

  checkDualStateCheckBoxFields(field: string, valueToUpdate: boolean) {
    switch (field) {
      case this.BlockedDualStateValue:
        this.checkFilterExist(this.filterArr, 'blocked');
        this.bhxMaterialMaster.blocked.value = valueToUpdate;
        const blocked = new Blocked();
        blocked.value = valueToUpdate;
        this.filterArr.push(blocked);
        break;
      case this.DeletedDualStateValue:
        this.checkFilterExist(this.filterArr, 'deleted');
        this.bhxMaterialMaster.deleted.value = valueToUpdate;
        const deleted = new Deleted();
        deleted.value = valueToUpdate;
        this.filterArr.push(deleted);
        break;
    }
  }

  // Takes the field and it's value to be updated with, switching between Cloth Ce, Cloth Ul, Hazardous and Thermostat fields and sets the Null, False or True supplied as `valueToUpdate`
  checkFieldWhichNeedsToBeUpdated(field: string, valueToUpdate: boolean) {
    switch (field) {
      case this.ClothCECheckBoxTitle:
        this.checkFilterExist(this.filterArr, 'ce');
        this.bhxMaterialMaster.ce.value = valueToUpdate;
        const ce = new CE();
        ce.value = valueToUpdate;
        this.filterArr.push(ce);
        break;
      case this.ClothULCheckBoxTitle:
        this.checkFilterExist(this.filterArr, 'ul');
        this.bhxMaterialMaster.ul.value = valueToUpdate;
        const ul = new UL();
        ul.value = valueToUpdate;
        this.filterArr.push(ul);
        break;
      case this.HazardousCheckBoxTitle:
        this.checkFilterExist(this.filterArr, 'hazardous');
        this.bhxMaterialMaster.hazardous.value = valueToUpdate;
        const hazardous = new Hazardous();
        hazardous.value = valueToUpdate;
        this.filterArr.push(hazardous);
        break;
      case this.ThermostatCheckBoxTitle:
        this.checkFilterExist(this.filterArr, 'thermostat');
        this.bhxMaterialMaster.thermostat.value = valueToUpdate;
        const thermostat = new Thermostat();
        thermostat.value = valueToUpdate;
        this.filterArr.push(thermostat);
        break;
      case this.ManualResetTSCheckBox:
        this.checkFilterExist(this.filterArr, 'manualResetThermostat');
        this.bhxMaterialMaster.manualResetThermostat.value = valueToUpdate;
        const manualResetThermostat = new ManualReset();
        manualResetThermostat.value = valueToUpdate;
        this.filterArr.push(manualResetThermostat);
        break;
      case this.PrivateLabelCheckBox:
        this.checkFilterExist(this.filterArr, 'privateLabel');
        this.bhxMaterialMaster.privateLabel.value = valueToUpdate;
        const privateLabel = new PrivateLabel();
        privateLabel.value = valueToUpdate;
        this.filterArr.push(privateLabel);
        break;
    }
  }

  addFormula() {
    const matDataConfig = new MatDialogConfig();
    let formula = this.masterDataService.equationFormatterFromFullNameToConst(this.strFormula);
    formula = formula ? formula.replace(/\s/g, '') : '';
    matDataConfig.data = { formula: formula, strFormula: this.strFormula };
    matDataConfig.width = PopupSize.size.popup_xxlg;
    matDataConfig.panelClass = 'sfl-formula-generator-model';
    const dialogRef = this.matDialog.open(EquationFormulaGeneratorComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res.success) {
        this.strFormula = res.strExpression;
        this.bhxMaterialMaster.formula.value = res.expression.join('');
        this.snakbarService.success('BHX Material Formula' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success);
        this.checkQuantityAndFormula();
      }
    });
  }

  clearFormula() {
    this.strFormula = '';
    this.bhxMaterialMaster.formula.value = '';
    this.checkQuantityAndFormula();
  }

  clearFilter(resetBhxMaterial: FormGroup) {
    resetBhxMaterial.reset();
    this.bhxMaterialMaster.ce.value = false;
    this.bhxMaterialMaster.ul.value = false;
    this.bhxMaterialMaster.manualResetThermostat.value = false;
    this.bhxMaterialMaster.hazardous.value = false;
    this.bhxMaterialMaster.privateLabel.value = false;
    this.bhxMaterialMaster.thermostat.value = false;
  }

  checkQuantityAndFormula() {
    if (this.bhxMaterialMaster.quantity.value) {
      this.disFormulaBtn = true;
      this.disQty = false;
    } else if (this.bhxMaterialMaster.formula.value) {
      this.disFormulaBtn = false;
      this.disQty = true;
    } else {
      this.disFormulaBtn = false;
      this.disQty = false;
    }
  }

  prepareBHXMaterialObject() {
    this.bhxMaterialMaster.formula.value = this.strFormula ? this.masterDataService.equationFormatterFromFullNameToConst(this.strFormula) : '';
    this.bhxMaterialMaster.formula.value = this.bhxMaterialMaster.formula.value.replace(/\s/g, '');
    this.bhxMaterialMaster.elementType.value = this.elementType.join();
    this.bhxMaterialMaster.wireType.value = this.wireType.join();
    this.bhxMaterialMaster.sensConn.value = this.sensConn.join();
    this.bhxMaterialMaster.sensorType.value = this.sensorType.join();
    this.bhxMaterialMaster.installationMethod.value = this.selectedInstallationMethods.join();
    this.bhxMaterialMaster.strainRelief.value = this.selectedStrainReliefs.join();
    this.bhxMaterialMaster.controller.value = this.selectedController.join();
    this.bhxMaterialMaster.layered.value = this.selectedLayeredMaterial.join();
    this.bhxMaterialMaster.closure.value = this.selectedClosuredMaterial.join();
    this.bhxMaterialMaster.sleeving.value = this.selectedSleeving.join();
    this.bhxMaterialMaster.greenLight.value = this.selectedGreenLights.join();
    this.bhxMaterialMaster.plug.value = this.selectedPlugs.join();
    this.bhxMaterialMaster.connector.value = this.selectedConnectors.join();
    this.bhxMaterialMaster.phase.value = this.selectedPhase.join();
    this.bhxMaterialMaster.jacketType.value = this.selectedJacketTypes.join();
    this.bhxMaterialMaster.productType.value = this.selectedProductTypes.join();
    this.bhxMaterialMaster.leadType.value = this.selectedLeadTypes.join();

    // if values are empty string `""` set it to `null`
    for (const property of Object.entries(this.bhxMaterialMaster)) {
      if ((property[1].dataType === 'String' || property[1].dataType === 'Number') && property[1].value) {
        this.filterArr.push(property[1]);
      }
    }
  }

  onSubmit() {
     this.prepareBHXMaterialObject();
    this.bhxMaterialMaster = new BhxMatetrialFilter();
    this.dialogRef.close(this.filterArr);
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }
}
