<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" [formGroup]="jacketGroupSelectionForm" (ngSubmit)="sendToPrint()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Jacket Groups" name="jacketGroupsIds" formControlName="jacketGroupIds" multiple>
            <mat-option #allSelected (click)="toggleAllSelection()" [value]="0">All</mat-option>
            <mat-option *ngFor="let jacketGroup of jacketGroups" [value]="jacketGroup.id" (click)="singleSelection()">
              {{ jacketGroup.name }}
            </mat-option>
          </mat-select>
          <mat-error> </mat-error>
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!jacketGroupSelectionForm.valid">Print</button>
    </div>
  </mat-dialog-actions>
</form>
