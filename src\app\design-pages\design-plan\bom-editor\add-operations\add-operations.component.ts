import { Component, OnInit, Input, OnD<PERSON>roy, Inject } from '@angular/core';
import { Subscription ,  Observable } from 'rxjs';
import { SnakbarService, Messages, Utils } from 'src/app/shared';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { BOMEditorComponent } from '../bom-editor.component';
import { BomEditorService } from '../bom-editor.service';
import { Operation, OperationsMasterData, JacketProductType } from '../bom-editor.model';
import { FormControl } from '@angular/forms';
import { map, startWith } from 'rxjs/operators';

@Component({
  selector: 'sfl-add-operations',
  templateUrl: './add-operations.component.html'
})
export class AddOperationsComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();

  operationList: OperationsMasterData[];
  operationDetails: OperationsMasterData;
  saveOperation: OperationsMasterData;
  jacketId: number;
  type: string;
  productType: string;
  selection: string;
  operationControl = new FormControl();
  lastFilter = '';
  operationsObservable$: Observable<OperationsMasterData[]>;

  constructor(
    public dialogRef: MatDialogRef<BOMEditorComponent>,
    private snakbarService: SnakbarService,
    private bomeditorService: BomEditorService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketId = data.jacketId;
    this.type = data.type;
  }

  ngOnInit() {
    this.operationDetails = new OperationsMasterData();
    this.saveOperation = new OperationsMasterData();
    this.getProductType();
  }

  getAllOperations() {
    this.subscription.add(
      this.bomeditorService.getAllMaterialByGroupId(this.productType, 5, this.type).subscribe((res: OperationsMasterData[]) => {
        if (res) {
          this.operationList = res;
          this.operationsObservable$ = this.operationControl.valueChanges.pipe(
            startWith<string | OperationsMasterData[]>(''),
            map(value => (typeof value === 'string' ? value : this.lastFilter)),
            map(filter => this.filter(filter))
          );
        }
      })
    );
  }

  displayFn(value: OperationsMasterData[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.partNumber + ', ' + material.description;
        } else {
          displayValue += ', ' + material.partNumber + ', ' + material.description;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  filter(filter: string): OperationsMasterData[] {
    this.lastFilter = filter;
    if (filter) {
      return this.operationList.filter(option => {
        return (
          option.operationName.toLowerCase().indexOf(filter.toLowerCase()) >= 0
        );
      });
    } else {
      return this.operationList ? this.operationList.slice() : [];
    }
  }

  onOperationSelect(i) {
    if (i !== 'other') {
      this.selection = '';
      this.operationDetails = i;
      this.operationControl.setValue(this.operationDetails.operationName);
    } else {
      this.selection = 'other';
      this.operationDetails = {};
    }
  }

  saveOperaton() {
    this.operationDetails.jacketId = this.jacketId;
    this.operationDetails.id = null;
    this.operationDetails.type = this.type;
    this.subscription.add(
      this.bomeditorService.saveOperation(this.operationDetails).subscribe((res: Operation[]) => {
        if (res.length) {
          this.dialogRef.close(res);
        }
      })
    );
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  // used to get the product type of a jacket by jacket id
  getProductType() {
    this.subscription.add(
      this.bomeditorService.getProductTypeByJacketId(this.jacketId).subscribe((typeObject: JacketProductType) => {
        if (typeObject) {
          this.productType = typeObject.productType.toUpperCase();
          this.getAllOperations();
        }
      })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
