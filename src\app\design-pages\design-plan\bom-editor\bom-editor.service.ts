import {catchError, map} from 'rxjs/operators';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {AppConfig} from '../../../app.config';
import {utils} from '../../../shared/helpers/app.helper';
import {
  BomEpicor,
  CommonPartDTO,
  ConfirmSyncDto,
  DuplicateBomDTO,
  ElementOnlyDTO,
  FacingLinerClosure,
  Label,
  LabelDetailedEntry,
  OperationsMasterData,
  Part,
  SensorsOnlyDTO,
  WirePlugging
} from './bom-editor.model';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {LabelDTO} from '../final-review/final-review.model';


@Injectable({providedIn: 'root'})
export class BomEditorService {
  contentTypeAppJson = {'Content-Type': 'application/json'};
  saveElementBom = Variable.saveElement;
  saveSensorBom = Variable.saveSensors;

  constructor(private http: HttpClient) {
  }

  getAllMaterialByGroupId(productType: string, id: number, type: string) {
    return this.http
      .get(AppConfig.GET_ALL_MMATERIAL_BY_GROUP_ID_AND_PRODUCT_TYPE + `${productType}` + '/' + `${id}` + '/' + `${type}`).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  saveOperation(operation: OperationsMasterData) {
    return this.http.post(AppConfig.SAVE_OPERATION, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  syncBomToEpicor(bom: BomEpicor) {
    return this.http.post(AppConfig.SYNC_BOM_TO_EPICOR, bom).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  syncBomToEpicorV1(bom: BomEpicor) {
    return this.http.post(AppConfig.SYNC_BOM_TO_EPICOR_V1, bom).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  checkSync(bom: ConfirmSyncDto) {
    return this.http.post(AppConfig.CHECK_IF_BOM_EXIST, bom).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editOperation(operation: OperationsMasterData) {
    return this.http.put(AppConfig.SAVE_OPERATION, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editAllOperation(operations: OperationsMasterData[]) {
    return this.http.put(AppConfig.SAVE_ALL_OPERATION, operations).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editAllFacing(facingLinerClosures: FacingLinerClosure[]) {
    return this.http.put(AppConfig.SAVE_ALL_FACING, facingLinerClosures).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editAllElement(elementOnlyDTOS: ElementOnlyDTO[]) {
    return this.http.put(AppConfig.SAVE_ALL_ELEMENT, elementOnlyDTOS).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editAllSensors(sensorsOnlyDTOS: SensorsOnlyDTO[]) {
    return this.http.put(AppConfig.SAVE_ALL_ELEMENT, sensorsOnlyDTOS).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editAllWires(wirePluggings: WirePlugging[]) {
    return this.http.put(AppConfig.SAVE_ALL_WIRES, wirePluggings).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editAllLabels(labels: Label[]) {
    return this.http.put(AppConfig.SAVE_ALL_LABELS, labels).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  saveLabels(operation: OperationsMasterData) {
    return this.http.post(AppConfig.SAVE_LABELS, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editLabels(operation: OperationsMasterData) {
    return this.http.put(AppConfig.SAVE_LABELS, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  saveElements(operation: OperationsMasterData) {
    return this.http.post(AppConfig.SVAE_ELEMENT_AND_SENSORS + '/' + this.saveElementBom, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  saveSensor(operation: OperationsMasterData) {
    return this.http.post(AppConfig.SVAE_ELEMENT_AND_SENSORS + '/' + this.saveSensorBom, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editElementsAndSensor(operation: OperationsMasterData) {
    return this.http.put(AppConfig.SVAE_ELEMENT_AND_SENSORS, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  saveFacingLinerClosures(operation: OperationsMasterData) {
    return this.http.post(AppConfig.SAVE_FACING_LINER_CLOSURES, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editFacingLinerClosures(operation: OperationsMasterData) {
    return this.http.put(AppConfig.SAVE_FACING_LINER_CLOSURES, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  saveWirePluggings(operation: OperationsMasterData) {
    return this.http.post(AppConfig.SAVE_WIRE_PLUGGINGS, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  editWirePluggings(operation: OperationsMasterData) {
    return this.http.put(AppConfig.SAVE_WIRE_PLUGGINGS, operation).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  getAllBOMData(id: number, type: string, isModified: boolean) {
    // if (type === Values.MadeInCountryUSA) {
    return this.http
      .get(AppConfig.GET_ALL_BOM_DATA + id + '/' + type + '/' + isModified).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  deleteOperations(id: number) {
    return this.http
      .delete(AppConfig.DELETE_OPERTION + id).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  deleteLabels(id: number, multiple: boolean) {
    return this.http
      .delete(AppConfig.DELETE_LABELS + id + '/' + multiple).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  deleteElement(elementSensor: ElementOnlyDTO) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: elementSensor
    };
    return this.http.delete(AppConfig.DELETE_ELEMENT, options).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  deleteSensors(elementSensor: SensorsOnlyDTO) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: elementSensor
    };
    return this.http.delete(AppConfig.DELETE_SENSORS, options).pipe(map(utils.extractData), catchError(utils.handleError),);
  }


  deleteFacingLinerClosures(facingClosures: FacingLinerClosure) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: facingClosures
    };
    return this.http.delete(AppConfig.DELETE_FACING_LINER_CLOSURES, options).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  deleteWirePluggings(wirePlugging: WirePlugging) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: wirePlugging
    };
    return this.http.delete(AppConfig.DELETE_WIRE_PLUGGINGS, options).pipe(map(utils.extractData), catchError(utils.handleError),);
  }


  getPartClassDisplayList() {
    return this.http.get(AppConfig.GET_PART_CLASS_DISPLAY).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  checkBOMDMTStatus(id: number, type: string) {
    return this.http
      .get(AppConfig.CHECK_SYNC_BOM_STATUS + id + '/' + type).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  getPartByJacketId(id: number) {
    return this.http
      .get(AppConfig.PART_JACKET_API + id).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  getDMTLogResponse(id: number, type: string) {
    return this.http
      .get(AppConfig.GET_DMT_RESPONSE + id + '/' + type).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  getDMTLogResponseV1(id: number, type: string, fileType: string) {
    return this.http
      .get(AppConfig.GET_DMT_RESPONSE_V1 + id + '/' + type + '/' + fileType).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  resetPartByJacketId(id: number) {
    return this.http
      .get(AppConfig.PART_RESET_JACKET_API + id).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  resetBomDataByJacketId(id: number, type: string) {
    return this.http
      .put(AppConfig.RESET_ALL_BOM_DATA + id + '/' + type, null).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  updatePart(part: Part) {
    return this.http
      .put(AppConfig.PART_API, part).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  resetPartDetails(id: number) {
    return this.http
      .get(AppConfig.PART_API + '/' + id + '/reset').pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  resetBySection(id: number, type: string, resetSection: string) {
    return this.http
      .put(AppConfig.RESET_SECTION + `${id}` + '/' + `${type}` + '/' + `${resetSection}`, null).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  checkLastSyncStatus(id: number, type: string, fileType: string) {
    return this.http
      .get(AppConfig.CHECK_LAST_SYNC_STATUS_BY_JACKET_ID + id + '/' + type + '/' + fileType).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  checkMultipleElementSensor(element) {
    return this.http.put(AppConfig.CHECK_IF_MULTIPLE_SENSORS_RECORDS_DELETE, element).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  checkMultipleSensor(element) {
    return this.http.put(AppConfig.CHECK_IF_MULTIPLE_SENSORS_RECORDS_DELETE, element).pipe(map(utils.extractData), catchError(utils.handleError),);
  }


  // checkMultipleElementSensorDelete(element) {
  //   const options = {
  //     headers: new HttpHeaders(this.contentTypeAppJson),
  //     body: element
  //   };
  //   return this.http
  //     .delete(AppConfig.CHECK_IF_MULTIPLE_ELEMENT_SENSOR_RECORDS_DELETE, options).pipe(
  //     map(utils.extractData),
  //     catchError(utils.handleError),);
  // }

  checkMultipleElementDelete(element) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: element
    };
    return this.http
      .delete(AppConfig.CHECK_IF_MULTIPLE_ELEMENT_RECORDS_DELETE, options).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  checkMultipleFacingLinerClosuresDelete(element) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: element
    };
    return this.http
      .delete(AppConfig.CHECK_IF_MULTIPLE_FACING_LINER_RECORDS_DELETE, options).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  checkMultipleWirePluggingsDelete(element) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: element
    };
    return this.http
      .delete(AppConfig.CHECK_IF_MULTIPLE_WIRE_PLUGGINGS_RECORDS_DELETE, options).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  checkMultipleFacingLiner(element) {
    return this.http.put(AppConfig.CHECK_IF_MULTIPLE_FACING_LINER_RECORDS, element).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  checkMultipleWirePluggings(element) {
    return this.http.put(AppConfig.CHECK_IF_MULTIPLE_WIRE_PLUGGINGS_RECORDS, element).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  checkMultipleLabels(element) {
    return this.http.put(AppConfig.CHECK_IF_MULTIPLE_LABELS_RECORDS, element).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  checkMultipleLabelsDelete(element) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: element
    };
    return this.http.delete(AppConfig.CHECK_IF_MULTIPLE_LABELS_RECORDS, options).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  getJacketsByRevId(revisionId) {
    return this.http
      .get(AppConfig.GET_ALL_JACKETS_By_REVID + revisionId).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  // copy BOM of selected jacket to target jacket
  copyBOMToTargetJacket(duplicateBomDTO: DuplicateBomDTO) {
    return this.http
      .post(AppConfig.COPY_BOM_TO_DIFF_JACKET, duplicateBomDTO).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  // used to get the product type of a jacket by jacket id
  getProductTypeByJacketId(jacketId: number) {
    return this.http
      .get(AppConfig.GET_PRODUCT_TYPE_BY_JACKET_ID + jacketId).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  updateAllLabelEntries(labelEntries: LabelDetailedEntry[]) {
    return this.http
      .put(AppConfig.LABEL_DETAILED_ENTRY_API + '/all', labelEntries).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  updateAllLabelEntriesEpicor(labelEntries: LabelDTO[]) {
    return this.http
      .put(AppConfig.LABEL_DETAILED_ENTRY_API + '/all', labelEntries).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }


  resetLabelConfig(jacketId: number, type: string) {
    return this.http
      .get(AppConfig.LABEL_DETAILED_ENTRY_API + '/reset/all/' + jacketId + '/' + type).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  downloadBOMExcel(id: number, type: string) {
    return this.http.get(AppConfig.DOWNLOAD_BOM_EXCEL_API + id + '/' + type, {'responseType': 'blob'}).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getEpicorPartDetails(commonPartDTO: CommonPartDTO) {
    return this.http
      .post(AppConfig.GET_EPICOR_PARTS, commonPartDTO).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }
}
