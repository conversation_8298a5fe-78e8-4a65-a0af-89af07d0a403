import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { GenericPageable } from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { ManagePartNumberMasterComponent } from './manage-part-number-master/manage-part-number-master.component';
import { PartNumberMasterDTO } from './manage-part-number-master/manage-part-number-model';

@Component({
  selector: 'app-part-number-master-data',
  templateUrl: './part-number-master-data.component.html',
  styleUrls: ['./part-number-master-data.component.css']
})
export class PartNumberMasterDataComponent implements OnInit,  OnDestroy {
  pageTitle = 'Part Number Master';
  length: number;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  numberOfElements: number;
  partNumberPageable: GenericPageable<PartNumberMasterDTO>;
  partNumberMasterDataSource = new MatTableDataSource<PartNumberMasterDTO>();
  partNumberMasterColumns = DisplayColumns.Cols.PartNumberMasterDataColumn;
  showLoader = false;
  pageSizeOptions = Variable.pageSizeOptions;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  subscription = new Subscription();
  
  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
    private readonly matDialog: MatDialog,
  ) { }

  ngOnInit() {
    this.getPartnumberMasterData(this.initialPageIndex, this.initialPageSize);
  }

  getPartnumberMasterData(pageIndex, pageSize) {
    const pageable = {page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField}
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPartNumberMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<PartNumberMasterDTO>) => {
          this.partNumberPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPartNumberTable(this.partNumberPageable);
          this.filter = [];
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    )
  }

  getMasterPartNumberPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPartnumberMasterData(this.pageIndex, this.pageSize);
  }

  addPartNumberController() {
    this.editPartNumberMaster(new PartNumberMasterDTO());
  }

  editPartNumberMaster(partNumberController: PartNumberMasterDTO) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = partNumberController;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-part-number-master-model';
    const dialogRef = this.matDialog.open(ManagePartNumberMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          partNumberController.id
            ? 'Master Part Number' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Master Part Number' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPartnumberMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  async deleteMasterPartNumber(Id: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteMasterPartNumber(Id).subscribe(
        () => {
          this.snakbarService.success('Master Part Number' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPartnumberMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  getMasterPartNumberSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPartnumberMasterData(this.pageIndex, this.pageSize);
  }


  createPartNumberTable(serviceRequestList: GenericPageable<PartNumberMasterDTO>) {
    this.partNumberMasterDataSource.data = serviceRequestList.content;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
