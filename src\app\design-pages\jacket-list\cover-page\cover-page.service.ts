
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';


import { CoverPageModel } from './cover-page.model';

@Injectable({ providedIn: 'root' })
export class CoverPageService {

    constructor(private http: HttpClient) { }

    getMasterData() {
        return this.http.get(AppConfig.GET_COVER_PAGE_MASTER_DATA).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

    getCoverPageDataByQuotationData(quotationId) {
        return this.http.get(AppConfig.SAVE_COVER_PAGE + '/quotationId/' + quotationId).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

    saveCoverPage(coverPage: CoverPageModel) {
        return this.http.post(AppConfig.SAVE_COVER_PAGE, coverPage).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

    updateCoverPage(coverPage: CoverPageModel) {
        return this.http.put(AppConfig.SAVE_COVER_PAGE, coverPage).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }
}
