html,
body {
  height: 100%;
}

body {
  background-color: #e2e2e2;
  padding: 0px;
  margin: 0px;
}
.cdk-global-overlay-wrapper {
  display: block;
  overflow: auto;
}

.close-button{
  position: relative;
  left: -10px;
}

.cdk-global-overlay-wrapper::before {
  content: '';
  height: 100%;
}

.cdk-overlay-pane {
  text-align: left;
}

a {
  text-decoration: none;
  color: inherit;
}

.cursor-pointer {
  cursor: pointer;
}

.feature-class{
  margin-top: auto;
}

.nodeName{
  position: absolute;
}

cdk-tree-node {
  display: block;
}

h1 {
  padding: 10px 10px;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.mat-text-muted {
  color: #66666f;
}

.mat-badge-warn {
  color: #ffffff;
  background: #f44336 !important;
  padding: 5px 10px;
  font-size: 13px;
  margin-bottom: 10px;
}

.mat-text-warn {
  color: #f44336 !important;
}

.pb-1 {
  padding-bottom: 1rem !important;
}

.pt-1 {
  padding-top: 1rem !important;
}

.outside-view {
  min-height: 100%;
  margin-bottom: -60px;
}

.inside-view {
  min-height: 100%;
  margin-bottom: -20px;
}

.account-wrapper {
  padding: 40px 1rem 80px 1rem;
}

.account {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  position: relative;
}

.account.register {
  max-width: 650px;
}

.mat-form-field {
  width: 100%;
}

.account .mat-text-muted {
  font-size: 14px;
}

.logo {
  background-image: url('data:image/png;base64,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');
  background-size: 174px 30px;
  width: 174px;
  height: 30px;
  margin: auto;
}

/* Header */

// mat-icon {
//     color: #ee3124;
// }
mat-toolbar.main-header {
  padding: 0 8px;
  position: relative;
  -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.3);
  z-index: 9;
}

/* Dashboard start */

hr {
  margin-top: 0;
  margin-bottom: 0;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.card {
  width: 25%;
  margin: 0.2rem;
}

.float-icon {
  position: absolute;
  right: 15px;
  top: 17%;
  height: 100px;
  width: 100px;
}

.mat-card-yellow {
  background-color: #cccc00;
  color: #ffffff;
}

.mat-card-purple {
  background-color: #9c27b0;
  color: #ffffff;
}

.mat-card-indigo {
  background-color: #3f51b5;
  color: #ffffff;
}

.mat-card-red {
  background-color: #f44336;
  color: #ffffff;
}

.mat-card-green {
  background-color: #008000;
  color: #ffffff;
}

.card-widget.mat-card {
  padding: 10px 0px;
}

.mat-card [mat-card-widget] {
  height: auto;
  flex-direction: row;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-grid-row-align: center;
  align-items: center;
  align-content: center;
  max-width: 100%;
  padding: 1rem;
}

.mat-teal {
  background-color: #009688 !important;
  color: white !important;
}

.icon-white {
  color: #ffffff;
  opacity: 0.7;
}

.btn {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  outline: 0;
  border: none;
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  white-space: nowrap;
  text-decoration: none;
  vertical-align: baseline;
  text-align: center;
  margin: 0;
  min-width: 88px;
  line-height: 36px;
  padding: 10px 16px;
  border-radius: 2px;
  transform: translate3d(0, 0, 0);
  transition: background 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
}

.info {
  padding: 15px;
  flex-flow: row wrap;
  box-sizing: border-box;
  display: flex;
}

table {
  width: 100%;
}

th.mat-sort-header-sorted {
  color: black;
}

.my-2 {
  flex-direction: row;
  box-sizing: border-box;
  display: flex;
  padding-right: 15px;
  font-size: 16px;
  color: #4c4c4c;
}

.font-lg {
  font-size: 28px;
}

.example-button-toggle-label {
  display: inline-block;
  margin: 16px;
}

.stretched-tabs {
  max-width: 100%;
}

.font-bold {
  font-weight: 700;
}

.text {
  text-align: justify;
  flex-direction: row;
  box-sizing: border-box;
  display: flex;
  padding-right: 15px;
  font-size: 16px;
  line-height: 25px;
}

.gary-icon {
  color: #676a6c;
}

.data {
  padding-left: 10px;
}

.data-1 {
  padding-left: 20px;
}

.example-icon {
  padding: 0 14px;
}

.example-spacer {
  flex: 1 1 auto;
}

.sign {
  height: 70px;
  width: 150px;
  margin-top: 0px;
}

.field {
  padding-top: 20px;
}

.field-1 {
  padding-top: 35px;
}

.mat-card-title {
  font-size: 20px;
  margin-bottom: 5px;
}

.space {
  padding-top: 10px;
}

.mat-card {
  margin: 0.5rem;
  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  display: block;
  position: relative;
  padding: 24px;
  border-radius: 2px;
}

.mat-card [mat-card-float-icon] {
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -20px;
  width: 40px;
  height: 40px;
}

.pl-0 {
  padding-left: 0 !important;
}

.mb-1 {
  margin-bottom: 1rem !important;
}

.mat-card [mat-card-widget] [mat-card-widget-title],
.mat-card [mat-card-widget] p {
  margin: 0;
  margin-top: 5px;
  padding: 0;
  line-height: 1.1 !important;
}

.mat-table {
  width: 100%;
  margin: 0.33333333rem;
  display: grid;
  overflow: auto;
}

.mat-cell,
.mat-footer-cell {
  padding-right: 5px;
}

.mat-card-lightgray {
  background-color: #f2f2f2;
}

.material-icons_md-48 {
  font-size: 50px;
}

.green-snackbar {
  background: #fff;
  color: #000;
}

.red-snackbar {
  background: #f44336;
  white-space: pre-line;
}

.green-snackbar .mat-simple-snackbar-action {
  background: red;
  color: #ffffff !important;
}

.red-snackbar .mat-simple-snackbar-action {
  background: #b71c1c;
  color: #ffffff !important;
}

.active .side-lbl .mat-icon {
  -webkit-transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
  -o-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.active .side-lbl {
  background-color: #e74133;
}

.side-lbl .mat-icon {
  padding: 0px;
}

.body-filter {
  margin-left: 15px;
}

.side-lbl {
  background-color: #282735;
  color: #ffffff;
  padding: 2px 15px;
  display: flex;
  width: 100px;
  position: absolute;
  left: -37px;
  margin-top: 100px !important;
  z-index: 10;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

.side-lbl span {
  padding-left: 5px;
  padding-top: 2px;
}

.side-remove-icon {
  background-color: #d2d2d2;
  color: #ffffff;
  padding: 2px;
  display: flex;
  position: absolute;
  right: 0;
  margin-top: -28px !important;
  z-index: 10;
  border-radius: 20px;
}

.error h2 {
  color: #000000;
}

.error .btn_red {
  background-color: #e74133;
  color: #ffffff;
}

.font-size-15em {
  font-size: 15em;
  margin: 0px;
}

.mw-fit{
  max-width: fit-content !important;
}

/* Dashboard start */

/* Menu */

.app-inner {
  position: relative;
  width: 100%;
  max-width: 100%;
  height: calc(100vh - 64px);
  display: -webkit-box;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-direction: normal;
  -webkit-box-orient: horizontal;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

mat-sidenav.sidebar-panel {
  position: absolute !important;
  overflow-x: hidden;
  width: 15rem;
  -webkit-box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12) !important;
  box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12) !important;
}

.mat-drawer-transition .mat-drawer-content {
  transition-duration: 0.4s;
  transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
  transition-property: transform, margin-left, margin-right;
}

mat-sidenav.sidebar-panel .mat-list-item .mat-list-item-content {
  display: block;
  height: auto;
  max-height: 48px;
  overflow: hidden;
  padding: 0;
  -webkit-transition: max-height 0.3s cubic-bezier(0.35, 0, 0.25, 1);
  transition: max-height 0.3s cubic-bezier(0.35, 0, 0.25, 1);
}

mat-sidenav.sidebar-panel .mat-list-item {
  height: auto;
  background-color: transparent;
  -webkit-transition: background-color 0.3s cubic-bezier(0.35, 0, 0.25, 1);
  transition: background-color 0.3s cubic-bezier(0.35, 0, 0.25, 1);
}

.mat-nav-list .mat-list-item {
  cursor: pointer;
  outline: 0;
}

mat-sidenav.sidebar-panel .mat-nav-list a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 48px;
  padding: 0 16px;
}

.mat-list-item.open > .mat-list-item-content {
  max-height: 2000px;
  background: mat-color(rgba(0, 0, 0, 0.04), 'hover');
}

.mat-nav-list a {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48px;
  padding: 0 16px;
}

.sub-menu {
  padding-top: 0;
  overflow: hidden;
  transition: 0.5s max-height cubic-bezier(0.35, 0, 0.25, 1);
  max-height: 0;
  transform: translateZ(0) !important;
}

mat-sidenav.sidebar-panel .navigation mat-icon:not(.menu-caret) {
  margin-right: 24px;
}

mat-list mat-list-item .mat-list-item-content,
mat-list a[mat-list-item] .mat-list-item-content,
mat-nav-list mat-list-item .mat-list-item-content,
mat-nav-list a[mat-list-item] .mat-list-item-content {
  font-size: 0.875rem !important;
}

/* Badges */

mat-sidenav.sidebar-panel .navigation .menu-badge {
  display: inline-block;
  height: 16px;
  min-width: 10px;
  line-height: 18px;
  text-align: center;
  border-radius: 16px;
  font-size: 10px;
  font-weight: 700;
  padding: 0 4px;
}

.mat-red {
  background-color: #f44336 !important;
  color: white !important;
}

.mat-blue-grey {
  background-color: #607d8b !important;
  color: white !important;
}

.mat-purple {
  background-color: #9c27b0 !important;
  color: white !important;
}

.mat-menu-item {
  font-size: 0.875rem !important;
}

/* Footer */

.footer {
  color: #4c4c4c;
  background: #ffffff;
  padding: 15px 20px;
  bottom: 0;
  right: 0;
  left: 0;
  height: 40px;
  margin-top: 20px;
}

@media screen and (min-width: 1200px) and (max-width: 1400px) {
  .side-nav-sm {
    margin-top: -58px;
  }

  .save-all {
    margin-left: 128px;
  }

  .md-header {
    margin-left: auto;
  }
}

@media (max-width: 1170px) {
  .footer {
    height: 40px;
  }

  .footer .float-left,
  .footer .float-right {
    float: none !important;
    text-align: center;
  }
}

.cdk-global-overlay-wrapper {
  display: flex;
  position: absolute;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.288);
  pointer-events: auto;
}

.cdk-global-overlay-wrapper,
.cdk-overlay-container {
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.mat-drawer-content > .ps {
  position: relative;
  height: 100%;
  min-height: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-box-direction: normal;
  -webkit-box-orient: vertical;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -moz-flex: 1;
  -ms-flex: 1;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.ps__rail-y {
  left: auto !important;
}

// =======================================================================================================================================
/*BriskHeat css start*/

* {
  box-sizing: border-box;
}

.bg-dark {
  background-color: #282735;
  color: #fff;
}

body {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  line-height: 1em;
}

button {
  background-color: transparent;
  padding: 0;
  border: 0;
  outline: 0;
  cursor: pointer;
}

input {
  background-color: transparent;
  padding: 0;
  border: 0;
  outline: 0;
}

input[type='submit'] {
  cursor: pointer;
}

input::-webkit-input-placeholder {
  font-size: 0.85rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  letter-spacing: 0.1rem;
  color: #ccc;
}

input:-ms-input-placeholder {
  font-size: 0.85rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  letter-spacing: 0.1rem;
  color: #ccc;
}

input::-ms-input-placeholder {
  font-size: 0.85rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  letter-spacing: 0.1rem;
  color: #ccc;
}

input::placeholder {
  font-size: 0.85rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  letter-spacing: 0.1rem;
  color: #ccc;
}

/**
   * Page background
   */

.user {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
}

.user_options-container {
  position: relative;
  width: 80%;
}

.user_options-container .mat-card {
  padding: 0%;
}

.user_options-text {
  display: flex;
  justify-content: space-between;
  width: 100%;
  background-color: #282735;
  border-radius: 3px;
}

/**
   * Registered and Unregistered user box and text
   */

.user_options-registered,
.user_options-unregistered {
  width: 50%;
  padding: 75px 45px;
  color: #fff;
  font-weight: 300;
}

.user_registered-title,
.user_unregistered-title {
  margin-bottom: 15px;
  font-size: 1.66rem;
  line-height: 1em;
}

.user_unregistered-text,
.user_registered-text {
  font-size: 0.83rem;
  line-height: 1.4em;
}

.user_registered-login,
.user_unregistered-signup {
  margin-top: 30px;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 10px 30px;
  color: #fff;
  text-transform: uppercase;
  line-height: 1em;
  letter-spacing: 0.2rem;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.user_registered-login:hover,
.user_unregistered-signup:hover {
  color: rgba(34, 34, 34, 0.85);
  background-color: #ccc;
}

/**
   * Login and signup forms
   */

.user_options-forms {
  position: absolute;
  top: 50%;
  left: 30px;
  width: calc(50% - 30px);
  min-height: 420px;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  -webkit-transform: translate3d(100%, -50%, 0);
  transform: translate3d(100%, -50%, 0);
  transition: -webkit-transform 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out, -webkit-transform 0.4s ease-in-out;
}

.user_options-forms .user_forms-login {
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out;
}

.user_options-forms .forms_title {
  margin-bottom: 45px;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1em;
  text-transform: uppercase;
  color: #e8716d;
  letter-spacing: 0.1rem;
}

.user_options-forms .forms_field:not(:last-of-type) {
  margin-bottom: 20px;
}

.user_options-forms .forms_field-input {
  width: 100%;
  border-bottom: 1px solid #ccc;
  padding: 6px 20px 6px 0;
  font-family: 'Montserrat', sans-serif;
  font-size: 1rem;
  font-weight: 300;
  color: gray;
  letter-spacing: 0.1rem;
  transition: border-color 0.2s ease-in-out;
}

.user_options-forms .forms_field-input:focus {
  border-color: gray;
}

.user_options-forms .forms_buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 35px;
}

.user_options-forms .forms_buttons-forgot {
  font-family: 'Montserrat', sans-serif;
  letter-spacing: 0.1rem;
  color: #ccc;
  text-decoration: underline;
  transition: color 0.2s ease-in-out;
}

.user_options-forms .forms_buttons-forgot:hover {
  color: #b3b3b3;
}

.user_options-forms .forms_buttons-action {
  background-color: #e8716d;
  border-radius: 3px;
  padding: 10px 35px;
  font-size: 1rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
  transition: background-color 0.2s ease-in-out;
}

.user_options-forms .forms_buttons-action:hover {
  background-color: #e14641;
}

.user_options-forms .user_forms-signup,
.user_options-forms .user_forms-login {
  position: absolute;
  top: 70px;
  left: 40px;
  width: calc(100% - 80px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out, -webkit-transform 0.5s ease-in-out;
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out, transform 0.5s ease-in-out;
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out, transform 0.5s ease-in-out, -webkit-transform 0.5s ease-in-out;
}

.user_options-forms .user_forms-signup {
  -webkit-transform: translate3d(120px, 0, 0);
  transform: translate3d(120px, 0, 0);
}

.user_options-forms .user_forms-signup .forms_buttons {
  justify-content: flex-end;
}

.user_options-forms .user_forms-login {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  opacity: 1;
  visibility: visible;
}

.mobile-label {
  display: none;
}

.search-bar {
  margin-left: 40px;
  width: auto !important;
}

@media screen and (max-width: 1200px){
  mat-toolbar.main-header .search-bar .search-form{
    width: 450px !important;
  }
  .searchButton{
    right: 5px !important;
  }
}
@media screen and (max-width: 992px){
  .logo-searchbar{
    flex-direction: column !important;
    align-items: flex-start !important;
  }
  .mat-toolbar-single-row{
    height: 110px !important;
  }
  mat-toolbar.main-header .search-bar .search-form{
    width: 350px !important;
  }
  .searchButton{
    right: 10px !important;
  }
}
@media screen and (max-width: 600px) {
  mat-toolbar.main-header .search-bar .search-form {
    width: auto;
  }
  .mat-toolbar-row, .mat-toolbar-single-row {
    height: 56px !important;
  }
}

mat-toolbar.main-header .search-bar .search-form {
  background: #f3f3f4;
  position: relative;
  border-radius: 2px;
  margin-right: 1rem;
  display: block;
  width: 600px;
}

.search-bar .search-form a {
  text-decoration: none;
}

mat-toolbar.main-header .search-bar .search-form .material-icons {
  position: absolute;
  top: 50%;
  left: 10px;
  margin-top: -12px;
  color: rgba(0, 0, 0, 0.87);
}

mat-toolbar.main-header .search-bar .search-form input {
  font-size: 1rem;
  padding: 0.5rem 0.75rem;
  z-index: 2;
  cursor: text;
  text-indent: 30px;
  border: none;
  background: transparent;
  width: 100%;
  outline: 0;
}

.search {
  width: 100%;
  position: relative;
}

.searchTerm {
  float: left;
  width: 100%;
  border: 3px solid #00b4cc;
  padding: 5px;
  height: 20px;
  border-radius: 5px;
  outline: none;
  color: #9dbfaf;
}

.searchTerm:focus {
  color: #00b4cc;
}

.searchButton {
  color: #fff;
  width: 8%;
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 0 3px 3px 0;
  margin: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/*Resize the wrap to see the search bar change!*/

.wrap {
  width: 30%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/**
   * Responsive 990px
   */

@media screen and (max-width: 990px) {
  .user_options-forms {
    min-height: 350px;
  }

  .user_options-forms .forms_buttons {
    flex-direction: column;
  }

  .user_options-forms .user_forms-login .forms_buttons-action {
    margin-top: 30px;
  }

  .user_options-forms .user_forms-signup,
  .user_options-forms .user_forms-login {
    top: 40px;
  }

  .user_options-registered,
  .user_options-unregistered {
    padding: 50px 45px;
  }
}

@media (max-width: 786px) {
  .mat-row {
    flex-direction: column;
    align-items: start;
    padding: 5px 24px;
    min-height: auto !important;
  }

  .mobile-label {
    width: 100px;
    display: inline-block;
    font-weight: bold;
  }

  mat-cell:first-child,
  mat-footer-cell:first-child,
  mat-header-cell:first-child {
    padding-left: 0px !important;
  }

  mat-cell {
    min-height: auto !important;
    padding: 5px 0px;
  }

  .mat-header-row {
    display: none;
  }
}

.list {
  flex: 1 1 150%;
  min-width: 300px;
  display: flex;
  margin-left: 35px;
}

@media (max-width: 500px) {
  .recipe-flex {
    flex-wrap: wrap;
  }

  .plan {
    border-left: 0;
  }

  .stages {
    margin-left: 48px;
  }
}

.steps {
  margin: 2em 0 0 -3px;
  padding: 0 1em 0 0;
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  counter-reset: steps;
}

.steps > li {
  padding: 7px 25px 30px;
  flex: 1 1 100%;
  position: relative;
  border-left: 2px solid;
  counter-increment: steps;
}

.steps > li::before {
  content: attr(data-description);
  position: absolute;
  top: 8px;
  left: 30px;
  font-style: italic;
  font-weight: bold;
  font-size: 12px;
}

.steps > li::after {
  position: absolute;
  top: 0;
  left: -18px;
  border-radius: 20px;
  width: 30px;
  height: 30px;
  display: block;
  text-align: center;
  font-weight: 800;
  line-height: 30px;
  content: counter(steps);
  border: 2px solid;
  background-color: #ffffff;
}

.steps ul li::before {
  width: 10px;
  height: 3px;
  display: block;
  position: absolute;
  top: 10px;
  left: -27px;
  border-radius: 10px;
}

.steps li a {
  padding: 2px 7px;
  text-decoration: none;
  cursor: pointer;
  font-weight: 700;
}

.list .active {
  color: #ed3c46;
  font-weight: 700;
}

.steps ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  margin-left: 9px;
  padding-top: 3px;
}

.steps ul li {
  position: relative;
}

.steps ul li::before {
  content: '';
  width: 10px;
  height: 3px;
  display: block;
  position: absolute;
  top: 10px;
  left: -27px;
  border-radius: 10px;
}

.steps > li .section {
  margin-top: 10px;
  flex: 1 1 100%;
  position: relative;
}

.steps .section::after {
  position: absolute;
  top: 0;
  left: -34px;
  border-radius: 20px;
  width: 15px;
  height: 15px;
  display: block;
  content: ' ';
}

.steps > li .section::before {
  content: attr(data-description);
  position: absolute;
  top: 0;
  left: 0px;
  font-style: italic;
  color: #777;
  font-size: 12px;
  background-color: transparent !important;
}

.steps > li.current .section,
.steps > li.current .section::after {
  border-color: #5bc0de;
}

.steps > li.current .section::after {
  background-color: #5bc0de;
}

.steps > li.done .section,
.steps > li.done .section::after {
  border-color: #b5d034;
}

.steps > li.done .section::after {
  background-color: #b5d034;
}

.steps > li.pending .section,
.steps > li.pending .section::after {
  border-color: #999;
}

.steps li a {
  padding: 2px 7px;
}

.steps li.active a {
  background-color: #5bc0de;
  color: black;
  border-radius: 20px;
}

.button-row button {
  margin-right: 8px;
}

.row mat-checkbox {
  margin-right: 8px;
}

.button-row .mat-card-actions {
  margin-left: 0px;
  margin-right: 0px;
  padding: 8px 0;
  display: block;
  margin-bottom: 0px;
}

.p-u {
  padding-bottom: 10px;
}

.form_file {
  position: relative;
  width: 100%;
  height: 100%;
  outline: none;
  cursor: pointer;
  border: 2px dashed #ed3c46;
}

.form_file:hover {
  border-style: solid;
  box-shadow: 1px 0px 0px #ed3c46;
}

.p1 {
  padding: 15px 15px 15px 15px;
}

.form_file p {
  width: 100%;
  height: 100%;
  text-align: center;
  color: #ed3c46;
  overflow: hidden;
  padding-top: 40px;
}

.form_file input {
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  color: #000;
  text-align: center;
}

.pointer h1 {
  cursor: pointer;
}

.input-field {
  position: relative;
  width: 250px;
  height: 44px;
  line-height: 44px;
}

input {
  width: 100%;
  border: 0;
  outline: 0;
  padding: 0;
  border-bottom: 2px solid #d3d3d3;
}

.material-form-field {
  position: relative;
  max-width: 250pt;
  margin: 10pt 20pt 15pt 20pt;
  display: inline-block;
}

.material-form-field-input {
  padding: 15pt 0 6pt 0;
  font-size: 16px;
  font-weight: 300;
  width: 100%;
  background: transparent;
  border-width: 0 0 1px 0;
  color: #000000;
}

.search-bar .material-form-field-input input {
  padding: 15pt 0 6pt 0;
  font-size: 16px;
  font-weight: 300;
  width: 100%;
  background: transparent;
  border-width: 0 0 1px 0;
  color: #000000;
}

.filter {
  background-color: transparent;
}

.filter .fields {
  margin-left: 10px;
  margin-top: 10px;
}

.filter .head {
  margin-left: 20px;
  padding-top: 5px;
  margin-right: 20px;
}

.filter .fields .mat-card-actions .mat-raised-button {
  margin-right: 25px;
}

.header {
  padding: 10px;
}

.p-1 {
  margin-left: 12px;
}

.bottom-btn-left {
  margin-right: 18px;
}

.btn-add {
  margin-top: 5px;
  margin-right: 13px;
}

.btn-option {
  width: 100%;
  background-color: red;
  color: #fff;
}

.icon-lg {
  background-repeat: no-repeat;
  display: inline-block;
  fill: currentColor;
  height: 24px;
  width: 24px;
}

.steps .no-border {
  border: none;
}

.du-documents {
  float: left;
}

.du-doc {
  width: 60px;
  height: 80px;
  float: left;
  text-align: center;
  margin: 0 10px 0 0;
}

.du-doc-img {
  width: 60px;
  text-align: center;
  margin: 0px 0;
  background: #f5f5f5;
  float: left;
  border: 1px solid #f5f5f5;
  transition: all 0.5s ease;
}

.filename .mat-card-title {
  padding-top: 0.4rem;
  padding-left: 0.5rem;
}

.filename .open-doc .mat-icon {
  display: none;
}

.filename:hover .mat-icon {
  display: inline;
}

/*filete*/

mat-toolbar.main-header .search-bar .filter-bar {
  border: solid 1px #f3f3f4;
  background: #fff;
  position: absolute;
  border-radius: 2px;
  margin-right: 1rem;
  display: block;
  width: 600px;
}

.search-bar .filter-bar .mat-card .mat-form-field-wrapper {
  padding-bottom: 25px;
  position: relative;
}

.my-tbl mat-cell:first-child,
mat-footer-cell:first-child,
mat-header-cell:first-child {
  padding: 0px;
}

.details-srooll::-webkit-scrollbar {
  width: 5px;
}

.details-srooll::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 20px;
}

.border-bottom {
  border-bottom: 1px solid #4caf50;
}

h1,
h2,
h3,
h4,
h5 {
  font-weight: 500;
  font-size: 14px;
}

.less-peding h2 {
  margin: 0px;
}

.cust_table .mat-header-row,
.mat-header-cell {
  min-height: 30px;
  background-color: #f1f1f1;
  font-size: 14px;
  text-align: center;
}

.cust_table .mat-row {
  min-height: 30px;
  margin-inline: 0;
}

.cust_fields .mat-form-field-appearance-outline .mat-form-field-prefix,
.mat-form-field-appearance-outline .mat-form-field-suffix {
  top: 0 !important;
}

.quantity::-webkit-inner-spin-button,
.quantity::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/*Download*/
/* Geometry css */

.mat-form-field-appearance-outline.mat-form-field-can-float .mat-input-server:focus + .mat-form-field-label-wrapper .mat-form-field-label,
.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  top: 2.4em !important;
}

/* high-performance display:none; helper */

/* added to mirrorContainer (default = body) while dragging */

/* added to the source element while its mirror is dragged */

.revDisable {
  pointer-events: none;
  opacity: 0.8;
  background: transparent;
  margin-top: 10px;
}

mat-toolbar.main-header .search-bar .filter-bar {
  border: solid 1px #f3f3f4;
  background: #fff;
  position: absolute;
  border-radius: 2px;
  margin-right: 1rem;
  display: block;
  width: 600px;
}

.search-bar .filter-bar a {
  text-decoration: none;
}

mat-toolbar.main-header .search-bar .filter-bar .material-icons {
  position: absolute;
  top: 50%;
  left: 10px;
  margin-top: -12px;
  color: rgba(0, 0, 0, 0.87);
}

mat-toolbar.main-header .search-bar .filter-bar input {
  text-align: left;
  font-size: inherit;
  font-weight: 400;
  line-height: 1.125;
  position: relative;
  display: inline-block;
}

mat-toolbar.main-header .search-bar .filter-bar .mat-card {
  margin: 0px;
}

.search-bar .filter-bar .mat-card {
  padding: 15px 15px 0px 15px;
  .searchbar-actions{
    align-items: end !important;
    padding-bottom: 25px;
    button{
      height: fit-content;
    }
  }
}

.search-bar .filter-bar .mat-card .mat-form-field-wrapper {
  padding-bottom: 25px;
  position: relative;
}

.search-bar .filter-bar .mat-card .mat-form-field-infix {
  padding: 0.5em 0;
  border-top: 0.2em solid transparent;
}

.details {
  margin: 7px;
  width: 100%;
  height: 100%;
  outline: none;
  border: 3px #d3d3d3;
  position: fixed;
  z-index: 1;
  display: inline-block;
}

.jackets-details {
  margin-left: auto;
  margin-right: 8px;
}

.details-box {
  margin: 7px;
  position: relative;
  width: 100%;
  height: 100%;
  outline: none;
  border: 3px dashed #d3d3d3;
  background-color: transparent;
}

.details-box:hover {
  border-style: solid;
}

.open-doc {
  cursor: pointer;
}

.mat-dialog-container {
  width: 1500px !important;
  margin: 20px !important;
  max-height: 100% !important;
}

.bg-gray {
  background-color: lightgray;
  width: 25%;
}

.mat-img {
  width: 100%;
  height: auto;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  margin-top: 10px;
}

.mat-img img {
  margin-top: 10px;
}

.highlight-mat-table .mat-table {
  background-color: #f1f1f1;
}

.lbl-view {
  margin-right: 20px;
  flex-flow: row;
}

.lbl {
  font-weight: 700;
  margin-right: 10px;
}

.my-tbl mat-cell:first-child,
mat-footer-cell:first-child,
mat-header-cell:first-child {
  padding: 0px;
}

.my-header {
  padding: 10px;
}

.feature {
  margin-left: 25px;
}

.white {
  background-color: #fff;
}

.light-gray {
  background-color: #f1f1f1;
  padding: 0 1.4em 1.4em 1.4em;
}

.geo-header {
  padding-right: 10px;
}

.display-details {
  height: 400px;
}

.details-srooll {
  display: block;
  overflow-x: hidden;
  overflow-y: auto;
}

.effect {
  padding: 5px 0 5px;
  border: 1px solid transparent;
  border-bottom-color: #ccc;
  transition: 0.4s;
}

.effect:focus {
  padding: 5px 14px 7px;
  transition: 0.4s;
  height: 36px;
  border: 1px solid #4caf50;
  z-index: 1000;
}

.border {
  border: 1px solid #4caf50;
}

.border-bottom label {
  padding-bottom: 2px;
}

.border .p-1 {
  padding: 5px;
}

.highlight {
  background-color: #f1f1f1;
  padding: 10px;
}

.p-10 .mat-card {
  padding: 10px;
}

h1,
h2,
h3,
h4,
h5 {
  font-weight: 500;
  font-size: 14px;
}

.font-md {
  font-size: 15px;
  line-height: 20px;
}

.highlight .m-0 {
  margin-bottom: 0 !important;
}

.less-peding .mat-card {
  padding: 10px;
}

.less-peding h2 {
  margin: 0px;
}

.geo-card h4 {
  margin-bottom: 0px;
}

.less-peding .sub-card .mat-card {
  padding: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.cust_table th {
  text-align: center !important;
}

.cust_table .mat-header-row,
.mat-header-cell {
  min-height: 30px;
  background-color: #f1f1f1;
  font-size: 14px;
  text-align: center;
}

.font-sm {
  font-size: 14px;
}

.cust_table .mat-row {
  min-height: 30px;
}

.cust_table .mat-table {
  margin: 0px;
}

.cust_fields .mat-form-field-appearance-outline .mat-form-field-wrapper {
  padding: 3px 0px 0px 0px;
  margin: 0px;
}

.cust_fields .mat-form-field-infix {
  border-top: 0px;
}

.cust_fields .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.7em 0 0.7em 0;
  font-size: 15px;
}

.cust_fields .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: #e9e9e9;
}

.cust_fields h4 {
  margin: 0px;
}

.mt8 {
  margin-top: -8px;
}

.featlist {
  border-left: 1px solid gray;
  border-right: 1px solid gray;
  border-bottom: 1px solid gray;
}

.featlist .mat-card {
  margin: 0px;
}

.geo-card .mat-card {
  margin: 0px;
  padding: 10px;
}

.pr-10 {
  padding-right: 10px;
}

.pb-5 .mat-form-field {
  padding: 3px;
}

.less-peding .mat-card-title {
  margin-bottom: 8px;
}

.mr-10 {
  margin-left: 10px !important;
}

.mr-20 {
  margin-left: 20px !important;
}

.mr-8 {
  margin-right: 8px !important;
}

.ml-3 {
  margin-left: 1rem;
}

.link {
  color: #ef3c43;
}

.quantity::-webkit-inner-spin-button,
.quantity::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.error {
  background: #ef3c43;
  color: WHITE;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 3px;
}

/*Download*/

.container {
  background: #fff;
  max-width: 50%;
  margin: 4% auto;
  padding: 20px;
  font-family: 'Open Sans', sans-serif;
  border-radius: 5px;
  overflow: hidden;
}

/* Geometry css */

.maingroup {
  border: 1px groove #ddd !important;
  padding: 0 1em 1em 1em !important;
}

.geo-title {
  margin-top: 10px;
  margin-bottom: 5px;
}

.dark-gray {
  background-color: #ccc;
  border-left: 1px solid gray;
  border-right: 1px solid gray;
  border-top: 1px solid gray;
}

.mat-card.dark-gray.mat-card.active {
  background-color: gray;
}

.mat-form-field-appearance-outline.mat-form-field-can-float .mat-input-server:focus + .mat-form-field-label-wrapper .mat-form-field-label,
.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  top: 2.4em !important;
}

.cust_fields .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-select-arrow-wrapper {
  vertical-align: bottom !important;
}

.mat-form-field-appearance-outline.mat-form-field-can-float .mat-input-server:focus + .mat-form-field-label-wrapper .mat-form-field-label,
.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  top: 2.4em !important;
}

.cust_fields .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
  vertical-align: bottom !important;
}

.vamp {
  background: rgba(255, 255, 255, 0.4);
  list-style: none;
  padding: 8px;
  color: #222;
}

.vamp:not(:last-child) {
  border-bottom: 1px solid #777;
}

.gu-mirror {
  position: fixed !important;
  margin: 0 !important;
  z-index: 9999 !important;
  opacity: 0.8;
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=80)';
  filter: alpha(opacity=80);
  pointer-events: none;
}

/* high-performance display:none; helper */

.gu-hide {
  left: -9999px !important;
}

/* added to mirrorContainer (default = body) while dragging */

.gu-unselectable {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* added to the source element while its mirror is dragged */

.gu-transit {
  opacity: 0.2;
  -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=20)';
  filter: alpha(opacity=20);
}

.empty-zone {
  background-color: #f1f1f1;
  padding: 10px;
  min-height: 30px;
  border: 1px dashed gray;
  outline: none;
}

.empty-zone p {
  text-align: center;
}

.table-container {
  display: flex;
}

.table-container table {
  border-collapse: collapse;
}

.table-container tr:first-child {
  background: none;
}

.table-container th {
  text-align: center;
}

.table-container td {
  color: #666;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

.table-container td:first-child {
  border-color: 1px solid #f1f1f1;
}

.table-container td:last-child {
  border-color: 1px solid #f1f1f1;
}

.steps ul.revDisable {
  opacity: 0.4;
  background: transparent;
  line-height: 32px;
  margin-left: 0;
  margin-top: 15px;
  font-size: 14px;
}

.steps ul.revDisable li::after {
  display: none;
}

.light-border {
  border-style: double;
  border-color: 1px #000;
}

@media print {
  @page {
    size: portrait;
    -webkit-print-color-adjust: exact;
    margin: 10px;
    min-height: 0;
  }

  html,
  body {
    height: 100%;
    -webkit-print-color-adjust: exact;
  }

  .print-title {
    font-size: 15px;
    font-weight: 700;
    line-height: 5px;
    padding-top: 5px;
  }

  .print-line-space {
    line-height: 16px;
  }

  .print-less-peding {
    padding: 12px 7px 7px 7px !important;
    background-color: #f1f1f1;
  }

  .print-friendly {
    -webkit-column-break-inside: avoid;
  }

  label {
    min-width: 150px !important;
    display: inline-block !important;
  }

  .lbl-view {
    margin-bottom: 10px !important;
  }
}

.no-records {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f44236;
  color: #fff;
}

/* Comperation css strat*/

.comparions-lbl mat-label {
  font-weight: 700;
}

.comparions-workflow {
  min-height: 176px;
}

.comparions-appinfo {
  min-height: 610px;
}

.comparions-pluginfo {
  min-height: 269px;
}

.comparions-closure {
  min-height: 176px;
}

.comparions-thermosate {
  min-height: 31px;
}

.comparions-sensors {
  min-height: 100px;
  max-height: 100px;
  overflow-y: auto;
}

.comparions-material {
  min-height: 100px;
  max-height: 100px;
  overflow-y: auto;
}

.loading-icon img {
  height: 35px;
  width: 35px;
  margin-right: 20px;
}

.under-line {
  border-bottom: 1px solid #cccccc;
  width: 100%;
}

.lbl-red {
  background-color: red;
  border-radius: 5px;
  color: white;
  padding: 1px;
}

.text-center {
  text-align: center;
}

.mt-5 {
  margin-top: 5px;
}

.error-msg {
  color: red;
}

.display-box {
  border: 1px solid;
  padding: 0px 10px;
  min-height: 20px;
}

.print-page label {
  min-width: 200px;
  display: inline-block;
}

.print-page .lbl-view .lbl {
  margin-bottom: 10px;
}

.fix-nav {
  position: fixed !important;
}

.page-not-found {
  line-height: normal !important;
  text-align: center;
  padding-top: 100px;
}

.mx-auto {
  margin-left: auto !important;
}

.error-404 {
  color: #5a5c69;
  font-size: 7rem;
  position: relative;
  line-height: 1;
  width: 12.5rem;
}

@-webkit-keyframes noise-anim {
  0% {
    clip: rect(89px, 9999px, 5px, 0);
  }

  5% {
    clip: rect(92px, 9999px, 71px, 0);
  }

  10% {
    clip: rect(63px, 9999px, 67px, 0);
  }

  15% {
    clip: rect(85px, 9999px, 47px, 0);
  }

  20% {
    clip: rect(76px, 9999px, 50px, 0);
  }

  25% {
    clip: rect(99px, 9999px, 10px, 0);
  }

  30% {
    clip: rect(76px, 9999px, 83px, 0);
  }

  35% {
    clip: rect(5px, 9999px, 45px, 0);
  }

  40% {
    clip: rect(3px, 9999px, 11px, 0);
  }

  45% {
    clip: rect(1px, 9999px, 87px, 0);
  }

  50% {
    clip: rect(56px, 9999px, 79px, 0);
  }

  55% {
    clip: rect(12px, 9999px, 54px, 0);
  }

  60% {
    clip: rect(38px, 9999px, 21px, 0);
  }

  65% {
    clip: rect(5px, 9999px, 15px, 0);
  }

  70% {
    clip: rect(20px, 9999px, 75px, 0);
  }

  75% {
    clip: rect(14px, 9999px, 33px, 0);
  }

  80% {
    clip: rect(70px, 9999px, 79px, 0);
  }

  85% {
    clip: rect(68px, 9999px, 78px, 0);
  }

  90% {
    clip: rect(96px, 9999px, 69px, 0);
  }

  95% {
    clip: rect(16px, 9999px, 95px, 0);
  }

  100% {
    clip: rect(96px, 9999px, 60px, 0);
  }
}

@keyframes noise-anim {
  0% {
    clip: rect(89px, 9999px, 5px, 0);
  }

  5% {
    clip: rect(92px, 9999px, 71px, 0);
  }

  10% {
    clip: rect(63px, 9999px, 67px, 0);
  }

  15% {
    clip: rect(85px, 9999px, 47px, 0);
  }

  20% {
    clip: rect(76px, 9999px, 50px, 0);
  }

  25% {
    clip: rect(99px, 9999px, 10px, 0);
  }

  30% {
    clip: rect(76px, 9999px, 83px, 0);
  }

  35% {
    clip: rect(5px, 9999px, 45px, 0);
  }

  40% {
    clip: rect(3px, 9999px, 11px, 0);
  }

  45% {
    clip: rect(1px, 9999px, 87px, 0);
  }

  50% {
    clip: rect(56px, 9999px, 79px, 0);
  }

  55% {
    clip: rect(12px, 9999px, 54px, 0);
  }

  60% {
    clip: rect(38px, 9999px, 21px, 0);
  }

  65% {
    clip: rect(5px, 9999px, 15px, 0);
  }

  70% {
    clip: rect(20px, 9999px, 75px, 0);
  }

  75% {
    clip: rect(14px, 9999px, 33px, 0);
  }

  80% {
    clip: rect(70px, 9999px, 79px, 0);
  }

  85% {
    clip: rect(68px, 9999px, 78px, 0);
  }

  90% {
    clip: rect(96px, 9999px, 69px, 0);
  }

  95% {
    clip: rect(16px, 9999px, 95px, 0);
  }

  100% {
    clip: rect(96px, 9999px, 60px, 0);
  }
}

.error-404:after {
  content: attr(data-text);
  position: absolute;
  left: 2px;
  text-shadow: -1px 0 #e74a3b;
  top: 0;
  color: #5a5c69;
  background: transparent;
  overflow: hidden;
  clip: rect(0, 900px, 0, 0);
  animation: noise-anim 2s infinite linear alternate-reverse;
}

@-webkit-keyframes noise-anim-2 {
  0% {
    clip: rect(50px, 9999px, 32px, 0);
  }

  5% {
    clip: rect(32px, 9999px, 76px, 0);
  }

  10% {
    clip: rect(28px, 9999px, 55px, 0);
  }

  15% {
    clip: rect(64px, 9999px, 11px, 0);
  }

  20% {
    clip: rect(23px, 9999px, 53px, 0);
  }

  25% {
    clip: rect(69px, 9999px, 48px, 0);
  }

  30% {
    clip: rect(84px, 9999px, 66px, 0);
  }

  35% {
    clip: rect(66px, 9999px, 70px, 0);
  }

  40% {
    clip: rect(21px, 9999px, 86px, 0);
  }

  45% {
    clip: rect(70px, 9999px, 85px, 0);
  }

  50% {
    clip: rect(40px, 9999px, 4px, 0);
  }

  55% {
    clip: rect(62px, 9999px, 47px, 0);
  }

  60% {
    clip: rect(17px, 9999px, 97px, 0);
  }

  65% {
    clip: rect(57px, 9999px, 94px, 0);
  }

  70% {
    clip: rect(78px, 9999px, 85px, 0);
  }

  75% {
    clip: rect(97px, 9999px, 48px, 0);
  }

  80% {
    clip: rect(8px, 9999px, 31px, 0);
  }

  85% {
    clip: rect(14px, 9999px, 17px, 0);
  }

  90% {
    clip: rect(92px, 9999px, 92px, 0);
  }

  95% {
    clip: rect(95px, 9999px, 63px, 0);
  }

  100% {
    clip: rect(49px, 9999px, 11px, 0);
  }
}

@keyframes noise-anim-2 {
  0% {
    clip: rect(50px, 9999px, 32px, 0);
  }

  5% {
    clip: rect(32px, 9999px, 76px, 0);
  }

  10% {
    clip: rect(28px, 9999px, 55px, 0);
  }

  15% {
    clip: rect(64px, 9999px, 11px, 0);
  }

  20% {
    clip: rect(23px, 9999px, 53px, 0);
  }

  25% {
    clip: rect(69px, 9999px, 48px, 0);
  }

  30% {
    clip: rect(84px, 9999px, 66px, 0);
  }

  35% {
    clip: rect(66px, 9999px, 70px, 0);
  }

  40% {
    clip: rect(21px, 9999px, 86px, 0);
  }

  45% {
    clip: rect(70px, 9999px, 85px, 0);
  }

  50% {
    clip: rect(40px, 9999px, 4px, 0);
  }

  55% {
    clip: rect(62px, 9999px, 47px, 0);
  }

  60% {
    clip: rect(17px, 9999px, 97px, 0);
  }

  65% {
    clip: rect(57px, 9999px, 94px, 0);
  }

  70% {
    clip: rect(78px, 9999px, 85px, 0);
  }

  75% {
    clip: rect(97px, 9999px, 48px, 0);
  }

  80% {
    clip: rect(8px, 9999px, 31px, 0);
  }

  85% {
    clip: rect(14px, 9999px, 17px, 0);
  }

  90% {
    clip: rect(92px, 9999px, 92px, 0);
  }

  95% {
    clip: rect(95px, 9999px, 63px, 0);
  }

  100% {
    clip: rect(49px, 9999px, 11px, 0);
  }
}

.error-404:before {
  content: attr(data-text);
  position: absolute;
  left: -2px;
  text-shadow: 1px 0 #4e73df;
  top: 0;
  color: #5a5c69;
  background: transparent;
  overflow: hidden;
  clip: rect(0, 900px, 0, 0);
  animation: noise-anim-2 3s infinite linear alternate-reverse;
}

.search-result {
  max-height: 360px;
  overflow-y: auto;
}

.product-img {
  display: inline-block;
  width: 200px;
  height: 250px;
  overflow: hidden;
}

.jumper-product-img {
  display: inline-block;
  width: 200px;
  height: 160px;
  overflow: hidden;
}

.lap-result {
  width: 85% !important;
  overflow-x: auto;
}

.col {
  inline-size: max-content;
}

.cal span {
  width: 60px;
  float: left;
}

.mb-20 {
  margin-bottom: 20px;
}

.text-warp {
  white-space: nowrap;
}

.container p {
  line-height: 20px;
  font-size: 14px;
}

.fullWidth {
  width: 100%;
}

.sfl-mr-2 {
  margin: 2% !important;
}

.read-text {
  line-height: 25px;
  font-size: 14px;
}

.dmtlog {
  margin-left: 10px;
  margin-top: -10px;
  line-height: normal;
}

.bom-sync-status {
  color: green;
  background-color: #fafafa;
  border-left: 2px solid green;
  padding: 3px 15px;
  text-align: left;
  white-space: nowrap;
  font-weight: 700;
}

.bom-sync-status-jacket-list {
  color: green;
}

.bom-sync-status:hover {
  color: green;
  background-color: #fff;
  border-left: 5px solid green;
  padding: 3px 15px;
  text-align: left;
  white-space: nowrap;
  font-weight: 700;
}

.sync-error {
  color: red;
  background-color: #fafafa;
  border-left: 2px solid red;
  padding: 3px 15px;
  text-align: left;
  white-space: nowrap;
  font-weight: 700;
}

.sync-progress {
  color: orange;
  background-color: #fafafa;
  border-left: 2px solid orange;
  padding: 3px 15px;
  text-align: left;
  white-space: nowrap;
  font-weight: 700;
}

.sync-error-jacket-list {
  color: red;
}

.sync-inprogress-jacket-list {
  color: #ebe046;
}

.sync-null-jacket-list {
  color: grey
}

.refresh-icon{
  cursor: pointer;
}

.sync-status{
  margin-left: 18px;
  margin-top: 15px;
}

.sync-error:hover {
  color: red;
  border-left: 5px solid red;
  background-color: #fafafa;
  padding: 3px 15px;
  text-align: left;
  white-space: nowrap;
  font-weight: 700;
}

.sync-progress:hover {
  color: orange;
  border-left: 5px solid orange;
  background-color: #fafafa;
  padding: 3px 15px;
  text-align: left;
  white-space: nowrap;
  font-weight: 700;
}

.mat-cell {
  padding-right: 10px;
  -webkit-box-flex: 1;
  flex: 1;
  -webkit-box-align: center;
  align-items: center;
  text-align: center;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-all;
  min-height: inherit;
  text-overflow: ellipsis;
  line-height: 1.5;
}

.offline-notification {
  position: fixed;
  bottom: 0px;
  background: #f44336;
  width: 100%;
  color: #ffffff;
  font-size: 15px;
  padding: 15px 0px;
  text-align: center;
  z-index: 99;
}

.offline-notification mat-icon {
  font-size: 20px;
  vertical-align: middle;
}

.features div {
  padding: 5px 0px 5px 0px;
}

.sub-heder span {
  font-size: 15px;
}

.hide {
  display: none;
}

.error-code {
  padding: 20px 20px 20px 20px;
}

.inactive-onhold-costreport {
  background: #ff7e7e;
}

.cost-report-tooltip {
  font-size: 12px;
  line-height: 14px;
}
.sfl-cost-report-model .mat-dialog-container {
  overflow: hidden;
}

.cust-table table {
  border-spacing: 0;
}
.cust-table tbody tr {
  border-bottom: 2px solid #f1f1f1;
}
.cust-table .mat-header-row {
  height: 32px;
}
tr.inner-row {
  text-indent: 25px;
}

tr.inner-row .mat-cell {
  font-weight: bold;
}

span.error-cell {
  float: left;
}

.cust-table .mat-header-cell {
  text-align: center;
}

.search-results {
  height: calc(100vh - 260px);
  overflow-y: scroll;
  overflow-x: hidden;
}

mat-progress-spinner {
  position: absolute;
  margin: -1px auto;
}

.no-jackets {
  margin: 100px auto;
}

.clickable-box {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  bottom: 0;
}

// Part number generation
input.sfl-sequ-number {
  text-align: left;
}
input.sfl-part-no {
  text-align: left;
}
.sfl-prt-no-fld .mat-form-field-wrapper {
  width: 62%;
}
.sfl-seq-no-fld .mat-form-field-wrapper {
  width: 48%;
}
label.seperator {
  font-weight: bold;
  margin: 0 2px;
}
.rootPN-message {
  font-size: 14px;
  line-height: 21px;
}
.partnumberBox {
  border: 1px solid #b0b0b0;
  border-radius: 5px;
  padding: 4px;
}
.partnumberBox:hover,
.partnumberBox:focus {
  border: 1px solid #fff;
}
@media screen and (max-width: 1400px) {
  .sfl-part-no::placeholder,
  .sfl-sequ-number::placeholder {
    font-size: 9px;
  }
}

// Gold Report
.gold-report-container {
  height: 100%;
  overflow: hidden;
}

.quotes-table100 {
  height: 100%;
  overflow-y: auto;
  margin-bottom: 20px;
}
.quotes-table50 {
  height: 170px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.gold-report {
  height: 300px;
  overflow-y: auto;
}

.sfl-spinner-loader {
  position: absolute !important;
  margin: 0 auto;
  left: 0;
  top: 50%;
  bottom: 0;
  right: 0;
  z-index: 1;
}
.sfl-global-spinner-loader {
  left: 50%;
  top: 50%;
  position: absolute;
  z-index: 101;
  width: 32px;
  height: 32px;
  margin-left: -16px;
  margin-top: -16px;
}
.sfl-loading {
  display: block;
  position: fixed !important;
  z-index: 100;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.16);
}

.download-gold-report {
  display: block;
  width: 100%;
}

.text-right {
  text-align: right;
}

.selected {
  background: #c7c7c7;
}
.take-awhile {
  color: #f44336;
  font-size: 15px;
  float: right;
}
.material-icons.info {
  color: #f44336;
  padding: 0;
  margin: 0;
  cursor: pointer;
  position: relative;
}
.sfl-gold-report-tool-tip-model h3 {
  line-height: 22px;
}

.sfl-create-quotation-alert {
  line-height: 30px;
}

.swal2-container .sfl-create-quotation-alert.swal2-popup .swal2-title {
  font-size: 20px;
}
/* ECR Management */
.mat-list-selected-pn mat-list-item.mat-list-item {
  height: 32px;
}
.sfl-mat-list .mat-list {
  overflow: auto;
}

.ecr-report .sfl-mat-list .mat-list,
.eco-report .sfl-mat-list .mat-list {
  height: 150px;
  overflow-y: auto;
  overflow-x: hidden;
}
.sfl-textarea {
  padding: 5px 5px 0 !important;
  resize: none !important;
  background: #e8e8e8 !important;
}
.sfl-ecr-management .mat-form-field-infix {
  width: 100%;
}
span.active-list-item {
  padding: 10px;
  background: #5a5a5a;
  color: #fff;
}
.sync-check label {
  float: right;
}
.disable-section {
  pointer-events: none;
}
.critical-check {
  margin-top: 7px;
}
.eco-checks {
  margin-left: 15px;
}
.pb-15 {
  margin: 15px 0;
  border-bottom: 1px solid #e8e8e8;
}
.mat-tab-group.mat-primary .mat-ink-bar,
.mat-tab-nav-bar.mat-primary .mat-ink-bar {
  background-color: #e74033;
}
.eco-matGroup .mat-tab-header {
  margin-bottom: 20px;
}
.mat-tab-label {
  background: #eee;
  border: 1px solid #dadada;
}

.drp-dwn-text .mat-select-arrow {
  visibility: hidden;
}
.add-new-icon-button {
  width: 24px !important;
  height: 24px !important;
  line-height: 24px !important;
}
.eco-report .add-new-icon-button {
  width: 50px !important;
  height: 50px !important;
  line-height: 50px !important;
}
.sfl-mat-list {
  background: #eee;
  padding: 5px 5px;
  height: 106px;
  overflow-y: auto;
  overflow-x: hidden;
}
.sfl-mat-list-ecr {
  background: #eee;
  height: 250px;
  overflow-y: auto;
  overflow-x: hidden;
}
.eco-report .sfl-mat-list {
  background: #e8e8e8;
  padding: 5px 5px;
  height: 122px;
}
.text-ellipsis {
  white-space: nowrap;
  width: 165px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
}
.search-user-input {
  position: fixed;
}
.generate-ecr .mat-text-warn {
  font-size: 12px;
  display: block;
  line-height: 16px;
}
.sfl-upload i {
  text-align: center;
  width: 100%;
  font-size: 100px;
}
.sfl-upload.form_file p {
  padding-top: 0;
}
.sfl-mat-list.pqp-family {
  height: 120px;
}
.generate-ecr .sfl-mat-list {
  height: 157px;
}

.critical-check .mat-checkbox-inner-container {
  margin-left: 15px;
}

.sfl-ecr-management .cust_fields .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 1em 0 1em 0;
  font-size: inherit;
}

// pre-loader css
@keyframes placeHolderShimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}
.linear-background {
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: placeHolderShimmer;
  animation-timing-function: linear;
  background: #f6f7f8;
  background: linear-gradient(to right, #eeeeee 8%, #dddddd 18%, #eeeeee 33%);
  background-size: 1000px 104px;
  height: 100px;
  position: relative;
  overflow: hidden;
  margin: 15px 0;
}
.inter-right--top {
  background: #fff;
  width: 100%;
  height: 20px;
  position: absolute;
  top: 20px;
  left: 0;
}
.inter-right--bottom {
  background: #fff;
  width: 100%;
  height: 50px;
  position: absolute;
  top: 60px;
  left: 0;
}

// epicore sync loader
.ecr-pn-sync .loading-icon img {
  height: 20px;
  width: 20px;
  margin-right: 10px;
}

.initial-name {
  font-size: 13px;
  display: block;
  line-height: 17px;
}
.sfl-initial-name-model .mat-form-field-wrapper {
  padding: 0;
}

// global search loader
.sfl-global-search-loader {
  width: 20px;
  position: absolute;
  right: 35px;
  top: 12px;
}

.sfl-cust-info-cust-height {
  height: 264px;
}
// Master data BHX Material
mat-card.cust_table.bhx-material-tbl-header.mat-card {
  overflow: hidden;
}
@media screen and (min-width: 1366px) {
  mat-card.cust_table.bhx-material-tbl-header.mat-card {
    height: 86vh;
  }
}
@media screen and (min-width: 1920px) {
  mat-card.cust_table.bhx-material-tbl-header.mat-card {
    height: 90vh;
  }
}
.bhx-material-tbl-header {
  overflow-x: scroll;
}

.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-description.mat-column-description,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-formula.mat-column-formula,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-sensorType.mat-column-sensorType,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-sensConn.mat-column-sensConn,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-plug.mat-column-plug,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-partNumber.mat-column-partNumber,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-customer.mat-column-customer,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-elementType.mat-column-elementType,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-wireType.mat-column-wireType,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-connector.mat-column-connector,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-jacketType.mat-column-jacketType,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-grouping.mat-column-grouping,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-sleeving.mat-column-sleeving,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-phase.mat-column-phase,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-installationMethod.mat-column-installationMethod,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-strainRelief.mat-column-strainRelief,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-manualResetThermostat.mat-column-manualResetThermostat,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-privateLabel.mat-column-privateLabel,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-appType.mat-column-appType,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-controller.mat-column-controller,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-operationName.mat-column-operationName,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-type.mat-column-type,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-greenLight.mat-column-greenLight,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-productType.mat-column-productType,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-layered.mat-column-layered,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-closure.mat-column-closure,
.cust_table.bhx-material-tbl mat-cell.mat-cell.cdk-column-leadTypes.mat-column-leadTypes,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-customerName.mat-column-customerName,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-projectTitle.mat-column-projectTitle,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-quotationNumber.mat-column-quotationNumber,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-productType.mat-column-productType,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-currentStatusComment.mat-column-currentStatusComment,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-designStatusId.mat-column-designStatusId,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-salesRepId.mat-column-salesRepId,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-folderLocation.mat-column-folderLocation,
.cust_table.quote-tracker-tbl mat-cell.mat-cell.cdk-column-folderLocation.mat-column-folderLocation {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.label-material-tbl .mat-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.final-review-screen .cust_table .mat-cell {
  text-align: left;
}

// open file path edit ccs
.sfl-tree-invisible {
  display: none;
}

.sfl-tree ul,
.sfl-tree li {
  margin-top: 0;
  margin-bottom: 0;
  list-style-type: none;
}

.sfl-type-icon {
  color: grey;
}

.sfl-item-position {
  margin-right: 10px;
}

.sfl-edit-btn {
  cursor: pointer;
  font-size: 18px;
  margin-right: 10px;
}

.sfl-add-btn {
  cursor: pointer;
  margin-top: -5px;
}

.sfl-delete-btn {
  cursor: pointer;
  font-size: 18px;
}

.formula-btn-1 button.mat-stroked-button.mat-warn {
  width: 23%;
  margin: 0 3px;
}

.label-btn button.mat-stroked-button.mat-warn {
  margin: 0 3px;
}
.sfl-add-formula-btn button {
  margin: 5px;
}
.sfl-formula-tooltip {
  font-size: 26px;
  width: auto;
  max-width: 100% !important;
  line-height: 23px;
}
// bhx material pageable css to fix in right
@media screen and (min-width: 1365px) {
  mat-table.mat-table {
    max-height: calc(100vh - 205px) !important;
  }
}

// highlight Element factor and Element grade fields
.highlight-red .mat-form-field-infix {
  background: #ffa8a2;
  color: #000;
  padding-left: 5px;
}
.blank-cards {
  // master data blank card to set the even layout
  padding: 24px;
  margin: 0.5rem;
}
.mat-primary .mat-pseudo-checkbox-checked,
.mat-primary .mat-pseudo-checkbox-indeterminate {
  background: #f44336;
}

// highlight Title block updated elements
.highlight-title-block-yellow input {
  background: #ffff00;
  padding: 5px 5px;
  border-radius: 4px;
}
.sfl-bhx-material-model mat-card-subtitle.mat-card-subtitle {
  font-size: 15px;
  color: #f44336;
  font-weight: 600;
}
.mat-dialog-container .mat-checkbox {
  line-height: 45px;
}
.mat-dialog-container .mat-slide-toggle {
  height: 45px;
}

// clear filter button x
.sfl-clear-filter-icon-btn mat-icon.mat-icon.material-icons {
  font-size: 15px;
}
.sfl-clear-filter-icon-btn .mat-button-focus-overlay {
  background-color: transparent;
}
.sfl-hide-row {
  display: none;
}
.final-review-bom-sync-status {
  color: green;
  padding: 3px 15px;
  text-align: left;
  white-space: nowrap;
  font-weight: 700;
}
.sfl-vn-stock {
  display: block;
  font-size: 11px;
}
.sfl-pull-right {
  float: right;
}
.sfl-pull-left {
  float: left;
}

// Quotation Tracker
.cust_table.quote-tracker-tbl mat-header-row.mat-header-row {
  padding: 8px 0;
}
.cust_table.quote-tracker-tbl mat-cell.mat-cell {
  text-align: left;
  display: block;
}
.cust_table.quote-tracker-tbl mat-header-cell.mat-header-cell {
  text-align: left;
  display: block;
}
mat-header-cell.mat-header-cell.cdk-column-folderSubmittedDate.mat-column-folderSubmittedDate {
  padding-right: 10px;
}
.highlight-total {
  color: #000;
  font-weight: bold;
  border-top: 1px solid #000;
}
.box-layout h4 {
  margin: 0px 10px;
  border: 1px solid #eee;
  font-size: 18px;
  padding: 5px 5px;
  margin-bottom: 8px;
}
.active-card {
  color: #f44336;
  background: #fff;
}
.active-card:after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 0;
  right: 0;
  border-top: 6px solid #e74133;
}
.qt-cnt {
  width: 15%;
  border: 1px solid #ffadad;
  padding: 10px;
  margin: 10px;
  border-radius: 5px;
}
.qt-cnt strong {
  float: right;
}
.add-btn{
  line-height: normal !important;
  padding: 8px !important;
}

// finalize screen
.finalize-mat-card {
  overflow-x: scroll;
}
@media screen and (max-width: 1919px) {
  .cust_table.finalize-table mat-table {
    width: 1800px;
  }
}
@media screen and (min-width: 1920px) {
  .cust_table.finalize-table mat-table {
    width: 1674px;
  }
}
.cust_table.finalize-table .mat-cell {
  text-align: left;
}

virtual-scroller.selfScroll {
  border: 1px solid rgb(209, 218, 223);
}
virtual-scroller.selfScroll.vertical {
  height: 70vh;
}

/*BriskHeat css end*/
