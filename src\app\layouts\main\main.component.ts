import { Component, AfterViewInit } from '@angular/core';
import { Router, NavigationStart, NavigationCancel, NavigationEnd } from '@angular/router';
import { NetworkService } from 'src/app/shared';

@Component({
    selector: 'sfl-app-main',
    templateUrl: './main.component.html'
})

export class MainComponent implements AfterViewInit {
    loading = true;

    isConnected = true;

    constructor(private router: Router,
        public network: NetworkService,
    ) { }

    ngAfterViewInit() {
        this.router.events
            .subscribe((event) => {
                if (event instanceof NavigationStart) {
                    this.loading = true;
                } else if (
                    event instanceof NavigationEnd ||
                    event instanceof NavigationCancel
                ) {
                    this.loading = false;
                }
            });
    }
}
