import { Component, OnInit, Inject, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';

@Component({
  selector: 'sfl-reset-tool-tip',
  templateUrl: './reset-tool-tip.component.html'
})
export class ResetToolTipComponent implements OnInit, OnDestroy {

  soNumber: string;
  constructor(public dialogRef: MatDialogRef<ResetToolTipComponent>, @Inject(MAT_DIALOG_DATA) data) {
    this.soNumber = data.soNumber;
  }

  ngOnInit() {
  }

  closeDialog() {
    this.dialogRef.close();
  }

  ngOnDestroy() { }

}
