import { Component, OnInit, ViewChild, OnD<PERSON>roy } from '@angular/core';
import { PowerCordVoltagesMaster, GenericPageable, PowerCordVoltagesFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManagePowerCordVoltagesComponent } from './manage-power-cord-voltages/manage-power-cord-voltages.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-power-cord-voltages',
  templateUrl: './power-cord-voltages.component.html'
})
export class PowerCordVoltagesComponent implements OnInit, OnDestroy {
  pageTitle = 'Power Cord Voltages Master';
  powerCordVoltages: PowerCordVoltagesMaster;
  powerCordVoltagesPageable: GenericPageable<PowerCordVoltagesMaster>;
  powerCordVoltagesDataSource = new MatTableDataSource<PowerCordVoltagesMaster>();
  powerCordVoltagesColumns = DisplayColumns.Cols.PowerCordVoltage;

  dataSource = new MatTableDataSource<PowerCordVoltagesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  powerCordVoltagesFilter: PowerCordVoltagesFilter = new PowerCordVoltagesFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldValue = Values.FilterFields.value;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getPowerCordVoltagesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new Power Cord Voltage
  addPowerCordVoltage() {
    this.editPowerCordVoltages(new PowerCordVoltagesMaster());
  }

  // used to add filter to Power Cord Voltage listing
  async addFilter() {
    this.filter =
      this.powerCordVoltagesFilter.value === '' ? [] : [{ key: this.filterFieldValue, value: this.powerCordVoltagesFilter.value }];
    this.getPowerCordVoltagesMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to Power Cord Voltage listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldValue,
        value: fieldToClear === this.filterFieldValue ? (this.powerCordVoltagesFilter.value = '') : this.powerCordVoltagesFilter.value
      }
    ];
    this.getPowerCordVoltagesMasterData(this.initialPageIndex, this.pageSize);
  }

  getPowerCordVoltagesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPowerCordVoltageMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<PowerCordVoltagesMaster>) => {
          this.powerCordVoltagesPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPowerCordVoltagesTable(this.powerCordVoltagesPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createPowerCordVoltagesTable(serviceRequestList: GenericPageable<PowerCordVoltagesMaster>) {
    this.powerCordVoltagesDataSource.data = serviceRequestList.content;
  }

  getPowerCordVoltagesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPowerCordVoltagesMasterData(this.pageIndex, this.pageSize);
  }

  getPowerCordVoltagesSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPowerCordVoltagesMasterData(this.pageIndex, this.pageSize);
  }

  editPowerCordVoltages(powerCordVoltages: PowerCordVoltagesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = powerCordVoltages;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-powerCordVoltages-master-model';
    const dialogRef = this.matDialog.open(ManagePowerCordVoltagesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          powerCordVoltages.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPowerCordVoltagesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deletePowerCordVoltage(powerCordVoltageId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePowerCordVoltage(powerCordVoltageId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPowerCordVoltagesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
