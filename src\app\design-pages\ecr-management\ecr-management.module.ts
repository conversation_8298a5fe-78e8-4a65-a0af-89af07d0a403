import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {EcrManagementComponent} from './ecr-management.component';
import {SharedModule} from 'src/app/shared/shared.module';
import {RouterModule} from '@angular/router';
import {EcrManagementRoutes} from './ecr-management.route';
import {GenerateNewEcrComponent} from './generate-new-ecr/generate-new-ecr.component';
import {AddPartnumberComponent} from './add-partnumber/add-partnumber.component';
import {EcoPlanComponent} from './eco-plan/eco-plan.component';
import {EcoWorkflowComponent} from './eco-plan/eco-workflow/eco-workflow.component';
import {EcoDesignActivityComponent} from './eco-plan/eco-design-activity/eco-design-activity.component';
import {EcoFinalReviewComponent} from './eco-plan/eco-final-review/eco-final-review.component';
import {EcoCostingComponent} from './eco-plan/eco-costing/eco-costing.component';
import {FinalEcrReviewComponent} from './eco-plan/final-review-ecr/final-ecr-review.component';
import {
  FinalEcrAddOperationComponent
} from './eco-plan/final-review-ecr/final-ecr-add-operation/final-ecr-add-operation.component';
import {FinalEcrAddLabelComponent} from './eco-plan/final-review-ecr/final-ecr-add-label/final-ecr-add-label.component';
import {
  FinalEcrAddMaterialComponent
} from './eco-plan/final-review-ecr/final-ecr-add-material/final-ecr-add-material.component';

@NgModule({
  imports: [RouterModule.forChild(EcrManagementRoutes), SharedModule],
  entryComponents: [GenerateNewEcrComponent, AddPartnumberComponent, FinalEcrAddOperationComponent, FinalEcrAddMaterialComponent, FinalEcrAddLabelComponent],
  declarations: [
    EcrManagementComponent,
    GenerateNewEcrComponent,
    AddPartnumberComponent,
    EcoPlanComponent,
    EcoWorkflowComponent,
    EcoDesignActivityComponent,
    EcoFinalReviewComponent,
    EcoCostingComponent,
    FinalEcrReviewComponent,
    FinalEcrAddOperationComponent,
    FinalEcrAddLabelComponent,
    FinalEcrAddMaterialComponent
  ],
  providers: [],
  exports: [EcrManagementComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ECRManagementModule {
}
