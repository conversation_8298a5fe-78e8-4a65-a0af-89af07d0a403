import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {catchError, map} from 'rxjs/operators';
import {AppConfig} from '../../../../app.config';
import {utils} from '../../../../shared/helpers';

@Injectable({
  providedIn: 'root'
})
export class SearchMfgPartService {
  constructor(private http: HttpClient) {
  }

  searchMFGPartNumber(partNumber) {
    return this.http.get(AppConfig.SEARCH_MFG_PART_NUMBER + partNumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
