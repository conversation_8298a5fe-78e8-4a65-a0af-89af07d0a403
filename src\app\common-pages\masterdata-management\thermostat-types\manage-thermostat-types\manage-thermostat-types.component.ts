import { Component, OnInit, Inject, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { ThermostatTypesMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-thermostat-types',
  templateUrl: './manage-thermostat-types.component.html'
})
export class ManageThermostatTypesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  thermostatType: ThermostatTypesMaster;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageThermostatTypesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.thermostatType = data;
    this.thermostatType.id ? (this.thermostatType.previousId = this.thermostatType.id) : (this.thermostatType.previousId = null);
  }

  ngOnInit() {
    this.thermostatType = this.thermostatType.id ? Object.assign({}, this.thermostatType) : new ThermostatTypesMaster();
    this.thermostatType.id ? (this.title = 'Update Thermostat Type') : (this.title = 'Add Thermostat Type');
  }

  updateThermostatType() {
    this.showLoader = true;
    if (this.thermostatType.previousId) {
      this.subscription.add(
        this.masterDataService.updateThermostatType(this.thermostatType).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addThermostatType(this.thermostatType).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
