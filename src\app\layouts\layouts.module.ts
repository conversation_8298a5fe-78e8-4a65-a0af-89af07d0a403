import {AddQuotationService} from './add-quotation/add-quotation.service';
import {NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';

import {SharedModule} from '../shared/shared.module';

import {errorRoute} from './error/error.route';
import {
  AccordionAnchorDirective,
  AccordionDirective,
  AccordionLinkDirective,
  AdminLayoutComponent,
  AdminNavbarComponent,
  ErrorComponent,
  FooterComponent,
  OutsideLayoutComponent,
  ServiceUnavailableComponent,
  TopbarComponent
} from '.';

import {AdminNavbarService} from './admin-layout/admin-navbar.service';
import {AddQuotationComponent} from './add-quotation/add-quotation.component';
import {DesignEngLayoutComponent} from './design-eng-layout/design-eng-layout.component';
import {DesignEngNavbarComponent} from './design-eng-layout/design-eng-navbar.component';
import {DesignEngNavbarService} from './design-eng-layout/design-eng-navbar.service';
import {DesignTopbarComponent} from './design-topbar/design-topbar.component';
import {SuperAdminLayoutComponent} from './super-admin-layout/super-admin-layout.component';
import {SuperAdminNavbarComponent} from './super-admin-layout/super-admin-layout-navbar.component';
import {SuperAdminNavbarService} from './super-admin-layout/super-admin-layout-navbar.service';
import {SuperAdminTopbarComponent} from './super-admin-topbar/super-admin-topbar.component';
import {MasterDataLayoutComponent} from './master-data-layout/master-data-layout.component';
import {MasterDataTopbarComponent} from './master-data-topbar/master-data-topbar.component';
import {MasterDataLayoutNavbarService} from './master-data-layout/master-data-layout-navbar.service';
import {MasterDataNavbarComponent} from './master-data-layout/master-data-navbar.component';
import {QuoteTrackerLayoutComponent} from './quote-tracker-layout/quote-tracker-layout.component';
import {QuoteTrackerNavbarComponent} from './quote-tracker-layout/quote-tracker-navbar.component';
import {QuoteTrackerTopbarComponent} from './quote-tracker-topbar/quote-tracker-topbar.component';
import {QuoteTrackerLayoutNavbarService} from './quote-tracker-layout/quote-tracker-layout-navbar.service';
import {SalesDeptTopbarComponent} from './sales-dept-topbar/sales-dept-topbar.component';
import {SalesDeptLayoutComponent} from './sales-dept-layout/sales-dept-layout.component';
import {SalesDeptNavbarComponent} from './sales-dept-layout/sales-dept-navbar.component';
import {SalesDeptLayoutNavbarService} from './sales-dept-layout/sales-dept-layout-navbar.service';
import {UnauthorizedPageComponent} from './unauthorized-page/unauthorized-page.component';
import {
  ViewJacketReferenceComponent
} from '../design-pages/design-plan/view-jacket-reference/view-jacket-reference.component';
import {SearchEcrComponent} from './search-ecr/search-ecr-component';
import {SearchMfgPartComponent} from '../design-pages/design-plan/bom-editor/search-mfg-part/search-mfg-part-component';

const LAYOUT_ROUTES = [...errorRoute];

@NgModule({
  imports: [RouterModule.forRoot(LAYOUT_ROUTES), SharedModule],
  declarations: [
    ErrorComponent,
    AccordionAnchorDirective,
    AccordionLinkDirective,
    AccordionDirective,

    TopbarComponent,
    DesignTopbarComponent,
    SuperAdminTopbarComponent,
    FooterComponent,

    AdminNavbarComponent,
    AdminLayoutComponent,

    DesignEngLayoutComponent,
    DesignEngNavbarComponent,

    SuperAdminLayoutComponent,
    SuperAdminNavbarComponent,

    OutsideLayoutComponent,
    ViewJacketReferenceComponent,
    AddQuotationComponent,
    ServiceUnavailableComponent,
    MasterDataLayoutComponent,
    MasterDataTopbarComponent,
    MasterDataNavbarComponent,
    QuoteTrackerLayoutComponent,
    QuoteTrackerNavbarComponent,
    QuoteTrackerTopbarComponent,
    SalesDeptLayoutComponent,
    SalesDeptNavbarComponent,
    SalesDeptTopbarComponent,
    UnauthorizedPageComponent,
    SearchEcrComponent,
    SearchMfgPartComponent
  ],
  entryComponents: [AddQuotationComponent, SearchEcrComponent, SearchMfgPartComponent],
  exports: [RouterModule, FooterComponent],
  providers: [
    AdminNavbarService,
    DesignEngNavbarService,
    SuperAdminNavbarService,
    AddQuotationService,
    MasterDataLayoutNavbarService,
    QuoteTrackerLayoutNavbarService,
    SalesDeptLayoutNavbarService
  ]
})
export class LayoutsModule {
}
