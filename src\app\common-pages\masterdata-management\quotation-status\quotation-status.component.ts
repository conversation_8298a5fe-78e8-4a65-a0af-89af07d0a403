import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { MatTableDataSource, MatSort, MatPaginator, MatDialogConfig, MatDialog } from '@angular/material';
import {
  QuotationStatusMaster,
  QuotationStatusMasterPageable,
  QuotationStatusFilter,
  GenericPageable
} from '../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageQuotStatusComponent } from './manage-quot-status/manage-quot-status.component';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-quotation-status',
  templateUrl: './quotation-status.component.html'
})
export class QuotationStatusComponent implements OnInit, OnDestroy {
  pageTitle = 'Quotation Statuses';
  quotationStatus: QuotationStatusMaster;
  quotationStatusPageable: GenericPageable<QuotationStatusMaster>;
  quotationStatusDataSource = new MatTableDataSource<QuotationStatusMaster>();
  quotStatusColumns = DisplayColumns.Cols.QuotationStatusMaster;
  quotStatusFilter: QuotationStatusFilter = new QuotationStatusFilter();

  dataSource = new MatTableDataSource<QuotationStatusMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  length: number;

  showLoader = false;
  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  quotTypes = Values.QuotStatusTypes;
  filterFieldStatus = Values.FilterFields.status;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getMasterDataListing(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new Quotation status
  addQuotStatus() {
    this.editQuotStatus(new QuotationStatusMaster(), false);
  }
  // used to add filter to Quotation status listing
  async addFilter() {
    this.filter = this.quotStatusFilter.status === '' ? [] : [{ key: this.filterFieldStatus, value: this.quotStatusFilter.status }];
    this.getMasterDataListing(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of Quotation status listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldStatus,
        value: fieldToClear === this.filterFieldStatus ? (this.quotStatusFilter.status = '') : this.quotStatusFilter.status
      }
    ];
    this.getMasterDataListing(this.initialPageIndex, this.pageSize);
  }

  getMasterDataListing(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getAllStatusesFromApi(this.filter, pageable).subscribe(
        (res: GenericPageable<QuotationStatusMaster>) => {
          this.quotationStatusPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createQuotStatusTable(this.quotationStatusPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.quotationStatusDataSource.data = [];
        }
      )
    );
  }

  createQuotStatusTable(serviceRequestList: GenericPageable<QuotationStatusMaster>) {
    this.quotationStatusDataSource.data = serviceRequestList.content;
  }

  getQuotStatePagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getMasterDataListing(this.pageIndex, this.pageSize);
  }

  getQuotStateSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getMasterDataListing(this.pageIndex, this.pageSize);
  }

  editQuotStatus(quotStatus: QuotationStatusMaster, isEdit = true) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { quotStatus: quotStatus, isEdit: isEdit };
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-quot-status-model';
    const dialogRef = this.matDialog.open(ManageQuotStatusComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          isEdit
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getMasterDataListing(this.pageIndex, this.pageSize);
      }
    });
  }

  async deleteQuotStatus(quotStatusId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteQuotationStatus(quotStatusId).subscribe(
        res => {
          this.quotationStatusPageable.content.splice(this.quotationStatusPageable.content.map(x => x.id).indexOf(quotStatusId), 1);
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getMasterDataListing(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
