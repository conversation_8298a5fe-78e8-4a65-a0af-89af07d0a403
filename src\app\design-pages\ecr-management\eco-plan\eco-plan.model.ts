export class EcoDto {
    constructor(
        public id?: number,
        public creationDate?: string,
        public changeType?: string,
        public criticalPart?: boolean,
        public coverPageGreenSheets?: boolean,
        public openSo?: string,
        public openJob?: boolean,
        public notesVietnam?: string,
        public finalNoticeSentOn?: string,
        public ecoCreator?: number,
        public finalNoticeSentBy?: number,
        public ecoStakeholderDTOS: EcoStakeholderDTOS[] = [],
        public ecrId?: number
    ) { }
}
export class EcoStakeholderDTOS {
    constructor(
        public id?: number,
        public userId?: number,
        public ecoId?: number
    ) { }
}

export class PqpDTOS {
    constructor(
        public id: number = null,
        public mtbfGoal: number = null,
        public confidence: number = null,
        public newPqpFamilyRequired: boolean = null,
        public contactedQuality: string = null,
        public stayAsNonPqp: boolean = null,
        public pqpComment: string = null,
        public ecoId: number = null,
        public pqpFamilies: PqpFamilyDTOS[] = [],
    ) { }
}

export class PqpFamilyDTOS {
    constructor(
        public id?: number,
        public name?: string,
        public ecoPqpId?: number
    ) { }
}

export class FinalReviewDto {
    constructor(
        public bomReviewed: boolean = null,
        public comment: string = null,
        public customerPdfUpdated: boolean = null,
        public designReleasedToPatterns: boolean = null,
        public ecoId: number = null,
        public elementDesignReviewed: boolean = null,
        public engineeringCostSpreadsheet: boolean = null,
        public heaterManualVerified: boolean = null,
        public id: number = null,
        public labelReviewed: boolean = null,
        public partsFullyActiveInEpicore: boolean = null,
        public reviewDate: string = null,
        public reviewedBy: boolean = null,
        public tsDoesNotInterfere: boolean = null
    ) { }
}

export class WorkFlowMasterDataDto {
    constructor(
        public childWorkFlows?: ChildWorkflow,
        public id?: number,
        public title?: string,
        public description?: string,
        public status?: boolean,
        public isActive: boolean = null,
        public completedBy: number = null,
        public completedDate:any = null,
        public ecoId: number = null,
        public workflowId: number = null,
        public ecoWorkflowId: number = null,
        public ecrId?: number,
        public newEcrId?: number

    ) { }
}

export class ChildWorkflow {
  constructor(
    public childWorkFlows?: ChildWorkflow,
      public id?: number,
      public title?: string,
      public description?: string,
      public status?: boolean,
      public isActive: boolean = null,
      public completedBy: number = null,
      public completedDate: string = null,
      public ecoId: number = null,
      public workflowId: number = null,
      public ecoWorkflowId: number = null,
      public ecrId?: number,
      public newEcrId?: number

  ) { }
}

export class EcoWorkFlowDto {
    constructor(
        public completedBy: number = null,
        public completedDate: string = null,
        public ecoId: number = null,
        public id: number = null,
        public workflowId: number = null
    ) { }
}

export class DesignActivityMasterDataDto {
    newEcrId: any;
  isChecked: any;
    constructor(
        public id?: number,
        public title?: string,
        public childDesignActivity: ChildDesignActivity[] = [],
        public description?: string,
        public status?: boolean,
        public isActive: boolean = null,
        public designedBy: number = null,
        public designDate: any = null,
        public reviewedBy: number = null,
        public reviewDate: any = null,
        public ecoId: number = null,
        public designActivityId: number = null,
        public ecoDesignActivityId: number = null,
        public comment: number = null,
        public infromQuality: number = null,
        public informDate: string = null,
        public informQualityEmail: number = null,
    ) { }
}

export class ChildDesignActivity {
  description: string;
}

export class DesignActivityDto {
    newEcrId: any;
    constructor(
        public designedBy: number = null,
        public designDate: string = null,
        public reviewedBy: number = null,
        public reviewDate: string = null,
        public ecoId: number = null,
        public id: number = null,
        public designActivityId: number = null,
        public comment: string = null,
        public informDate: string = null,
        public informQualityEmail: number = null,
    ) { }
}

export class ECOCostReportDto {
    constructor(
        public burdenCost?: number,
        public inActive?: boolean,
        public laborCost?: number,
        public listPrice?: number,
        public margin?: number,
        public materialCost?: number,
        public mtlBurCost?: number,
        public netPrice?: number,
        public onHold?: boolean,
        public partNumber?: string,
        public subConstCost?: number,
        public totalCost?: number,
        public status?: string,
        public childCostDetailsDTOS?: EcoChildCostDto[],
        public valid?: boolean
    ) { }
}

export class EcoChildCostDto {
    constructor(
        public partNumber?: string,
        public listPrice?: number,
        public netPrice?: number,
        public materialCost?: number,
        public laborCost?: number,
        public burdenCost?: number,
        public subConstCost?: number,
        public mtlBurCost?: number,
        public totalCost?: number,
        public margin?: number,
        public status?: string
    ) { }
}
