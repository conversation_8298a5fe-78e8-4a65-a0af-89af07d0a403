import { JumperPlug, LeadPlug, LeadType } from "src/app/admin-pages/new-quotation/ccdc-model/ccdc.model";

export class CcdcTemplateDTO {
  constructor(
    public id?: number,
    public name?: string,
    public measurementUnit?: string,
    public temperatureUnit?: string,
    public notes?: string,
    public sensorNotes?: string,
    public thermostatNotes?: string,
    public materialNotes?: string,
    public userId?: number,
    public description?: string,
    public applicationInfoTemplateDTO: ApplicationInfoTemplateDTO = new ApplicationInfoTemplateDTO(),
    public closureTemplateDTO: ClosureTemplateDTO = new ClosureTemplateDTO(),
    public materialInfoTemplateDTOS = new Array<MaterialInfoTemplateDTO>(),
    public pluggingInformationTemplateDTO: PluggingInformationTemplateDTO = new PluggingInformationTemplateDTO(),
    public sensorsInformationTemplateDTOS?: SensorsInformationTemplateDTO[],
    public thermostatInformationTemplateDTOS?: ThermostatInformationTemplateDTO,
  ) {

  }
}

export class ApplicationInfoTemplateDTO {
  constructor(
    public ccdcTemplateId?: number,
    public contentDensity?: number,
    public contentId?: number,
    public contentMotion?: string,
    public contentMotionFlowingRate?: number,
    public contentName?: string,
    public contentSpecificHeat?: string,
    public controlType?: string,
    public heatupFrom?: number,
    public heatupIn?: number,
    public heatupTo?: number,
    public id?: number,
    public jacketType?: string,
    public materialDensity?: number,
    public contentHeat?: number,
    public jacketGroupId?: number,
    public materialId?: number,
    public materialName?: string,
    public materialHeat?: number,
    public materialSpecificHeat?: string,
    public maxExposureTemp?: number,
    public minAmbientTemp?: number,
    public notes?: string,
    public operatingTemp?: number,
    public otherContentDensity?: number,
    public otherContentHeat?: number,
    public otherControlType?: string,
    public otherMaterialDensity?: number,
    public otherMaterialHeat?: number,
    public phase?: string,
    public pipeThickness?: number,
    public productType?: string,
    public voltage?: number,
    public wattDensity?: number,
    public fitTypeEnum?: string,
  ) { }
}

export class ClosureTemplateDTO {
  constructor(
    public cat5Tunnel?: boolean,
    public ccdcTemplateId?: number,
    public closureMaterialCostPerSq?: number,
    public closureMaterialId?: number,
    public closureMaterialImageUrl?: string,
    public closureMaterialMaxTemp?: string,
    public closureMaterialName?: string,
    public estSurfaceTemp?: number,
    public extendedFlap?: boolean,
    public id?: number,
    public notes?: string,
    public otherClosure?: string,
    public otherClosureCost?: number,
    public otherClosurePartNumber?: string,
  ) { }
}

export class MaterialInfoTemplateReq {
  constructor(public materialInfoDTOList?: MaterialInfoTemplateDTO[], public notes?: string) { }
}

export class MaterialInfoTemplateDTO {
  constructor(
    public ccdcTemplateId?: number,
    public costPerSq?: number,
    public id?: number,
    public imageUrl?: string,
    public inventory?: string,
    public layerName?: string,
    public material?: string,
    public materialId?: number,
    public materialInfoIndex?: number,
    public maxTemp?: string,
    public maxTempF?: string,
    public otherFacing?: string,
    public otherFacingCost?: string,
    public otherFacingPartNumber?: string,
    public otherInsulation?: string,
    public otherInsulationCost?: string,
    public otherInsulationPartNumber?: string,
    public otherLiner?: string,
    public otherLinerCost?: string,
    public otherLinerPartNumber?: string,
    public partNumber?: string,
  ) { }
}

export class PluggingInformationTemplateDTO {
  constructor(
    public ccdcTemplateId?: number,
    public greenLightId?: number,
    public greenLightName?: string,
    public id?: number,
    public jumperPlugDTO: JumperPlug = new JumperPlug(),
    public leadPlugDTO: LeadPlug = new LeadPlug(),
    public leadTypeDTO: LeadType = new LeadType(),
    public notes?: string,
    public otherConnector?: string,
    public otherConnectorCost?: number,
    public otherConnectorPartNumber?: string,
    public otherGreenLight?: string,
    public otherGreenLightCost?: number,
    public otherGreenLightPartNumber?: string,
    public otherPlug?: string,
    public otherPlugCost?: number,
    public otherPlugPartNumber?: string,
    public otherRedLight?: string,
    public otherRedLightCost?: number,
    public otherRedLightPartNumber?: string,
    public otherSleevingType?: string,
    public otherSleevingTypeCost?: number,
    public otherSleevingTypePartNumber?: string,
    public otherStrainRelief?: string,
    public otherStrainReliefCost?: number,
    public otherStrainReliefPartNumber?: string,
    public redLightId?: number,
    public redLightName?: string,
    public sleevingTypeId?: number,
    public sleevingTypeName?: string,
    public strainReliefId?: number,
    public strainReliefName?: string
  ) { }
}

export class JumperPlugDTO {
  constructor(
    public id?: number,
    public jumperLength?: number,
    public plugId?: number,
    public plugName?: string,
    public quantity?: number,
  ) { }
}

export class LeadPlugDTO {
  constructor(
    public id?: number,
    public leadLength?: number,
    public plugId?: number,
    public plugName?: string
  ) { }
}

export class LeadTypeDTO {
  constructor(
    public costPerFoot?: number,
    public id?: number,
    public isObsolete?: boolean,
    public leadName?: string,
    public maxTemp?: number,
    public maxVolts?: number,
    public partNumber?: number
  ) { }
}

export class SensorsInformationTemplateDTO {
  constructor(
    public ccdcTemplateId?: number,
    public id?: number,
    public otherSensorConnector?: string,
    public otherSensorLocation?: string,
    public otherSensorType?: string,
    public sensorConnector: SensorConnector = new SensorConnector(),
    public sensorLeadLength?: number,
    public sensorLocation?: string,
    public sensorTempType?: string,
    public sensorType: SensorType = new SensorType()
  ) { }
}

export class SensorInformationObject {
  constructor(public notes?: string, public sensorsInformationDTOList?: SensorsInformationTemplateDTO[]) { }
}

export class SensorConnector {
  constructor(
    public createdBy?: string,
    public createdDate?: string,
    public id?: string,
    public isObsolete?: boolean,
    public lastModifiedBy?: string,
    public lastModifiedDate?: string
  ) { }
}

export class SensorType {
  constructor(
    public id?: number,
    public isObsolete?: boolean,
  ) { }
}

export class ThermostatInfo {
  constructor(public thermostatInformationDTOList?: ThermostatInformationTemplateDTO[], public notes?: string) { }
}

export class ThermostatInformationTemplateDTO {
  constructor(
    public amps?: string,
    public ccdcTemplateId?: number,
    public closeTemp?: string,
    public closeTempF?: string,
    public tempUnit?: string,
    public cost?: number,
    public id?: number,
    public installationMethodDTO: InstallationMethodDTO = new InstallationMethodDTO(),
    public manualReset?: boolean,
    public openOnRise?: boolean,
    public openTemp?: string,
    public openTempF?: string,
    public otherThermostatCost?: string,
    public otherThermostatPartNumber?: string,
    public otherThermostatType: ThermostatType = new ThermostatType(),
    public partNumber?: string,
    public thermostatListId?: number,
    public thermostatType: ThermostatType = new ThermostatType(),
    public tolerance?: number,
    public toleranceF?: number,
    public type?: string,
    public usaStock?: number,
    public vietnamStock?: number,
    public volts?: string
  ) { }
}

export class InstallationMethodDTO {
  constructor(
    public id?: number,
    public methodName?: string,
  ) { }
}

export class ThermostatType {
  constructor(
    public createdBy?: string,
    public createdDate?: string,
    public id?: number,
    public isObsolete?: boolean,
    public lastModifiedBy?: string,
    public lastModifiedDate?: string,
  ) { }
}

export class TemplateUserName {
  constructor(
    public dataType = 'Number',
    public key = 'userId',
    public operator = 'Eq',
    public value?: number
  ) { }
}

export class TemplateName {
  constructor(
    public dataType = 'String',
    public key = 'name',
    public operator = 'Li',
    public value?: string
  ) { }
}

export class TemplateDescription {
  constructor(
    public dataType = 'String',
    public key = 'description',
    public operator = 'Li',
    public value?: string
  ) { }
}

export class ImportCcdcFilter {
  constructor(
    public userId: TemplateUserName = new TemplateUserName(),
    public name: TemplateName = new TemplateName(),
    public description: TemplateDescription = new TemplateDescription()
  ) { }
}
