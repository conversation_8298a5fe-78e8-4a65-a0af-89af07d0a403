import { Pipe, PipeTransform } from '@angular/core';
import { SharedService } from '../../shared';

@Pipe({
  name: 'fileLocationTransform'
})
export class FileLocationTransformPipe implements PipeTransform {

  userLocation:string;
  constructor(private readonly sharedService: SharedService) {
    this.userLocation = sharedService.getUsersCountry();
  }

  transform(value: string, ...args: unknown[]): string {
    if(this.userLocation && value){
      if(this.userLocation.toLowerCase()==='usa'){
        if(value.includes('************')){
          value =  value.replace('************\\PUBLIC\\ENGINEERING','**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering');
        }
      }
      else{
        if(value.includes('**********')){
          value =  value.replace('**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering','************\\PUBLIC\\ENGINEERING');
        }
      }
      return value;
    }
    else{
      return value;
    }
  }

}
