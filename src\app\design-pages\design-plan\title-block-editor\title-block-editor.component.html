<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<mat-card class="cust_fields">
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="mb-10">
    <span fxflex></span>
    <div fxLayoutAlign="end center" fxLayoutGap="10px">
      <button mat-raised-button color="warn" (click)="drawJacketDesign()">Create DXF's</button>
      <button mat-raised-button color="warn" (click)="refreshTitleBlock()" id="refresh">Reset Title Block</button>
      <button mat-raised-button color="warn" (click)="updateTitleBlock()" id="updatetitleblock">Update Title Block</button>
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div fxLayout="column" fxFlex.gt-lg="40" fxFlex.gt-md="50">
      <mat-form-field
        appearance="outline"
        [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.elementType ? 'highlight-title-block-yellow' : ''"
      >
        <mat-label>Design Name</mat-label>
        <input matInput [(ngModel)]="titleBlock.elementType" name="ElementType" />
      </mat-form-field>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="32"
          fxFlex.gt-md="32"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.number1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Number 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.number1" name="Num1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Number 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.number2" name="Num2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Number 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.number3" name="Num3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="22"
          fxFlex.gt-md="23"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.wirePart1_1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Wire Part 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.wirePart1_1" name="WIREPART1_1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="10" fxFlex.gt-md="9">
          <input matInput [(ngModel)]="titleBlock.wirePart1_2" name="WIREPART1_1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="22" fxFlex.gt-md="23">
          <mat-label>Wire Part 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.wirePart2_1" name="WIREPART2_1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="10" fxFlex.gt-md="9">
          <input matInput [(ngModel)]="titleBlock.wirePart2_2" name="WIREPART2_2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="22" fxFlex.gt-md="23">
          <mat-label>Wire Part 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.wirePart3_1" name="WIREPART3_1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="10" fxFlex.gt-md="9">
          <input matInput [(ngModel)]="titleBlock.wirePart3_2" name="WIREPART3_2" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="32"
          fxFlex.gt-md="32"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.wireOhmsfT1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Wire OHM/FT 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.wireOhmsfT1" name="WIREOHMSFT1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Wire OHM/FT 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.wireOhmsfT2" name="WIREOHMSFT2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Wire OHM/FT 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.wireOhmsfT3" name="WIREOHMSFT3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="32"
          fxFlex.gt-md="32"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.length1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Length 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.length1" name="LENGTH1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Length 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.length2" name="LENGTH2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Length 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.length3" name="LENGTH3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="32"
          fxFlex.gt-md="32"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.width1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Width 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.width1" name="WIDTH1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Width 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.width2" name="WIDTH2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Width 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.width3" name="WIDTH3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>TPI</mat-label>
          <input matInput [(ngModel)]="titleBlock.tpi" name="TPI" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>TPI 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.tpi1" name="TPI1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>TPI 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.tpi2" name="TPI2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>TPI 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.tpi3" name="TPI3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>STRAND</mat-label>
          <input matInput [(ngModel)]="titleBlock.strand" name="STRAND" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>STRAND 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.strand1" name="STRAND1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>STRAND 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.strand2" name="STRAND2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>STRAND 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.strand3" name="STRAND3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="32"
          fxFlex.gt-md="32"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.construct1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Construct 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.construct1" name="CONSTRUCT1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Construct 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.construct2" name="CONSTRUCT2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Construct 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.construct3" name="CONSTRUCT3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>PICK</mat-label>
          <input matInput [(ngModel)]="titleBlock.pick" name="PICK" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="24"
          fxFlex.gt-md="24"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.pick1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>PICK 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.pick1" name="PICK1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>PICK 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.pick2" name="PICK2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>PICK 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.pick3" name="PICK3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>WARP</mat-label>
          <input matInput [(ngModel)]="titleBlock.warp" name="WARP" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="24"
          fxFlex.gt-md="24"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.warp1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>WARP 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.warp1" name="PICK1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>WARP 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.warp2" name="PICK2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-label>WARP 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.warp3" name="PICK3" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="40" fxFlex.gt-md="40">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerTL 5</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTL5" name="LayerTL5" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerTR 5</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTR5" name="LayerTR5" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label> LayerTL 4</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTL4" name="LayerTL4" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerTR 4</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTR4" name="LayerTR4" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label> LayerTL 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTL3" name="LayerTL3" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerTR 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTR3" name="LayerTR3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label> LayerTL 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTL2" name="LayerTL2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerTR 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTR2" name="LayerTR2" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label> LayerTL 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTL1" name="LayerTL1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerTR 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerTR1" name="LayerTR1" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>LayerML</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerML" name="LayerML" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>Tape</mat-label>
          <input matInput [(ngModel)]="titleBlock.tape" name="Tape" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="32" fxFlex.gt-md="32">
          <mat-label>LayerMR</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerMR" name="LayerMR" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBL 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBL1" name="LayerBL1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBR 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBR1" name="LayerBR1" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBL 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBL2" name="LayerBL2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBR 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBR2" name="LayerBR2" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBL 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBL3" name="LayerBL3" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBR 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBR3" name="LayerBR3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBL 4</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBL4" name="LayerBL4" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBR 4</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBR4" name="LayerBR4" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBL 5</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBL5" name="LayerBL5" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>LayerBR 5</mat-label>
          <input matInput [(ngModel)]="titleBlock.layerBR5" name="LayerBR5" />
        </mat-form-field>
      </div>
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div fxLayout="column" fxFlex.gt-lg="69" fxFlex.gt-md="69">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="15"
          fxFlex.gt-md="15"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.ohmPerfT1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>OHMS/FT 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.ohmPerfT1" name="OHMPERFT1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>OHMS/FT 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.ohmPerfT2" name="OHMPERFT2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>OHMS/FT 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.ohmPerfT3" name="OHMPERFT3" />
        </mat-form-field>
        <div fxFlex.gt-lg="51" fxFlex.gt-md="51"></div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="15"
          fxFlex.gt-md="15"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.maxOhms1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Max 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.maxOhms1" name="MAXOHMS1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>Max 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.maxOhms2" name="MAXOHMS2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>Max 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.maxOhms3" name="MAXOHMS3" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="15"
          fxFlex.gt-md="15"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.totMaxOhms ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>TOTAL MAX OHMS</mat-label>
          <input matInput [(ngModel)]="titleBlock.totMaxOhms" name="MAXOHMS3" />
        </mat-form-field>
        <div fxFlex.gt-lg="35" fxFlex.gt-md="35"></div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="15"
          fxFlex.gt-md="15"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.tgtOhms1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>TGT 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.tgtOhms1" name="TGTOHMS1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>TGT 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.tgtOhms2" name="TGTOHMS2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>TGT 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.tgtOhms3" name="TGTOHMS3" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="15"
          fxFlex.gt-md="15"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.totTgtOhms ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>TOTAL TGT OHMS</mat-label>
          <input matInput [(ngModel)]="titleBlock.totTgtOhms" name="TOTTGTOHMS" />
        </mat-form-field>
        <div fxFlex.gt-lg="35" fxFlex.gt-md="35"></div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="15"
          fxFlex.gt-md="15"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.minOhms1 ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>MIN 1</mat-label>
          <input matInput [(ngModel)]="titleBlock.minOhms1" name="MINOHMS1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>MIN 2</mat-label>
          <input matInput [(ngModel)]="titleBlock.minOhms2" name="MINOHMS2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
          <mat-label>MIN 3</mat-label>
          <input matInput [(ngModel)]="titleBlock.minOhms3" name="MINOHMS3" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="15"
          fxFlex.gt-md="15"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedElement?.totMinOhms ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>TOTAL MIN OHMS</mat-label>
          <input matInput [(ngModel)]="titleBlock.totMinOhms" name="TOTMINOHMS" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>Design Temp (F)</mat-label>
          <input matInput [(ngModel)]="titleBlock.fahrenheit" name="Fahrenheit" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>Design Temp (C)</mat-label>
          <input matInput [(ngModel)]="titleBlock.celcius" name="Celcius" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="29" fxFlex.gt-md="29">
      <mat-form-field appearance="outline">
        <mat-label>Customer</mat-label>
        <input matInput [(ngModel)]="titleBlock.customerName" name="Customer" />
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>DESC 1</mat-label>
        <input matInput [(ngModel)]="titleBlock.description1" name="Description1" />
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label>DESC 2</mat-label>
        <input matInput [(ngModel)]="titleBlock.description2" name="Description2" />
      </mat-form-field>
      <mat-form-field
        appearance="outline"
        [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedGeometry?.jacketDiams ? 'highlight-title-block-yellow' : ''"
      >
        <mat-label>DIMS</mat-label>
        <input matInput [(ngModel)]="titleBlock.jacketDiams" name="JacketDims" />
      </mat-form-field>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="79" fxFlex.gt-md="79">
          <mat-label>DWG #</mat-label>
          <!-- <input matInput [(ngModel)]="titleBlock.partNumber" name="DWG" /> -->
          <input matInput [(ngModel)]="titleBlock.oldPartNumber" name="DWG" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="19"
          fxFlex.gt-md="19"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.revision ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Rev</mat-label>
          <!-- <input matInput [(ngModel)]="titleBlock.revision" name="Revision" /> -->
          <input matInput [(ngModel)]="titleBlock.oldRevision" name="Revision" />
        </mat-form-field>
      </div>
    </div>
  </div>
  <br />
  <div fxLayout="row wrap">
    <h4>Revisions</h4>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div fxLayout="column" fxFlex.gt-lg="49" fxFlex.gt-md="49">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="7" fxFlex.gt-md="9">
          <mat-label>No</mat-label>
          <input matInput [(ngModel)]="titleBlock.revLetter1" name="RevNo1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>By</mat-label>
          <input matInput [(ngModel)]="titleBlock.revBy1" name="RevName1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDate1" name="RevDate1" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="57" fxFlex.gt-md="56">
          <mat-label>Description</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDescR1" name="RevDesc1" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="7" fxFlex.gt-md="9">
          <mat-label>No</mat-label>
          <input matInput [(ngModel)]="titleBlock.revLetter2" name="RevNo2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>By</mat-label>
          <input matInput [(ngModel)]="titleBlock.revBy2" name="RevName2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDate2" name="RevDate2" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="57" fxFlex.gt-md="56">
          <mat-label>Description</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDescR2" name="RevDesc2" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="7" fxFlex.gt-md="9">
          <mat-label>No</mat-label>
          <input matInput [(ngModel)]="titleBlock.revLetter3" name="RevNo3" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>By</mat-label>
          <input matInput [(ngModel)]="titleBlock.revBy3" name="RevName3" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDate3" name="RevDate3" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="57" fxFlex.gt-md="56">
          <mat-label>Description</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDescR3" name="RevDesc3" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="7" fxFlex.gt-md="9">
          <mat-label>No</mat-label>
          <input matInput [(ngModel)]="titleBlock.revLetter4" name="RevNo" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>By</mat-label>
          <input matInput [(ngModel)]="titleBlock.revBy4" name="RevName" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17">
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDate4" name="RevDate" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="57" fxFlex.gt-md="56">
          <mat-label>Description</mat-label>
          <input matInput [(ngModel)]="titleBlock.revDescR4" name="RevDesc1" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="25">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.drawnBy ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>DRAWN</mat-label>
          <input matInput [(ngModel)]="titleBlock.drawnBy" name="DrawnBy" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.drawnDate ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.drawnDate" name="DrawnDate" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.tapeBy ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>TAPE</mat-label>
          <input matInput [(ngModel)]="titleBlock.tapeBy" name="TapBy" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.tapeDate ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.tapeDate" name="TapeDate" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.bomBy ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>BOM</mat-label>
          <input matInput [(ngModel)]="titleBlock.bomBy" name="BOMBy" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.bomDate ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.bomDate" name="BOMDate" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="
            titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.engineeringApprovalBy ? 'highlight-title-block-yellow' : ''
          "
        >
          <mat-label>Approved</mat-label>
          <input matInput [(ngModel)]="titleBlock.engineeringApprovalBy" name="EngineeringApprovalBy" />
        </mat-form-field>
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedJacketList?.engAppDate ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Date</mat-label>
          <input matInput [(ngModel)]="titleBlock.engAppDate" name="EngAppDate" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="30" fxFlex.gt-md="25">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedCommon?.volts ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Volts</mat-label>
          <input matInput [(ngModel)]="titleBlock.volts" name="Volts" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>Fit</mat-label>
          <input matInput [(ngModel)]="titleBlock.fit" name="Fit" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="49"
          fxFlex.gt-md="49"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedCommon?.watts ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Watts</mat-label>
          <input matInput [(ngModel)]="titleBlock.watts" name="Watts" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <mat-label>Finish</mat-label>
          <input matInput [(ngModel)]="titleBlock.finish" name="Finish" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field
          appearance="outline"
          fxFlex.gt-lg="29"
          fxFlex.gt-md="29"
          [ngClass]="titleBlock?.titleBlockHighlighterDTO?.highlightedCommon?.amps ? 'highlight-title-block-yellow' : ''"
        >
          <mat-label>Amps</mat-label>
          <input matInput [(ngModel)]="titleBlock.amps" name="Amps" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex.gt-lg="69" fxFlex.gt-md="69">
          <mat-label>Customer P/N</mat-label>
          <input matInput [disabled]="titleBlock.custPartNoLabel != '' && titleBlock.custPartNoLabel != null" [(ngModel)]="titleBlock.custPartNoLabel" name="CustPartNoLabel" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field appearance="outline" fxFlex.gt-lg="100" fxFlex.gt-md="100">
          <mat-label>So #</mat-label>
          <input matInput [(ngModel)]="titleBlock.salesOrderNumber" name="salesordernum" />
        </mat-form-field>
      </div>
    </div>
  </div>
</mat-card>
