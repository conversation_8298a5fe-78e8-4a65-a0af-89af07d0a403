<div class="sfl-loading" *ngIf="showLoader">
    <mat-progress-spinner
    class="sfl-global-spinner-loader"
    [mode]="mode"
    [color]="color"
    [diameter]="spinnerDiameter">
    </mat-progress-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex.gt-sm="33.33%" fxFlex="100">
    <mat-card class="mat-card-yellow card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">description</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <h2 mat-card-widget-title>{{statuscount?.rfqInQueueStatus}}</h2>
          <p>RFQ In Queue</p>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxFlex.gt-sm="33.33%" fxFlex="100">
    <mat-card class="mat-card-red card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">refresh</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <h2 mat-card-widget-title>{{statuscount?.quotingInProcessStatus}}</h2>
          <p>Quoting In Process</p>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxFlex.gt-sm="33.33%" fxFlex="100">
    <mat-card class="mat-card-purple card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">playlist_add_check</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <h2 mat-card-widget-title>{{statuscount?.quoteCompleteStatus}}</h2>
          <p>Quote Complete</p>
        </div>
      </div>
    </mat-card>
  </div>
</div>

<div class="less-peding" [ngClass]="{'disable-section' : userRole==='ROLE_SALES'}">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center" class="flex-wrap">
        <div class="mr-3 my-1">
          <button mat-raised-button type="button" id="ecr-management" color="warn" [routerLink]="['/design-eng/ecr-management']">ECR Management</button>
        </div>
        <div class="my-1">
          <button mat-raised-button type="button" id="android-data-import" color="warn" (click)="importAndroidData()">Mobile Data Import</button>
        </div>
      </div>
    </div>
  </mat-card>
</div>

<div class="filter less-peding cust_fields">
  <mat-card class="mb-10">
    <form class="fields" #quotationFilterForm="ngForm">
      <div class="d-sm-flex justify-content-between">
        <div class="row w-100">
          <mat-form-field appearance="outline" class="col col-md-3">
            <mat-label>Quotation #</mat-label>
          <input matInput placeholder="Quotation #" [(ngModel)]="quotationSearchFilter.quotationNumber"
            name="quotationNumber">
          </mat-form-field>
          <mat-form-field appearance="outline" class="col col-md-3">
            <mat-label>Status</mat-label>
            <mat-select placeholder="Status" [(ngModel)]="quotationSearchFilter.status" name="status">
              <mat-option *ngFor="let allstatus of status" [value]="allstatus.status">
                {{ allstatus?.status }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" class="col col-md-3">
            <mat-label>Sales Associate</mat-label>
          <mat-select placeholder="Sales Associate" [(ngModel)]="quotationSearchFilter.salesAssociateName"
            name="salesAssociateName">
            <mat-option *ngFor="let salesass of salesassociate" [value]="salesass.firstName +' '+ salesass.lastName">{{salesass?.firstName}}
              {{salesass?.lastName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" class="col col-md-3">
            <mat-label>Customer</mat-label>
          <input matInput placeholder="Customer" [(ngModel)]="quotationSearchFilter.customerName" name="customerName">
          </mat-form-field>
        </div>
        <div class="ml-sm-3 mt-1 mt-sm-0 d-flex align-items-end">
          <mat-actions-row fxLayoutAlign="center center">
            <button mat-raised-button color="warn" (click)="addFilter()">Search</button>&nbsp;
            <button mat-raised-button color="warn" (click)="resetFilter()">Reset</button>
          </mat-actions-row>
        </div>
      </div>
    </form>
  </mat-card>
</div>
<br />
<div class="less-peding">
  <mat-card class="cust_table" style="margin-top:-10px">
    <table
      class="w-auto"
      aria-hidden="true"
      mat-table
      matSort
      matSortDisableClear
      [dataSource]="dataSource"
      (matSortChange)="getSorting($event)"
      [ngClass]="{'disable-section' : userRole==='ROLE_SALES'}"
    >
      <ng-container matColumnDef="quotationNumber">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Quote Number </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="15">
          <span class="mobile-label">Quote Number:</span>
          <a class="link" [routerLink]="['/app-eng/ccdc']"
            [queryParams]="{ quotId: element?.id }">{{element?.quotationNumber}}</a>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="salesOrderNumber">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> SO Number </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="10">
          <span class="mobile-label">Name:</span>
          <a class="link" [routerLink]="['/app-eng/ccdc']"
            [queryParams]="{ quotId: element?.id }">{{element?.salesOrderNumber}}</a>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="quotationStatus">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="17"> Status </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="17">
          <span class="mobile-label">SO Number:</span>
          {{element?.quotationStatusName}} </mat-cell>
      </ng-container>
      <ng-container matColumnDef="revision">
        <mat-header-cell *matHeaderCellDef fxFlex="5"> Rev </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="5">
          <span class="mobile-label">Rev:</span>
          {{element?.revisionName}} </mat-cell>
      </ng-container>
      <ng-container matColumnDef="salesAssociate">
        <mat-header-cell *matHeaderCellDef fxFlex="10"> Sales Associate </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="10">
          <span class="mobile-label">Sales Associate:</span>
          {{element?.salesAssociate}} </mat-cell>
      </ng-container>
      <ng-container matColumnDef="customerName">
        <mat-header-cell *matHeaderCellDef fxFlex="30"> Customer </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="30">
          <span class="mobile-label">Customer:</span>
          {{element?.customerName}} </mat-cell>
      </ng-container>
      <ng-container matColumnDef="design">
        <mat-header-cell *matHeaderCellDef fxFlex="14"> Design </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="14">
          <span class="mobile-label">Design:</span>
          {{element?.design}} </mat-cell>
      </ng-container>
      <ng-container matColumnDef="actions">
        <mat-header-cell *matHeaderCellDef fxLayoutAlign="center center" fxFlex="7">Actions</mat-header-cell>
        <mat-cell *matCellDef="let element" fxLayoutAlign="center center" fxFlex="7">
          <span class="mobile-label">Actions:</span>
          <button mat-icon-button [matMenuTriggerFor]="menu">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu class="menu" #menu="matMenu">
            <button mat-menu-item (click)="deleteQuotations(element?.id)">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
            <button mat-menu-item (click)="openCopyQuotation(element?.id)">
              <mat-icon>file_copy</mat-icon>
              <span>Copy Quotation</span>
            </button>
          </mat-menu>
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
    </table>
    <div *ngIf="isNoDataFound | async">
      <ng-template class="no-records" *ngIf="!showLoader">No data found</ng-template>
    </div>
    <mat-paginator
        [length]="length"
        [pageSizeOptions]="pageSizeOptions"
        [pageSize]="pageSize"
        [pageIndex]="pageIndex"
        (page)="getPagination($event)"
        showFirstLastButtons
      >
    </mat-paginator>
  </mat-card>
</div>
