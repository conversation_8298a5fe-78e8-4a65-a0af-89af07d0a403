import { Component } from '@angular/core';

import { Router } from '@angular/router';
import { SalesDeptLayoutNavbarService } from './sales-dept-layout-navbar.service';

@Component({
  selector: 'sfl-sales-dept-navbar',
  templateUrl: './sales-dept-navbar.component.html'
})
export class SalesDeptNavbarComponent {
  constructor(public salesDeptNavbarService: SalesDeptLayoutNavbarService, private router: Router) {}

  addMenuItem(): void {
    this.salesDeptNavbarService.add({
      state: 'menu',
      name: 'MENU',
      type: 'sub',
      icon: 'trending_flat',
      children: [
        { state: 'menu', name: 'MENU' },
        { state: 'timeline', name: 'MENU' }
      ]
    });
  }
}
