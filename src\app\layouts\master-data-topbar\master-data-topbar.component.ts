import { Component, OnInit, Output, OnDestroy, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { SharedService } from 'src/app/shared';
import { DashboardService } from 'src/app/admin-pages/dashboard';
import { TopbarService } from '../topbar/topbar.service';
import { NgForm } from '@angular/forms';
import { Values } from 'src/app/shared/constants/values.constants';
import { Subscription, Observable } from 'rxjs';
import { Status, SalesAssociate, Quotation, QuotationSearchFilter } from 'src/app/admin-pages/dashboard/dashboard.model';
import { QuotationList } from '../topbar/topbar.model';
import * as screenfull from 'screenfull';
import { Route } from 'src/app/shared/constants/router.constants';
import { MatDialog } from '@angular/material';
import { AddQuotationComponent } from '../add-quotation/add-quotation.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';

@Component({
  selector: 'sfl-master-data-topbar',
  templateUrl: './master-data-topbar.component.html'
})
export class MasterDataTopbarComponent implements OnInit, OnDestroy {
  @Output() toggleSidenav = new EventEmitter<void>();

  subscription = new Subscription();

  filter: boolean;
  isDesignAdmin: boolean;
  isAppAdmin: boolean;
  isFullScreen: boolean;

  constructor(private router: Router, private sharedService: SharedService) {}

  async ngOnInit() {}

  fullScreenToggle(): void {
    if (screenfull.enabled) {
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    }
  }

  goToDashboard() {
    this.router.navigate(['master-data/management']);
  }

  switchToDesign() {
    this.router.navigate(['/design-eng/dashboard']);
  }
  switchToSuperAdmin() {
    this.router.navigate([Route.SUPER_ADMIN.user_list]);
  }

  switchToAppAdmin() {
    this.router.navigate(['/app-eng/dashboard']);
  }
  // used to switch to the Quote tracker screen
  switchToQuoteTracker() {
    this.router.navigate([Route.QUOTE_TRACKER.dashboard]);
  }

  logout() {
    this.sharedService.logout(null);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
