<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Entry Method
  <hr />
</h2>
<form #entryMethodForm="ngForm">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field appearance="outline">
          <mat-label>Entry Method</mat-label>
          <mat-select placeholder="Entry Method" name="entrymethod" [(ngModel)]="entryMethod" required>
            <mat-option value="true">True Dimensions</mat-option>
            <mat-option value="centerLine">Centerline Dimensions</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>
  <hr />
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button
        mat-raised-button
        color="warn"
        type="submit"
        (click)="changeEntryMethod(entryMethodForm, 'save')"
        name="save"
        [disabled]="!entryMethodForm.valid"
      >
        Save
      </button>
      <button mat-raised-button type="button" (click)="closeDialog()" name="cancel">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button
        mat-raised-button
        type="submit"
        (click)="changeEntryMethod(entryMethodForm, 'saveandnext')"
        name="saveandnext"
        [disabled]="!entryMethodForm.valid"
      >
        Save And Next
      </button>
    </div>
  </mat-dialog-actions>
</form>
