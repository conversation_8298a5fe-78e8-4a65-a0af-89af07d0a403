import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { DashboardComponent } from '.';
import { dashboardRoutes } from './dashboard.route';
import { LayoutModule } from '@angular/cdk/layout';
import { DashboardService } from './dashboard.service';
import { CopyQuotationComponent } from './copy-quotation/copy-quotation.component';
@NgModule({
    imports: [
        RouterModule.forChild(dashboardRoutes),
        SharedModule,
        LayoutModule,
    ],
    declarations: [
        DashboardComponent,
        CopyQuotationComponent
    ],
    entryComponents: [
      CopyQuotationComponent
    ],
    providers: [
        DashboardService
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class DashboardModule { }
