import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { QuotationComparisonComponent } from './quotation-comparison.component';
import { QuotationComparisonRoutes } from './quotation-comparison.route';
import { SensorService } from '../new-quotation/Add Sensors/add-sensors.service';

@NgModule({
    imports: [
        RouterModule.forChild(QuotationComparisonRoutes),
        SharedModule
    ],
    declarations: [
        QuotationComparisonComponent
    ],
    entryComponents: [
    ],
    providers: [
        SensorService
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class QuotationComparisonModule { }
