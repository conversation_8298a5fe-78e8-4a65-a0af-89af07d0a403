import { Injectable } from '@angular/core';
import { Role } from '../constants/user-roles.constants';
import { SharedService } from './shared.service';

@Injectable({
  providedIn: 'root'
})
export class RoleAuthorisationServiceService {

  constructor(public readonly sharedService: SharedService) { }

  public isAuthorised(roles: Role[]): boolean {
    const currentUserRole = this.sharedService.getRole();
    if (!currentUserRole) return false;
    return (roles.indexOf(currentUserRole) >= 0);
  }
}
