<h2 mat-dialog-title>Export CCDC
  <hr>
</h2>
<form class="forms_form" #ExportCcdcForm="ngForm">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="User Name" [(ngModel)]="ccdcTemplateExportDTO.userId" name="name" disabled>
            <mat-option *ngFor="let user of users" [value]="user?.id" readonly>
              {{ user?.firstName }} {{user?.lastName}}
            </mat-option>
        </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Template Name"
            name="templateName"
            [(ngModel)] = "ccdcTemplateExportDTO.name"
            sflNoWhiteSpaces
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Description"
            name="description"
            [(ngModel)] = "ccdcTemplateExportDTO.description"
            sflNoWhiteSpaces
          />
        </mat-form-field>
      </div>
      </div>
    </mat-dialog-content>
</form>
<hr>
<mat-dialog-actions>

  <hr>
  <button mat-raised-button type="submit" color="warn" [disabled] = "!ExportCcdcForm.valid" (click)="exportCcdcData()">Export</button>
  <button mat-raised-button type="submit" (click)="closeDialog(false)">Cancel</button>
</mat-dialog-actions>

