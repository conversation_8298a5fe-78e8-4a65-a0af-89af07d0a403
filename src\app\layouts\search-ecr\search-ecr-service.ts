import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppConfig} from '../../app.config';
import {catchError, map} from 'rxjs/operators';
import {utils} from '../../shared/helpers';

@Injectable({
  providedIn: 'root'
})
export class SearchEcrService {
  constructor(private http: HttpClient) {
  }

  getSONumbersByPartNumber(partNumber) {
    return this.http.get(AppConfig.GET_SO_NUMBER_BY_PART_NUMBER + partNumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getEcrsByPartNumber(partNumber) {
    return this.http.get(AppConfig.GET_ECR_PART_NUM + partNumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
