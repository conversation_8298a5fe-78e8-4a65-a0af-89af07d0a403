import { UserService } from './user.service';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { UsersComponent } from './users.component';
import { UsersRoutes } from './users.route';
import { FormsModule } from '@angular/forms';
import { AddUserComponent } from './add-user/add-user.component';


@NgModule({
    imports: [
        RouterModule.forChild(UsersRoutes),
        SharedModule,
        FormsModule
    ],
    declarations: [
        UsersComponent,
        AddUserComponent
    ],
    entryComponents: [
    ],
    providers: [
        UserService
    ],
    exports: [
        UsersComponent
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class UsersModule { }
