import { SweetAlertService } from './../../shared/service/sweet-alert.service';
import { RevisionAccessoriesData } from './Add Accessories/add-accessories.model';
import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { MatDialog, MatTableDataSource, MatDialogConfig } from '@angular/material';
import { Subscription } from 'rxjs';
import { AddAccessoriesComponent } from './Add Accessories/add-accessories.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { AccessoriesService } from './accessories.service';
import { JacketList } from './accessories.model';
import { PowerCordComponent } from './Power-Cord/power-cord.component';
import { SharedService, Utils, SnakbarService, Messages } from 'src/app/shared';
import { map } from 'rxjs/operators';
@Component({
  selector: 'sfl-accessories',
  templateUrl: './accessories.component.html'
})
export class AccessoriesComponent implements OnInit, OnDestroy {

  subscription: Subscription = new Subscription();
  private _revisionId: number;
  jacketList: JacketList[];
  hideme = [];
  revisionAccessoriesData: RevisionAccessoriesData[];
  discount: number;
  jacketDiscount: number;
  accdisplayedColumns = ['qty', 'pn', 'descripation', 'listprice', 'discount', 'netprice', 'extnetprice', 'margin', 'action', 'order'];
  accdataSource = new MatTableDataSource<RevisionAccessoriesData>();
  isNoDataFound = this.accdataSource.connect().pipe(map(data => data.length === 0));
  applyDiscountObject: object;
  totalNetPrice = 0;
  isRevisionActive: boolean;

  materialCostTotal = 0;
  factoryCostTotal = 0;
  listPriceTotal = 0;
  netPriceTotal = 0;
  extNetPriceTotal = 0;

  get revisionId() {
    return this._revisionId;
  }

  @Input() quotId;
  @Input()
  set revisionId(val) {
    this._revisionId = val;
    this.getAllJacketsAccessories(this._revisionId);
    this.getRevisionAccessoriesList(this.revisionId);
  }

  constructor(
    private matDialog: MatDialog,
    private accessoriesService: AccessoriesService,
    private sweetAlertService: SweetAlertService,
    private sharedService: SharedService,
    private snakbarService: SnakbarService,
  ) { }

  ngOnInit() {
    this.sharedService.isCurrentRevisionActive.subscribe(res => this.isRevisionActive = res);
  }

  getAllJacketsAccessories(revisionId) {
    this.subscription.add(this.accessoriesService.getAllJacketsAccessories(revisionId).subscribe((res: JacketList[]) => {
      if (res.length > 0) {
        this.jacketList = [];
        res.forEach(data=>{
          if (data && data!==null && data!==undefined){
            this.jacketList.push(data);
          }
        })
        this.calculateTotalCost(this.jacketList);
      }
    }));
  }
  calculateTotalCost(jacketList: JacketList[]) {
    this.jacketList.forEach(jacket => {
      jacket.jacketAccessoryList.forEach(accessory => {
        this.materialCostTotal += accessory.materialCost;
        this.listPriceTotal += accessory.listPrice;
        this.netPriceTotal += accessory.netPrice;
        this.extNetPriceTotal += accessory.extendedNetPrice;
      });
      this.factoryCostTotal += jacket.totalFactoryCost;
    });
  }



  getRevisionAccessoriesList(revisionId) {
    this.subscription.add(this.accessoriesService.getAccessoriesByRevisionId(revisionId).subscribe((res: RevisionAccessoriesData[]) => {
      if (res.length > 0) {
        this.revisionAccessoriesData = [];
        this.revisionAccessoriesData = res;
        this.accdataSource.data = this.revisionAccessoriesData;
        this.calculateTotalNetPrice(this.revisionAccessoriesData);
      } else {
        this.accdataSource.data = [];
      }
    }));
  }

  addAccessories() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { revisionId: this.revisionId === undefined ? 0 : this.revisionId, discount: this.discount === undefined ? 0 : this.discount };
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog.open(AddAccessoriesComponent, matDataConfig).afterClosed().subscribe(res => {
      if (res) {
        this.revisionAccessoriesData = res;
        this.accdataSource.data = this.revisionAccessoriesData;
        this.calculateTotalNetPrice(this.revisionAccessoriesData);
      }
    });
  }

  saveElement(ele) {
    this.subscription.add(this.accessoriesService.updateAccessory(ele).subscribe(res => {
      if (res) {
        const index = this.revisionAccessoriesData.indexOf(ele);
        this.revisionAccessoriesData[index] = res;
        this.accdataSource.data = this.revisionAccessoriesData;
      }
      this.calculateTotalNetPrice(this.revisionAccessoriesData);
    },
    (error) => {
      if (error.applicationStatusCode === 1232) {
        this.snakbarService.error(error.message);
        }
      }
    ));
  }

  applyDiscount() {
    this.applyDiscountObject = { 'revisionId': this.revisionId, 'discount': this.discount };
    this.subscription.add(this.accessoriesService.applyDiscountToAllAccessory(this.applyDiscountObject).subscribe((res: RevisionAccessoriesData[]) => {
      if (res.length > 0) {
        this.revisionAccessoriesData = res;
        this.accdataSource.data = this.revisionAccessoriesData;
        this.calculateTotalNetPrice(this.revisionAccessoriesData);
      }
    }));
  }

  applyJacketDiscount() {
    this.subscription.add(this.accessoriesService.applyJacketDiscountToAllJacket(this.revisionId, this.jacketDiscount).subscribe((res: JacketList[]) => {
      if (res.length > 0) {
        this.jacketList = res;
      }
    }));
  }

  calculateTotalNetPrice(accessories: RevisionAccessoriesData[]) {
    this.totalNetPrice = 0;
    accessories.forEach(element => {
      this.totalNetPrice = this.totalNetPrice + element.extendedNetPrice;
    });
  }

  async confirmDelete(ele) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.deleteAccessory(ele);
    }
  }

  deleteAccessory(revisionAccessory) {
    this.subscription.add(this.accessoriesService.deleteAccessory(revisionAccessory).subscribe(res => {
      this.revisionAccessoriesData.splice(this.revisionAccessoriesData.indexOf(revisionAccessory), 1);
      this.accdataSource.data = this.revisionAccessoriesData;
      this.accdataSource._updateChangeSubscription();
      this.calculateTotalNetPrice(this.revisionAccessoriesData);
    }));
  }

  powerCord() {
    this.matDialog.open(PowerCordComponent, {
      width: PopupSize.size.popup_md
    });
  }

  up(i) {
    if (i > 0) {
      const new_index = i - 1;
      this.accdataSource.data.splice(new_index, 0, this.accdataSource.data.splice(i, 1)[0]);
      this.accdataSource._updateChangeSubscription();
    }
  }

  down(i) {
    if (i < this.accdataSource.data.length) {
      const new_index = i + 1;
      this.accdataSource.data.splice(new_index, 0, this.accdataSource.data.splice(i, 1)[0]);
      this.accdataSource._updateChangeSubscription();
    }
  }

  submitChange() {
    for (let i = 0; i < this.accdataSource.data.length; i++) {
      this.accdataSource.data[i].accessoryIndex = i;
    }
    this.subscription.add(this.accessoriesService.updateRevOrder(this.revisionAccessoriesData).subscribe((res: RevisionAccessoriesData[]) => {
      if (res) {
        this.snakbarService.success(Messages.save_Chnages.success);
      }
    }));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
