<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div class="row mr-0" fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Alloy Name</mat-label>
          <input matInput [(ngModel)]="heatingTapesFilter.alloyName" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldAlloyName)"
            *ngIf="heatingTapesFilter.alloyName"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Tape Width</mat-label>
          <input matInput [(ngModel)]="heatingTapesFilter.width" (change)="addFilterTapeWidth()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldTapeWidth)"
            *ngIf="heatingTapesFilter.width"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto" fxFlex.gt-sm="15" fxFlex.gt-xs="50">
          <mat-label>Search Tape Type</mat-label>
          <mat-select (selectionChange)="addFilterTapeType()" placeholder="Search Tape Type" [(ngModel)]="heatingTapesFilter.tapeType"
            name="tapeType">
            <mat-option *ngFor="let type of tapeTypes" [value]="type.id">
              {{type?.value}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Tape Part Number</mat-label>
          <input matInput [(ngModel)]="heatingTapesFilter.tapePartNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldTapePartNumber)"
            *ngIf="heatingTapesFilter.tapePartNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn mt-2" (click)="addHeatingTapes()">Add New Heating Tape</button>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn mt-2" (click)="resetFilter()">Reset</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="heatingTapesDataSource"
        (matSortChange)="getHeatingTapesSorting($event)"
      >
        <ng-container matColumnDef="tapeType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Tape Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.tapeType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tapePartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Tape Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.tapePartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="sequenceNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Sequence Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.sequenceNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="width">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Width </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.width }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="ohmsPerFt">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> OHMS Per Ft </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.ohmsPerFt }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="alloyName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Alloy Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.alloyName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="wireType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Wire Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.wireType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="strands">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Strands </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.strands }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tpi">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="8"> TPI </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8"> {{ element?.tpi }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="picks">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Picks </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.picks }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="warps">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Warps </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.warps }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isDualWireTape">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="14"> is Dual Wire Tape </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="14"> {{ element?.dualWireTape }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="8"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="3"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="3">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editHeatingTapes(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteHeatingTapes(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="heatingTapesColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: heatingTapesColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!heatingTapesDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getHeatingTapesPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
