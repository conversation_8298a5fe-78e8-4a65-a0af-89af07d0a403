<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Sensor Information
  <hr />
</h2>
<mat-dialog-content>
  <form #sensorInfoForm="ngForm" name="sensorInfoForm" (ngSubmit)="addToSensor(sensorInfoForm)" novalidate>
    <div fxLayout="column">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
        <input type="text" placeholder="Sensor Type" aria-label="Sensor Type" matInput [(ngModel)]="sensorInfo.sensorType.id" [matAutocomplete]="autoSensorType"
        [formControl]="sensorTypesControl"  />
      </mat-form-field>
      <mat-autocomplete  #autoSensorType="matAutocomplete" [displayWith]="displaySensorTypes" (optionSelected)="onSelectionSensorTypes($event.option.value)">
        <mat-option  *ngFor="let data of sensorTypesObservable$ | async; let i = index" [value]="data">
          {{ data?.id }}
        </mat-option>

      </mat-autocomplete>
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
        <mat-select
          placeholder="Sensor Location"
          name="sensorLocation"
          (selectionChange)="onSensorLocationChanged($event.value)"
          [(ngModel)]="sensorInfo.sensorLocation"
          #sensorLocationSelect="ngModel"
        >
          <mat-option *ngFor="let location of sensorLocations | orderBy:'value'" [value]="location?.id">
            {{ location?.value }}
          </mat-option>
          <mat-option>None</mat-option>
        </mat-select>

      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
        <input type="text" placeholder="Sensor Connector" aria-label="Sensor Connector" matInput [(ngModel)]="sensorInfo.sensorConnector.id" [matAutocomplete]="autoSensorConnector"
        [formControl]="sensorConnectorsControl"  />
      </mat-form-field>
      <mat-autocomplete #autoSensorConnector="matAutocomplete" [displayWith]="displaySensorConnectors" (optionSelected)="onSelectionSensorConnectors($event.option.value)">
        <mat-option  *ngFor="let data of sensorConnectorsObservable$ | async; let i = index" [value]="data">
          {{ data?.id }}
        </mat-option>
        <mat-option value="none">None</mat-option>
      </mat-autocomplete>
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
        <input
          matInput
          placeholder="Sensor Lead Length ({{ measureUnit ? measureUnit : '' }})"
          name="sensorLeadLength"
          [(ngModel)]="sensorInfo.sensorLeadLength"
          #sensorLeadLengthInput="ngModel"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
        <mat-select
          placeholder="Sensor Temp Type"
          name="sensorTempType"
          [(ngModel)]="sensorInfo.sensorTempType"
          #sensorTempTypeSelect="ngModel"
          required
        >
          <mat-option *ngFor="let type of sensorsType | orderBy:'id'" [value]="type?.id">
            {{ type?.id }}
          </mat-option>
          <mat-option>None</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between" class="mb-10">
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100" *ngIf="isOtherSensorType">
        <input
          matInput
          placeholder="Other Sensor Type"
          name="otherSensorType"
          [(ngModel)]="sensorInfo.otherSensorType"
          #sensorLeadLengthInput="ngModel"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100" *ngIf="isOtherSensorLocation">
        <input
          matInput
          placeholder="Other Sensor location"
          name="otherSensorLocation"
          [(ngModel)]="sensorInfo.otherSensorLocation"
          #sensorLeadLengthInput="ngModel"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100" *ngIf="isOtherSensorConnector">
        <input
          matInput
          placeholder="Other Sensor Connector"
          name="otherSensorConnector"
          [(ngModel)]="sensorInfo.otherSensorConnector"
          #sensorLeadLengthInput="ngModel"
        />
      </mat-form-field>
      <div fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19"></div>
      <div fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19"></div>
    </div>
    <mat-dialog-actions fxLayoutAlign="end" class="mb-10">
      <button mat-raised-button color="warn" type="submit" [disabled]="sensorInfoForm.invalid">Add</button>
    </mat-dialog-actions>
  </div>
  </form>
  <br />
  <div class="highlight-mat-table cust_table mt-10">
    <div fxLayout="column">
    <mat-table [dataSource]="sensorsDataSource" *ngIf="sensorInfos.length > 0">
      <ng-container matColumnDef="sensorType">
        <mat-header-cell *matHeaderCellDef fxFlex="20"> Sensor Type </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="20">{{
          element?.sensorType !== 'Other' ? element?.sensorType?.id : element?.otherSensorType
        }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="sensorLocation">
        <mat-header-cell *matHeaderCellDef fxFlex="35"> Location </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="35">{{
          element?.sensorLocation !== 'Other' ? element?.sensorLocation : element?.otherSensorLocation
        }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="sensorConnector">
        <mat-header-cell *matHeaderCellDef fxFlex="15"> Connector </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="15">{{
          element?.sensorConnector !== 'Other' ? element?.sensorConnector?.id : element?.otherSensorConnector
        }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="sensorLeadLength">
        <mat-header-cell *matHeaderCellDef fxFlex="10"> Leads ({{ measureUnit ? measureUnit : '' }})</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.sensorLeadLength }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="sensorTempType">
        <mat-header-cell *matHeaderCellDef fxFlex="15">Sensor Temp Type</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="15">{{ element?.sensorTempType }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="action">
        <mat-header-cell *matHeaderCellDef fxFlex="10"> Action </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="10">
          <mat-icon class="open-doc" (click)="editFromSensor(element)">edit</mat-icon>
          <mat-icon class="open-doc" (click)="removeFromSensor(element)">delete</mat-icon>
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="SensorsdisplayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: SensorsdisplayedColumns"></mat-row>
    </mat-table>
    <div fxFlex="100" class="mt-10">
      <mat-form-field>
        <textarea matInput placeholder="Notes" rows="5" name="notes" [(ngModel)]="sensorInfoObject.notes" #notesInput="ngModel"></textarea>
      </mat-form-field>
    </div>
  </div>
  </div>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button color="warn" type="submit" (click)="saveSensor('save')" name="save">Save</button>
    <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="submit" (click)="saveSensor('saveandnext')" name="saveandnext">Save And Next</button>
  </div>
</mat-dialog-actions>
