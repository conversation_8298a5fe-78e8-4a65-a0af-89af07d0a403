<h2 mat-dialog-title>
  Add Label
  <hr />
</h2>
<form #materialForm="ngForm" role="form">

  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field>
      <mat-select placeholder="Select" name="select" [(ngModel)]="selection" (selectionChange)="onSelectionChanges($event.value)" required>
        <mat-option *ngFor="let data of masterDataList; let i = index" [value]="i"
          >{{ data.partNumber }}, {{ data.description }}</mat-option
        >
        <mat-option value="other">Other</mat-option>
      </mat-select>
    </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <ng-container *ngIf="selection !== 'other'">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Format" [(ngModel)]="masterDataDetails.partNumber" name="partNumber" autocomplete="off" />
      </mat-form-field>
      <mat-form-field>
        <input
          matInput
          placeholder="Description"
          [(ngModel)]="masterDataDetails.description"
          name="description"
          autocomplete="off"
          readonly
        />
      </mat-form-field>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Qty" [(ngModel)]="masterDataDetails.qty" name="qty" autocomplete="off" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="UOM" [(ngModel)]="masterDataDetails.uom" name="uom" autocomplete="off" readonly />
        </mat-form-field>
      </div>
    </ng-container>
    </div>
    <ng-container *ngIf="selection === 'other'">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Format" [(ngModel)]="masterDataDetails.partNumber" name="partNumber" autocomplete="off" />
      </mat-form-field>
      <mat-form-field>
        <input matInput placeholder="Description" [(ngModel)]="masterDataDetails.description" name="description" autocomplete="off" />
      </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="Qty" [(ngModel)]="masterDataDetails.qty" name="qty" autocomplete="off" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <input matInput placeholder="UOM" [(ngModel)]="masterDataDetails.uom" name="uom" autocomplete="off" />
        </mat-form-field>
      </div>
    </ng-container>
  </mat-dialog-content>
  <hr />
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveData()" [disabled]="materialForm.invalid">Add Label</button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</form>
