<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Alloy Name</mat-label>
          <input matInput [(ngModel)]="alloyFilter.alloyName" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldAlloyName)"
            *ngIf="alloyFilter.alloyName"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search GND Part Number</mat-label>
          <input matInput [(ngModel)]="alloyFilter.gndPartNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldGNDPartNumber)"
            *ngIf="alloyFilter.gndPartNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addalloy()">Add New Alloys</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="alloyDataSource" (matSortChange)="getalloySorting($event)">
        <ng-container matColumnDef="alloyName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Alloy Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.alloyName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="ohmsPerFoot">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> OHMS Per Foot </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.ohmsPerFoot }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxTempC">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Max Temp. C </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.maxTempC }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="gndPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> GND Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.gndPartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="fgPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> FG Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.fgPartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="samPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> SAM Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.samPartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tpignPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> TPIGN Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.tpignPartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tpifgPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> TPIFG Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.tpifgPartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tpisamPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="12"> TPISAM Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="12"> {{ element?.tpisamPartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tpiWperIn">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> TPI W per In </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.tpiWperIn }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="msm">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> MSM </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.msm }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="msb">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> MSB </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.msb }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="3"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="3">         
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editalloy(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deletealloy(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="alloyColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: alloyColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!alloyDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getalloyPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
