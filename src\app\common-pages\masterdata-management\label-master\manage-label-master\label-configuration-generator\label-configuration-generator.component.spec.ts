import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { LabelConfigurationGenratorComponent } from './label-configuration-genrator.component';

describe('LabelConfigurationGenratorComponent', () => {
  let component: LabelConfigurationGenratorComponent;
  let fixture: ComponentFixture<LabelConfigurationGenratorComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ LabelConfigurationGenratorComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LabelConfigurationGenratorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
