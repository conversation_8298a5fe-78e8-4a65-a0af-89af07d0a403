<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayoutAlign="start center">
      <mat-card-title>{{ pageTitle }}</mat-card-title>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addPartNumberController()">Add New Part Number</button>
      </div>
    </div><br>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="partNumberMasterDataSource" (matSortChange)="getMasterPartNumberSorting($event)">
        <ng-container matColumnDef="facing">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="32"> Facing </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="32"> {{ element?.facings }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="insulation">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="35"> Insulation </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.insulations }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="liner">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="35"> Liner </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.liners }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="number">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="22"> Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="22"> {{ element?.number }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="pattern">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="22"> Pattern </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="22"> {{ element?.pattern }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editPartNumberMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteMasterPartNumber(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="partNumberMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: partNumberMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!partNumberMasterDataSource?.data?.length">No data found</div>
    <mat-paginator [length]="length" [pageSizeOptions]="pageSizeOptions" [pageSize]="pageSize" [pageIndex]="pageIndex" (page)="getMasterPartNumberPagination($event)"
      showFirstLastButtons>
    </mat-paginator>
  </mat-card>
</div>