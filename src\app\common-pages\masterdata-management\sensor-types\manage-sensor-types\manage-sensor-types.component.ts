import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { SensorConnectorsAndTypesMaster, SensorTypesMaster } from '../../masterdata-management.model';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-sensor-types',
  templateUrl: './manage-sensor-types.component.html'
})
export class ManageSensorTypesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  sensorType: SensorTypesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageSensorTypesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.sensorType = data;
    this.sensorType.id ? (this.sensorType.previousId = this.sensorType.id) : (this.sensorType.previousId = null);
  }

  ngOnInit() {
    this.sensorType = this.sensorType.id ? Object.assign({}, this.sensorType) : new SensorTypesMaster();
    this.sensorType.id ? (this.title = 'Update Sensor Type') : (this.title = 'Add Sensor Type');
  }

  updateSensrType() {
    this.showLoader = true;
    if (this.sensorType.previousId) {
      this.subscription.add(
        this.masterDataService.updateSensorTypes(this.sensorType).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addSensorTypes(this.sensorType).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
