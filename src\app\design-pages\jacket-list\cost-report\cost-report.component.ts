import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatTableDataSource, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { CostReportList } from './cost-report.model';
import { CostReportService } from './cost-report.service';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';

@Component({
  selector: 'sfl-cost-report',
  templateUrl: './cost-report.component.html'
})
export class CostReportComponent implements OnInit, OnDestroy {
  revisionId: string;
  soNumber: string;
  subscription = new Subscription();
  displayedColumns = DisplayColumns.Cols.CostReport;
  showLoader = false;

  costReportList: CostReportList;
  dataSource = new MatTableDataSource<CostReportList>();
  toggleHiddenRow = [];
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(public dialogRef: MatDialogRef<CostReportComponent>, private costReportService: CostReportService, @Inject(MAT_DIALOG_DATA) data) {
    this.revisionId = data.revisionId;
    this.soNumber = data.soNumber;
  }

  ngOnInit() {
    this.generateCostReport();
  }

  generateCostReport() {
    return new Promise(resolve => {
      this.showLoader = true;
      this.subscription.add(this.costReportService.getCostReport(this.revisionId).subscribe((res: CostReportList[]) => {
        if (res) {
          this.dataSource.data = res;
          this.showLoader = false;
          resolve();
        } else {
          this.showLoader = false;
        }
      }, (error) => {
        this.showLoader = false;
      }));
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  downloadCostReportExcel() {
    this.subscription.add(this.costReportService.downloadCRExcel(this.soNumber, this.dataSource.data).subscribe((success) => {
      this.downloadExcel(success, 'Cost-Report.xlsx');
    }));
  }

  downloadExcel(response, sheetName: string) {
    const dwldLink = document.createElement('a');
    const url = URL.createObjectURL(response);
    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('target', '_blank');
    dwldLink.setAttribute('download', sheetName);
    dwldLink.style.visibility = 'hidden';
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
