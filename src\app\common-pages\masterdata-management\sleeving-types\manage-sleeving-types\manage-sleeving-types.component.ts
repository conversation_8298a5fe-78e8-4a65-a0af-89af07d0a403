import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { SleevingTypesAndStrainReliefsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-sleeving-types',
  templateUrl: './manage-sleeving-types.component.html'
})
export class ManageSleevingTypesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  sleevingType: SleevingTypesAndStrainReliefsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageSleevingTypesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.sleevingType = Object.assign({}, data);
    data.clothCE === 'true' || data.clothCE === true ? (this.sleevingType.clothCE = true) : (this.sleevingType.clothCE = false);
    data.clothUL === 'true' || data.clothUL === true ? (this.sleevingType.clothUL = true) : (this.sleevingType.clothUL = false);
  }

  ngOnInit() {
    this.sleevingType = this.sleevingType.id ? Object.assign({}, this.sleevingType) : new SleevingTypesAndStrainReliefsMaster();
    this.sleevingType.id ? (this.title = 'Update Sleeving Type') : (this.title = 'Add Sleeving Type');
  }

  updateSleevingType() {
    this.showLoader = true;
    if (this.sleevingType.id) {
      this.subscription.add(
        this.masterDataService.updateSleevingTypes(this.sleevingType).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addSleevingTypes(this.sleevingType).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
