import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { LabelConfigVariables } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-label-configuration-generator',
  templateUrl: './label-configuration-generator.component.html',
  styleUrls: ['./label-configuration-generator.component.css']
})
export class LabelConfigurationGeneratorComponent {
  title = 'Label Expression Generator';
  labelConfigVariables = LabelConfigVariables;
  expression = '';
  constructor(public readonly dialogRef: MatDialogRef<LabelConfigurationGeneratorComponent>,
    @Inject(MAT_DIALOG_DATA) data) {
    if (data && data.fieldToUpdate) {
      this.expression = data.fieldToUpdate;
    }
  }

  addExpressionVariable(expressionVariable: string) {
    if (expressionVariable) {
      this.expression = this.expression.concat(expressionVariable);
    }
  }

  addExpression() {
    this.closeDialog(this.expression)
  }

  closeDialog(expression: string) {
    this.dialogRef.close(expression);
  }

}
