<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding" [ngStyle]="{ overflow: 'visible !important' }">
  <mat-card class="cust_table bhx-material-tbl-header">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addBHXMaterial()">Add New BHX Material</button>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="filterBHXMaterial()">Filter BHX Material</button>
        <button mat-raised-button type="submit" color="warn" class="w-auto text-wrap add-btn" (click)="resetBHXMaterial()">Reset BHX Material Filter
        </button>
      </div>
    </div>
    <div class="cust_table bhx-material-tbl">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="bhxMaterialMasterDataSource"
        (matSortChange)="getBHXMaterialMasterSorting($event)"
      >
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editBHXMaterialMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="duplicateBHXMaterialMaster(element)">
                <mat-icon>file_copy</mat-icon>
                <span>Duplicate</span>
              </button>
              <button mat-menu-item (click)="deleteBHXMaterialMaster(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell class="mat-column-partNumber" *matHeaderCellDef mat-sort-header fxFlex="20"> Part Number
          </mat-header-cell>
          <mat-cell class="mat-column-partNumber" *matCellDef="let element" fxFlex="20"
                    matTooltip="{{ element?.partNumber }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.partNumber }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="grouping">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Grouping</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">
            <span
              *ngFor="let group of groupings"
              matTooltip="{{ element?.grouping === group?.id ? group?.name : '' }}"
              matTooltipClass="sfl-formula-tooltip"
            >{{ element?.grouping === group?.id ? group?.name : '' }}</span
            >
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Description</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.description }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.description }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="siliconOpr">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Silicon Opr</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.siliconOpr }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="clothOpr">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Cloth Opr</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.clothOpr }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="inseparableOpr">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Inseperable Opr</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.inseparableOpr }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="qty">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Quantity</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.qty }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="formula">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Formula</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10" matTooltip="{{ element?.formula }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.formula }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="operationName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Operation Name</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.operationName }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.operationName }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="sequence">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Sequence</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.sequence }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="opNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> OP Number</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.opNumber }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="prodHrs">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Prod Hrs</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.prodHrs }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="setupHrs">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Setup Hrs</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.setupHrs }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="uom">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> UOM</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.uom }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="blocked">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Blocked</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.blocked | convertToYesNo }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="customer">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Customer</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.customer }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.customer }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="customerAbbreviation">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Customer Abbr</mat-header-cell>
          <mat-cell
            *matCellDef="let element"
            fxFlex="20"
            matTooltip="{{ element?.customerAbbreviation }}"
            matTooltipClass="sfl-formula-tooltip"
          >
            {{ element?.customerAbbreviation }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="elementType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Element Type</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.elementType }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.elementType }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="wireType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Wire Type</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.wireType }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.wireType }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="ce">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> CE</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            {{ element?.ce === true ? checkBoxYesLabel : element?.ce === false ? checkBoxNoLabel : checkBoxNullLabel }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="ul">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> UL</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            {{ element?.ul === true ? checkBoxYesLabel : element?.ul === false ? checkBoxNoLabel : checkBoxNullLabel }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="manualResetThermostat">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Manual Reset T/S</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            {{
              element?.manualResetThermostat === true
                ? checkBoxYesLabel
                : element?.manualResetThermostat === false
                  ? checkBoxNoLabel
                  : checkBoxNullLabel
            }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="privateLabel">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Private Label</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            {{ element?.privateLabel === true ? checkBoxYesLabel : element?.privateLabel === false ? checkBoxNoLabel : checkBoxNullLabel }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="layered">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Layered</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.layered }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.layered }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxDiameter">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Max Diameter</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.maxDiameter }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="minDiameter">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Min Diameter</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.minDiameter }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="closure">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Closure</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.closure }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.closure }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="plug">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Plug</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25" matTooltip="{{ element?.plug }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.plug }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="connector">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Connector</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.connector }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.connector }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="sensConn">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Sens Conn</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.sensConn }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.sensConn }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="sleeving">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Sleeving</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.sleeving }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.sleeving }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="thermostat">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Thermostat</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            {{ element?.thermostat === true ? checkBoxYesLabel : element?.thermostat === false ? checkBoxNoLabel : checkBoxNullLabel }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxLength">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Max Length</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.maxLength }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="minLength">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Min Length</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.minLength }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="greenLight">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Green Light</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.greenLight }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.greenLight }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="minJumpers">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Min Jumpers</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.minJumpers }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="sensorType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Sensor Type</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.sensorType }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.sensorType }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="productType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Product Type</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.productType }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.productType }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="minVolts">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Min Volts</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.minVolts }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxVolts">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Max Volts</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.maxVolts }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="minAmps">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Min Amps</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.minAmps }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxAmps">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Max Amps</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.maxAmps }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="minLead">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Min Lead</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.minLead }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxLead">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Max Lead</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.maxLead }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="minTemp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Min Temp</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.minTemp }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxTemp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Max Temp</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.maxTemp }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="phase">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Phase</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.phaseValue }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.phaseValue }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="installationMethod">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Installation Method</mat-header-cell>
          <mat-cell
            *matCellDef="let element"
            fxFlex="20"
            matTooltip="{{ element?.installationMethod }}"
            matTooltipClass="sfl-formula-tooltip"
          >
            {{ element?.installationMethod }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="strainRelief">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Strain Relief</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.strainRelief }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.strainRelief }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="hazardous">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Hazardous</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            {{ element?.hazardous === true ? checkBoxYesLabel : element?.hazardous === false ? checkBoxNoLabel : checkBoxNullLabel }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="appType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> App Type</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.jacketType }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.jacketType }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="controller">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Controller</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.controller }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.controller }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="leadTypes">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Lead Types</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20" matTooltip="{{ element?.leadType }}"
                    matTooltipClass="sfl-formula-tooltip">
            {{ element?.leadType }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="deleted">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Deleted</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            {{ element?.deleted | convertToYesNo }}
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="bhxMaterialMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: bhxMaterialMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!bhxMaterialMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getBHXMaterialMasterPagination($event)"
      showFirstLastButtons
      class="bhx-material-paginator"
    >
    </mat-paginator>
  </mat-card>
</div>
