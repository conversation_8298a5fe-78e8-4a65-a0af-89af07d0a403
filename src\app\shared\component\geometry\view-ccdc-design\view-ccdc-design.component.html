<div class="main-body">
  <div fxLayout="column wrap">
    <mat-card>
      <div *ngIf="workflow">
        <div fxLayout="row">
          <mat-card-title fxFlex>Workflow Information</mat-card-title>
          <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addWorkflow()">edit</mat-icon>
        </div>
        <hr />
        <div class="mb-10"></div>
        <div class="mb-10" fxLayout="column">
          <div fxLayout="row" class="mb-10">
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">Approval Level</label>
            <p *ngIf="workflow?.approvalLevel">{{ workflow?.approvalLevel | approvalLevel }}</p>
          </div>
          <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="48" fxFlex.gt-xs="100">
            <label class="lbl">Entry Date</label>
            <p>{{ workflow?.entryDate }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">App Engineer Started</label>
            <p>{{ workflow?.dateAppStarted }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">App Engineer Completed</label>
            <p>{{ workflow?.dateAppCompleted }}</p>
          </div>
        </div>
        <div fxLayout="row" class="mb-10">
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">App Engineer Assigned</label>
            <p>{{ workflow?.assignedAppEngineerId | getFirstNameLastName: salesassociates }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">Account Manager</label>
            <p>{{ workflow?.accountMgrId | getAccountManagerName: accountManagers }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">Product Type</label>
            <p>{{ workflow?.productType | productType }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">External Quote Required</label>
            <p>{{ workflow?.externalQuoteRequired | convertToYesNo }}</p>
          </div>
        </div>

        <div fxLayout="row" class="mb-10">
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">Project Name</label>
            <p>{{ workflow?.projectName }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">Approval Format</label>
            <p *ngIf="workflow?.approvalFormatList">{{ workflow?.approvalFormatList | approvalFormats }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">Markings</label>
            <p *ngIf="workflow?.markingList">{{ workflow?.markingList | markings }}</p>
          </div>
          <div fxFlex.gt-lg="34" fxFlex.gt-md="38" fxFlex.gt-sm="38" fxFlex.gt-xs="100">
            <label class="lbl">Customer Clarification Required</label>
            <p>{{ workflow?.customerClarificationRequired | convertToYesNo }}</p>
          </div>
        </div>

        <div fxLayout="row" class="mb-10">
          <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
            <label class="lbl">Notes</label>
            <p>
              {{ workflow?.notes }}
            </p>
          </div>
          <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
            <label class="lbl">Current Status</label>
            <p>
              {{ workflow?.currentStatusComment }}
            </p>
          </div>
        </div>
        </div>
      </div>
      <div *ngIf="!workflow">
        <div
          class="text-center"
          fxFlex.gt-lg="98"
          fxFlex.gt-md="98"
          fxFlex.gt-sm="98"
          fxFlex.gt-xs="100"
        >
          <h1>No Workflow Information Is Available</h1>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxLayout="column wrap">
    <mat-card>
      <div *ngIf="appInfo">
        <div fxLayout="column wrap">
          <mat-card-title fxFlex>Application Information</mat-card-title>
        </div>
        <hr />
        <div class="mb-10"></div>
        <div class="mb-10" fxLayout="column">
          <div fxLayout="row" class="mb-10" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Element Type</label>
              <p>{{ appInfo?.jacketType }}</p>
            </div>

            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Voltage</label>
              <p>{{ appInfo?.voltage }}</p>
            </div>

            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Phase</label>
              <p>{{ appInfo?.phase }}</p>
            </div>
          </div>
          <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Operating Temp ({{ tempUnit ? tempUnit : '' }})</label>
              <p>{{ appInfo?.operatingTemp }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Max Exposure Temp ({{ tempUnit ? tempUnit : '' }})</label>
              <p>{{ appInfo?.maxExposureTemp }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Min Ambient Temp ({{ tempUnit ? tempUnit : '' }})</label>
              <p>{{ appInfo?.minAmbientTemp }}</p>
            </div>
          </div>
          <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Heat up From</label>
              <p>{{ appInfo?.heatupFrom }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Heat up To</label>
              <p>{{ appInfo?.heatupTo }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">In (Hours)</label>
              <p>{{ appInfo?.heatupIn }}</p>
            </div>
          </div>
          <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Pipe Thickness ({{ measureUnit ? measureUnit : '' }})</label>
              <p>{{ appInfo?.pipeThickness }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Content Motion</label>
              <p>{{ appInfo?.contentMotion }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Flowing Rate</label>
              <p>{{ appInfo?.contentMotionFlowingRate }}</p>
            </div>
          </div>
          <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Pipe Material</label>
              <p>{{ appInfo?.materialName }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})</label>
              <p>{{ appInfo?.otherMaterialHeat !== null ? appInfo?.otherMaterialHeat : appInfo?.materialSpecificHeat }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Density (lb/ft3)</label>
              <p>{{ appInfo?.otherMaterialDensity !== null ? appInfo?.otherMaterialDensity : appInfo?.materialDensity }}</p>
            </div>
          </div>
          <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Pipe Contents</label>
              <p>{{ appInfo?.contentName }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})</label>
              <p>{{ appInfo?.otherContentHeat !== null ? appInfo?.otherContentHeat : appInfo?.contentSpecificHeat }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Density (lb/ft3)</label>
              <p>{{ appInfo?.otherContentDensity !== null ? appInfo?.otherContentDensity : appInfo?.contentDensity }}</p>
            </div>
          </div>
          <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Control Type</label>
              <p>{{ appInfo?.controlType !== 'Other' ? appInfo?.controlType : appInfo?.otherControlType }}</p>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
              <label class="lbl">Watt Density</label>
              <p>{{ appInfo?.wattDensity }}</p>
            </div>

          </div>
          <!-- <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
              <label class="lbl">Product Type</label>
              <p>{{ appInfo?.productType | productType }}</p>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Fit Type</label>
              <p>{{ appInfo?.fitTypeEnum }}</p>
            </div>
            </div> -->
        <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
          <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
            <label class="lbl">Notes</label>
            <p>{{ appInfo?.notes }}</p>
          </div>
          <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
            <label class="lbl">Fit Type</label>
            <p>{{ appInfo?.fitTypeEnum }}</p>
          </div>
          </div>
        </div>
      </div>
      <div *ngIf="!appInfo">
        <div
          class="text-center"
          fxFlex.gt-lg="98"
          fxFlex.gt-md="98"
          fxFlex.gt-sm="98"
          fxFlex.gt-xs="100"
        >
          <h1>No Application Information Is Available</h1>
        </div>
      </div>
    </mat-card>
    <mat-card>
      <div *ngIf="pluggingInformation">
        <div fxLayout="row wrap">
          <mat-card-title fxFlex>Plugging Information</mat-card-title>
        </div>
        <hr />
        <div class="mb-10"></div>
        <div class="mb-20" fxLayout="column">
          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Sleeving Type</label><label>{{
                pluggingInformation?.sleevingTypeName !== 'Other'
                ? pluggingInformation?.sleevingTypeName
                : pluggingInformation?.otherSleevingType
                }}</label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Strain Relief</label><label>{{
                pluggingInformation?.strainReliefName !== 'Other'
                ? pluggingInformation?.strainReliefName
                : pluggingInformation?.otherStrainRelief
                }}</label>
            </div>
          </div>

          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Green Light</label><label>{{
                pluggingInformation?.greenLightName !== 'Other'
                ? pluggingInformation?.greenLightName
                : pluggingInformation?.otherGreenLight
                }}</label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Red Light</label><label>{{
                pluggingInformation?.redLightName !== 'Other' ? pluggingInformation?.redLightName : pluggingInformation?.otherRedLight
                }}</label>
            </div>
          </div>
          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Plug</label><label>{{
                pluggingInformation?.leadPlugDTO?.plugName !== 'Other'
                ? pluggingInformation?.leadPlugDTO?.plugName
                : pluggingInformation?.otherPlug
                }}</label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Connector</label><label>{{
                pluggingInformation?.jumperPlugDTO?.plugName !== 'Other'
                ? pluggingInformation?.jumperPlugDTO?.plugName
                : pluggingInformation?.otherConnector
                }}</label>
            </div>
          </div>
          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Lead Length ({{ measureUnit ? measureUnit : '' }})</label><label>{{ pluggingInformation?.leadPlugDTO?.leadLength }} </label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Jumper Length ({{ measureUnit ? measureUnit : '' }})</label><label>{{ pluggingInformation?.jumperPlugDTO?.jumperLength }} </label>
            </div>
          </div>
          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Jumper Qty</label><label>{{ pluggingInformation?.jumperPlugDTO?.quantity }}</label>
            </div>
          </div>
          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Lead Type</label><label>{{ pluggingInformation?.leadTypeDTO?.leadName }}</label>
            </div>
          </div>
          <div fxLayout="row" fxLayoutAlign="space-between">
            <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Lead Part Number</label><label>{{ pluggingInformation?.leadTypeDTO?.partNumber }}</label>
            </div>
            <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Max Temp (°C)</label><label>{{ pluggingInformation?.leadTypeDTO?.maxTemp }}</label>
            </div>
          </div>
          <div fxLayout="row" fxLayoutAlign="space-between">
            <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Cost/ft.</label><label>{{ pluggingInformation?.leadTypeDTO?.costPerFoot | currency }}</label>
            </div>
            <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Max Volts</label><label>{{ pluggingInformation?.leadTypeDTO?.maxVolts }}</label>
            </div>
          </div>
          <div class="mb-20" fxFlex="100">
            <label class="lbl">Notes</label>
            <p>{{ pluggingInformation?.notes }}</p>
          </div>
        </div>
      </div>
      <div *ngIf="!pluggingInformation">
        <div
          class="text-center"
          fxFlex.gt-lg="98"
          fxFlex.gt-md="98"
          fxFlex.gt-sm="98"
          fxFlex.gt-xs="100"
        >
          <h1>No Plugging Information Is Available</h1>
        </div>
      </div>
    </mat-card>
    <mat-card class="details" fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-xs="100">
      <div *ngIf="materialData">
        <div fxLayout="row wrap">
          <mat-card-title fxFlex>Material Information</mat-card-title>
        </div>
        <hr />
        <div class="mb-10"></div>
        <div class="mb-10" fxLayout="column">
          <div class="highlight-mat-table">
            <mat-table [dataSource]="materialDataSource" fxFlex="100">
              <ng-container matColumnDef="layerName">
                <mat-header-cell *matHeaderCellDef fxFlex="30"> Layer </mat-header-cell>
                <mat-cell *matCellDef="let element" fxFlex="30"> {{ element.layerName }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="material">
                <mat-header-cell *matHeaderCellDef fxFlex="40"> Material </mat-header-cell>
                <mat-cell *matCellDef="let element" fxFlex="40">{{
                  element?.material === 'Other'
                  ? element?.layerName === 'Facing'
                  ? element?.otherFacing
                  : element?.layerName === 'Insulation'
                  ? element?.otherInsulation
                  : element?.otherLiner
                  : element?.material
                  }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="partNumber">
                <mat-header-cell *matHeaderCellDef fxFlex="20"> Part Number </mat-header-cell>
                <mat-cell *matCellDef="let element" fxFlex="20">
                  {{
                  element?.otherFacingPartNumber
                  ? element?.otherFacingPartNumber
                  : element?.otherInsulationPartNumber
                  ? element?.otherInsulationPartNumber
                  : element?.otherLinerPartNumber
                  ? element?.otherLinerPartNumber
                  : element?.partNumber
                  }}
                </mat-cell>
              </ng-container>
              <ng-container matColumnDef="maxTemp">
                <mat-header-cell *matHeaderCellDef fxFlex="17"> Max Temp ({{ tempUnit ? tempUnit : '' }}) </mat-header-cell>
                <mat-cell *matCellDef="let element" fxFlex="17"> {{ tempUnit === '°F' ? element?.maxTempF : element?.maxTemp }}</mat-cell>
              </ng-container>
              <ng-container matColumnDef="costPerSq">
                <mat-header-cell *matHeaderCellDef fxFlex="17"> Cost / Sq. Ft. </mat-header-cell>
                <mat-cell *matCellDef="let element" fxFlex="17">${{
                  element?.otherFacingCost
                  ? element?.otherFacingCost
                  : element?.otherInsulationCost
                  ? element?.otherInsulationCost
                  : element?.otherLinerCost
                  ? element?.otherLinerCost
                  : element?.costPerSq
                  }}</mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="materialdisplayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: materialdisplayedColumns"></mat-row>
            </mat-table>
          </div>
          <div fxFlex="100" class="col mt-10">
            <label class="lbl">Notes</label>
            <p>{{ materialData?.notes }}</p>
          </div>
        </div>
      </div>
      <div *ngIf="!materialData">
        <div
          class="text-center"
          fxFlex.gt-lg="98"
          fxFlex.gt-md="98"
          fxFlex.gt-sm="98"
          fxFlex.gt-xs="100"
        >
          <h1>No Material Data Information Is Available</h1>
        </div>
      </div>
    </mat-card>

    <mat-card>
      <div *ngIf="closureInfo">
        <div fxLayout="row wrap" class="mb-10">
          <mat-card-title fxFlex>Closure Information</mat-card-title>
        </div>
        <hr />
        <div fxLayout="column" class="mb-20">
          <div class="mb-10"></div>
          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100" *ngIf="closureInfo?.otherClosure === null">
              <label class="lbl">Closure</label><label>{{ closureMaterial?.name }}</label>
            </div>
            <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100" *ngIf="closureInfo?.otherClosure !== null">
              <label class="lbl">Closure</label><label>{{ closureInfo?.otherClosure }}</label>
            </div>
            <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
              <label class="lbl">Extended Flap</label><label>{{ closureInfo?.extendedFlap === true | convertToYesNo }}</label>
            </div>
            <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
              <label class="lbl">Cat 5 Tunnel</label><label>{{ closureInfo?.cat5Tunnel === true | convertToYesNo }}</label>
            </div>
          </div>
          <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
              <label class="lbl">Est Surface Temp ({{ tempUnit ? tempUnit : '' }})</label><label>{{ closureInfo?.estSurfaceTemp }}</label>
            </div>
            <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
              <label class="lbl">Max Temp ({{ tempUnit ? tempUnit : '' }})</label><label>{{ tempUnit === '°F' ? closureMaterial?.maxTempF : closureMaterial?.maxTemp }}</label>
            </div>
            <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
              <label class="lbl">Cost per foot</label><label>${{ closureInfo?.otherClosureCost ? closureInfo?.otherClosureCost : closureMaterial?.costPerSq }}</label>
            </div>
          </div>
          <div fxFlex="100" class="col mt-10">
            <label class="lbl">Notes</label>
            <p>{{ closureInfo?.notes }}</p>
          </div>
        </div>
      </div>
      <div *ngIf="!closureInfo">
        <div
          class="text-center"
          fxFlex.gt-lg="98"
          fxFlex.gt-md="98"
          fxFlex.gt-sm="98"
          fxFlex.gt-xs="100"
        >
          <h1>No Closure Information Is Available</h1>
        </div>
      </div>
    </mat-card>
    <div>
      <mat-card>
        <div *ngIf="sensorInfoObject">
          <div fxLayout="row wrap">
            <mat-card-title fxFlex>Sensor Information</mat-card-title>
          </div>
          <hr />
          <div class="mb-10"></div>
          <div fxLayout="column" class="mb-10">
            <div class="highlight-mat-table">
              <mat-table [dataSource]="sensorsDataSource">
                <ng-container matColumnDef="sensorType">
                  <mat-header-cell *matHeaderCellDef fxFlex="15"> Sensor Type </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="15">{{
                    element?.sensorType !== 'Other' ? element?.sensorType?.id : element?.otherSensorType
                    }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="sensorLocation">
                  <mat-header-cell *matHeaderCellDef fxFlex="30"> Location </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="30">{{
                    element?.sensorLocation !== 'Other' ? element?.sensorLocation : element?.otherSensorLocation
                    }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="sensorConnector">
                  <mat-header-cell *matHeaderCellDef fxFlex="20"> Connector </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="20">{{
                    element?.sensorConnector !== 'Other' ? element?.sensorConnector?.id : element?.otherSensorConnector
                    }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="sensorLeadLength">
                  <mat-header-cell *matHeaderCellDef fxFlex="15"> Lead ({{ measureUnit ? measureUnit : '' }}) </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.sensorLeadLength }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="sensorTempType">
                  <mat-header-cell *matHeaderCellDef fxFlex="25">Sensor Temp Type </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.sensorTempType }}</mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="SensorsdisplayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: SensorsdisplayedColumns"></mat-row>
              </mat-table>
            </div>
            <div fxFlex="100" class="col mt-10">
              <label class="lbl">Notes</label>
              <p>{{ sensorInfoObject?.notes }}</p>
            </div>
          </div>
        </div>
        <div *ngIf="!sensorInfoObject">
          <div
            class="text-center"
            fxFlex.gt-lg="98"
            fxFlex.gt-md="98"
            fxFlex.gt-sm="98"
            fxFlex.gt-xs="100"
          >
            <h1>No Sensor Information Is Available</h1>
          </div>
        </div>
      </mat-card>
      <mat-card>
        <div *ngIf="thermostatInfo">
          <div class="highlight-mat-table">
            <div fxLayout="row wrap">
              <mat-card-title fxFlex>Thermostat Information</mat-card-title>
            </div>
            <hr />
            <div class="mb-10"></div>
            <div fxLayout="column" class="mb-10">
              <mat-table [dataSource]="thermostatdataSource">
                <ng-container matColumnDef="thermostatType">
                  <mat-header-cell *matHeaderCellDef fxFlex="12"> Type </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="15">{{
                    element?.thermostatType?.id ? element?.thermostatType?.id : element.otherThermostatType?.id
                    }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="installationMethod">
                  <mat-header-cell *matHeaderCellDef fxFlex="13"> Installation Method </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="15">{{ element?.installationMethodDTO?.methodName }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="openTemp">
                  <mat-header-cell *matHeaderCellDef fxFlex="14"> Open Temp ({{ tempUnit ? tempUnit : '' }}) </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="13">
                    {{ tempUnit === '°F' ? element?.openTempF : element?.openTemp }}
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="partNumber">
                  <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="11">{{
                    element?.partNumber ? element?.partNumber : element?.otherThermostatPartNumber
                    }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="cost">
                  <mat-header-cell *matHeaderCellDef fxFlex="8"> Cost </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="8">${{ element?.cost ? element?.cost : element?.otherThermostatCost }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="manualReset">
                  <mat-header-cell *matHeaderCellDef fxFlex="10">Manual Reset </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.manualReset | convertToYesNo }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="openOnRise">
                  <mat-header-cell *matHeaderCellDef fxFlex="10">Open On Rise </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.openOnRise | convertToYesNo }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="usaStock">
                  <mat-header-cell *matHeaderCellDef fxFlex="11">USA Stock </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="11">{{ element?.usaStock }}</mat-cell>
                </ng-container>
                <ng-container matColumnDef="vietnamStock">
                  <mat-header-cell *matHeaderCellDef fxFlex="11">Vietnam Stock </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="11">{{ element?.vietnamStock }}</mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="thermostatdisplayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: thermostatdisplayedColumns"></mat-row>
              </mat-table>
              <div fxFlex="100" class="mt-10">
                <label class="lbl">Notes</label>
                <p>{{ thermostatInfo?.notes }}</p>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="!thermostatInfo">
          <div
            class="text-center"
            fxFlex.gt-lg="98"
            fxFlex.gt-md="98"
            fxFlex.gt-sm="98"
            fxFlex.gt-xs="100"
          >
            <h1>No Thermostat Information Is Available</h1>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
  <mat-dialog-actions>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</div>
