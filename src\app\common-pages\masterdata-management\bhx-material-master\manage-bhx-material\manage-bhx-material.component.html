<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #BHXMaterialForm="ngForm" (ngSubmit)="updateBHXMaterial()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutGap="15px">
      <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="show-all-fields" color="warn" [(ngModel)]="bhxMaterial.allFieldsOptionChecked" (change)="toggleShowAllFields(bhxMaterial.allFieldsOptionChecked)">
          Show All Fields
        </mat-checkbox>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="55" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Grouping" [(ngModel)]="bhxMaterial.grouping" name="grouping" #groupingInput="ngModel" required (selectionChange)="checkMaterialGroupingData()">
            <mat-option *ngFor="let group of groupings" [value]="group?.id">
              {{ group?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="groupingInput.touched && groupingInput.invalid">
          <small class="mat-text-warn" *ngIf="groupingInput?.errors?.required">Grouping is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="55" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Part Number" [(ngModel)]="bhxMaterial.partNumber" name="partnumber" #partnumberInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="55" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="App Type" [(ngModel)]="selectedJacketTypes" name="jacketType" #jacketTypeInput="ngModel" multiple>
            <mat-option *ngFor="let jacketType of jacketTypes" [value]="jacketType?.id">
              {{ jacketType?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-form-field>
          <textarea matInput placeholder="Description" rows="3" name="description" #descriptionInput="ngModel" [(ngModel)]="bhxMaterial.description"></textarea>
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Cloth Operation" [(ngModel)]="bhxMaterial.clothOpr" name="clothOpr" #clothOprInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Silicon Operation" [(ngModel)]="bhxMaterial.siliconOpr" name="siliconOpr" #siliconOprInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Inseperable Operation" [(ngModel)]="bhxMaterial.inseparableOpr" name="inseparableOpr" #inseparableOprInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="UOM" [(ngModel)]="bhxMaterial.uom" name="uom" #uomInput="ngModel">
            <mat-option *ngFor="let uom of uomMaster" [value]="uom">
              {{ uom }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput type="number" placeholder="Quantity" [(ngModel)]="bhxMaterial.qty" name="qty" #qtyInput="ngModel" (change)="checkQuantityAndFormula()"
            [disabled]="disQty" />
        </mat-form-field>
      </div>

    </div>
    <div fxLayout="row wrap" fxLayoutAlign="start">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Customer" [(ngModel)]="bhxMaterial.customer" name="customer" #customerInput="ngModel" />
        </mat-form-field>
      </div>
      <div  fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field class="custom-abb">
          <input matInput placeholder="Customer Abbreviation" [(ngModel)]="bhxMaterial.customerAbbreviation" name="customerAbbr" #customerAbbrInput="ngModel" />
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="start" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothCE" color="warn" [indeterminate]="bhxMaterial?.ce === null" [ngModel]="bhxMaterial?.ce === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterial?.ce, ClothCECheckBoxTitle)">
          Cloth CE - {{ bhxMaterial?.ce === true ? checkBoxYesLabel : bhxMaterial?.ce === false ? checkBoxNoLabel : checkBoxNullLabel }}
        </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothUL" color="warn" [indeterminate]="bhxMaterial?.ul === null" [ngModel]="bhxMaterial?.ul === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterial?.ul, ClothULCheckBoxTitle)">
          Cloth UL - {{ bhxMaterial?.ul === true ? checkBoxYesLabel : bhxMaterial?.ul === false ? checkBoxNoLabel : checkBoxNullLabel }}
        </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="hazardous" color="warn" [indeterminate]="bhxMaterial?.hazardous === null" [ngModel]="bhxMaterial?.hazardous === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterial?.hazardous, HazardousCheckBoxTitle)">
          Hazardous -
          {{ bhxMaterial?.hazardous === true ? checkBoxYesLabel : bhxMaterial?.hazardous === false ? checkBoxNoLabel : checkBoxNullLabel }}
        </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="manualreset" color="warn" [indeterminate]="bhxMaterial?.manualResetThermostat === null" [ngModel]="bhxMaterial?.manualResetThermostat === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterial?.manualResetThermostat, ManualResetTSCheckBox)">
          Manual Reset T/S -
          {{
          bhxMaterial?.manualResetThermostat === true
          ? checkBoxYesLabel
          : bhxMaterial?.manualResetThermostat === false
          ? checkBoxNoLabel
          : checkBoxNullLabel
          }}
        </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="privateLabel" color="warn" [indeterminate]="bhxMaterial?.privateLabel === null" [ngModel]="bhxMaterial?.privateLabel === true"
          (ngModelChange)="setCheckBoxTriStateValues(bhxMaterial?.privateLabel, PrivateLabelCheckBox)">
          Private Label -
          {{
          bhxMaterial?.privateLabel === true
          ? checkBoxYesLabel
          : bhxMaterial?.privateLabel === false
          ? checkBoxNoLabel
          : checkBoxNullLabel
          }}
        </mat-checkbox>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex.gt-lg="40" fxFlex.gt-md="40" fxFlex.gt-sm="40" fxFlex.gt-xs="100">
        <mat-slide-toggle *ngIf="bhxMaterial.grouping==1" name="labelEntryRequired" color="warn" [(ngModel)]="bhxMaterial.labelEntryRequired"> Label Entry Required </mat-slide-toggle>
        <mat-slide-toggle name="blocked" color="warn" [(ngModel)]="bhxMaterial.blocked"> Blocked </mat-slide-toggle>
        <mat-slide-toggle name="deleted" color="warn" [(ngModel)]="bhxMaterial.deleted"> Deleted </mat-slide-toggle>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Product Type" [(ngModel)]="selectedProductTypes" name="productType" #productTypeInput="ngModel" multiple>
            <mat-option *ngFor="let prodType of productTypes" [value]="prodType?.id">
              {{ prodType?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Minmum Voltage" [(ngModel)]="bhxMaterial.minVolts" name="minVolt" #minVoltInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Maximum Voltage" [(ngModel)]="bhxMaterial.maxVolts" name="maxVolt" #maxVoltInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Minimum Amperage" [(ngModel)]="bhxMaterial.minAmps" name="minAmp" #minAmpInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Maximum Amperage" [(ngModel)]="bhxMaterial.maxAmps" name="maxAmp" #maxAmpInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Min Lead Length" [(ngModel)]="bhxMaterial.minLead" name="minLead" #minLeadInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Max Lead Length" [(ngModel)]="bhxMaterial.maxLead" name="maxLead" #maxLeadInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Minimum Temperature" [(ngModel)]="bhxMaterial.minTemp" name="minTemp" #minTempInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Maximum Temperature" [(ngModel)]="bhxMaterial.maxTemp" name="maxTemp" #maxTempInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Phase" name="phase" [(ngModel)]="selectedPhase" #phaseSelect="ngModel" multiple>
            <mat-option *ngFor="let phase of phaseTypes" [value]="phase?.id">
              {{ phase?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Install Method" [(ngModel)]="selectedInstallationMethods" name="installMethod" #installMethodInput="ngModel" multiple>
            <mat-option *ngFor="let installMethod of installationMethods" [value]="installMethod?.methodName">
              {{ installMethod?.methodName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Strain Relief" [(ngModel)]="selectedStrainReliefs" name="strainRelief" #strainReliefInput="ngModel" multiple>
            <mat-option *ngFor="let strainRelief of strainReliefs" [value]="strainRelief?.name">
              {{ strainRelief?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Controller" [(ngModel)]="selectedController" name="controller" #controllerInput="ngModel" multiple>
            <mat-option *ngFor="let controller of accessoryControllers" [value]="controller?.name">
              {{ controller?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="76" fxFlex.gt-md="72" fxFlex.gt-sm="60" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Click Add Formula" [(ngModel)]="strFormula" name="formula" #formulaInput="ngModel" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="27" fxFlex.gt-sm="40" fxFlex.gt-xs="100" class="sfl-add-formula-btn">
        <button type="button" class="formula-btn-1" mat-stroked-button color="warn" (click)="addFormula()" [disabled]="disFormulaBtn">
          Add Formula
        </button>
        <button type="button" mat-stroked-button color="warn" (click)="clearFormula()" [disabled]="disFormulaBtn">Clear Formula</button>
      </div>
    </div>

    <!-- Switching view based on the grouping selection -->
    <!-- grouping 1 is for Label -->
    <mat-card sfl-bhx-material-card label-grp *ngIf="bhxMaterial.grouping === 1 || bhxMaterial.allFieldsOptionChecked">
      <mat-card-subtitle>Grouping Label</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutGap="30px">
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Max Diameter" [(ngModel)]="bhxMaterial.maxDiameter" name="maxDiameter" #maxDiameterInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Min Diameter" [(ngModel)]="bhxMaterial.minDiameter" name="minDiameter" #minDiameterInput="ngModel" />
          </mat-form-field>
        </div>
      </div>
    </mat-card>
    <!-- grouping 2 is for Element Sensor -->
    <mat-card sfl-bhx-material-card element-sensor-grp *ngIf="bhxMaterial.grouping === 2 || bhxMaterial.allFieldsOptionChecked">
      <mat-card-subtitle>Grouping Element Sensor</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Element Type" [(ngModel)]="elementType" name="elementType" #elementTypeInput="ngModel" multiple>
              <mat-option *ngFor="let elementType of elementTypes" [value]="elementType?.id">
                {{ elementType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Wire Type" [(ngModel)]="wireType" name="wireType" #wireTypeInput="ngModel" multiple>
              <mat-option *ngFor="let wireType of wireTypes" [value]="wireType?.id">
                {{ wireType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Connector" [(ngModel)]="sensConn" name="sensConn" #sensConnInput="ngModel" multiple>
              <mat-option *ngFor="let sensConn of sensorsConnectors" [value]="sensConn?.id">
                {{ sensConn?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageSensorConnectors()">Manage Sensor Connectors</button>
          </div>
        </div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Type" [(ngModel)]="sensorType" name="sensorType" #sensorTypeInput="ngModel" multiple>
              <mat-option *ngFor="let sensorType of sensorTypes" [value]="sensorType?.id">
                {{ sensorType?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageSensorTypes()">Manage Sensor Types</button>
          </div>
        </div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sleeving" [(ngModel)]="selectedSleeving" name="sleeving" #sleevingInput="ngModel" multiple>
              <mat-option *ngFor="let sleeving of sleevingTypes" [value]="sleeving?.name">
                {{ sleeving?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageSleevingTypes()">Manage Sleevings</button>
          </div>
        </div>
      </div>

      <div fxLayout="row wrap" fxLayoutGap="30px">
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Max Length" [(ngModel)]="bhxMaterial.maxLength" name="maxLength" #maxLengthInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Min Length" [(ngModel)]="bhxMaterial.minLength" name="minLength" #minLengthInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Min Jumper" [(ngModel)]="bhxMaterial.minJumpers" name="minJumpers" #minJumpersInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-checkbox name="thermostat" color="warn" [indeterminate]="bhxMaterial?.thermostat === null" [ngModel]="bhxMaterial?.thermostat === true"
            (ngModelChange)="setCheckBoxTriStateValues(bhxMaterial?.thermostat, ThermostatCheckBoxTitle)">
            Thermostat -
            {{
            bhxMaterial?.thermostat === true ? checkBoxYesLabel : bhxMaterial?.thermostat === false ? checkBoxNoLabel : checkBoxNullLabel
            }}
          </mat-checkbox>
        </div>
      </div>
    </mat-card>
    <!-- grouping 3 is for Facing Liner Closure -->
    <mat-card class="sfl-bhx-material-card facing-liner-grp" *ngIf="bhxMaterial.grouping === 3 || bhxMaterial.allFieldsOptionChecked">
      <mat-card-subtitle>Grouping Facing Liner Closure</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Layered (Material)" [(ngModel)]="selectedLayeredMaterial" name="layered" #layeredInput="ngModel" multiple>
              <mat-option *ngFor="let material of materialMaster" [value]="material?.material">
                {{ material?.material }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageMaterial()">Manage Material</button>
          </div>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Connector" [(ngModel)]="sensConn" name="sensConn" #sensConnInput="ngModel" multiple>
              <mat-option *ngFor="let sensConn of sensorsConnectors" [value]="sensConn?.id">
                {{ sensConn?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageSensorConnectors()">Manage Sensor Connectors</button>
          </div>
        </div>
      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Max Diameter" [(ngModel)]="bhxMaterial.maxDiameter" name="maxDiameter" #maxDiameterInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Min Diameter" [(ngModel)]="bhxMaterial.minDiameter" name="minDiameter" #minDiameterInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Closure Material" [(ngModel)]="selectedClosuredMaterial" name="closure" #closureInput="ngModel" multiple>
              <mat-option *ngFor="let closure of closureMaterial" [value]="closure?.name">
                {{ closure?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </mat-card>
    <!-- grouping 4 is for Wire Plugging -->
    <mat-card class="sfl-bhx-material-card wire-plugging-grp" *ngIf="bhxMaterial.grouping === 4 || bhxMaterial.allFieldsOptionChecked">
      <mat-card-subtitle>Grouping Wire Plugging</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Element Type" [(ngModel)]="elementType" name="elementType" #elementTypeInput="ngModel" multiple>
              <mat-option *ngFor="let elementType of elementTypes" [value]="elementType?.id">
                {{ elementType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Wire Type" [(ngModel)]="wireType" name="wireType" #wireTypeInput="ngModel" multiple>
              <mat-option *ngFor="let wireType of wireTypes" [value]="wireType?.id">
                {{ wireType?.value }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Plug" [(ngModel)]="selectedPlugs" name="plug" #plugInput="ngModel" [disabled]="noPlug" (ngModelChange)="changePlugData($event)" multiple>
              <mat-option *ngFor="let plug of plugs" [value]="plug?.plugName">
                {{ plug?.plugName }} {{ plug?.jacketType ? '- ' + plug?.jacketType : '' }}
              </mat-option>
              <mat-option value="none">None</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="managePlugs()">Manage Plug</button>
          </div>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Connector" [(ngModel)]="selectedConnectors" name="connector" #connectorInput="ngModel" [disabled]="noConnector"
              (ngModelChange)="changeConnectorData($event)" multiple>
              <mat-option *ngFor="let plug of plugs" [value]="plug?.plugName">
                {{ plug?.plugName }} {{ plug?.jacketType ? '- ' + plug?.jacketType : '' }}
              </mat-option>
              <mat-option value="none">None</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="managePlugs()">Manage Plug</button>
          </div>
        </div>
      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sensor Connector" [(ngModel)]="sensConn" name="sensConn" #sensConnInput="ngModel" multiple>
              <mat-option *ngFor="let sensConn of sensorsConnectors" [value]="sensConn?.id">
                {{ sensConn?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageSensorConnectors()">Manage Sensor Connectors</button>
          </div>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Sleeving" [(ngModel)]="selectedSleeving" name="sleeving" #sleevingInput="ngModel" multiple>
              <mat-option *ngFor="let sleeving of sleevingTypes" [value]="sleeving?.name">
                {{ sleeving?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageSleevingTypes()">Manage Sleevings</button>
          </div>
        </div>
      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Lead Type" name="leadType" [(ngModel)]="selectedLeadTypes" multiple>
              <mat-option *ngFor="let leadType of leadTypes" [value]="leadType?.leadName">{{ leadType?.leadName }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <div>
            <button mat-stroked-button type="button" color="warn" (click)="manageLeadTypes()">Manage Lead Types</button>
          </div>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <mat-select placeholder="Green Light" [(ngModel)]="selectedGreenLights" name="greenLight" #greenLightInput="ngModel" multiple>
              <mat-option *ngFor="let greenLight of greenLights" [value]="greenLight?.name">
                {{ greenLight?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100"></div>
      </div>
    </mat-card>
    <!-- grouping 5 is for Operation -->
    <mat-card class="sfl-bhx-material-card operation-grp" *ngIf="bhxMaterial.grouping === 5 || bhxMaterial.allFieldsOptionChecked">
      <mat-card-subtitle>Grouping Operation</mat-card-subtitle>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput placeholder="Operation" [(ngModel)]="bhxMaterial.operationName" name="operationName" #operationNameInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput placeholder="Sequence" [(ngModel)]="bhxMaterial.sequence" name="sequence" #sequenceInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Operation Number" [(ngModel)]="bhxMaterial.opNumber" name="opNumber" #opNumberInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Production Hours" [(ngModel)]="bhxMaterial.prodHrs" name="prodHrs" #prodHrsInput="ngModel" />
          </mat-form-field>
        </div>
        <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-form-field>
            <input matInput type="number" placeholder="Setup Hours" [(ngModel)]="bhxMaterial.setupHrs" name="setupHrs" #setupHrsInput="ngModel" />
          </mat-form-field>
        </div>
      </div>
    </mat-card>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="BHXMaterialForm.invalid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
