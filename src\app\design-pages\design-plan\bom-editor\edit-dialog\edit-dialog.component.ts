import {Component, Inject, OnDestroy} from '@angular/core';
import {Observable, Subscription} from 'rxjs';
import {SnakbarService} from 'src/app/shared';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {BOMEditorComponent} from '../bom-editor.component';
import {BomEditorService} from '../bom-editor.service';
import {LabelDetailedEntry, OperationsMasterData} from '../bom-editor.model';
import {FormControl} from '@angular/forms';
import {Variable} from '../../../../shared/constants/Variable.constants';

@Component({
  selector: 'sfl-edit-dialog',
  templateUrl: './edit-dialog.component.html'
})
export class EditDialogComponent implements OnDestroy {
  subscription: Subscription = new Subscription();
  data;
  type: string;
  selection: string;
  operationControl = new FormControl();
  operationsObservable$: Observable<OperationsMasterData[]>;
  color = Variable.warnRed;
  showLoader = false;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public dialogRef: MatDialogRef<BOMEditorComponent>,
    private snakbarService: SnakbarService,
    private bomeditorService: BomEditorService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.data = {...data.data};
    this.type = data.type;
  }

  saveOperaton() {
    if (this.type === 'facing') {
      this.updateFacingLinerClosures(this.data);
    } else if (this.type === 'sensors') {
      this.updateSensors(this.data);
    } else if (this.type === 'element') {
      this.updateElementsAndSensor(this.data);
    } else if (this.type === 'wiring') {
      this.updateWiring(this.data);
    } else if (this.type === 'labels') {
      this.updateLables(this.data);
    } else if (this.type === 'operation') {
      this.updateOperation(this.data);
    } else if (this.type === 'labels-entry') {
      this.updatelabelConfig(this.data);
    }
  }

  async updateFacingLinerClosures(element) {
    this.bomeditorService.editFacingLinerClosures(element).subscribe(() => {
        this.showLoader = false;
        this.snakbarService.success('Data updated successfully');
        this.savecloseDialog();
      },
      (error) => {
        this.showLoader = false;
        this.snakbarService.error('Something went wrong');
        this.savecloseDialog();
      }
    );
  }

  async updateOperation(element) {
    this.bomeditorService.editOperation(element).subscribe(() => {
        this.showLoader = false;
        this.snakbarService.success('Data updated successfully');
        this.savecloseDialog();
      },
      (error) => {
        this.showLoader = false;
        this.snakbarService.error('Something went wrong');
        this.savecloseDialog();
      }
    );
  }

  updatelabelConfig(element) {
    this.showLoader = true;
    let labelArray: LabelDetailedEntry[] = [];
    labelArray.push(element);
    this.bomeditorService
      .updateAllLabelEntries(labelArray)
      .subscribe(() => {
          this.showLoader = false;
          this.snakbarService.success('Data updated successfully');
          this.savecloseDialog();
        },
        (error) => {
          this.showLoader = false;
          this.snakbarService.error('Something went wrong');
          this.savecloseDialog();
        }
      );
  }

  async updateElementsAndSensor(element) {
    this.bomeditorService.editElementsAndSensor(element).subscribe(() => {
        this.showLoader = false;
        this.snakbarService.success('Data updated successfully');
        this.savecloseDialog();
      },
      (error) => {
        this.showLoader = false;
        this.snakbarService.error('Something went wrong');
        this.savecloseDialog();
      }
    );
  }

  async updateWiring(element) {
    this.bomeditorService.editWirePluggings(element).subscribe(() => {
        this.showLoader = false;
        this.snakbarService.success('Data updated successfully');
        this.savecloseDialog();
      },
      (error) => {
        this.showLoader = false;
        this.snakbarService.error('Something went wrong');
        this.savecloseDialog();
      }
    );
  }

  async updateLables(element) {
    this.bomeditorService.editLabels(element).subscribe(() => {
        this.showLoader = false;
        this.snakbarService.success('Data updated successfully');
        this.savecloseDialog();
      },
      (error) => {
        this.showLoader = false;
        this.snakbarService.error('Something went wrong');
        this.savecloseDialog();
      }
    );
  }


  async updateSensors(element) {
    this.bomeditorService.editElementsAndSensor(element).subscribe(() => {
        this.showLoader = false;
        this.snakbarService.success('Data updated successfully');
        this.savecloseDialog();
      },
      (error) => {
        this.showLoader = false;
        this.snakbarService.error('Something went wrong');
        this.savecloseDialog();
      }
    );
  }

  savecloseDialog(): void {
    this.dialogRef.close('save');
  }

  closeDialog(): void {
    this.dialogRef.close('close');
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
