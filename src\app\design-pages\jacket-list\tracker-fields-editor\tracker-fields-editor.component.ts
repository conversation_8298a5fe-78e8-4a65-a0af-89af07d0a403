import { I } from "@angular/cdk/keycodes";
import { DatePipe } from "@angular/common";
import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from "@angular/material";
import { Subscription } from "rxjs";
import { DesignEngineeringQuoteTracker, FilterQuoteTracker } from "src/app/quote-tracker/quote-tracker.model";
import { SharedService } from "src/app/shared";
import { Values } from "src/app/shared/constants/values.constants";
import { Variable } from "src/app/shared/constants/Variable.constants";
import { SalesAssociate } from "../../dashboard/dashboard.model";
import { Statuses } from "../../ecr-management/ecr-management.model";
import { EditableTrackerFields } from "../jacket-list.model";
import { JacketListService } from "../jacket-list.service";

@Component({
  selector: "sfl-tracker-fields-editor",
  templateUrl: "./tracker-fields-editor.component.html",
})
export class TrackerFieldsEditorComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  _quotationId: number;
  _salesOrderNumber: number;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  showLoader = false;
  title: string;
  trackerFields: EditableTrackerFields = new EditableTrackerFields();
  trackerFieldsForm: FormGroup;
  statuses: Statuses[];
  colorStatus: Statuses;
  salesassociates: SalesAssociate[];
  designers: SalesAssociate[] = [];
  productTypes = Values.TRACKER_FIELDS_PRODUCT_TYPES;
  countries: object = Values.ManufacturingCountries;
  _quotationStatusId: number;
  designRowColorDataObs: DesignEngineeringQuoteTracker = new DesignEngineeringQuoteTracker();
  primaryColorValue: string;
  type = Variable.typeDesign;
  designerCountry: string;
  filterRequest: FilterQuoteTracker = new FilterQuoteTracker();

  constructor(
    public readonly dialogRef: MatDialogRef<TrackerFieldsEditorComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    private readonly formBuilder: FormBuilder,
    private readonly sharedService: SharedService,
    private readonly jacketListService: JacketListService,
    private readonly datePipe: DatePipe
   ) {
    this._quotationId = data.quotationId;
    this._quotationStatusId = data.designStatusId;
    this._salesOrderNumber = data.salesOrderNumber ? data.salesOrderNumber : "";
    this.title = "Update Tracker Fields For SO# " + this._salesOrderNumber;
  }

  async ngOnInit() {
    this.createEditableTrackerFieldForm();
    this.getQuoteStatuses();
    await this.getSaleAssociate();
    await this.getAllDesigners();
    await this.getDesignQuoteTrackerDefaultValues();
    this.primaryColorValue = this.trackerFields.primaryColorValue;
  }

  // used to get the previously set values for the design tracker/ default values
  getDesignQuoteTrackerDefaultValues() {
    this.showLoader = true;
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService
          .getDesignQuoteTrackerDefaultValues(this.type,this._quotationId)
          .subscribe(
            (defaultValues: EditableTrackerFields) => {
              this.trackerFields = defaultValues;
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            },
            () => {
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            }
          )
      );
    });
  }

  // used to get the quotes statuses
  getQuoteStatuses() {
    this.subscription.add(
      this.sharedService
        .getStatuesByTypeOrderByOrderNumber()
        .subscribe((res: Array<Statuses>) => {
          if (res) {
            this.statuses = [];
            for (const statusType of res) {
              if (statusType.type === "both" || statusType.type === "design") {
                this.statuses.push(statusType);
              }
            }
            res = this.statuses;
          }
        })
    );
  }

  // used to get sales associates list
  getSaleAssociate() {
    this.showLoader = true;
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService.getDesignSalesAssociate(false).subscribe(
          (res: Array<SalesAssociate>) => {
            this.salesassociates = res;
            this.showLoader = false;
            resolve();
          },
          () => {
            resolve();
            this.showLoader = false;
          }
        )
      );
    });
  }

  // used to get the available designers
  getAllDesigners() {
    this.showLoader = true;
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService.getAllDesigners().subscribe(
          (res: Array<SalesAssociate>) => {
            this.designers = res;
            this.showLoader = false;
            resolve();
          },
          () => {
            resolve();
            this.showLoader = false;
          }
        )
      );
    });
  }

  // used to create form
  createEditableTrackerFieldForm() {
    this.trackerFieldsForm = this.formBuilder.group({
      quotationId: new FormControl(),
      soNumber: new FormControl(),
      dollarAmount: new FormControl(),
      designLocation: new FormControl(),
      folderSubmittedDate: new FormControl(),
      shipDate: new FormControl(),
      releaseDate: new FormControl(),
      customerName: new FormControl(),
      quotationStatusId: new FormControl(),
      ofa1Date: new FormControl(),
      approval1Date: new FormControl(),
      ofa2Date: new FormControl(),
      approval2Date: new FormControl(),
      salesRepId: new FormControl(),
      salesRepName: new FormControl(),
      productType: new FormControl(),
      assignedDesignerId: new FormControl(),
      appEnggAssigned: new FormControl(),
      designComments: new FormControl(),
      noOfCustomRevision: new FormControl(),
      customNoOfDesigns: new FormControl(),
      projectTitle: new FormControl(),
      designerCountry: new FormControl(),
      designerUpdate: new FormControl()
    });

  }

  // used to set up the default values for the form
  setDefaultValuesForForm() {
    this.trackerFieldsForm.controls.quotationId.patchValue(this._quotationId);
    this.trackerFieldsForm.controls.designLocation.patchValue(
      this.trackerFields.designLocation
    );
    this.trackerFieldsForm.controls.folderSubmittedDate.patchValue(
      this.trackerFields.folderSubmittedDate
        ? new Date(this.trackerFields.folderSubmittedDate)
        : ""
    );
        this.trackerFieldsForm.controls.shipDate.patchValue(
      this.trackerFields.shipDate ? new Date(this.trackerFields.shipDate) : ""
    );
    this.trackerFieldsForm.controls.releaseDate.patchValue(
      this.trackerFields.releaseDate ? new Date(this.trackerFields.releaseDate) : ""
    );
    this.trackerFieldsForm.controls.quotationStatusId.patchValue(
      this.trackerFields.quotationStatusId
    );
    this.trackerFieldsForm.controls.soNumber.patchValue(
      this.trackerFields.soNumber
    );
    this.trackerFieldsForm.controls.dollarAmount.patchValue(
      this.trackerFields.dollarAmount
    );
    this.trackerFieldsForm.controls.projectTitle.patchValue(
      this.trackerFields.projectTitle
    );
    this.trackerFieldsForm.controls.ofa1Date.patchValue(
      this.trackerFields.ofa1Date ? new Date(this.trackerFields.ofa1Date) : ""
    );
    this.trackerFieldsForm.controls.approval1Date.patchValue(
      this.trackerFields.approval1Date
        ? new Date(this.trackerFields.approval1Date)
        : ""
    );
    this.trackerFieldsForm.controls.ofa2Date.patchValue(
      this.trackerFields.ofa2Date ? new Date(this.trackerFields.ofa2Date) : ""
    );

    this.trackerFieldsForm.controls.approval2Date.patchValue(
      this.trackerFields.approval2Date
        ? new Date(this.trackerFields.approval2Date)
        : ""
    );
    this.trackerFieldsForm.controls.salesRepId.patchValue(
      this.trackerFields.salesRepId
    );
    this.trackerFieldsForm.controls.appEnggAssigned.patchValue(
      this.trackerFields.appEnggAssigned
    );
    this.trackerFieldsForm.controls.productType.patchValue(
      this.trackerFields.productType
    );
    this.trackerFieldsForm.controls.designerCountry.patchValue(
      this.trackerFields.designerCountry
    );
    this.trackerFieldsForm.controls.designerUpdate.patchValue(
      this.trackerFields.designerUpdate
    );
    this.trackerFieldsForm.controls.customerName.patchValue(
      this.trackerFields.customerName
    );
    this.trackerFieldsForm.controls.assignedDesignerId.patchValue(
      this.trackerFields.assignedDesignerId
    );
    this.trackerFieldsForm.controls.designComments.patchValue(
      this.trackerFields.designComments
    );
    this.trackerFieldsForm.controls.noOfCustomRevision.patchValue(
      this.trackerFields.noOfCustomRevision
    );
    this.trackerFieldsForm.controls.customNoOfDesigns.patchValue(
      this.trackerFields.customNoOfDesigns
    );
  }

  // used to save the tracker fields
  saveTrackerFields() {
    this.showLoader = true;
    this.trackerFieldsForm.controls.folderSubmittedDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.folderSubmittedDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.ofa1Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.ofa1Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.approval1Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.approval1Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.ofa2Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.ofa2Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.approval2Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.approval2Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.shipDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.shipDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.releaseDate.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.releaseDate.value,
        Values.dateFormat.formatHyphen
      )
    );
    const salesRepresentative = this.salesassociates.find(
      (salesRep) =>
        salesRep.id === this.trackerFieldsForm.controls.salesRepId.value
    );
    if (salesRepresentative) {
      this.trackerFieldsForm.controls.salesRepName.setValue(
        salesRepresentative.firstName + " " + salesRepresentative.lastName
      );
    }
    const newCountry = this.designers.find(designerNewCountry => designerNewCountry.id === this.trackerFieldsForm.value.assignedDesignerId);
    const isNewCountry = this.designers.find(designerNewCountry => designerNewCountry.id === this.trackerFields.assignedDesignerId);
    if(newCountry) {
    this.trackerFieldsForm.controls.designerCountry.patchValue(newCountry.country);
    }
    if(isNewCountry) {
    this.trackerFieldsForm.controls.designerUpdate.patchValue(newCountry.country === isNewCountry.country ? false : true);
    }
    this.subscription.add(
      this.jacketListService
        .saveDesignQuoteTrackerFields(this.trackerFieldsForm.value,'All')
        .subscribe(
          (res: EditableTrackerFields) => {
            this.trackerFields = res;
            this.showLoader = false;
            this.dialogRef.close(res);
          },
          () => {
            this.showLoader = false;
          }
        )
    );
  }

  // used to close the dialog
  closeDialog() {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
