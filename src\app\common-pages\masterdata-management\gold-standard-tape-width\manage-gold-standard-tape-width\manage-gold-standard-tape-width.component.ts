import { Component, OnInit, OnD<PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { GoldStandardTapeWidthMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-manage-gold-standard-tape-width',
  templateUrl: './manage-gold-standard-tape-width.component.html'
})
export class ManageGoldStandardTapeWidthComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  goldStandardTapeWidth: GoldStandardTapeWidthMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  goldStandardTapeWidthTypes = Values.GoldStandardTapeWidthTypes;
  constructor(
    public readonly dialogRef: MatDialogRef<ManageGoldStandardTapeWidthComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.goldStandardTapeWidth = data;
  }

  ngOnInit() {
    this.goldStandardTapeWidth = this.goldStandardTapeWidth.id
      ? Object.assign({}, this.goldStandardTapeWidth)
      : new GoldStandardTapeWidthMaster();
    this.goldStandardTapeWidth.id ? (this.title = 'Update Gold Standard Tape Width') : (this.title = 'Add Gold Standard Tape Width');
  }

  updateGoldStandardTapeWidth() {
    this.showLoader = true;
    if (this.goldStandardTapeWidth.id) {
      this.subscription.add(
        this.masterDataService.updateGoldStandardTapeWidth(this.goldStandardTapeWidth).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addGoldStandardTapeWidth(this.goldStandardTapeWidth).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
