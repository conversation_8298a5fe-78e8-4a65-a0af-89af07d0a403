@import '~bootstrap/dist/css/bootstrap.css';
.sticky-table-header {
  background-color: #f1f1f1;
  color: #0000008a;
  z-index: 1000 !important;
  position: sticky;
}
.sticky-col {
  position: -webkit-sticky;
  position: sticky;
  left: 40px;
  background-color: #f1f1f1;
  z-index: 1; /* Ensure sticky column stays above other columns */
}
.sticky-col-sales {
  position: -webkit-sticky;
  position: sticky;
  left: 0px;
  background-color: #f1f1f1;
  z-index: 1; /* Ensure sticky column stays above other columns */
}
.form-container .mat-form-field {
  margin-bottom: 15px; /* Adjust the value as needed */
}

tr td {
  font-size: 14px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  border: 1px solid black;
  padding: 2px !important;
  line-height: 1;
  height: auto;
}
td{
  cursor: pointer;
}
/* table {
  table-layout:fixed;
} */

.overflow-text-css.highlighted {
  background: green;
}

.dynamic-virtual-scroll {
  height: 80vh !important;
}

.highlight{
  background: #42A948; /* green */
}

.link {
  color: black;
  font-weight: bold;
  text-decoration: underline;
}

.td_size{
  min-width:210px;
}

.td-size-customer{
  white-space: nowrap;        /* Prevents text from wrapping to the next line */
  overflow: hidden;           /* Hides overflowed text */
  text-overflow: ellipsis;    /* Adds ellipsis (...) when text is truncated */
  max-width: 120px;
}

.td-size-status{
  white-space: nowrap;        /* Prevents text from wrapping to the next line */
  overflow: hidden;           /* Hides overflowed text */
  text-overflow: ellipsis;    /* Adds ellipsis (...) when text is truncated */
  max-width: 120px;
}

.td_size_comments{
text-align: left;
}

.td_size_projectTitle{
  white-space: nowrap;        /* Prevents text from wrapping to the next line */
  overflow: hidden;           /* Hides overflowed text */
  text-overflow: ellipsis;    /* Adds ellipsis (...) when text is truncated */
  max-width: 120px;
}

.td-size-productType{
  min-width: 120px;
}

.td_size_shipDate{
  min-width: 80px;
}

.td_size_designer{
  min-width: 80px;
}

.td_size_ofa{
  min-width: 80px;
}

.td_size_app{
  min-width: 135px;
}

.td_size_action{
  max-width: 0px;
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  background-color: #f1f1f1;
  z-index: 2; /* Ensure sticky column stays above other columns */
}

.menu-icon{
  height: 0px;
  font-size: 0px;
}

.link:hover {
  color: black;
  text-decoration: none;
  text-decoration: underline;
}

th{
  cursor: pointer;
  text-align: left; /* Aligns text to the left */
  padding: 8px; /* Adds padding for space */
  position: relative; /* Positions the elements relatively */
}

.header-content {
  display: flex; /* Uses flexbox layout */
  align-items: center; /* Centers items vertically */
  justify-content: space-between; /* Spaces items horizontally */
  gap: 5px; /* Adds gap between text and icon */
}

.open-doc {
  flex-grow: 1; /* Allows the text to take up available space */
  margin-right: 5px; /* Adds space to the right of text */
}

.material-icons {
  font-size: 20px; /* Sets the icon size */
  vertical-align: middle; /* Aligns icons vertically to the middle */
}

/* Ensures that all icons are the same size and centered */
th i {
  line-height: 1; /* Sets line height for alignment */
  vertical-align: middle; /* Aligns vertically to the middle */
}

#search-popup {
  position: fixed;
  top: 90px;
  right: 20px;
  background-color: #f1f3f4;
  border: 1px solid #dadce0;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  padding: 6px;
  z-index: 1000;
}

#search-input {
  border: none;
  background-color: transparent;
  font-size: 14px;
  outline: none;
  width: 150px;
  margin-right: 8px;
}

.button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin: 0 2px;
  color: #5f6368;
  font-size: 18px;
}

.button:hover {
  color: #202124;
}

#match-count {
  font-size: 12px;
  color: #5f6368;
  margin: 0 8px;
}

#close-button {
  margin-left: 8px;
}

.highlight{
  background-color: orange;
  padding: 10px;
}

.previous-highlight{
  background-color: yellow;
  padding: 10px;
}
