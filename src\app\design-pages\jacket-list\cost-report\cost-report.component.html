<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
  class="sfl-global-spinner-loader"
  [mode]="mode"
  [color]="color"
  [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
    Cost Report
    <hr>
</h2>

<mat-dialog-content>
    <div class="cust-table">
        <table aria-describedby="costing_table">
          <tr class="mat-header-row">
            <th class="mat-header-cell" scope="col">
              Part Number
            </th>
            <th class="mat-header-cell" scope="col">
              List
            </th>
            <th class="mat-header-cell" scope="col">
              Net
            </th>
            <th class="mat-header-cell" scope="col">
              Material
            </th>
            <th class="mat-header-cell" scope="col">
              Labor
            </th>
            <th class="mat-header-cell" scope="col">
              Burden
            </th>
            <th class="mat-header-cell" scope="col">
              Sub Cnt
            </th>
            <th class="mat-header-cell" scope="col">
              Mtl Burd
            </th>
            <th class="mat-header-cell" scope="col">
              Total
            </th>
            <th class="mat-header-cell" scope="col">
              Margin
            </th>
            <th class="mat-header-cell" scope="col">
              Status
            </th>
          </tr>
          <tbody *ngFor="let data of dataSource.data; let i = index" class="text-center">
            <tr id="collaps" (click)="toggleHiddenRow[i] = !toggleHiddenRow[i]" [ngClass]="(data.inActive || data.onHold)? 'open-doc' : '' ">
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.partNumber}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.listPrice | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.netPrice | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.materialCost | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.laborCost | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.burdenCost | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.subConstCost | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.mtlBurCost | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.totalCost | currency}}
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                {{data?.margin }}%
              </td>
              <td class="mat-cell" [ngClass]="(data?.inActive || data?.onHold ? 'inactive-onhold-costreport' : '')" [matTooltip]="data?.onHold == true || data?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
                &nbsp;
              </td>
            </tr>
            <tr *ngFor="let childCostDetail of data?.childCostDetailsDTOS;" class="inner-row" [hidden]="!toggleHiddenRow[i]">
              <td class="mat-cell">
                {{childCostDetail?.partNumber}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.listPrice | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.netPrice | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.materialCost | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.laborCost | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.burdenCost | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.subConstCost | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.mtlBurCost | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.totalCost | currency}}
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.margin }}%
              </td>
              <td class="mat-cell">
                {{ childCostDetail?.status }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
</mat-dialog-content>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
      <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </div>
  <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" (click)="downloadCostReportExcel()">Download Excel</button>
  </div>
</mat-dialog-actions>
