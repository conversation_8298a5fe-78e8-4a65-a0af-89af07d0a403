import {QuotationSearchFilter, Status, StatusCount} from './../../admin-pages/dashboard/dashboard.model';
import {SharedService} from 'src/app/shared/service/shared.service';
import {QuotationPageable, SalesAssociate} from './dashboard.model';
import {DashboardService} from './dashboard.service';
import {fromEvent, Observable, Subscription} from 'rxjs';
import {AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {MatDialog, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {debounceTime, distinctUntilChanged, map, startWith, switchMap, tap} from 'rxjs/operators';
import {Values} from 'src/app/shared/constants/values.constants';
import {Title} from '@angular/platform-browser';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {DisplayColumns} from 'src/app/shared/constants/displayColName.constants';
import {FormControl} from '@angular/forms';
import {PopupSize} from '../../shared/constants/popupsize.constants';
import {SearchEcrComponent} from '../../layouts/search-ecr/search-ecr-component';
import {SearchMfgPartComponent} from '../design-plan/bom-editor/search-mfg-part/search-mfg-part-component';

@Component({
  selector: 'sfl-dashboard',
  templateUrl: './dashboard.component.html'
})
export class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {
  noOfRecord: number = Values.pageSize;
  subscription = new Subscription();
  salesassociate: SalesAssociate[];
  statuscount: StatusCount;
  displayedColumns = DisplayColumns.Cols.DesignQuotationList;
  quotationId: number;
  quotationSearchFilter = new QuotationSearchFilter();
  totalPages: number;
  showLoader = false;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.itemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.itemsPerPage;
  length: number;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortBy;
  sortByDate = Variable.sortByQuotationStatus;
  dataSource = new MatTableDataSource();
  status: Status[];
  statusObservable$: Observable<Status[]>;
  statusControl = new FormControl();
  lastFilter = '';

  statusCount: StatusCount;
  quotationPageable: QuotationPageable = new QuotationPageable();

  isNoDataFound = this.dataSource.connect().pipe(map(data => data.length === 0));

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  keyupSubscription: Subscription;
  userId: number;
  userRole: string;

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('filterProjectTitle') filterProjectTitle: ElementRef;

  constructor(private dashboardService: DashboardService, private sharedService: SharedService, private titleService: Title, private readonly matDialog: MatDialog,) {
  }

  async ngOnInit() {
    this.titleService.setTitle('Dashboard - Design Eng');
    this.subscription.add(
      this.sharedService.setDesignQuotationSearchObject(new QuotationSearchFilter()) // if user leaves the page, all the search param should be blank or when user re-visit the page user would get the previously search result.
    );
    this.userRole = this.sharedService.getRole();
    this.status = await this.sharedService.getAllStatus();
    this.status = this.status.filter(
      status => (status.type === Values.DesignEng || status.type === Values.Both) && status.status !== Values.Archived
    );
    this.getAdvSearch();
    this.onLoadData();
    this.userId = this.sharedService.getUserId();
  }

  async onLoadData() {
    this.countStatus();
    await this.getSaleAssociate();
  }

  // used to handle the search quotations from the available list
  ngAfterViewInit() {
    const pageable = {
      page: this.initialPageIndex,
      size: this.pageSize,
      sort: this.sortField + ',' + this.sortOrder,
      direction: this.sortOrder
    };
    this.keyupSubscription = fromEvent(this.filterProjectTitle.nativeElement, 'keyup')
      .pipe(
        debounceTime(600),
        map((event: Event) => (<HTMLInputElement>event.target).value.trim()),
        distinctUntilChanged(),
        tap(() => (this.showLoader = true)),
        switchMap(quotationNumber => this.dashboardService.searchDesignQuotations(quotationNumber, pageable))
      )
      .subscribe(
        (res: QuotationPageable) => {
          this.quotationPageable = res;
          this.length = res.totalElements;
          this.totalPages = res.totalPages;
          this.pageIndex = res.number;
          this.createMatTable(this.quotationPageable);
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      );
  }

  countStatus() {
    this.dashboardService.countStatus().subscribe((res: StatusCount) => {
      this.statuscount = res;
      this.statusObservable$ = this.statusControl.valueChanges.pipe(
        startWith<string | Status[]>(''),
        map(value => (typeof value === 'string' ? value : this.lastFilter)),
        map(filter => this.filterStatus(filter))
      );
    });
  }

  getSaleAssociate() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.dashboardService.getSalesAssociate(false).subscribe(
        (res: Array<SalesAssociate>) => {
          this.salesassociate = res;
          this.showLoader = false;
          resolve();
        },
        () => (this.showLoader = false)
      );
    });
  }

  filterStatus(filter: string): Status[] {
    this.lastFilter = filter;
    if (filter) {
      return this.status.filter(option => {
        if (option.status !== null) {
          return (
            option.status.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.status ? this.status.slice() : [];
    }
  }

  searchEcr() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {};
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-tracker-fields-model';
    const dialogRef = this.matDialog.open(SearchEcrComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(() => {
    });
  }

  displayStatus(materialStatuses: Status[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(materialStatuses)) {
      materialStatuses.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.status;
        }
      });
    } else {
      displayValue = materialStatuses;
    }
    return displayValue;
  }


  // allows to search using the global search for design eng side
  getAdvSearch() {
    if (this.sharedService.refreshDesignSearchResult) {
      this.subscription.add(
        this.sharedService.refreshDesignSearchResult.subscribe(result => {
          if (result.customerName || result.salesAssociateName || result.status) {
            this.showLoader = true;
            this.quotationSearchFilter = result;
            this.searchDesignQuotationsList(this.initialPageIndex, this.pageSize);
            this.getQuotationCount();
          } else {
            this.quotationSearchFilter = new QuotationSearchFilter(); // if global search is reset search object should be reset as this.quotationSearchFilter is global object. // Fetching quotations without any filter applied.
            this.searchDesignQuotationsList(this.initialPageIndex, this.pageSize);
            this.getQuotationCount();
          }
        })
      );
    }
  }

  // used to filter the data source of Quotation list
  searchDesignQuotationsList(pageIndex, pageSize, showloader = true) {
    const pageable = {
      page: pageIndex,
      size: pageSize,
      sort: this.sortByDate + ',' + this.sortOrder,
      direction: this.sortOrder
    };
    this.quotationSearchFilter.projectTitle =
      this.quotationSearchFilter.projectTitle === '' ? undefined : this.quotationSearchFilter.projectTitle;
    this.showLoader = showloader;
    this.subscription.add(
      this.dashboardService
        .searchDesignQuotations(this.quotationSearchFilter.projectTitle, pageable)
        .subscribe((res: QuotationPageable) => {
          if (res) {
            this.quotationPageable = res;
            this.length = res.totalElements;
            this.totalPages = res.totalPages;
            this.pageIndex = res.number;
            this.createMatTable(this.quotationPageable);
            this.showLoader = false;
          } else {
            this.dataSource.data = [];
            this.showLoader = false;
          }
        })
    );
  }

  // used to create mat table from pageable object
  createMatTable(serviceRequestList: QuotationPageable) {
    this.dataSource.data = serviceRequestList.content;
  }

  // gets the count for number of quotation
  getQuotationCount() {
    this.subscription.add(
      this.dashboardService.getQuotationCount().subscribe((res: StatusCount) => {
        this.statusCount = res;
      })
    );
  }

  async addFilter() {
    this.quotationSearchFilter.projectTitle =
      this.quotationSearchFilter.projectTitle === '' ? undefined : this.quotationSearchFilter.projectTitle;
    this.quotationSearchFilter.customerName =
      this.quotationSearchFilter.customerName === '' ? undefined : this.quotationSearchFilter.customerName;
    this.searchQuotations(this.initialPageIndex, this.pageSize);
  }

  searchQuotations(pageIndex, pageSize) {
    const pageable = {
      page: pageIndex,
      size: pageSize,
      sort: this.sortField + ',' + this.sortOrder,
      direction: this.sortOrder
    };
    this.showLoader = true;
    this.subscription.add(
      this.dashboardService.searchQuotations(this.quotationSearchFilter, pageable).subscribe((res: QuotationPageable) => {
        if (res) {
          this.quotationPageable = res;
          this.length = res.totalElements;
          this.totalPages = res.totalPages;
          this.pageIndex = res.number;
          this.createMatTable(this.quotationPageable);
          this.showLoader = false;
        } else {
          this.dataSource.data = [];
          this.showLoader = false;
        }
      })
    );
  }

  async resetFilter() {
    this.quotationSearchFilter = new QuotationSearchFilter();
    this.searchQuotations(this.initialPageIndex, this.pageSize);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  // gets the sorting for mat table
  getSorting(event) {
    this.sortField = event.active;
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    this.searchDesignQuotationsList(this.initialPageIndex, this.pageSize);
  }

  // gets the pagination for mat table
  getPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.searchDesignQuotationsList(this.pageIndex, this.pageSize);
  }

  searchMfgPart() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {};
    matDataConfig.width = PopupSize.size.popup_xlg;
    matDataConfig.panelClass = 'sfl-search-mfg-part';
    const dialogRef = this.matDialog.open(SearchMfgPartComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(() => {
    });
  }
}
