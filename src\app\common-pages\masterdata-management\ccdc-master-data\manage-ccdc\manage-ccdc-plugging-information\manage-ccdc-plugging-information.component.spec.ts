import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageCcdcPluggingInformationComponent } from './manage-ccdc-plugging-information.component';

describe('ManageCcdcPluggingInformationComponent', () => {
  let component: ManageCcdcPluggingInformationComponent;
  let fixture: ComponentFixture<ManageCcdcPluggingInformationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageCcdcPluggingInformationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageCcdcPluggingInformationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
