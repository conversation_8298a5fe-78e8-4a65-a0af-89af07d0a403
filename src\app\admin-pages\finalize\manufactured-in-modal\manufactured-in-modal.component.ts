import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { SendToDesignConfigurationDTO } from '../finalize.model';
import { FinalizeService } from '../finalize.service';
import { Values } from './../../../shared/constants/values.constants';

@Component({
  selector: 'sfl-manufactured-in-modal',
  templateUrl: './manufactured-in-modal.component.html',
})
export class ManufacturedInModalComponent implements OnInit, OnDestroy {

  subscriptionManager = new Subscription();
  countries: object = Values.ManufacturingCountries;
  quotationId: number;
  sendToDesignConfigurationDTO: SendToDesignConfigurationDTO = new SendToDesignConfigurationDTO();

  constructor(private dialogRef: MatDialogRef<ManufacturedInModalComponent>,
    private finalizeService: FinalizeService,
    @Inject(MAT_DIALOG_DATA) data) {
    this.quotationId = data.quotationId;
  }

  ngOnInit() {
    this.getQuotationConfiguration();
  }

  getQuotationConfiguration() {
    this.subscriptionManager.add(this.finalizeService.getManufacturedInByQuotationId(this.quotationId).subscribe(
      (res: SendToDesignConfigurationDTO) => {
        if (res) {
          this.sendToDesignConfigurationDTO = res;
          this.sendToDesignConfigurationDTO.expedite = res.expedite ? res.expedite : false;
          this.sendToDesignConfigurationDTO.stimulationRequired = res.stimulationRequired ? res.stimulationRequired : false;
        }
      }));
  }

  setManufacturedIn(manufacturedIn: string) {
    this.sendToDesignConfigurationDTO.quotationId = this.quotationId;
    this.sendToDesignConfigurationDTO.manufacturedIn = manufacturedIn
    this.subscriptionManager.add(this.finalizeService.setManufacturedIn(this.sendToDesignConfigurationDTO).subscribe(res => {
      this.closeDialog(this.sendToDesignConfigurationDTO);
    }, err => {
      this.closeDialog(null);
    }));
  }

  closeDialog(res: SendToDesignConfigurationDTO) {
    this.dialogRef.close(res);
  }

  ngOnDestroy(): void {
    if (this.subscriptionManager) {
      this.subscriptionManager.unsubscribe();
    }
  }

}
