<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #goldStandardTapeWidthForm="ngForm" (ngSubmit)="updateGoldStandardTapeWidth()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Type" [(ngModel)]="goldStandardTapeWidth.tapeType" name="tapeType" #tapeTypeInut="ngModel" required>
            <mat-option *ngFor="let type of goldStandardTapeWidthTypes" [value]="type.id">
              {{ type.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="tapeTypeInut.touched && tapeTypeInut.invalid">
          <small class="mat-text-warn" *ngIf="tapeTypeInut?.errors.required">Gold standard tape width type is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Width" [(ngModel)]="goldStandardTapeWidth.width" name="width" #widthInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="goldStandardTapeWidth.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!goldStandardTapeWidthForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
