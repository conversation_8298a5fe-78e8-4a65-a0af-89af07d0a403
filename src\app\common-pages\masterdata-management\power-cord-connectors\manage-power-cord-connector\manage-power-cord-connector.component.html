<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title fxFlex>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #powerCordConnectorForm="ngForm" (ngSubmit)="updatePowerCordConnector()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Power Cord Connector Name"
            [(ngModel)]="powerCordConnector.name"
            name="name"
            #nameInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="nameInput.touched && nameInput.invalid">
          <small class="mat-text-warn" *ngIf="nameInput?.errors?.required">Power cord connector name is required.</small>
          <small class="mat-text-warn" *ngIf="nameInput?.errors?.whitespace && !nameInput?.errors?.required"
            >Invalid power cord connector name.</small
          >
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number"
            [(ngModel)]="powerCordConnector.partNumber"
            name="partNumber"
            #partNumberInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partNumberInput.touched && partNumberInput.invalid">
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors?.required">Part number is required.</small>
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors?.whitespace && !partNumberInput?.errors?.required"
            >Invalid part number.</small
          >
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Price"
            [(ngModel)]="powerCordConnector.price"
            name="price"
            #priceInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="priceInput.touched && priceInput.invalid">
          <small class="mat-text-warn" *ngIf="priceInput?.errors?.required">Price is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Value" [(ngModel)]="powerCordConnector.value" name="value" #valueInput="ngModel" required />
        </mat-form-field>
        <div *ngIf="valueInput.touched && valueInput.invalid">
          <small class="mat-text-warn" *ngIf="valueInput?.errors?.required">Value is required.</small>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!powerCordConnectorForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
