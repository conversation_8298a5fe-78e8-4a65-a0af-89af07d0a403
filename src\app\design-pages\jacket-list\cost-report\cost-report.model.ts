export class CostReportList {
    constructor(
        public burdenCost?: number,
        public inActive?: boolean,
        public laborCost?: number,
        public listPrice?: number,
        public margin?: number,
        public materialCost?: number,
        public mtlBurCost?: number,
        public netPrice?: number,
        public onHold?: boolean,
        public partNumber?: string,
        public subConstCost?: number,
        public totalCost?: number,
        public status?: string,
        public childCostDetailsDTOS?: ChildCostList[]
    ) { }
}

export class ChildCostList {
    constructor(
        public partNumber?: string,
        public listPrice?: number,
        public netPrice?: number,
        public materialCost?: number,
        public laborCost?: number,
        public burdenCost?: number,
        public subConstCost?: number,
        public mtlBurCost?: number,
        public totalCost?: number,
        public margin?: number,
        public status?: string
    ) { }
}
