import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageSwBlockThermostatInfoComponent } from './manage-sw-block-thermostat-info.component';

describe('ManageSwBlockThermostatInfoComponent', () => {
  let component: ManageSwBlockThermostatInfoComponent;
  let fixture: ComponentFixture<ManageSwBlockThermostatInfoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageSwBlockThermostatInfoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageSwBlockThermostatInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
