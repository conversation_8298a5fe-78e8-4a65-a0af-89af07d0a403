import { Routes } from '@angular/router';
import { AccessoryControllersComponent } from './accessory-controllers/accessory-controllers.component';
import { AccessoryMasterComponent } from './accessory/accessory-master.component';
import { AlloyMasterComponent } from './alloy-master/alloy-master.component';
import { BhxGoldWireTapesComponent } from './bhx-gold-wire-tapes/bhx-gold-wire-tapes.component';
import { BhxMaterialMasterComponent } from './bhx-material-master/bhx-material-master.component';
import { CcdcMasterDataComponent } from './ccdc-master-data/ccdc-master-data.component';
import { ManageCcdcComponent } from './ccdc-master-data/manage-ccdc/manage-ccdc.component';
import { ClosureMaterialMasterComponent } from './closure-material-master/closure-material-master.component';
import { CurrencyMasterComponent } from './currency-master/currency-master.component';
import { DepartmentsMastersComponent } from './departments-masters/departments-masters.component';
import { EcoLineRevisionMasterComponent } from './eco-line-revision-master/eco-line-revision-master.component';
import { EcrStatusesComponent } from './ecr-statuses/ecr-statuses.component';
import {
  EstimatedEngReleaseMasterComponent
} from './estimated-eng-release-master/estimated-eng-release-master.component';
import { FeaturesMasterComponent } from './features-master/features-master.component';
import { GoldStandardTapeWidthComponent } from './gold-standard-tape-width/gold-standard-tape-width.component';
import { HeatingTapesComponent } from './heating-tapes/heating-tapes.component';
import { LabelMasterComponent } from './label-master/label-master.component';
import { LaborMasterComponent } from './labor-master/labor-master.component';
import { LeadTypesComponent } from './lead-types/lead-types.component';
import { MasterDataManagementComponent } from './master-data-management.component';
import { MaterialMasterComponent } from './material-master/material-master.component';
import { MaterialPropertiesComponent } from './material-properties/material-properties.component';
import { PartNumberMasterDataComponent } from './part-number-master-data/part-number-master-data.component';
import { PlugLightsComponent } from './plug-lights/plug-lights.component';
import { PlugMasterComponent } from './plug-master/plug-master.component';
import { PowerCordAmpsComponent } from './power-cord-amps/power-cord-amps.component';
import { PowerCordConnectorsComponent } from './power-cord-connectors/power-cord-connectors.component';
import { PowerCordMaterialComponent } from './power-cord-material/power-cord-material.component';
import { PowerCordOptionsComponent } from './power-cord-options/power-cord-options.component';
import { PowerCordVoltagesComponent } from './power-cord-voltages/power-cord-voltages.component';
import { PqpFamilyComponent } from './pqp-family/pqp-family.component';
import { ProductTypeComponent } from './product-type/product-type.component';
import { QuotationStatusComponent } from './quotation-status/quotation-status.component';
import { SensorConnectorComponent } from './sensor-connector/sensor-connector.component';
import { SensorTypesComponent } from './sensor-types/sensor-types.component';
import { SleevingTypesComponent } from './sleeving-types/sleeving-types.component';
import { SolidworksMasterComponent } from './solidworks-master/solidworks-master.component';
import { StrainReliefsComponent } from './strain-reliefs/strain-reliefs.component';
import { ThermostatListComponent } from './thermostat-list/thermostat-list.component';
import { ThermostatTypesComponent } from './thermostat-types/thermostat-types.component';
import { WarpsMasterComponent } from './warps-master/warps-master.component';

export const MasterdataManagementRoutes: Routes = [
  { path: '', component: MasterDataManagementComponent },
  { path: 'quotation-status', component: QuotationStatusComponent },
  { path: 'accessory', component: AccessoryMasterComponent },
  { path: 'material', component: MaterialMasterComponent },
  { path: 'plug', component: PlugMasterComponent },
  { path: 'closure-material', component: ClosureMaterialMasterComponent },
  { path: 'material-properties', component: MaterialPropertiesComponent },
  { path: 'thermostat', component: ThermostatListComponent },
  { path: 'features', component: FeaturesMasterComponent },
  { path: 'accessory-controller', component: AccessoryControllersComponent },
  { path: 'product-group', component: ProductTypeComponent },
  { path: 'pqp-family', component: PqpFamilyComponent },
  { path: 'power-cord-connector', component: PowerCordConnectorsComponent },
  { path: 'sleeving-types', component: SleevingTypesComponent },
  { path: 'strain-reliefs', component: StrainReliefsComponent },
  { path: 'plug-lights', component: PlugLightsComponent },
  { path: 'gold-standard-tape-width', component: GoldStandardTapeWidthComponent },
  { path: 'gold-wire-tapes', component: BhxGoldWireTapesComponent },
  { path: 'alloys', component: AlloyMasterComponent },
  { path: 'heating-tapes', component: HeatingTapesComponent },
  { path: 'departments', component: DepartmentsMastersComponent },
  { path: 'ecr-statuses', component: EcrStatusesComponent },
  { path: 'bhx-material', component: BhxMaterialMasterComponent },
  { path: 'sensor-connectors', component: SensorConnectorComponent },
  { path: 'power-cord-materials', component: PowerCordMaterialComponent },
  { path: 'power-cord-voltages', component: PowerCordVoltagesComponent },
  { path: 'power-cord-amps', component: PowerCordAmpsComponent },
  { path: 'power-cord-options', component: PowerCordOptionsComponent },
  { path: 'sensor-types', component: SensorTypesComponent },
  // { path: 'sensor-control-types', component: SensorControlTypesComponent },
  { path: 'warps', component: WarpsMasterComponent },
  { path: 'thermostat-types', component: ThermostatTypesComponent },
  { path: 'lead-types', component: LeadTypesComponent },
  { path: 'currency-master', component: CurrencyMasterComponent },
  { path: 'ccdc-master-data', component: CcdcMasterDataComponent },
  { path: 'part-number-master-data', component: PartNumberMasterDataComponent },
  { path: 'labor-master-data', component: LaborMasterComponent },
  { path: 'manage-ccdc', component: ManageCcdcComponent },
  { path: 'label-master', component: LabelMasterComponent },
  { path: 'eco-line-revision-master', component: EcoLineRevisionMasterComponent },
  { path: 'solidworks-master', component: SolidworksMasterComponent },
  { path: 'est-eng-rel-date-master', component: EstimatedEngReleaseMasterComponent }
];
