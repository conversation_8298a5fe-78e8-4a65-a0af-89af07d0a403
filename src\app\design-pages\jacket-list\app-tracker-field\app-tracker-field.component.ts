import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { AppEngineeringQuoteTracker, Statuses } from 'src/app/quote-tracker/quote-tracker.model';
import { QuoteTrackerService } from 'src/app/quote-tracker/quote-tracker.service';
import { SharedService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { EditableTrackerFields, TrackerFieldDialog } from '../jacket-list.model';
import { JacketListService } from '../jacket-list.service';

@Component({
  selector: 'app-app-tracker-field',
  templateUrl: './app-tracker-field.component.html',
  styleUrls: ['./app-tracker-field.component.css']
})
export class AppTrackerFieldComponent implements OnInit {
   _quotationId: number;
  _quotationStatusId: number;
  highlightedColors = Values.highlightColors;
  quoteId: number;
  showLoader = false;
  subscription: Subscription = new Subscription();
  statuses: Statuses[];
  trackerFields: EditableTrackerFields = new EditableTrackerFields();
  trackerFieldsForm: FormGroup;
  type = Variable.typeApp;
  constructor(
    public readonly dialogRef: MatDialogRef<AppTrackerFieldComponent>,
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly formBuilder: FormBuilder,
    private readonly sharedService: SharedService,
    private readonly jacketListService: JacketListService,
    @Inject(MAT_DIALOG_DATA) data: TrackerFieldDialog,
  ) {
    this._quotationId = data.quotationId;
    this._quotationStatusId = data.designStatusId;
  }

  async ngOnInit() {
    this.createEditableTrackerFieldForm();
    this.getQuoteStatuses();
    await this.getAppDefaultValues();
  }

  selectRow(colorValue: string) {
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.appRowHighlighting(this._quotationId, colorValue, this._quotationStatusId).subscribe(
        (res: AppEngineeringQuoteTracker) => {
          if (res) {
            this.closeDialog(true);
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  getAppDefaultValues() {
    this.showLoader = true;
    return new Promise((resolve) => {
      this.subscription.add(
        this.jacketListService
          .getAppQuoteTrackerDefaultValues(this.type, this._quotationId)
          .subscribe(
            (defaultValues: EditableTrackerFields) => {
              this.trackerFields = defaultValues;
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            },
            () => {
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            }
          )
      );
    });
  }

  getQuoteStatuses() {
    this.subscription.add(
      this.sharedService
        .getStatuesByTypeOrderByOrderNumber()
        .subscribe((res: Array<Statuses>) => {
          if (res) {
            this.statuses = res.filter(statusTypeApp => statusTypeApp.type === this.type);
            }
          }
        )
    );
  }

  createEditableTrackerFieldForm() {
    this.trackerFieldsForm = this.formBuilder.group({
      quotationStatusId: new FormControl(),
      quotationId: new FormControl()
    });
  }

  setDefaultValuesForForm() {
    this.trackerFieldsForm.controls.quotationId.patchValue(this._quotationId);
    this.trackerFieldsForm.controls.quotationStatusId.patchValue(
      this.trackerFields.quotationStatusId
    );
  }

  saveTrackerFields() {
    this.showLoader = true;
    this.subscription.add(
      this.jacketListService
        .saveDesignQuoteTrackerFields(this.trackerFieldsForm.value,'All')
        .subscribe(
          (res: EditableTrackerFields) => {
            this.trackerFields = res;
            this.showLoader = false;
            this.dialogRef.close();
          },
          () => {
            this.showLoader = false;
          }
        )
    );
  }


  closeDialog(flag = false): void {
    this.dialogRef.close(flag);
  }
}
