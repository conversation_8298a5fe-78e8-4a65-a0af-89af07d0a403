<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Closure Information
  <mat-icon
    *ngIf="showReloadButton"
    class="open-doc sfl-pull-right"
    [matTooltip]="outDatedViewErrorMessage"
    color="warn"
    matTooltipClass="sfl-formula-tooltip"
    (click)="reloadPage()"
    id="refresh"
  >
    cached
  </mat-icon>
  <hr />
</h2>
<mat-dialog-content>
  <form class="cust_table" #closureInfoForm="ngForm">
    <div fxLayout="column">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxLayout="column wrap" fxFlex="49">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <div>
            <mat-checkbox color="warn" name="extendedFlap" [(ngModel)]="closureTemplatedto.extendedFlap" #extendedFlapInput="ngModel"
              >Extended Flap</mat-checkbox
            >&nbsp;&nbsp;
            <mat-checkbox color="warn" name="cat5Tunnel" [(ngModel)]="closureTemplatedto.cat5Tunnel" #cat5TunnelCheck="ngModel"
              >Cat5 Tunnel</mat-checkbox
            >
          </div>
          <mat-form-field>
            <mat-select
              placeholder="Select an option"
              name="material"
              [(ngModel)]="closureTemplatedto.closureMaterialId"
              #materialSelect="ngModel"
              (selectionChange)="onMaterialChange($event.value)"
            >
              <mat-option *ngFor="let mat of materials" [value]="mat.id">
                {{ mat?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field *ngIf="isOther">
            <input
              matInput
              placeholder="Other Closure"
              name="other"
              [(ngModel)]="closureTemplatedto.otherClosure"
              #otherInput="ngModel"
              type="text"
            />
          </mat-form-field>
          <mat-form-field *ngIf="isOther">
            <input
              matInput
              placeholder="Other Closure Part Number"
              name="otherPartnumber"
              [(ngModel)]="closureTemplatedto.otherClosurePartNumber"
              #otherInput="ngModel"
              type="text"
            />
          </mat-form-field>
          <mat-form-field *ngIf="isOther">
            <input
              matInput
              placeholder="Other Closure Cost"
              name="otherCost"
              [(ngModel)]="closureTemplatedto.otherClosureCost"
              #otherInput="ngModel"
              type="text"
            />
          </mat-form-field>
          <mat-form-field>
            <input
              matInput
              placeholder="Est. Surf. Temp ({{ tempUnit ? tempUnit : '' }})"
              name="estSurfaceTemp"
              [(ngModel)]="closureTemplatedto.estSurfaceTemp"
              #estSurfaceTempInput="ngModel"
              type="number"
              readonly
            />
          </mat-form-field>
          <mat-form-field *ngIf="!isOther">
            <input
              *ngIf="tempUnit === '°F'"
              matInput
              placeholder="Maximum Temp ({{ tempUnit ? tempUnit : '' }})"
              name="maxTempF"
              [(ngModel)]="material.maxTempF"
              type="number"
              #maxTempFInput="ngModel"
              readonly
            />
            <input
              *ngIf="tempUnit !== '°F'"
              matInput
              placeholder="Maximum Temp ({{ tempUnit ? tempUnit : '' }})"
              name="maxTemp"
              (ngModelChange)="material.maxTemp = $event"
              [ngModel]="material?.maxTemp"
              type="number"
              #maxTempInput="ngModel"
              readonly
            />
          </mat-form-field>
          <mat-form-field *ngIf="!isOther">
            <input
              matInput
              placeholder="Cost Per Foot"
              name="costPerSq"
              (ngModelChange)="material.costPerSq = $event"
              [ngModel]="material?.costPerSq"
              type="number"
              #costPerSqInput="ngModel"
              readonly
            />
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="column wrap" fxLayoutAlign="center center" fxFlex="49" class="mat-img">
        <img class="product-img" src="{{ imageUrl }}" alt="" />
      </div>
    </div>
    <div fxFlex="100">
      <mat-form-field>
        <textarea matInput placeholder="Notes" rows="5" name="notes" [(ngModel)]="closureTemplatedto.notes"></textarea>
      </mat-form-field>
    </div>
  </div>
  </form>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="end">
    <button mat-raised-button matStepperNext>Next</button>
  </div>
</mat-dialog-actions>
