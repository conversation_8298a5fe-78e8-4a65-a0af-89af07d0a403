import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageSensorInformationComponent } from './manage-sensor-information.component';

describe('ManageSensorInformationComponent', () => {
  let component: ManageSensorInformationComponent;
  let fixture: ComponentFixture<ManageSensorInformationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageSensorInformationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageSensorInformationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
