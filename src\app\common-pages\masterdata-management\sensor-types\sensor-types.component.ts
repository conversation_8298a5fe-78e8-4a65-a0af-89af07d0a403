import { Component, OnInit, <PERSON><PERSON>hild, On<PERSON><PERSON>roy } from '@angular/core';
import {
  SensorConnectorsAndTypesMaster,
  GenericPageable,
  SensorConnectorsAndTypesFilter,
  SensorTypesFilter
} from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageSensorTypesComponent } from './manage-sensor-types/manage-sensor-types.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-sensor-types',
  templateUrl: './sensor-types.component.html'
})
export class SensorTypesComponent implements OnInit, OnDestroy {
  pageTitle = 'Sensor Types Master';
  sensorTypes: SensorConnectorsAndTypesMaster;
  sensorTypesPageable: GenericPageable<SensorConnectorsAndTypesMaster>;
  sensorTypesDataSource = new MatTableDataSource<SensorConnectorsAndTypesMaster>();
  sensorTypesColumns = DisplayColumns.Cols.SensorConnectorsAndTypes;

  dataSource = new MatTableDataSource<SensorConnectorsAndTypesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  sensorTypesFilter: SensorTypesFilter = new SensorTypesFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldId = Values.FilterFields.id;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getSensorTypesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new sensor type
  addSensorTypes() {
    this.editSensorTypes(new SensorConnectorsAndTypesMaster());
  }

  // used to add filter to sensor type listing
  async addFilter() {
    this.filter = this.sensorTypesFilter.id === '' ? [] : [{ key: this.filterFieldId, value: this.sensorTypesFilter.id }];
    this.getSensorTypesMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of sensor type listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldId,
        value: fieldToClear === this.filterFieldId ? (this.sensorTypesFilter.id = '') : this.sensorTypesFilter.id
      }
    ];
    this.getSensorTypesMasterData(this.initialPageIndex, this.pageSize);
  }

  getSensorTypesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getSensorTypesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<SensorConnectorsAndTypesMaster>) => {
          this.sensorTypesPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createSensorTypesTable(this.sensorTypesPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createSensorTypesTable(serviceRequestList: GenericPageable<SensorConnectorsAndTypesMaster>) {
    this.sensorTypesDataSource.data = serviceRequestList.content;
  }

  getSensorTypesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getSensorTypesMasterData(this.pageIndex, this.pageSize);
  }

  getSensorTypesSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getSensorTypesMasterData(this.pageIndex, this.pageSize);
  }

  editSensorTypes(sensorTypes: SensorConnectorsAndTypesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = sensorTypes;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-sensor-types-master-model';
    const dialogRef = this.matDialog.open(ManageSensorTypesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          sensorTypes.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getSensorTypesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteSensorTypes(sensorTypesId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteSensorTypes(sensorTypesId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getSensorTypesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
