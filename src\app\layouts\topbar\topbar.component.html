<mat-toolbar class="main-header bg-dark">
  <div class="d-flex align-items-center logo-searchbar">
    <div class="d-flex align-items-center">
      <button (click)="toggleSidenav.emit()" mat-icon-button>
        <mat-icon>menu</mat-icon>
      </button>
      <div class="branding">
        <div (click)="gotDashboard()" class="logo"></div>
      </div>
    </div>
    <div fxLayoutAlign="center">
      <div fxLayout="row">
        <div class="search-bar">
          <form class="search-form" [ngClass]="{ 'disable-section': userRole === 'ROLE_SALES' }" [ngStyle.xs]="{ display: 'none' }">
            <mat-icon>search</mat-icon>
            <input type="text" placeholder="Search for Quote # or Sales Order" name="quote" [(ngModel)]="quote" [matAutocomplete]="auto" (ngModelChange)="quoteFilter($event)" />
            <div class="searchButton cursor-pointer">
              <mat-icon (click)="showFilter()">keyboard_arrow_down</mat-icon>
            </div>
            <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="getQuotNumberFn" (optionSelected)="selectQuotation($event.option.value)">
              <mat-option *ngFor="let option of quotationQbservable$ | async" [value]="option" [routerLink]="['/app-eng/ccdc']" [queryParams]="{ quotId: option?.id }">
                <span
                  >Quotation Number: <strong>{{ option?.quotationNumber }}</strong
                  >, SO Number: <strong>{{ option?.salesOrderNumber }}</strong></span
                >
              </mat-option>
            </mat-autocomplete>
            <button class="mr-10" mat-raised-button color="warn" type="submit" (click)="openAddQuo()">Add Quotation</button>
          </form>
          <div *ngIf="filter" class="filter-bar">
            <mat-card>
              <form class="fields" #quotationFilterForm="ngForm">
                <div fxLayout="row wrap" fxLayoutAlign="space-between">
                  <mat-form-field fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                    <mat-select placeholder="Status" [(ngModel)]="quotationSearchFilter.status" name="status">
                      <mat-option *ngFor="let allstatus of status" [value]="allstatus.status">
                        {{ allstatus?.status }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                    <mat-select placeholder="Sales Associate" [(ngModel)]="quotationSearchFilter.salesAssociateName" name="salesAssociateName">
                      <mat-option *ngFor="let salesass of salesassociate" [value]="salesass.firstName + ' ' + salesass.lastName">
                        {{ salesass?.firstName }} {{ salesass?.lastName }}</mat-option
                      >
                    </mat-select>
                  </mat-form-field>
                </div>
                <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100" class="ml-2">
                  <input matInput placeholder="Customer" [(ngModel)]="quotationSearchFilter.customerName" name="customerName" />
                </mat-form-field>
                <mat-dialog-actions fxLayoutAlign="end" class="searchbar-actions">
                  <button mat-button (click)="reset()">Reset</button>&nbsp;
                  <button mat-raised-button color="warn" (click)="searchQuotations(quotationFilterForm)">Search</button>
                </mat-dialog-actions>
              </form>
            </mat-card>
          </div>
        </div>
      </div>
    </div>
  </div>
  <samp fxFlex></samp>
  <button *ngIf="!isFullScreen" (click)="fullScreenToggle()" mat-icon-button>
    <mat-icon>fullscreen</mat-icon>
  </button>
  <button *ngIf="isFullScreen" (click)="fullScreenToggle()" mat-icon-button>
    <mat-icon>fullscreen_exit</mat-icon>
  </button>
  <button [matMenuTriggerFor]="user" mat-icon-button class="ml-xs">
    <mat-icon>person</mat-icon>
  </button>
  <mat-menu #user="matMenu" x-position="before">
    <button *ngIf="userRole !== 'ROLE_SALES'" mat-menu-item id="switchtosuperadmin" (click)="switchToSuperAdmin()">
      <mat-icon>supervisor_account</mat-icon>
      Super Admin
    </button>
    <button mat-menu-item id="switchtodesign" (click)="switchToDesign()">
      <mat-icon>supervisor_account</mat-icon>
      Switch to Design
    </button>
    <button *ngIf="userRole !== 'ROLE_SALES'" mat-menu-item id="masterdata" (click)="switchToMasterdataManagement()">
      <mat-icon>account_balance</mat-icon>
      Master Data Management
    </button>
    <button mat-menu-item id="switchtoquotetracker" (click)="switchToQuoteTracker()">
      <mat-icon>timeline</mat-icon>
      Trackers
    </button>
    <a href="/view-jacket-reference" routerLink="/view-jacket-reference" target="_blank">
      <button mat-menu-item id="switchtojacketreference">
        <mat-icon>timeline</mat-icon>
        BOM Reference
      </button>
    </a>
    <button *ngIf="userRole === 'ROLE_SALES'" mat-menu-item id="switchtosalesdashboard" (click)="goToSalesDashboard()">
      <mat-icon>card_travel</mat-icon>
      Sales
    </button>
    <button mat-menu-item id="logout" (click)="logout()">
      <mat-icon>exit_to_app</mat-icon>
      Log Out
    </button>
  </mat-menu>
</mat-toolbar>
