<h2 mat-dialog-title>Add Operation
  <hr>
</h2>
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<form #operationForm="ngForm" role="form">
  <mat-dialog-content
    *ngIf="type==='facing' || type==='sensors' || type==='element' || type==='wiring' || type==='labels'">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Part number" [(ngModel)]="data.partNumber" name="partNumber"
               autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Rel. Op." [(ngModel)]="data.relOperation" name="relOperation"
               autocomplete="off">
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Quantity" [(ngModel)]="data.qty" name="qty"
               autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="UOM" [(ngModel)]="data.uom" name="uom"
               autocomplete="off">
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100">
        <textarea matInput placeholder="Description" [(ngModel)]="data.description" name="description"
                  autocomplete="off"></textarea>
      </mat-form-field>
    </div>
  </mat-dialog-content>
  <mat-dialog-content *ngIf="type==='operation'">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Sequence" [(ngModel)]="data.sequence" name="sequence"
               autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Op. Number" [(ngModel)]="data.opNumber" name="opNumber"
               autocomplete="off">
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Operation Name" [(ngModel)]="data.operationName" name="operationName"
               autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Prod. Hours" [(ngModel)]="data.prodHrs" name="prodHrs"
               autocomplete="off">
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100">
        <input matInput placeholder="Set. Up. Hours" [(ngModel)]="data.setupHrs" name="setupHrs"
               autocomplete="off">
      </mat-form-field>
    </div>
  </mat-dialog-content>
  <mat-dialog-content *ngIf="type==='labels-entry'">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Label Part Number" [(ngModel)]="data.labelPartNumber" name="labelPartNumber"
               autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Format" [(ngModel)]="data.format" name="format" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Volts" [(ngModel)]="data.volts" name="volts" autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Watts" [(ngModel)]="data.watts" name="watts" autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Amps" [(ngModel)]="data.amps" name="amps" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Phase" [(ngModel)]="data.phase" name="phase" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Size" [(ngModel)]="data.size" name="size" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Model Number" [(ngModel)]="data.modelNumber" name="modelNumber" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Open 1" [(ngModel)]="data.open1" name="open1" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Open 2" [(ngModel)]="data.open2" name="open2" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Open 3" [(ngModel)]="data.open3" name="open3" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Width" [(ngModel)]="data.width" name="width" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Length" [(ngModel)]="data.length" name="length" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="CS Volts" [(ngModel)]="data.csv" name="csv" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="CS Amps" [(ngModel)]="data.csa" name="csa" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="MHL Volts" [(ngModel)]="data.mhlv" name="mhlv" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="MHL Amps" [(ngModel)]="data.mhla" name="mhla" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Temp Range" [(ngModel)]="data.tempRange" name="tempRange" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Low Temp" [(ngModel)]="data.lowT" name="lowT" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="High Temp" [(ngModel)]="data.highT" name="highT" autocomplete="off">
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30">
        <input matInput placeholder="Part Number" [(ngModel)]="data.partNumber" name="partNumber" autocomplete="off">
      </mat-form-field>
    </div>
  </mat-dialog-content>
  <hr>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveOperaton()">Save</button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</form>
