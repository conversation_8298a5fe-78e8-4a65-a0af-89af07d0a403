import { DatePipe } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material';
import { Subscription } from 'rxjs';
import { User } from 'src/app/common-pages/users/user.model';
import { Messages, SharedService, SnakbarService, SweetAlertService } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { AddAttachmentComponent } from '../add-attachment/add-attachment.component';
import { AddPartnumberComponent } from '../add-partnumber/add-partnumber.component';
import {
  AddAttachment,
  EcrDto,
  EcrPartNumberDTO,
  EcrPartnumberDto,
  SelectedStakeHolders,
  SyncPartNumberDto
} from '../ecr-management.model';
import { EcrManagementService } from '../ecr-management.service';
import { EcoDto, EcoWorkFlowDto, PqpDTOS, PqpFamilyDTOS, WorkFlowMasterDataDto } from './eco-plan.model';
import { EcoPlanService } from './eco-plan.service';
import { FinalEcrReviewService } from './final-review-ecr/final-ecr-review.service';

@Component({
  selector: 'sfl-eco-plan',
  templateUrl: './eco-plan.component.html',
  styleUrls: ['./eco-plan.component.scss'],

})
export class EcoPlanComponent implements OnInit, OnDestroy {

  criticalPart = false;
  pqpFamilyList: PqpFamilyDTOS;
  dropdownPqp = new PqpFamilyDTOS();
  partnumbers = [];
  selectedPqpFamily: PqpFamilyDTOS = new PqpFamilyDTOS();
  pqpDto: PqpDTOS = new PqpDTOS();
  selectedStakeHolders: SelectedStakeHolders = new SelectedStakeHolders();
  stakeHolders = [];
  users: User[];
  maxDate = new Date();
  addAttachmentDto: AddAttachment = new AddAttachment();
  formData = new FormData();
  ecrNumber: number;
  _ecoDto: EcoDto = new EcoDto;
  _ecrDto: EcrDto = new EcrDto;
  changeTypes = [];

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  subscription: Subscription = new Subscription();
  showLoader = false;
  ecoPqpDetailsAvailable = false;
  syncPartNumberDTO: SyncPartNumberDto = new SyncPartNumberDto();
  syncInProcess = false;
  isDataAvailable: boolean;
  workFlowMasterData: WorkFlowMasterDataDto[] = [];
  ecoWorkFlowData: EcoWorkFlowDto[] = [];
  workFlowDto: EcoWorkFlowDto[] = [];
  holdStakeHolders = [];
  partNumberList: EcrPartNumberDTO[] = [];
  partNumber: string;
  jacketId:number;
  get ecrDto() {
    return this._ecrDto;
  }

  get ecoDto() {
    return this._ecoDto;
  }

  @Input()
  set ecoDto(val) {
    this._ecoDto = val;
  }

  @Input()
  set ecrDto(val) {
    this._ecrDto = val;
    this.getEcoDetails();
  }

  get _users() {
    return this.users;
  }

  @Input()
  set _users(users) {
    this.users = users;
  }

  myControl = new FormControl();
  currentTab = 'coverPage';

  constructor(private snakbarService: SnakbarService, private matDialog: MatDialog, private ecoPlanService: EcoPlanService,
              private ecrManagementService: EcrManagementService, private sweetAlertService: SweetAlertService,
              private datePipe: DatePipe, private sharedService: SharedService, private readonly finalReviewService: FinalEcrReviewService) {
  }

  ngOnInit() {
    this.changeTypes = Values.EcoChangeTypes;
    this.ecrNumber = this.ecrDto.ecrNo;
    this.getEcoWorkFlow();
    this.getWorkFlowMasterData();
    this.setUpStakeHolders(this._ecoDto.ecoStakeholderDTOS);
  }

  private _filter(value: string): User[] {
    const filterValue = value.toLowerCase();
    return this.users.filter(option => option.firstName.toLowerCase().includes(filterValue));
  }

  setCreatorId(userName) {
    const userNameArray = userName.split('-');
    let userId;
    if (this.users) {
      this.users.filter(user => {
        if (user.firstName === userNameArray[0] && user.lastName === userNameArray[1]) {
          userId = user.id;
        }
      });
    }
    if (userId) {
      this.ecoDto.ecoCreator = userId;
      this.updateEco();
      this.setCreator();
    }
  }

  setCreator() {
    if (this.ecoDto.ecoCreator && this.users) {
      const temp = this.users.filter(x => x.id === this.ecoDto.ecoCreator);
      this.myControl.setValue(temp[0].firstName + ' ' + temp[0].lastName);
    }
  }

  removeCreator(creatorName) {
    if (creatorName === '') {
      this.ecoDto.ecoCreator = null;
      this.updateEco();
    }
  }


  switchingTab(tabs) {
    switch (tabs.index) {
      case 0:
        this.currentTab = 'coverPage';
        this.getEcoDetails();
        break;
      case 1:
        // workflow
        this.currentTab = 'workFlow';
        break;
      case 2:
        // design activity
        this.currentTab = 'designActivity';
        break;
      case 3:
        // title block
        this.getPartNumber();
        this.currentTab = 'titleBlock';
        break;
      case 4:
        // final review
        this.currentTab = 'finalReview';
        break;
      case 5:
        // costing
        this.currentTab = 'costing';
        break;
    }
  }

  getPartNumber() {
    this.finalReviewService.getECRPartNumberByEcr(this.ecrDto.id).subscribe((res: EcrPartNumberDTO[]) => {
        this.partNumberList = res;
      }
    );
  }

  getFinalReviewECR() {
    this.subscription.add(
      this.finalReviewService.getFinalDesignReviews(this.partNumber).subscribe((res) => {
          this.jacketId = res.jacketId;
        }, error => {
        this.jacketId = null;
        }
      )
    );
  }

  createWorkFlow() {
    if (this.ecoWorkFlowData) {
      this.ecoWorkFlowData['Approvals Required'].filter((x) => {
        if (this.workFlowDto) {
          this.workFlowDto.filter((appWork) => {
            if (appWork.workflowId === x.id) {
              x.completedBy = appWork.completedBy;
              // x.completedDate = appWork.completedDate;
              // x.completedDate = new Date(appWork.completedDate.replace(Values.dateTimeZone, ""));
              x.ecoId = appWork.ecoId;
              x.ecoWorkflowId = appWork.id;
              x.workflowId = appWork.workflowId;
              x.isChecked = true;
              x.status = true;
            }
          });
        }
      });
    }
  }

  getEcoWorkFlow() {
    return new Promise<void>((resolve, reject) => {
      this.subscription.add(this.ecoPlanService.getWorkFlowByEcoId(this._ecrDto.id).subscribe((response: EcoWorkFlowDto[]) => {
        this.workFlowDto = response;
        this.createWorkFlow();
        resolve();
      }, error => {
        reject();
      }));
    });
  }

  getEcoDetails() {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.getEcos(this._ecrDto.id).subscribe((ecoDetails: EcoDto) => {
      if (ecoDetails) {
        this.ecoDto = ecoDetails;
        this.showLoader = false;
        this.initCoverPage();
      } else {
        // else eco details are null hence we need to create ECO for this ECR number
        this.ecoDto.ecrId = this.ecrDto.id;
        this.ecoDto.creationDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        this.ecoDto.creationDate = this.ecoDto.creationDate + Values.timeZone;
        this.ecoDto.ecoCreator = this.sharedService.getUserId();
        this.subscription.add(this.ecoPlanService.createEco(this.ecoDto).subscribe((ecoResponse: EcoDto) => {
          if (ecoResponse) {
            this.ecoDto = ecoResponse;
            this.initCoverPage();
          }
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
        }));
      }
    }, error => {
      this.showLoader = false;
    }));
  }

  initCoverPage() {
    this.getPqpDetails();
    this.getPqpList();
    this.setCreator();
  }

  groupBy = (array, key) => {
    return array.reduce((result, currentValue) => {
      // If an array already present for key, push it to the array. Else create an array and push the object
      (result[currentValue[key]] = result[currentValue[key]] || []).push(currentValue);
      // Return the current iteration `result` value, this will be taken as next iteration `result` value and accumulate
      return result;
    }, {});
  };

  getWorkFlowMasterData() {
    return new Promise<void>((resolve, reject) => {
      this.subscription.add(this.ecoPlanService.getWorkFlowMasterData().subscribe((workFlowMasterData: WorkFlowMasterDataDto[]) => {
        if (workFlowMasterData) {
          this.workFlowMasterData = workFlowMasterData;

          this.ecoWorkFlowData = this.groupBy(this.workFlowMasterData, 'title');
          setTimeout(() => {
            this.isDataAvailable = true;
          }, 500);
          resolve();
        }
      }, error => {
        reject();
      }));
    });
  }

  setUpStakeHolders(stakHoldersList) {
    this.holdStakeHolders = stakHoldersList;
    this.holdStakeHolders.filter((stake, i) => {
      this._users.filter(user => {
        if (stake.userId === user.id) {
          this.stakeHolders[i] = {
            userName: user.firstName + ' ' + user.lastName,
            userId: user.id,
            stakeHolderId: stake.id,
            id: stake.id,
            ecoId: this._ecoDto.id
          };
        }
      });
    });
  }

  updateEcoWorkFlow(workFlowData: WorkFlowMasterDataDto) {
    this.showLoader = true;
    return new Promise<void>((resolve, reject) => {
      workFlowData.ecoId = this._ecoDto.id;
      workFlowData.workflowId = workFlowData.id;
      if (workFlowData.status) {
        let obj;
        obj = Object.assign({}, workFlowData);
        workFlowData.completedDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        obj.completedDate = workFlowData.completedDate + Values.timeZone;
        obj.id = null;
        // create Work flow, nullyfying the id as this id belongs to the master data id
        this.subscription.add(this.ecoPlanService.createWorkFlowForEco(obj).subscribe(async (createResponse: any) => {
          obj.ecoWorkflowId = createResponse.id;
          obj.id = createResponse.workflowId;
          this.showLoader = false;
          resolve();
        }, error => {
          this.showLoader = false;
          obj.status = false;
          reject();
        }));
      } else {
        // remove work flow
        this.subscription.add(this.ecoPlanService.deleteWorkFlowEco(workFlowData.id).subscribe(async () => {
          workFlowData.completedDate = null;
          workFlowData.completedBy = null;
          resolve();
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
          workFlowData.status = true;
          reject();
        }));
      }
    });
  }


  getPqpDetails() {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.getPqpDetails(this.ecoDto.id).subscribe((pqpDetails: PqpDTOS) => {
      if (pqpDetails) {
        this.pqpDto = pqpDetails;
        this.ecoPqpDetailsAvailable = true;
      }
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  getPqpList() {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.getPqpList().subscribe((pqpFamilyList: PqpFamilyDTOS) => {
        this.pqpFamilyList = pqpFamilyList;
      },
      error => {
        if (error.applicationStatusCode === 1239) {
          this.snakbarService.error(error.message);
        }
      }
    ));
  }

  onPqpFamilyChange(pqpFamily) {
    this.showLoader = true;
    this.pqpDto.pqpFamilies.push(pqpFamily);
    this.subscription.add(this.ecoPlanService.addPqpItem(this.pqpDto).subscribe((res: PqpDTOS) => {
      this.pqpDto = res;
      this.ecoPqpDetailsAvailable = true;
      this.showLoader = false;
      this.dropdownPqp = new PqpFamilyDTOS();
    }, error => {
      this.pqpDto.pqpFamilies.splice(this.pqpDto.pqpFamilies.indexOf(pqpFamily), 1);
      this.showLoader = false;
      this.dropdownPqp = new PqpFamilyDTOS();
    }));
  }

  // remove pqp family item
  async confirmDelete(itemValue) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.removePqpFamily(itemValue);
    }
  }

  async confirmPartNumberAndAttachment(itemValue, toBeDeleted) {
    if (await this.sweetAlertService.deleteAlert()) {
      toBeDeleted === 'partNumber' ? this.removePartnumber(itemValue) : this.removeAttachment(itemValue);
    }
  }

  removePqpFamily(pqpFamily: PqpFamilyDTOS) {
    // this.pqpFamily.splice(pqpFamily.index, 1);
    this.pqpDto.pqpFamilies.splice(this.pqpDto.pqpFamilies.map(x => x.id).indexOf(pqpFamily.id), 1);
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.addPqpItem(this.pqpDto).subscribe((pqpDto: PqpDTOS) => {
      this.pqpDto = pqpDto;
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  openDoc(blob) {
    this.subscription.add(this.ecrManagementService.getDocument(blob.id).subscribe((success) => {
        const url = URL.createObjectURL(success);
        window.open(url, '_target');
      },
      error => {
        if (error.applicationStatusCode === 1240) {
          this.snakbarService.error(error.message);
        }
      }
    ));
  }

  addPartNumber() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_sm;
    matDataConfig.panelClass = 'sfl-add-partnumber-model';
    const dialogRef = this.matDialog.open(AddPartnumberComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((addedPartnumber: EcrPartnumberDto) => {
      if (addedPartnumber.partNumber) {
        if (!this.ecrDto.ecrPartNumbers.find(element => element['partNumber'] === addedPartnumber.partNumber)) {
          addedPartnumber.ecrId = this.ecrDto.id;
          this.subscription.add(this.ecrManagementService.addPartNumber(addedPartnumber).subscribe((res: EcrPartnumberDto) => {
            this.ecrDto.ecrPartNumbers.push(res);
          }));
        } else {
          this.snakbarService.error('Part number ' + addedPartnumber.partNumber + ' already exist!');
        }
      }
    });
  }

  addAttachment(ecrId) {
    this.showLoader = true;
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-add-partnumber-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      if (uploadedAttachment !== undefined) {
        this.addAttachmentDto.ecrId = ecrId;
        this.formData = new FormData();
        this.formData.append('file', uploadedAttachment, uploadedAttachment.name);
        this.formData.append('document', JSON.stringify(this.addAttachmentDto));
        this.subscription.add(this.ecrManagementService.addAttachment(this.formData).subscribe(res => {
          this.ecrDto.ecrAttachments.push(res);
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
        }));
      } else {
        this.showLoader = false;
      }
    });
  }

  removePartnumber(partNumberId) {
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.deletePartNumber(partNumberId).subscribe(res => {
      this.ecrDto.ecrPartNumbers.splice(this.ecrDto.ecrPartNumbers.map(x => x.id).indexOf(partNumberId), 1);
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  removeAttachment(fileId) {
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.deleteAttachment(fileId).subscribe(() => {
      this.ecrDto.ecrAttachments.splice(this.ecrDto.ecrAttachments.map(x => x.id).indexOf(fileId), 1);
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  updateEcr() {
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.updateEcr(this.ecrDto).subscribe((response: EcrDto) => {
      this.ecrDto = response;
      this.ecrNumber = this.ecrDto.ecrNo;
      this.showLoader = false;
    }, error => {
      this.ecrDto.ecrNo = this.ecrNumber;
      this.showLoader = false;
    }));
  }

  updateEco(isPqpCheck = false) {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.updateEco(this.ecoDto).subscribe((response: EcoDto) => {
      this.ecoDto = response;
      if (isPqpCheck && this.ecoDto.criticalPart) {
        this.checkForPqpExist();
      }
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  // when user checks critical part checkbox we need to check if pqp exist or if not then create it
  async checkForPqpExist() {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.getPqpByEcoId(this.ecoDto.id).subscribe((pqpDto: PqpDTOS) => {
      if (pqpDto) {
        this.pqpDto = pqpDto;
        this.ecoPqpDetailsAvailable = true;
        this.showLoader = false;
      } else {
        // Create PQP
        this.pqpDto.ecoId = this.ecoDto.id;
        this.subscription.add(this.ecoPlanService.createEcoPqp(this.pqpDto).subscribe((createdPqp: PqpDTOS) => {
          this.pqpDto = createdPqp;
          this.ecoPqpDetailsAvailable = true;
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
        }));
      }
    }, error => {
      this.showLoader = false;
    }));
  }

  updatePqp() {
    this.showLoader = true;
    this.pqpDto.ecoId = this.ecoDto.id;
    this.subscription.add(this.ecoPlanService.updatePqp(this.pqpDto).subscribe((response: PqpDTOS) => {
      this.pqpDto = response;
      this.ecoPqpDetailsAvailable = true;
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  syncOnhold(partNumberDto: EcrPartnumberDto,syncType:string) {
    // call epicore
    this.showLoader = true;
    return new Promise(resolve => {
      partNumberDto.syncStatus = 'IN_PROGRESS';
      partNumberDto.disable = true;

      this.subscription.add(this.ecrManagementService.isPartnumberExistInEpicore(partNumberDto.partNumber).subscribe((res) => {
        if (res) {
          this.showLoader = false;
          partNumberDto.loading = true;
          partNumberDto.syncStatus = 'IN_PROGRESS';
          partNumberDto.disable = true;
          this.subscription.add(this.ecrManagementService.syncPartNumberToEpicore(partNumberDto,syncType).subscribe(() => {
          }));
        } else {
          partNumberDto.syncStatus = '';
          this.snakbarService.error(Messages.ECR_MANAGEMENT.Partnumber_Not_Exist);
        }
      }, (error) => {
        this.showLoader = false;
        return false;
      }));
    });
  }

  getECRPartSyncStatus(partNumberDto: EcrPartnumberDto){
    this.ecrManagementService.getECRPartSyncStatus(partNumberDto.id).subscribe((res) => {
      if(res && res!=undefined && res!=null){
        partNumberDto.syncStatus = res;
      }
    })
  }


  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
