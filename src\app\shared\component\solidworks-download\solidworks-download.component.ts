import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material';
import { PartNumberComponent } from '../Part Number/part-number.component';

@Component({
    selector: 'sfl-solidworks-download',
    templateUrl: './solidworks-download.component.html'
})

export class SolidworksDownloadComponent {


    constructor(
        public dialogRef: MatDialogRef<PartNumberComponent>,
    ) { }

    closeDialog(): void {
        this.dialogRef.close();
    }
}
