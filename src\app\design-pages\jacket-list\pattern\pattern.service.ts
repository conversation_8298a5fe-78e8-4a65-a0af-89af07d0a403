
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';



@Injectable({ providedIn: 'root' })
export class PatternService {
  constructor(private readonly http: HttpClient) {}

  savePatternDesign(patternDesigns) {
    return this.http.post(AppConfig.SAVE_PATTERN_DESIGN, patternDesigns).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  updatePatternDesign(patternDesigns, jacketId) {
    return this.http.delete(AppConfig.UPDATE_PATTERN_DESIGN + jacketId, patternDesigns).pipe(map(utils.extractData),catchError(utils.handleError),);
  }


  getPatternDesignByJacketId(jacketId) {
    return this.http
      .get(AppConfig.SAVE_PATTERN_DESIGN + '/jacket/' + jacketId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
