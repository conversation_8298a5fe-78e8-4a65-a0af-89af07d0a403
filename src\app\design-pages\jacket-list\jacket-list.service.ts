import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map } from 'rxjs/operators';


import { forkJoin, Observable } from 'rxjs';
import { createRequestOption, PageableQuery, SharedService } from 'src/app/shared';
import { MoveJacketFeaturesDTO } from 'src/app/shared/component/geometry/geometry.model';
import { utils } from '../../shared/helpers/app.helper';
import { AppConfig } from './../../app.config';
import { DownloadZipRequestDTO, FilePath, FilePathDirectory, Jacket, RepeatJacketDTO, SelectedJacketFilePathDTO } from './jacket-list.model';

@Injectable({ providedIn: 'root' })
export class JacketListService {
  constructor(private http: HttpClient, private readonly sharedService: SharedService) { }
  getJacketListByQuotationId(quotationId) {
    return this.http
      .get(AppConfig.GET_JACKETS_BY_QUOTATIONID + quotationId).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getJacketGroupByRevId(revisionId) {
    return this.http
      .get(AppConfig.GET_JACKETS_GROUP_BY_REVISON_ID + revisionId).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getJacketListByRevId(revisionId, pageableObject: PageableQuery) {
    return this.http
      .get(AppConfig.GET_JACKETS_LIST_BY_REVISON_ID + revisionId, { params: createRequestOption(pageableObject) }).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getCustomerSQTLink(quoteId) {
    return this.http
      .get(AppConfig.GET_CUSTOMER_SQT_LINK + quoteId).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getJacketListDropdownByRevId(revisionId) {
    return this.http
      .get(AppConfig.GET_JACKETS_LIST_BY_REVISON_ID_DROPDOWN + revisionId).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  moveJacketFeatures(moveJacketFeaturesDTO: MoveJacketFeaturesDTO) {
    return this.http
      .post(AppConfig.MOVE_JACKET_FEATURES, moveJacketFeaturesDTO).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  multipleApiCallFunction(revisionId, jacketIds, pageableObject: PageableQuery): Observable<any[]> {
    const api1 = this.http
      .get(AppConfig.GET_JACKETS_LIST_BY_REVISON_ID + revisionId, { params: createRequestOption(pageableObject) }).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
    const api2 = this.http.post(AppConfig.GET_SYNC_STATUS, jacketIds).pipe(
      map(utils.extractData),
      catchError(utils.handleError));
    return forkJoin([api1, api2]);
  }

  getHalfJacketList(jacketGroupIdList) {
    return this.http
      .post(AppConfig.GET_OTHER_NEW_HALF_JACKET_LIST, jacketGroupIdList).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getOtherHalfJacketList(jacketId) {
    return this.http
      .get(AppConfig.GET_NEW_HALF_JACKET_LIST + jacketId).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getJacketGroupByQuotationId(quotationId) {
    return this.http
      .get(AppConfig.JACKETGROUP_BY_QUOTATION_API + quotationId).pipe(
      map(utils.extractData),
      catchError(utils.handleError));
  }

  updateVietnamConv(vietnamConversion, jacketId) {
    return this.http
      .delete(AppConfig.UPDATE_VIETNAM_CONVERSION + jacketId, vietnamConversion).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  updateConvReview(conversionReview, jacketId) {
    return this.http
      .delete(AppConfig.UPDATE_CONVERSION_REVIEW + jacketId, conversionReview).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  markJacketAsRepeat(repeatJacketDTO: RepeatJacketDTO) {
    return this.http
      .post(AppConfig.GET_REPEAT_JACKET, repeatJacketDTO).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getJacketsByJacketGroupId(jgId) {
    return this.http
      .get(AppConfig.JACKET_BY_JACKETGROUP_API + jgId).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  saveVietnamConversion(vietnamConversion,quotationId:number) {
    return this.http.post(AppConfig.VIETNAM_CONVERSION_SAVE+'/'+quotationId, vietnamConversion).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  deleteJacket(id:number) {
    return this.http.delete(AppConfig.DELETE_JACKET_FROM_LIST + id).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  toggleOnHold(jacketId, isOnHold) {
    return this.http.put(`${AppConfig.TOGGLE_ON_HOLD}/${jacketId}?onHold=${isOnHold}`, null).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  saveConversionReview(conversionReview) {
    return this.http.post(AppConfig.CONVERSION_REVIEW_SAVE, conversionReview).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  createFolders(jacketData) {
    return this.http.post(AppConfig.CreateFolder, {
      Jackets: jacketData,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations()
    });
  }

  generatePartNumber(jgId: number, isReset: boolean, jacket: Jacket) {
    return this.http
      .put(`${AppConfig.GENERATE_PART_NUMBER_API}${jgId}/${isReset}`, jacket).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  resetPartNumber(jgIds: number, jacket: Jacket) {
    return this.http
      .put(AppConfig.RESET_PART_NUMBER_API + jgIds, jacket).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  updatePartNumber(jacket: Jacket, quotationId: number) {
    return this.http.put(AppConfig.UPDATE_PART_NUMBER_API + quotationId, jacket).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  updateBHCPartNumber(jacket: Jacket) {
    return this.http.put(AppConfig.UPDATE_BHC_PART_NUMBER_API, jacket).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  saveCustomerPartNumber(jackets: Jacket[]) {
    return this.http.post(AppConfig.SAVE_CUSTOMER_PN, jackets).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  updatePartNumbersFromRootPN(jacket: Jacket) {
    return this.http.put(AppConfig.UPDATE_ROOT_PN_API, jacket).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  updatePath(pathData, jgId: number) {
    return this.http
      .put(AppConfig.SAVE_PATH + jgId, pathData, { responseType: 'text' }).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  updateMultipleJackets(selectedJacketsWithCommonFilePath: SelectedJacketFilePathDTO) {
    return this.http
      .post(AppConfig.UPDATE_MULTIPLE_JACKETS, selectedJacketsWithCommonFilePath).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  openFileInSLD(filePath: FilePath, selectedPartFilePath: boolean) {
    return this.http.post(AppConfig.OPEN_FILE, {
      path: filePath.path,
      partNumber: filePath.partNumber,
      rev: filePath.rev,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations(),
      CustomPath: selectedPartFilePath
    });
  }

  openFileInExplorer(filePath: FilePathDirectory) {
    return this.http.post(AppConfig.OPEN_FILE_LOCATION, {
      path: filePath.path,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations()
    });
  }

  resetJacketsPath(jackets: Jacket[]) {
    return this.http
      .post(AppConfig.RESET_PATH, jackets).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getDocumentListyByQuoteId(id) {
    return this.http
      .get(AppConfig.DOCUMENT_API + 'quotationId/' + id).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  uploadDocument(formData: FormData) {
    return this.http.post(AppConfig.DOCUMENT_API, formData).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  getDocument(id) {
    return this.http
      .get(AppConfig.DOCUMENT_API + id, { responseType: 'blob' }).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  deleteDocument(id) {
    return this.http
      .delete(AppConfig.DOCUMENT_API + id).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  // used to get the available design sales associates
  getDesignSalesAssociate(isGlobalSearch: boolean) {
    return this.http
      .get(AppConfig.SalesAssociate + '/' + isGlobalSearch).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  // used to get the available designers
  getAllDesigners() {
    return this.http.get(AppConfig.GET_ALL_DESIGNERS).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  // used to get the previously set values for the design tracker/ default values
  getAppQuoteTrackerDefaultValues(type: string, quotationId: number) {
    return this.http
      .get(AppConfig.DESIGN_QUOTE_DEFAULTS + `${type}/workflow/${quotationId}`).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  getDesignQuoteTrackerDefaultValues(type: string, quotationId: number) {
    return this.http
      .get(AppConfig.DESIGN_QUOTE_DEFAULTS + `${type}/workflow/${quotationId}`).pipe(
        map(utils.extractData),
        catchError(utils.handleError));
  }

  // used to update/ save the design tracker fields
  saveDesignQuoteTrackerFields(designTrackerFields,field:string) {
    return this.http.post(AppConfig.SAVE_UPDATE_DESIGN_QUOTE+ `/${field}`, designTrackerFields).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  downloadElementExcel(id: number) {
    return this.http.get(AppConfig.DOWNLOAD_ELEMENT_EXCEL_API + id, {'responseType': 'blob'}).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  downloadZipFiles(downloadZipRequestDTO: DownloadZipRequestDTO) {
    return this.http.post(AppConfig.DOWNLOAD_SO_ZIP_FILE_API, downloadZipRequestDTO).pipe(map(utils.extractData), catchError(utils.handleError));
  }
}
