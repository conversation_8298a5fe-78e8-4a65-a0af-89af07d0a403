import { Values } from './../../../shared/constants/values.constants';
import { LevelOneReviewService } from './level1-review.service';
import { DatePipe } from '@angular/common';
import { LevelOneReview, ChecklistLevelOneReviewDTO } from './level1-review.model';
import { Subscription } from 'rxjs';
import { Component, Inject, OnInit, OnDestroy } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { UndoResponse } from '../pattern/pattern.model';
import { SweetAlertService } from 'src/app/shared/service/sweet-alert.service';
import { JacketStatus } from '../jacket-list.model';

@Component({
  selector: 'sfl-level1-review',
  templateUrl: './level1-review.component.html'
})
export class Level1ReviewComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  levelOneReview = new LevelOneReview();
  checklistLevelOneReviewDTO: ChecklistLevelOneReviewDTO = new ChecklistLevelOneReviewDTO();
  jacketList = new Array<number>();
  jacketId: number;
  quotationId: number;
  showLoader: boolean;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  undoResponseDTO: UndoResponse = new UndoResponse();
  JacketStatuses: JacketStatus = new JacketStatus();

  constructor(
    public readonly dialogRef: MatDialogRef<Level1ReviewComponent>,
    private readonly sweetAlertService: SweetAlertService,
    private readonly datePipe: DatePipe,
    private readonly levelOneReviewService: LevelOneReviewService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketList = data.jacketList;
    this.jacketId = data.jacketId;
    this.jacketId = data.jacketId;
    this.quotationId = data.quotationId;
  }

  ngOnInit() {
    this.levelOneReview.levelOneReviewDate = new Date();
    if (this.jacketId) {
      this.getLevelOneReviewByJacketId(this.jacketId);
    }
    this.checklistLevelOneReviewDTO.quotationID = this.quotationId;
  }

  getLevelOneReviewByJacketId(jacketId) {
    this.subscription.add(
      this.levelOneReviewService.getLevelOneReviewByJacketId(jacketId).subscribe((res: LevelOneReview) => {
        if (res) {
          this.levelOneReview = res;
        }
      })
    );
  }

  saveLevelReview() {
    this.showLoader = true;
    this.levelOneReview.levelOneReviewDate = this.datePipe.transform(this.levelOneReview.levelOneReviewDate, Values.dateFormat.format);
    if (this.jacketList && this.jacketList.length > 0) {
      this.jacketList.forEach(ele => {
        const levelOneReviewConst = Object.assign({}, this.levelOneReview);
        levelOneReviewConst.jacketId = ele;
        levelOneReviewConst.checked = true;
        this.checklistLevelOneReviewDTO.levelOneReviewDTOs.push(levelOneReviewConst);
      });
    } else {
      if (this.jacketId) {
        this.levelOneReview.jacketId = this.jacketId;
        this.checklistLevelOneReviewDTO.levelOneReviewDTOs.push(this.levelOneReview);
      }
    }

    if (this.checklistLevelOneReviewDTO.levelOneReviewDTOs.length > 0) {
      this.subscription.add(
        this.levelOneReviewService.saveLevelOneReview(this.checklistLevelOneReviewDTO).subscribe(
          res => {
            this.checklistLevelOneReviewDTO = new ChecklistLevelOneReviewDTO();
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.SAVE, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    } else {
      this.showLoader = false;
    }
  }

  async undoLevelOne() {
    if (await this.sweetAlertService.warningWhenUndoSignature()) {
      this.subscription.add(
        this.levelOneReviewService.updateLevelOneReview(this.checklistLevelOneReviewDTO, this.jacketId).subscribe(
          res => {
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.UNDO, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  closeDialog(updated?: string, jacketId?: number): void {
    this.undoResponseDTO.status = updated;
    this.undoResponseDTO.id = jacketId;
    this.dialogRef.close(this.undoResponseDTO);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
