import {Injectable} from '@angular/core';

export interface BadgeItem {
  type: string;
  value: string;
}

export interface ChildrenItems {
  state: string;
  name: string;
  type: string;
}

export interface Menu {
  state: string;
  name: string;
  type: string;
  icon: string;
  badge?: BadgeItem[];
  children?: ChildrenItems[];
}

const MENUITEMS = [
  {
    state: 'quote-tracker/dashboard',
    name: 'Quotation Tracker',
    type: 'link',
    icon: 'timeline'
  },
  {
    state: '/quote-tracker/dashboard/app-engineering-quote-tracker',
    name: 'Application Engineering Quote Tracker',
    type: 'link',
    icon: 'timeline'
  },
  {
    state: '/quote-tracker/dashboard/design-engineering-quote-tracker',
    name: 'Design Engineering SO Tracker',
    type: 'link',
    icon: 'timeline'
  }
];

@Injectable()
export class QuoteTrackerLayoutNavbarService {
  sidePanelOpened: boolean;
  getAll(): Menu[] {
    return MENUITEMS;
  }

  add(menu) {
    MENUITEMS.push(menu);
  }
}
