<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #accessoryForm="ngForm" (ngSubmit)="updateAccessory()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select
            placeholder="Controller Name"
            [(ngModel)]="accessory.controllerName"
            name="controllerName"
            #controllerNameInput="ngModel"
            required
          >
            <mat-option *ngFor="let controller of accessoryControllers" [value]="controller.name">
              {{ controller.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="controllerNameInput.touched && controllerNameInput.invalid">
          <small class="mat-text-warn" *ngIf="controllerNameInput?.errors.required">Controller is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <div>
          <button mat-stroked-button type="button" color="warn" (click)="manageAccessoryControllers()">Manage Accessory Controllers</button>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <textarea
            matInput
            placeholder="Description"
            [(ngModel)]="accessory.description"
            name="accessoryDescription"
            #descriptionInput="ngModel"
            required
            sflNoWhiteSpaces
          ></textarea>
        </mat-form-field>
        <div *ngIf="descriptionInput.touched && descriptionInput.invalid">
          <small class="mat-text-warn" *ngIf="descriptionInput?.errors.required">Accessory description is required.</small>
          <small class="mat-text-warn" *ngIf="descriptionInput?.errors?.whitespace && !descriptionInput?.errors?.required">
            Invalid description.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="List Price"
            [(ngModel)]="accessory.listPrice"
            name="listPrice"
            #listPriceInput="ngModel"
            sflIsDecimal
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number"
            [(ngModel)]="accessory.partNumber"
            name="partNumber"
            #partNumberInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partNumberInput.touched && partNumberInput.invalid">
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors.required">Accessory part number is required.</small>
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors?.whitespace && !partNumberInput?.errors?.required">
            Invalid part number.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="US Cost" [(ngModel)]="accessory.usCost" name="usCost" #usCostInput="ngModel" sflIsDecimal />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="accessory.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!accessoryForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
