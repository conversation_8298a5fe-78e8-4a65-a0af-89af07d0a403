import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { Messages, SharedService, SnakbarService, SweetAlertService } from 'src/app/shared';
import { AddJackets, AddNewJackets } from 'src/app/shared/component/geometry/geometry.model';
import { GemometryService } from 'src/app/shared/component/geometry/geometry.service';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { TitleBlock, TitleBlockSuccess } from './title-block-editor.model';
import { TitleBlockService } from './title-block-editor.service';

@Component({
  selector: 'sfl-title-block-editor',
  templateUrl: './title-block-editor.component.html'
})
export class TitleBlockEditorComponent implements OnInit, OnDestroy {
  private _JacketID: number;
  private _revisionId: number;
  titleBlock: TitleBlock;
  subscription = new Subscription();
  optJacketList: AddNewJackets[];
  showLoader = false;
  newPartNumber: number;
  isRevisionActive: boolean;
  jacketId: number;

  get revisionId() {
    return this._revisionId;
  }

  @Input()
  set revisionId(val) {
    this._revisionId = val;
  }

  get jacketID() {
    return this._JacketID;
  }

  @Input()
  set jacketID(val) {
    this._JacketID = val;
    this.getTitleBlock();
    this.getJacketByJacketId();
  }

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(private readonly geometryService: GemometryService, private readonly sharedService: SharedService, private readonly snakbarService: SnakbarService, private readonly titleBlockService: TitleBlockService,private readonly sweetAlertService: SweetAlertService) {}

  ngOnInit() {
    this.optJacketList = new Array<AddNewJackets>();
    this.titleBlock = new TitleBlock();
  }

  getJacketByJacketId() {
    if (this.jacketID) {
      this.optJacketList = [];
      this.subscription.add(
        this.geometryService.getJacketByJacketId(this.jacketID).subscribe((res: AddJackets[]) => {
          this.optJacketList.push(res);
          this.optJacketList.forEach(jacket => (jacket.watts = Math.round(jacket.watts)));
          this.getid(this.jacketID);
          this._revisionId = this.optJacketList[0].revisionId;
        })
      );
    }
  }

  getid(id) {
    this.jacketId = id;
  }

  getInactiveJacketsByRevisionId() {
    this.subscription.add(
      this.geometryService.getJacketsByRevId(this._revisionId).subscribe(
        (res: AddNewJackets[]) => {
          if (res.length > 0) {
            this.optJacketList = res;
            this.showLoader = false;
          }
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  async drawJacketDesign() {
    this.showLoader = true;
    this.optJacketList[0].dxfRequest = true
    let userCountry = this.sharedService.getUsersCountry();
    let matchingSolidWorksBlocks: string[] = [];
    matchingSolidWorksBlocks = await this.getMatchingSWBlockForDrawing(userCountry);
    this.subscription.add(this.geometryService.drawJacketDesign(this.optJacketList[0], matchingSolidWorksBlocks,false).subscribe(res => {
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }))
  }

  private getMatchingSWBlockForDrawing(userCountry: string) {
    return new Promise<string[]>((resolve) => {
      this.subscription.add(this.geometryService
        .getMatchingSWBlockPartFilesLocationByJacketIdAndUserCountry(this.jacketID, userCountry)
        .subscribe((res: string[]) => {
          if (res) {
            resolve(res);
          }
        }));
    })
  }

  fileTransform(value:string){
    let userLocation:string  = this.sharedService.getUsersCountry();
    if(userLocation && value){
      if(userLocation.toLowerCase()==='usa'){
        if(value.includes('************')){
          value =  value.replace('************\\PUBLIC\\ENGINEERING','**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering');
        }
      }
      else{
        if(value.includes('**********')){
          value =  value.replace('**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering','************\\PUBLIC\\ENGINEERING');
        }
      }
      return value;
    }
    else{
      return value;
    }
}

  // used to update the changes done in the Title Block Screen to the Solid works design and to BHX service
  updateTitleBlock() {
    this.showLoader = true;
    this.titleBlock.partFilePath = this.fileTransform(this.titleBlock.partFilePath);
    this.subscription.add(
      this.titleBlockService.updateTitleBlock(this.titleBlock).subscribe(
        (res: TitleBlockSuccess) => {
          if (res.success) {
            this.snakbarService.success(Messages.Title_Block.save_success_drawing_file);
            this.showLoader = false;
            this.updateTitleBlockBHX(res.data);
          } else {
            this.showLoader = false;
            this.snakbarService.error(res.message);

            this.updateTitleBlockBHX(this.titleBlock);
          }
        },
        error => {
          this.showLoader = false;
          this.snakbarService.error(Messages.Solid_Work.not_install);
          this.updateTitleBlockBHX(this.titleBlock);
        }
      )
    );
  }

  async refreshTitleBlock() {
    if (await this.sweetAlertService.confirmResetTitleBlock()) {
      this.showLoader = true;
      this.subscription.add(
        this.titleBlockService.refreshTitleBlock(this._JacketID).subscribe(
          (res: TitleBlock) => {
            if (res) {
              this.titleBlock = res;
              this.showLoader = false;
              this.titleBlock.pick = 'PICKS';
              this.titleBlock.warp = 'WARPS';
            } else {
              this.showLoader = false;
            }
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  getTitleBlock() {
    this.subscription.add(
      this.titleBlockService.getTitleBlock(this._JacketID).subscribe((res: TitleBlock) => {
        if (res) {
          this.titleBlock = res;
          this.titleBlock.oldRevision = this.titleBlock.revision;
          this.titleBlock.oldPartNumber = this.titleBlock.partNumber;
          this.titleBlock.pick = 'PICKS';
          this.titleBlock.warp = 'WARPS';
        }
      })
    );
  }

  updateTitleBlockBHX(data) {
    this.showLoader = true;
    this.titleBlock.partNumber = this.titleBlock.oldPartNumber;
    this.titleBlock.revision = this.titleBlock.oldRevision;
    this.titleBlock.watts = Math.round(this.titleBlock.watts);
    this.subscription.add(
      this.titleBlockService.updateTitleBlockBHX(data).subscribe(
        (res: TitleBlock) => {
          if (res) {
            this.titleBlock = res;
            this.titleBlock.oldPartNumber = this.titleBlock.partNumber;
            this.titleBlock.oldRevision = this.titleBlock.revision;
            this.showLoader = false;
            this.snakbarService.success(Messages.Title_Block.save_success);
          }
        },
        () => {
          this.snakbarService.success(Messages.Title_Block.error_msg);
          this.showLoader = false;
        }
      )
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
