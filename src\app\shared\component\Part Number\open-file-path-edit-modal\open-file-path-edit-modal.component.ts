import { FlatTreeControl } from '@angular/cdk/tree';
import { ChangeDetectorRef, Component, Inject, On<PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatDialogRef, MatTreeFlatDataSource, MatTreeFlattener, MAT_DIALOG_DATA } from '@angular/material';
import { BehaviorSubject, Subscription } from 'rxjs';
import { SnakbarService } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SharedService } from 'src/app/shared/service/shared.service';
import { SweetAlertService } from 'src/app/shared/service/sweet-alert.service';
import { TreeFunctionService } from 'src/app/shared/service/tree.service';
import { TreeData } from '../../tree/tree-data.model';
import { NewPartFilePathComponent } from './new-part-file-path/new-part-file-path.component';

export class TodoItemFlatNode {
  item: string;
  level: number;
  expandable: boolean;
}

@Component({
  selector: 'sfl-open-file-path-edit-modal',
  templateUrl: './open-file-path-edit-modal.component.html',
  styleUrls: ['./open-file-path-edit-modal.scss']
})

export class OpenFilePathEditModalComponent implements OnInit, OnDestroy {

  /** Map from flat node to nested node. This helps us finding the nested node to be modified */
  flatNodeMap = new Map<TodoItemFlatNode, TreeData>();

  /** Map from nested node to flattened node. This helps us to keep the same object for selection */
  nestedNodeMap = new Map<TreeData, TodoItemFlatNode>();

  treeControl: FlatTreeControl<TodoItemFlatNode>;

  treeFlattener: MatTreeFlattener<TreeData, TodoItemFlatNode>;

  dataSource: MatTreeFlatDataSource<TreeData, TodoItemFlatNode>;
  subscription = new Subscription();
  selectedNode: TodoItemFlatNode;
  showLoader = false;
  existingFilePathOfJacket: string;
  selectedPath = '';
  newPathString: string;
  usaDirectoryPath = '\\\\**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES'; // default root path for USA to start listing directories from
  vietnamDirectoryPath = '\\\\************\\PUBLIC\\ENGINEERING\\DESIGN ENGINEERING'; // default root path for Vietnam to start listing directories from
  directoryPath = '';
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  // kept D: drive as of now but needs to be changed to shared drive.
  _dataChange = new BehaviorSubject<TreeData[]>([{ Id: 1, Name: this.directoryPath, isExpanded: false, isRootNode: true, Children: [] }]);


  transformer = (node: TreeData, level: number) => {
    const existingNode = this.nestedNodeMap.get(node);
    const flatNode =
      existingNode && existingNode.item === node.Name
        ? existingNode
        : new TodoItemFlatNode();
    flatNode.item = node.Name;
    flatNode.level = level;
    flatNode.expandable = !!node.Children;
    this.flatNodeMap.set(flatNode, node);
    this.nestedNodeMap.set(node, flatNode);
    return flatNode;
  };

  getLevel = (node: TodoItemFlatNode) => node.level;

  isExpandable = (node: TodoItemFlatNode) => node.expandable;

  getChildren = (node: TreeData): TreeData[] => node.Children;

  hasChild = (_: number, _nodeData: TodoItemFlatNode) => _nodeData.expandable;

  hasNoContent = (_: number, _nodeData: TodoItemFlatNode) =>
    _nodeData.item === '';

  constructor(
    private dialogRef: MatDialogRef<OpenFilePathEditModalComponent>,
    private changeDetector: ChangeDetectorRef,
    private matDialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) data,
    private treeService: TreeFunctionService,
    private snakbarService: SnakbarService,
    private alertService: SweetAlertService,
    private readonly sharedService: SharedService
  ) {
    if (data) {
      this.existingFilePathOfJacket = data.partFilePath;
      if (this.sharedService.getUsersCountry() === 'Vietnam') {
        this.directoryPath = this.vietnamDirectoryPath;
      } else {
        this.directoryPath = this.usaDirectoryPath;
      }
    }
    this._dataChange = new BehaviorSubject<TreeData[]>([
      { Id: 1, Name: this.directoryPath, isExpanded: false, isRootNode: true, Children: [] }]);

    this.treeFlattener = new MatTreeFlattener(
      this.transformer,
      this.getLevel,
      this.isExpandable,
      this.getChildren
    );

    this.treeControl = new FlatTreeControl<TodoItemFlatNode>(
      this.getLevel,
      this.isExpandable
    );

    this.dataSource = new MatTreeFlatDataSource(
      this.treeControl,
      this.treeFlattener
    );

    this._dataChange.subscribe(data => {
      this.dataSource.data = data;
    });

  }

  ngOnInit() {}

  // loads all child folder of selected node.
  loadFoldersFromSelectedPath(path: string): Promise<Array<TreeData>> {
    return new Promise((resolve, reject) => {
      const responseArray = new Array<TreeData>();
      this.subscription.add(
        this.treeService.enumerateFolder(path).subscribe(
          (res: any) => {
            if (res) {
              const children = res.data.children;
              if (children) {
                children.forEach(child => {
                  const tree = new TreeData(null, child, false, false);
                  responseArray.push(tree);
                });
              }
              resolve(responseArray);
            }
          },
          err => {
            reject();
          }
        )
      );
    });
  }

  getPathFromSelectedNodeRootNode(node: TodoItemFlatNode): string {
    if (node.level < 1) return this.directoryPath.concat('\\');
    let path = '';
    let basePathExists = false;
    let obj = node;
    let oldObj: TodoItemFlatNode;
    do {
      oldObj = obj;
      obj = this.getParentNode(oldObj);
      if (obj != null) {
        if (basePathExists) {
          path = obj.item + '\\' + path;
        } else {
          path = obj.item + '\\' + path + '\\' + node.item;
          basePathExists = true;
        }
      }
    }
    while (obj);
    return path ? path : this.directoryPath.concat('\\');
  }

  getParentNode(node: TodoItemFlatNode): TodoItemFlatNode | null {
    const currentLevel = this.getLevel(node);

    if (currentLevel < 1) {
      return null;
    }

    const startIndex = this.treeControl.dataNodes.indexOf(node) - 1;

    for (let i = startIndex; i >= 0; i--) {
      const currentNode = this.treeControl.dataNodes[i];

      if (this.getLevel(currentNode) < currentLevel) {
        return currentNode;
      }
    }
    return null;
  }

  // toggles the state selected state of node, and updates the selected path ,
  // if node is not expanded then it's expanded and children are added to the node.
  // if node is already expanded then toggling would result in removing all children.
  async onToggle(node: TodoItemFlatNode) {
    this.selectedNode = node;
    this.showLoader = true;
    const parentNode = this.flatNodeMap.get(node);
    parentNode.isExpanded = !parentNode.isExpanded;

    this.selectedPath = this.getPathFromSelectedNodeRootNode(node);
    if (parentNode.isExpanded) {
      await this.loadFoldersFromSelectedPath(this.selectedPath)
        .then(res => {
          const responseArray = res;
          parentNode.Children.push(...responseArray);
          this.refreshTreeData();
          this.treeControl.expand(this.selectedNode);
          this.showLoader = false;
        })
        .catch(err => {
          this.snakbarService.error(err);
          this.showLoader = false;
        });
    } else {
      this.flatNodeMap.forEach((val, key) => {
        if (key.level >= node.level + 1) {
          this.flatNodeMap.delete(key);
        }
      });
      this.nestedNodeMap.forEach((val, key) => {
        if (val.level >= node.level + 1) {
          this.nestedNodeMap.delete(key);
        }
      });
      parentNode.Children = [];
      this.refreshTreeData();
      this.showLoader = false;
    }
  }

  // on any add,update,delete node operation tree needs to be refreshed
  refreshTreeData() {
    this._dataChange.next(this.dataSource.data);
  }

  async addChildNode(childrenNodeData) {
    // if the parent node is not expanded, expand it by getting its child and then add new child node
    if (!childrenNodeData.currentNode.isExpanded) {
      await this.onToggle(childrenNodeData.currentNode);
      this.treeControl.expand(childrenNodeData.currentNode);
    }
    const oldDataSource = this.dataSource.data;
    this.showLoader = true;
    this.subscription.add(
      this.treeService.addFolderToSelectedPath(this.selectedPath + '\\' + childrenNodeData.newAddedNode.Name).subscribe(
        (res: any) => {
          if (res.success) {
            const parentNode = this.flatNodeMap.get(childrenNodeData.currentNode);
            if (parentNode.Children) {
              parentNode.Children.push(childrenNodeData.newAddedNode);
              this.refreshTreeData();

            }
            this.selectedNode = this.nestedNodeMap.get(childrenNodeData.newAddedNode);
            this.selectedPath = this.getPathFromSelectedNodeRootNode(this.selectedNode);
            this.showLoader = false;
          } else {
            this.dataSource.data = oldDataSource;
            this.refreshTreeData();
            this.showLoader = false;
            this.snakbarService.error(res.message);
          }
        },
        err => {
          this.nodeErrorHandler(err, oldDataSource);
        }
      )
    );
  }

  addPath() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { newPathString: this.newPathString, selectedPath: this.selectedPath };
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(NewPartFilePathComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if(res) {
          this.selectedPath = res;
        }
      });
  }


  editNode(nodeToBeEdited) {
    this.showLoader = true;
    const oldDataSource = this.dataSource.data;
    let pathArray = this.selectedPath.split('\\').filter(char => char !== ''); // replacing the folder name with new folder name.
    pathArray = pathArray.slice(0, pathArray.length - 1);
    const newPath = '\\\\' + pathArray.join('\\').concat('\\').concat(nodeToBeEdited.node.Name);
    // const newPath = pathArray.join('\\').concat('\\').concat(nodeToBeEdited.node.Name);

    this.subscription.add(
      this.treeService.renameFolderFromSelectedPath(this.selectedPath, newPath).subscribe((res: any) => {
        if (res.success) {
          const nestedNode = this.flatNodeMap.get(nodeToBeEdited.currentNode);
          nestedNode.Name = nodeToBeEdited.node.Name;
          this.refreshTreeData();
          this.selectedNode = this.nestedNodeMap.get(nestedNode);
          this.selectedPath = newPath;
          this.showLoader = false;
        } else {
          this.dataSource.data = oldDataSource;
          this.refreshTreeData();
          this.showLoader = false;
          this.snakbarService.error(res.message);
        }
      })
    );
  }

  nodeErrorHandler(err, oldDataSource) {
    this.dataSource.data = oldDataSource;
    this.refreshTreeData();
    this.showLoader = false;
    this.snakbarService.error(err);
  }

  saveFilePathForJacket() {
    this.closeDialog(this.selectedPath);
  }

  closeDialog(filePath: string): void {
    this.dialogRef.close(filePath);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
