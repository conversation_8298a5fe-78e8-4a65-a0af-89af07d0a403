<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="filter less-peding">
  <mat-card>
    <div fxLayout="row wrap" fxLayoutGap="10px">
      <div class="sub-heder" fxLayoutAlign="start center" fxLayoutGap="10px" fxFlex>
        <span><strong>Customer:</strong> {{ this.dataSourceJacket.data[0]?.customerName }}
        </span>
        <span><strong>SO # :</strong> {{ this.dataSourceJacket.data[0]?.salesOrderNumber }}
        </span>
      </div>
      <div class="cust_fields" fxLayoutAlign="end center">
        <mat-form-field  appearance="outline" >
          <mat-label>Status</mat-label>
          <mat-select
            name="status"
            [(ngModel)]="quotation.quotationStatusId"
            (selectionChange)="updateQuoteStatus(quotation.quotationStatusId)"
          >
            <mat-option *ngFor="let status of quotationStatuses" [value]="status.id">
              {{ status?.status }}
            </mat-option>
          </mat-select>
          <mat-error>This field is required</mat-error>
        </mat-form-field>
      </div>
      <div class="cust_fields" fxLayoutAlign="end center">
        <mat-form-field  appearance="outline" >
          <mat-label>Assigned Designer</mat-label>
          <mat-select placeholder="Design Engineer" (selectionChange)="updateDesigner(quotation.assignedDesignerId)" name="designEngineer" [(ngModel)]="quotation.assignedDesignerId">
            <mat-option *ngFor="let designengineer of salesassociates" [value]="designengineer?.id">
              {{ designengineer?.firstName }} {{ designengineer?.lastName }}
            </mat-option>
          </mat-select>
          <mat-error>This field is required</mat-error>
        </mat-form-field>
      </div>
      <div fxLayoutAlign="end center" fxLayoutGap="10px">
        <button mat-raised-button color="warn" (click)="trackerFields()">
          Tracker Fields
        </button>
        <button mat-raised-button color="warn" (click)="exportElementsToExcel()">
          Export Elements
        </button>
        <button mat-raised-button color="warn" type="button" (click)="downloadSOExcel()">
          Download SO</button>&nbsp;
        <button mat-raised-button type="submit" color="warn" id="costreport" (click)="costReport()">
          Cost Report
        </button>
        <button mat-raised-button type="submit" color="warn" id="links" (click)="openLinks()">
          Links
        </button>
        <button mat-raised-button type="submit" color="warn" id="coverpage" (click)="CoverPage()">
          Cover Page
        </button>
        <button mat-raised-button type="submit" color="warn" id="partnumber" (click)="PartNumber()">
          Generate P/N
        </button>
      </div>
      <div class="cust_fields" fxLayoutAlign="end center">
        <mat-form-field appearance="outline">
          <mat-label>Jacket Group</mat-label>
          <mat-select [(ngModel)]="selectedJacketGroup" (selectionChange)="onJacketGroupChange($event.value)">
            <mat-option *ngFor="let jacketgroup of newJackGroupName" [value]="jacketgroup.name">
              {{ jacketgroup?.name }}
            </mat-option>
            <mat-option value="All"> All </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <button mat-icon-button class="ml-xs" (click)="openDoclist()" class="ml-xs overflow-visible">
        <mat-icon>folder</mat-icon>
      </button>
      <hr />
    </div>
    <div fxLayout="row wrap">
      <div fxLayoutAlign="end" fxLayoutGap="10px" fxFlex>
        <a *ngIf="solidWorksNotInstalled" class="open-doc error-msg mt-10" (click)="openSolidworkDownloadPage()">
          Click here to install solidworks service
        </a>
      </div>
    </div>
  </mat-card>
</div>
<br />
<div class="demo" fxLayout="row wrap" style="margin-top: -22px">
  <div class="less-peding" fxFlex.gt-lg="{{ sizeLG }}" fxFlex.gt-md="{{ sizeMD }}" fxFlex.gt-sm="{{ sizeSM }}" fxFlex.gt-xs="{{ sizeXS }}" fxFlex>
    <mat-card class="cust_table">
      <mat-table [dataSource]="dataSourceJacket" matSort matSortDirection="asc" matSortDisableClear (matSortChange)="getSorting($event)">
        <ng-container matColumnDef="customerPN">
          <mat-header-cell *matHeaderCellDef fxFlex="8" mat-sort-header>
            Customer P/N
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8">
            <span class="mobile-label">Customer P/N</span>
            <input class="customerPN-input" matInput [(ngModel)]="element.customerPN" />
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8"> </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="repeat">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Repeat
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label">Repeat</span>
            <mat-checkbox color="warn" [(ngModel)]="element.repeat" (change)="repeatJacket(element?.id, $event)">
            </mat-checkbox>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="6"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="6">
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="downloadZipFile(element)">
                <mat-icon class="mr-8">download</mat-icon>
                <span>Download</span>
              </button>
              <button mat-menu-item (click)="deleteJackets(element)">
                <mat-icon class="open-doc mr-8">delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="6"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef fxFlex="5" mat-sort-header>
            Jacket Name
          </mat-header-cell>
          <mat-cell class="open-doc" *matCellDef="let element; let i = dataIndex;" fxFlex="5">
            <span class="mobile-label">Jacket Name</span>
            <a class="link" [routerLink]="['/design-eng/design-plan']" [queryParams]="{
                jacketId: element?.id,
                quotId: quotationId,
                jgId: element?.jacketGroupId,
                entryMethod: entryMethod
              }">{{ element?.name }}</a>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="5"> </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef fxFlex="12" mat-sort-header>
            Part Number
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="12">
            <span class="mobile-label">Part Number</span>
            <a class="link" [routerLink]="['/design-eng/design-plan']" [queryParams]="{
                jacketId: element?.id,
                quotId: quotationId,
                jgId: element?.jacketGroupId,
                entryMethod: entryMethod
              }">{{ element?.partNumber }}</a>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="12"> </mat-footer-cell>
        </ng-container>
        <mat-footer-cell *matFooterCellDef> </mat-footer-cell>
        <ng-container matColumnDef="revisionName">
          <mat-header-cell *matHeaderCellDef fxFlex="5">
            Revision
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">
            <span class="mobile-label">Revision</span>
            <a>{{ element?.designRevision }}</a>
            <mat-icon class="mr-20 open-doc" matTooltip="New revision" (click)="updateRevision(element)">edit</mat-icon>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="5"> </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="jacketgroup">
          <mat-header-cell *matHeaderCellDef fxFlex="8">
            Jacket Group
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8">
            <span class="mobile-label">Jacket Group</span>
            <a>{{ element?.jacketGroupName }}</a>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8"> </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="reference">
          <mat-header-cell *matHeaderCellDef fxFlex="8">
            Reference
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8">
            <span class="mobile-label">Reference</span>
            <a>{{ element?.reference }}</a>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8"> </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="path">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Path
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label">Path</span>
            <a class="link open-doc text-warp" (click)="openFileInExplorer(element)" matTooltip="{{ element.partFilePath | fileLocationTransform }}">{{ element.partFilePath | fileLocationTransform }}</a>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8">
            <div class="mt-10 save-button">
              <div class="mt-10 mb-10">
                <button mat-raised-button type="submit" color="warn" (click)="saveCustomerPN()">Save Customer P/N</button>
              </div>
              <div class="mt-10 mb-10 ml-3">
                <button mat-raised-button color="warn" (click)="retrieveAllZipFiles()">Retrieve Zipped Files</button>
              </div>
            </div>
          </mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="pattern">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Design
          </mat-header-cell>
          <mat-cell *matCellDef="let row; let i = index" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label">Pattern Design</span>
            <mat-checkbox *ngIf="row.patternChecked === null" color="warn" (click)="$event.stopPropagation()" (change)="patternSelectionChanged($event, row)"
              [checked]="selectionp.isSelected(row)" id="patternDesign_{{ i }}">
            </mat-checkbox>
            <label (click)="openPattern(row.id)" class="open-doc link">{{ row?.patternChecked ? row?.patternDesignUserName : ' ' }}</label>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">
            <div class="mt-10" *ngIf="dataSourceJacket.data.length > 0">
              <div>
                <mat-checkbox fxLayoutAlign="center" id="allpatternDesign" matTooltip="Select All" color="warn" (change)="$event ? masterTogglep() : null"
                  [checked]="selectionp.hasValue() && isAllSelectedp()" [indeterminate]="selectionp.hasValue() && !isAllSelectedp()">
                </mat-checkbox>
              </div>
              <div class="mt-10 mb-10">
                <button mat-raised-button type="submit" color="warn" (click)="openPattern()" [disabled]="selectedJacketListForPattern.length === 0" id="signoffpattern">
                  Sign Off
                </button>
              </div>
            </div>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="level1">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Level 1 Review
          </mat-header-cell>
          <mat-cell *matCellDef="let row; let i = index" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label">Level 1 Review</span>
            <mat-checkbox color="warn" *ngIf="row.levelOneChecked === null" (click)="$event.stopPropagation()" (change)="reviewSelectionChanged($event, row)"
              [checked]="selectionl.isSelected(row)" id="levelOneReview_{{ i }}">
            </mat-checkbox>
            <label (click)="openLevelReview(row.id)" class="open-doc link">{{ row?.levelOneChecked ? row?.levelOneUserName : ' ' }}</label>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">
            <div class="mt-10" *ngIf="dataSourceJacket.data.length > 0">
              <div>
                <mat-checkbox fxLayoutAlign="center" matTooltip="Select All" color="warn" (change)="$event ? masterTogglel() : null"
                  [checked]="selectionl.hasValue() && isAllSelectedl()" id="alllevelOneReview" [indeterminate]="selectionl.hasValue() && !isAllSelectedl()">
                </mat-checkbox>
              </div>
              <div class="mt-10 mb-10">
                <button mat-raised-button type="submit" color="warn" (click)="openLevelReview()" [disabled]="selectedJacketListForLevelOneReview.length === 0"
                  id="signofflevel1review">
                  Sign Off
                </button>
              </div>
            </div>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="simulations">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Simulation
          </mat-header-cell>
          <mat-cell *matCellDef="let row; let i = index" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label">Simulation</span>
            <mat-checkbox color="warn" *ngIf="row.simulationChecked === null" (click)="$event.stopPropagation()" (change)="simulationChanged($event, row)"
                          [checked]="selections.isSelected(row)" id="simulations_{{ i }}">
            </mat-checkbox>
            <label (click)="openSimulation(row.id)" class="open-doc link">{{ row?.simulationChecked ? row?.simulationUserName : ' ' }}</label>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">
            <div class="mt-10" *ngIf="dataSourceJacket.data.length > 0">
              <div>
                <mat-checkbox fxLayoutAlign="center" matTooltip="Select All" color="warn" (change)="$event ? masterToggles() : null"
                              [checked]="selections.hasValue() && isAllSelecteds()" id="allSimulations" [indeterminate]="selections.hasValue() && !isAllSelecteds()">
                </mat-checkbox>
              </div>
              <div class="mt-10 mb-10">
                <button mat-raised-button type="submit" color="warn" (click)="openSimulation()" [disabled]="selectedJacketListForSimulation.length === 0"
                        id="signoffsimulation">
                  Sign Off
                </button>
              </div>
            </div>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="element">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Element & BOM
          </mat-header-cell>
          <mat-cell *matCellDef="let row; let i = index" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label">Element design & BOM</span>
            <mat-checkbox color="warn" (click)="$event.stopPropagation()" (change)="elementBomSelectionChanged($event, row)" [checked]="selectione.isSelected(row)"
              id="elementBOM_{{ i }}" *ngIf="row.elementBomChecked === null">
            </mat-checkbox>
            <label (click)="openElement(row.id)" class="open-doc link">{{ row?.elementBomChecked ? row?.elementBomUserName : ' ' }}</label>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">
            <div class="mt-10" *ngIf="dataSourceJacket.data.length > 0">
              <div>
                <mat-checkbox fxLayoutAlign="center" matTooltip="Select All" color="warn" (change)="$event ? masterTogglee() : null"
                  [checked]="selectione.hasValue() && isAllSelectede()" id="allelementBOM" [indeterminate]="selectione.hasValue() && !isAllSelectede()">
                </mat-checkbox>
              </div>
              <div class="mt-10 mb-10">
                <button mat-raised-button type="submit" color="warn" (click)="openElement()" [disabled]="selectedJacketListForElementBom.length === 0" id="signoffelement">
                  Sign Off
                </button>
              </div>
            </div>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="final">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Final Review
          </mat-header-cell>
          <mat-cell *matCellDef="let row; let i = index" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label">Final Review</span>
            <mat-checkbox color="warn" (click)="$event.stopPropagation()" (change)="finalReviewSelectionChanged($event, row)" [checked]="selectionf.isSelected(row)"
              id="finalReview_{{ i }}" *ngIf="row.finalReviewChecked === null">
            </mat-checkbox>
            <label (click)="openFinalReview(row.id)" class="open-doc link">{{ row?.finalReviewChecked ? row?.finalReviewUserName : ' ' }}</label>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">
            <div class="mt-10" *ngIf="dataSourceJacket.data.length > 0">
                <mat-checkbox fxLayoutAlign="center" id="allfinalReview" id="allfinalreview" matTooltip="Select All" color="warn" (change)="$event ? masterTogglef() : null"
                  [checked]="selectionf.hasValue() && isAllSelectedf()" [indeterminate]="selectionf.hasValue() && !isAllSelectedf()">
              </mat-checkbox>
              <div class="mt-10 mb-10">
                <button mat-raised-button type="submit" color="warn" (click)="openFinalReview()" [disabled]="selectedJacketListForFinalReview.length === 0" id="signofffinalreview">
                  Sign Off
                </button>
              </div>
            </div>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="vietanamconv">
          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">
            Conversion
          </mat-header-cell>
          <mat-cell *matCellDef="let row; let i = index" fxFlex="8" fxLayoutAlign="center center">
            <span class="mobile-label"> Vietnam Conv. </span>
            <mat-checkbox color="warn" (click)="$event.stopPropagation()" (change)="vietnamConversionChanged($event, row)" [checked]="selectionv.isSelected(row)"
              id="vietnamConversion_{{ i }}" *ngIf="row.vietnamConversionChecked === null">
            </mat-checkbox>
            <label (click)="undoVietnamConv(row.id)" class="open-doc link">{{ row?.vietnamConversionChecked ? row?.vietnamConversionUserName : ' ' }}</label>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">
            <div class="mt-10" *ngIf="dataSourceJacket.data.length > 0">
              <div>
                <mat-checkbox fxLayoutAlign="center" matTooltip="Select All" color="warn" (change)="$event ? masterTogglev() : null"
                  [checked]="selectionv.hasValue() && isAllSelectedv()" id="allvieanamconv" [indeterminate]="selectionv.hasValue() && !isAllSelectedv()">
                </mat-checkbox>
              </div>
              <div class="mt-10 mb-10">
                <button mat-raised-button type="submit" color="warn" (click)="saveVietnameConversion()" [disabled]="selectedJacketsForVietnamConversion.length === 0"
                  id="signoffvietnamconv">
                  Sign Off
                </button>
              </div>
            </div>
          </mat-footer-cell>
        </ng-container>
        <!--        <ng-container matColumnDef="convreview">-->
        <!--          <mat-header-cell *matHeaderCellDef fxFlex="8" fxLayoutAlign="center center">-->
        <!--            Conv. Review&nbsp;&nbsp;-->
        <!--          </mat-header-cell>-->
        <!--          <mat-cell *matCellDef="let row; let i = index" fxFlex="8" fxLayoutAlign="center center">-->
        <!--            <span class="mobile-label">Conv. Review</span>-->
        <!--            <mat-checkbox color="warn" (click)="$event.stopPropagation()" [checked]="selectionc.isSelected(row)" id="conversionReview_{{ i }}"-->
        <!--              (change)="conversionReviewChanged($event, row)" *ngIf="row.conversionReviewChecked === null">-->
        <!--            </mat-checkbox>-->
        <!--            <label (click)="undoConvReview(row.id)" class="open-doc link">{{ row?.conversionReviewChecked ? row?.conversionReviewUserName : ' ' }}</label>-->
        <!--          </mat-cell>-->
        <!--          <mat-footer-cell *matFooterCellDef fxFlex="8" fxLayoutAlign="center center">-->
        <!--            <div class="mt-10" *ngIf="dataSourceJacket.data.length > 0">-->
        <!--              <div>-->
        <!--                <mat-checkbox fxLayoutAlign="center" matTooltip="Select All" color="warn" (change)="$event ? masterTogglec() : null"-->
        <!--                  [checked]="selectionc.hasValue() && isAllSelectedc()" id="allconversionReview" [indeterminate]="selectionc.hasValue() && !isAllSelectedc()">-->
        <!--                </mat-checkbox>-->
        <!--              </div>-->
        <!--              <div class="mt-10 mb-10">-->
        <!--                <button mat-raised-button type="submit" color="warn" (click)="saveConversionReview()" [disabled]="selectedJacketsForConversionReview.length === 0"-->
        <!--                  id="signoffconversionReview">-->
        <!--                  Sign Off-->
        <!--                </button>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </mat-footer-cell>-->
        <!--        </ng-container>-->
        <ng-container matColumnDef="syncStatus">
          <mat-header-cell *matHeaderCellDef fxFlex="8">
            US Sync
            <mat-icon class="refresh-icon" (click)="refreshJacketList()">refresh</mat-icon>
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8">
            <span class="mobile-label">Sync Status</span>
            <div class="sync-status" [ngSwitch]="element?.syncStatus">
              <p class="bom-sync-status-jacket-list" *ngSwitchCase="'SUCCESS'">
                <mat-icon>check_circle</mat-icon>
              </p>
              <p class="sync-error-jacket-list" *ngSwitchCase="'FAILED'">
                <mat-icon>disabled_by_default</mat-icon>
              </p>
              <p class="sync-inprogress-jacket-list" *ngSwitchCase="'IN_PROGRESS'">
                <mat-icon>hourglass_bottom</mat-icon>
              </p>
              <p class="sync-null-jacket-list" *ngSwitchCase="null">
                <mat-icon>remove_circle</mat-icon>
              </p>
            </div>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8"> </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="vnSync">
          <mat-header-cell *matHeaderCellDef fxFlex="8">
            VN Sync
            <mat-icon class="refresh-icon" (click)="refreshJacketList()">refresh</mat-icon>
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8">
            <span class="mobile-label">VN Sync</span>
            <div class="sync-status" [ngSwitch]="element?.vietnamStatus">
              <p class="bom-sync-status-jacket-list" *ngSwitchCase="'SUCCESS'">
                <mat-icon>check_circle</mat-icon>
              </p>
              <p class="sync-error-jacket-list" *ngSwitchCase="'FAILED'">
                <mat-icon>disabled_by_default</mat-icon>
              </p>
              <p class="sync-inprogress-jacket-list" *ngSwitchCase="'IN_PROGRESS'">
                <mat-icon>hourglass_bottom</mat-icon>
              </p>
              <p class="sync-null-jacket-list" *ngSwitchCase="null">
                <mat-icon>remove_circle</mat-icon>
              </p>
            </div>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8"> </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="crSync">
          <mat-header-cell *matHeaderCellDef fxFlex="8">
            CR Sync
            <mat-icon class="refresh-icon" (click)="refreshJacketList()">refresh</mat-icon>
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="8">
            <span class="mobile-label">CR Sync</span>
            <div class="sync-status" [ngSwitch]="element?.costaRicaStatus">
              <p class="bom-sync-status-jacket-list" *ngSwitchCase="'SUCCESS'">
                <mat-icon>check_circle</mat-icon>
              </p>
              <p class="sync-error-jacket-list" *ngSwitchCase="'FAILED'">
                <mat-icon>disabled_by_default</mat-icon>
              </p>
              <p class="sync-inprogress-jacket-list" *ngSwitchCase="'IN_PROGRESS'">
                <mat-icon>hourglass_bottom</mat-icon>
              </p>
              <p class="sync-null-jacket-list" *ngSwitchCase="null">
                <mat-icon>remove_circle</mat-icon>
              </p>
            </div>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex="8"> </mat-footer-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
        <mat-footer-row *matFooterRowDef="displayedColumns; sticky: true" [ngClass]="{ hide: dataSourceJacket.data.length === 0 }"></mat-footer-row>
      </mat-table>
      <div class="no-records" *ngIf="isNoDataFound | async">No data found</div>
      <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
    </mat-card>
  </div>
  <div class="file-upload-card" *ngIf="documentslist" fxFlex.gt-lg="{{ 100 - sizeLG }}" fxFlex.gt-md="{{ 100 - sizeMD - 25 }}" fxFlex.gt-sm="{{ 100 - sizeSM }}"
    fxFlex.gt-xs="{{ 100 - sizeXS }}">
    <mat-card fxFlex="100">
      <div fxLayout="row wrap" class="filename" *ngFor="let file of documentDataSource.data; let i = index">
        <mat-icon>insert_drive_file</mat-icon>
        <h4 fxFlex class="open-doc" (click)="openDoc(file.id)">
          {{ file?.name }}
        </h4>
        <a (click)="confirmDelete(file.id)">
          <div class="open-doc" fxLayoutAlign="end">
            <mat-icon>delete</mat-icon>
          </div>
        </a>
        <hr />
      </div>
      <br />
      <div class="form-group form_file">
        <input type="file" (change)="readUrl($event)" />
        <p class="font-weight-bold">{{ value }}</p>
      </div>
      <br />
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayoutAlign="start">
          <label class="link" *ngIf="isValidFileSize">*File must be less than 10MB</label>
          <span *ngIf="isUploading" class="loading-icon">
            <img src="../../../../assets/images/loader.gif" alt="loader" />
          </span>
        </div>
        <div fxLayoutAlign="end">
          <button mat-raised-button color="warn" [disabled]="!filename" (click)="uploadFile()">
            Upload
          </button>
        </div>
      </div>
    </mat-card>
  </div>
</div>
