<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="materialPropertiesFilter.name" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldplugName)"
            *ngIf="materialPropertiesFilter.name"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addMaterialProperties()">Add New Material Properties</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="materialPropertyDataSource"
        (matSortChange)="getMaterialPropertiesSorting($event)"
      >
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="40"> Material Property Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="40"> {{ element?.name }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="density">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Density </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.density }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="materialType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Material Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.materialType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="specificHeat">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Specific Heat </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.specificHeat }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editMaterialProperties(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteMaterialProperties(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="materialPropertyMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: materialPropertyMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!materialPropertyDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getMaterialPropertiesPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
