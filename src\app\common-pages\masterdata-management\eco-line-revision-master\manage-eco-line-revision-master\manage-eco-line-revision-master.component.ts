import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { SnakbarService } from 'src/app/shared';
import { EcoLineMaster } from '../../masterdata-management.model';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'app-manage-eco-line-revision-master',
  templateUrl: './manage-eco-line-revision-master.component.html',
  styleUrls: ['./manage-eco-line-revision-master.component.css']
})
export class ManageEcoLineRevisionMasterComponent implements OnInit {

  ecoLineRevisionMaster: EcoLineMaster;
  title = '';
  formData = new FormData();
  showLoader: boolean;
  subscription = new Subscription();
  newRevisionId: number;

  constructor(
    public readonly dialogRef: MatDialogRef<ManageEcoLineRevisionMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.ecoLineRevisionMaster = data;
   }

  ngOnInit() {
    this.ecoLineRevisionMaster.id ? (this.title = 'Update Eco Line Material') : (this.title = 'Add Eco Line Material');
  }

  updateEcoLineMaster() {
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.updateEcoLineMaster(this.ecoLineRevisionMaster).subscribe(
        () => {
          this.showLoader = false;
          this.dialogRef.close(true);
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

}
