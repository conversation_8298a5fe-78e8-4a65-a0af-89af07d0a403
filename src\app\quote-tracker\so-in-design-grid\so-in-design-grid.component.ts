import { Component, OnInit, OnDestroy } from '@angular/core';
import { MatTableDataSource } from '@angular/material';
import { DisplayColumns } from '../../shared/constants/displayColName.constants';
import { Variable } from '../../shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { SoInDesign, GenericPageable, QuoteStatusCount } from '../quote-tracker.model';
import { QuoteTrackerService } from '../quote-tracker.service';
import { Values } from '../../shared/constants/values.constants';
import { DatePipe } from '@angular/common';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'sfl-so-in-design-grid',
  templateUrl: './so-in-design-grid.component.html'
})
export class SoInDesignGridComponent implements OnInit, OnDestroy {
  soInDesignTitle = 'SO in Design';
  soInDesign: SoInDesign;
  subscription = new Subscription();
  soInDesignPageable: GenericPageable<SoInDesign>;
  soInDesignDataSource = new MatTableDataSource<SoInDesign>();
  detailedSoInDesignDataSource: SoInDesign;
  soInDesignColumns = DisplayColumns.Cols.SoInDesignColumn;

  showLoader = false;
  showDetailed = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  fromDate: Date = new Date();
  toDate: Date = new Date();
  quotesCountsByDate: QuoteStatusCount;

  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly datePipe: DatePipe,
    private readonly titleService: Title
  ) {}

  ngOnInit() {
    this.titleService.setTitle('QTS - SO in Design');
    // setting up the default filter of 30 days from current day
    this.fromDate.setDate(this.toDate.getDate() - 30);
    this.searchSOIndesignAndCounters();
  }

  // searches the so in design list and te counters
  searchSOIndesignAndCounters() {
    this.getSoInDesign();
    this.getQuotesCounter();
  }

  // used to ger the listing of SO which are in design
  getSoInDesign() {
    const dateFilter = {
      startDate: this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen),
      endDate: this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen)
    };
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.getSoInDesign(dateFilter).subscribe(
        (res: SoInDesign[]) => {
          this.soInDesignDataSource.data = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to geT the counter of quotes statuses based on the date range selected
  getQuotesCounter() {
    const dateFilter = {
      startDate: this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen),
      endDate: this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen)
    };
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.getQuotesCounterBasedOnTheDateRange(dateFilter).subscribe(
        (res: QuoteStatusCount) => {
          this.quotesCountsByDate = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to open the detailed view of specific SO which is clicked
  openSoDetailed(detailedDataSource: SoInDesign) {
    this.detailedSoInDesignDataSource = detailedDataSource;
    this.showDetailed = true;
  }
  // used to toggle the Detailed view to Main So In detail grid view
  toggleDetailedView() {
    this.showDetailed = !this.showDetailed;
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
