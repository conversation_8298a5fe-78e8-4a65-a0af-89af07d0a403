import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {JacketListComponent} from './jacket-list.component';
import {JacketListRoutes} from './jacket-list.route';
import {PatternComponent} from './pattern/pattern.component';
import {Level1ReviewComponent} from './level1-review/level1-review.component';
import {FinalReviewComponent} from './final-review/final-review.component';
import {VietnamConversionComponent} from './vietnam-conversion/vietnam-conversion.component';
import {CoverPageComponent} from './cover-page/cover-page.component';
import {ElementBomComponent} from './element-bom/element-bom.component';
import {NewRevisionComponent} from './new-revision/new-revision.component';
import {CostReportComponent} from './cost-report/cost-report.component';
import {GoldReportComponent} from './gold-report/gold-report.component';
import {ResetToolTipComponent} from './gold-report/reset-tool-tip/reset-tool-tip.component';
import {FileLocationTransformPipe} from './FileLocationTransformPipe';
import {LinkDialogComponent} from './link-dialog/link-dialog.component';
import {SimulationComponent} from './simulation/simulation.component';

@NgModule({
  imports: [
    RouterModule.forChild(JacketListRoutes),
    SharedModule
  ],
  declarations: [
    JacketListComponent,
    PatternComponent,
    Level1ReviewComponent,
    SimulationComponent,
    FinalReviewComponent,
    VietnamConversionComponent,
    CoverPageComponent,
    ElementBomComponent,
    NewRevisionComponent,
    CostReportComponent,
    LinkDialogComponent,
    GoldReportComponent,
    ResetToolTipComponent,
    FileLocationTransformPipe,
    LinkDialogComponent
  ],
  entryComponents: [
    ResetToolTipComponent
  ],
  providers: [],
  exports: [
    JacketListComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class JacketListModule {
}
