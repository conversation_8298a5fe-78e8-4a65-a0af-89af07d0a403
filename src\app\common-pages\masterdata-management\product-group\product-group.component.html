<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex.gt-lg="30" fxFlex.gt-md="30">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="productGroupFilter.name" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldName)"
            *ngIf="productGroupFilter.name"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="productGroupDataSource" (matSortChange)="getProductGroupSorting($event)">
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef fxFlex="20"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.name }} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="productGroupColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: productGroupColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!productGroupDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getProductGroupPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
