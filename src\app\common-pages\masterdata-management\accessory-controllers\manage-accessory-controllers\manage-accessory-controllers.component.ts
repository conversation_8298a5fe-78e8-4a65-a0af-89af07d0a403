import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { AccessoryControllerMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-accessory-controllers',
  templateUrl: './manage-accessory-controllers.component.html'
})
export class ManageAccessoryControllersComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  accessoryController: AccessoryControllerMaster;
  _data: AccessoryControllerMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageAccessoryControllersComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this._data = data;
  }

  ngOnInit() {
    this.accessoryController = this._data.id ? Object.assign({}, this._data) : new AccessoryControllerMaster();
    this._data.id ? (this.title = 'Update Accessory Controller') : (this.title = 'Add Accessory Controller');
  }

  updateAccessoryController() {
    this.showLoader = true;
    if (this._data.id) {
      this.subscription.add(
        this.masterDataService.updateAccessoryControllers(this.accessoryController).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addAccessoryControllers(this.accessoryController).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
