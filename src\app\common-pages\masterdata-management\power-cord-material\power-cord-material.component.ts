import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { PowerCordMaterialsMaster, GenericPageable, PowerCordMaterialsFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { ManagePowerCordMaterialComponent } from './manage-power-cord-material/manage-power-cord-material.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-power-cord-material',
  templateUrl: './power-cord-material.component.html'
})
export class PowerCordMaterialComponent implements OnInit, OnDestroy {
  pageTitle = 'Power Cord Material Master';
  powerCordMaterial: PowerCordMaterialsMaster;
  powerCordMaterialPageable: GenericPageable<PowerCordMaterialsMaster>;
  powerCordMaterialDataSource = new MatTableDataSource<PowerCordMaterialsMaster>();
  powerCordMaterialColumns = DisplayColumns.Cols.PowerCordMaterialsCols;

  dataSource = new MatTableDataSource<PowerCordMaterialsMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  powerCordMaterialFilter: PowerCordMaterialsFilter = new PowerCordMaterialsFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldValue = Values.FilterFields.value;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getPowerCordMaterialsMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new Power Cord Material
  addPowerCordMaterials() {
    this.editPowerCordMaterials(new PowerCordMaterialsMaster());
  }

  // used to add filter to Power Cord Material listing
  async addFilter() {
    this.filter =
      this.powerCordMaterialFilter.value === '' ? [] : [{ key: this.filterFieldValue, value: this.powerCordMaterialFilter.value }];
    this.getPowerCordMaterialsMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of Power Cord Material listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldValue,
        value: fieldToClear === this.filterFieldValue ? (this.powerCordMaterialFilter.value = '') : this.powerCordMaterialFilter.value
      }
    ];
    this.getPowerCordMaterialsMasterData(this.initialPageIndex, this.pageSize);
  }

  getPowerCordMaterialsMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPowerCordMaterialsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<PowerCordMaterialsMaster>) => {
          this.powerCordMaterialPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPowerCordMaterialsTable(this.powerCordMaterialPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createPowerCordMaterialsTable(serviceRequestList: GenericPageable<PowerCordMaterialsMaster>) {
    this.powerCordMaterialDataSource.data = serviceRequestList.content;
  }

  getPowerCordMaterialsPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPowerCordMaterialsMasterData(this.pageIndex, this.pageSize);
  }

  getPowerCordMaterialsSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPowerCordMaterialsMasterData(this.pageIndex, this.pageSize);
  }

  editPowerCordMaterials(powerCordMaterial: PowerCordMaterialsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = powerCordMaterial;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-power-cord-material-master-model';
    const dialogRef = this.matDialog.open(ManagePowerCordMaterialComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          powerCordMaterial.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPowerCordMaterialsMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deletePowerCordMaterial(powerCordMaterialId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePowerCordMaterials(powerCordMaterialId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPowerCordMaterialsMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
