
import {catchError, map} from 'rxjs/operators';

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { utils } from '../../shared/helpers/app.helper';
import { JacketAccessoriesList } from '../accessories/accessories.model';
import { AddLeadTime, SendToDesignConfigurationDTO } from './finalize.model';


@Injectable()
export class FinalizeService {
  contentTypeAppJson = { 'Content-Type': 'application/json' };
  constructor(private http: HttpClient) { }

  getFinalizeDetails(id: number, manufacturedIn: string) {
    return this.http.get(AppConfig.FINALIZE_DETAILS_API + id + '/' + manufacturedIn).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  resetFinalize(id: number, type: string) {
    return this.http.get(AppConfig.FINALIZE_RESET_DETAILS_API + id + '/' + type).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  sendToDesign(quotId: number) {
    return this.http.get(AppConfig.SEND_TO_DESING + quotId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  updateQty(jacketAccessoriesList: JacketAccessoriesList) {
    return this.http.put(AppConfig.UPDATE_QTY_FINALIZE, jacketAccessoriesList).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  massUpdateJacketCosts(jacketAccessoriesList: JacketAccessoriesList[]) {
    return this.http.post(AppConfig.MASS_UPDATE_JACKET_COSTS, jacketAccessoriesList).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  downloadQuoteExcel(quotId: number) {
    return this.http.get(AppConfig.DOWNLOAD_QUOTE_EXCEL_API + quotId, { 'responseType': 'blob' }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  downloadSOExcel(revId: number, currencyId: number) {
    return this.http.get(AppConfig.DOWNLOAD_SO_EXCEL_API + revId + '/currency/' + currencyId, { 'responseType': 'blob' }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  downloadSOExcelFinalize(discountValueObject) {
    return this.http.post(AppConfig.DOWNLOAD_SO_EXCEL_API, discountValueObject, { 'responseType': 'blob' }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  applyVietnamDiscount(discountValueObject) {
    return this.http.post(AppConfig.APPLY_VIETNAM_DISCOUNT, discountValueObject).pipe(catchError(utils.handleError));
  }

  addLeadTime(leadTime: AddLeadTime) {
    return this.http.post(AppConfig.LEAD_TIME, leadTime).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getAllCurrency() {
    return this.http.get(AppConfig.GET_ALL_CURRENCIES).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }

  getLeadTime(quotId: number) {
    return this.http.get(AppConfig.LEAD_TIME + '/' + quotId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  updateSingleJacketVietnamDiscount(discountValueObject) {
    return this.http.post(AppConfig.FINALIZE_DETAILS_API_DISCOUNT_VIETNAM, discountValueObject).pipe(catchError(utils.handleError));
  }

  resetSelectedJacketLines(jacketAccessoriesList: JacketAccessoriesList[]) {
    return this.http.put(AppConfig.FINALIZE_RESET_SELECTED_JACKETS_DETAILS_API, jacketAccessoriesList).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteSelectedJacketLines(jacketAccessoriesList: JacketAccessoriesList[]) {
    const options = {
      headers: new HttpHeaders(this.contentTypeAppJson),
      body: jacketAccessoriesList
    };
    return this.http.delete(AppConfig.FINALIZE_DELETE_SELECTED_JACKETS, options).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  setManufacturedIn(sendToDesignConfigurationDTO: SendToDesignConfigurationDTO) {
    return this.http.post(AppConfig.MANUFACTURED_IN, sendToDesignConfigurationDTO).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getManufacturedInByQuotationId(quoteId: number) {
    return this.http.get(AppConfig.MANUFACTURED_IN + '/' + quoteId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
