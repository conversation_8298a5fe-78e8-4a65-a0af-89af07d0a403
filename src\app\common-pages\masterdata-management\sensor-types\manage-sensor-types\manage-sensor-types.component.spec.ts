import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageSensorTypesComponent } from './manage-sensor-types.component';

describe('ManageSensorTypesComponent', () => {
  let component: ManageSensorTypesComponent;
  let fixture: ComponentFixture<ManageSensorTypesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageSensorTypesComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageSensorTypesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
