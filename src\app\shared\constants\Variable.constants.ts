export class Variable {
  // Quotation Listing Data table configs

  public static itemsPerPage = 25;
  public static tenItemsPerPage = 10;
  public static defaultSortOrder = 'asc';
  public static saveElement = 'true';
  public static saveSensors = 'false';
  public static defaultSortOrderDescending = 'desc';
  public static defaultSortOrderDescendingDesignQuote = 'asc';
  public static defaultSortBy = 'quotationDate';
  public static defaultJacketSortBy = 'id';
  public static quotePattern = '^[A-Za-z0-9]+(?: +[A-Za-z0-9]+)*$';
  public static sortByQuotation_number = 'quotationNumber';
  public static sortByshipDate = 'shipDate';
  public static sortByEngReleaseDate = 'engReleaseDate';
  public static sortBySalesOrderNumber = 'salesOrderNumber';
  public static sortByQuotationStatus = 'quotationStatus';
  public static defaultSortByUserMaster = 'id';
  public static defaultSortById = 'id';
  public static jacketListLink = '/design-eng/jacket-list';
  public static designEngJacketListLink = '#/design-eng/jacket-list';
  public static appEngQuoteLink = '#/app-eng/ccdc';
  public static appJacketListLink = '/app-eng/ccdc';
  public static typeApp = 'app';
  public static typeDesign = 'design';
  public static sortAscending = true;
  public static activePage = 0;
  public static initialPage = 1;
  public static pageSizeOptions = [10, 20, 25, 30, 40, 50];
  public static jacketsToLoad = 10;
  public static maxIndex = 0;
  public static pageNumber = 0;
  public static endOfJackets = false;
  public static warnRed = 'warn';
  public static spinnerMode = 'indeterminate';
  public static smallSpinnerDiameter = 35;
  public static smallSpinnerDiameter75 = 75;
  public static rootPAthForAssemblyFile =
    '\\\\10.145.2.6\\2311Briskheat_Common\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES\\HEATERMANS\\';
  public static rootPathForAssemblyFileVietnam = '\\\\10.145.14.21\\PUBLIC\\ENGINEERING\\DESIGN ENGINEERING\\01 DESIGN FILES\\HEATERMANS\\';
  public static readingAssemblyMessage = 'Please be patient, system may take upto 15 minutes to process your first request!';
  public static ecrPageTitle = 'ECR Log';
  public static ecoPageTitle = 'ECO Plan';
  public static ecrReviewLevel = ['Level 1', 'Level 2', 'Level 3'];
  public static ecrColor = ['Black', 'Red', 'Green', 'Yellow', 'Blue'];
  public static defaultSortByEcrLog = 'id';

  public static CFD_LINK_US = '\\\\10.145.2.6\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES\\CFD\\APM\\';
  public static CFD_LINK_VIETNAM = 'V:\\ENGINEERING\\DESIGN ENGINEERING\\01 DESIGN FILES\\CFD\\APM\\';
  public static HEATERMAN_LINK_US = '\\\\10.145.2.6\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES\\HEATERMANS\\';
  public static HEATERMAN_LINK_VIETNAM = 'V:\\ENGINEERING\\DESIGN ENGINEERING\\01 DESIGN FILES\\HEATERMANS\\';


  //Design dashboard default status for ready to work
  public static STATUS_ARRAY_FOR_READY_TO_WORK: string[] = [
    "Submitted to Design",
    "Design Queue",
    "Level 1 Review",
    "Ready for OFA",
    "Revisions",
    "Revisions Review",
    "Element and BOM",
    "Final Review",
    "Vietnam Conversion",
    "Costa Rica Conversion",
    "Ready for Release",
    "Simulation",
    "Pattern Design",
    "Approved",
    "Approved as Noted"
  ];

  public static STATUS_ARRAY_BY_DEFAULT: string[] = [
    "Preparing for Design Submission",
    "Archived",
    "Released",
    "Cancelled",
    "Project Cancelled",
    "Out For Approval",
    "On Hold"
  ];

  public static STATUS_ARRAY_FOR_IN_PROGRESS: string[] = [
    "Pattern Design (IP)",
    "Level 1 Review (IP)",
    "Revisions (IP)",
    "Revisions Review (IP)",
    "Element and BOM (IP)",
    "Final Review (IP)",
    "Simulation (IP)",
    "Vietnam Conversion",
    "Costa Rica Conversion"
  ];

  public static STATUS_ARRAY_FOR_OFA: string[] = [
    "Out for Approval"
  ];
}
