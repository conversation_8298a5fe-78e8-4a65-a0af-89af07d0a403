import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatTableDataSource } from '@angular/material';
import { QuoteCompleted, QuoteStatusCount } from '../quote-tracker.model';
import { DisplayColumns } from '../../shared/constants/displayColName.constants';
import { Variable } from '../../shared/constants/Variable.constants';
import { QuoteTrackerService } from '../quote-tracker.service';
import { DatePipe } from '@angular/common';
import { Values } from '../../shared/constants/values.constants';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'sfl-quote-completed',
  templateUrl: './quote-completed.component.html'
})
export class QuoteCompletedComponent implements OnInit, OnDestroy {
  headingTitle = 'Quote Completed';
  quoteCompleted: QuoteCompleted;
  subscription = new Subscription();
  quoteCompletedDataSource = new MatTableDataSource<QuoteCompleted>();
  detailedQuoteCompletedDataSource: QuoteCompleted;
  quoteCompletedColumns = DisplayColumns.Cols.QuoteCompletedColumn;

  showLoader = false;
  showDetailed = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  fromDate: Date = new Date();
  toDate: Date = new Date();
  quotesCountsByDate: QuoteStatusCount;

  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly datePipe: DatePipe,
    private titleService: Title
  ) {}

  ngOnInit() {
    this.titleService.setTitle('QTS - Quote Completed');
    this.fromDate.setDate(this.toDate.getDate() - 30);
    this.searchQuoteCompleted();
  }

  // used to handle the filter for quote completed by Date range
  searchQuoteCompleted() {
    this.getQuotesCounter();
    this.getCompletedQuotes();
  }

  // used to geT the counter of quotes statuses based on the date range selected
  getQuotesCounter() {
    const dateFilter = {
      startDate: this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen),
      endDate: this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen)
    };
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.getQuotesCounterBasedOnTheDateRange(dateFilter).subscribe(
        (res: QuoteStatusCount) => {
          this.quotesCountsByDate = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to get the completed quotes
  getCompletedQuotes() {
    const dateFilter = {
      startDate: this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen),
      endDate: this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen)
    };
    this.showLoader = true;
    // start date** & end date**

    this.subscription.add(
      this.quoteTrackerService.getQuoteCompleted(dateFilter).subscribe(
        (completedQuotes: QuoteCompleted[]) => {
          this.quoteCompletedDataSource.data = completedQuotes;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to display detailed view of the Quotes completed
  openCompletedQuoteDetailed(rowData: QuoteCompleted) {
    this.detailedQuoteCompletedDataSource = rowData;
    this.showDetailed = true;
  }

  // used to handle the toggle of summery view and detailed view of quote completed
  toggleDetailedView() {
    this.showDetailed = !this.showDetailed;
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
