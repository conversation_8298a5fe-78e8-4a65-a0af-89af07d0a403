<section class="loading-spinner">
  <svg width="200px" height="200px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid"
    class="lds-facebook" style="background: none;">
    <rect x="17.5" y="35" width="15" height="30" fill="#ef3c43">
      <animate attributeName="y" calcMode="spline" values="12.5;35;35" keyTimes="0;0.5;1" dur="1" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"
        begin="-0.2s" repeatCount="indefinite"></animate>
      <animate attributeName="height" calcMode="spline" values="75;30;30" keyTimes="0;0.5;1" dur="1" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"
        begin="-0.2s" repeatCount="indefinite"></animate>
    </rect>
    <rect x="42.5" y="35" width="15" height="30" fill="#ef3c43">
      <animate attributeName="y" calcMode="spline" values="18.125;35;35" keyTimes="0;0.5;1" dur="1" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"
        begin="-0.1s" repeatCount="indefinite"></animate>
      <animate attributeName="height" calcMode="spline" values="63.75;30;30" keyTimes="0;0.5;1" dur="1" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"
        begin="-0.1s" repeatCount="indefinite"></animate>
    </rect>
    <rect x="67.5" y="35" width="15" height="30" fill="#ef3c43">
      <animate attributeName="y" calcMode="spline" values="23.75;35;35" keyTimes="0;0.5;1" dur="1" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"
        begin="0s" repeatCount="indefinite"></animate>
      <animate attributeName="height" calcMode="spline" values="52.5;30;30" keyTimes="0;0.5;1" dur="1" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"
        begin="0s" repeatCount="indefinite"></animate>
    </rect>
  </svg>
</section>
