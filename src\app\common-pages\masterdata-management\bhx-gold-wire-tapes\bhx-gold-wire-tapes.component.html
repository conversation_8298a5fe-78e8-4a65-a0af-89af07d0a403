<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Alloy Name</mat-label>
          <input matInput [(ngModel)]="goldWireTapesFilter.alloyName" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldAlloyName)"
            *ngIf="goldWireTapesFilter.alloyName"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addGoldWireTapes()">Add Gold Wire Tape</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="goldWireTapesDataSource"
        (matSortChange)="getGoldWireTapesSorting($event)"
      >
        <ng-container matColumnDef="alloyName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Alloy Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.alloyName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="type">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.type }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tpiStr">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> TPI STR </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.tpiStr }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="picks">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Picks </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.picks }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="wireType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Wire Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.wireType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editGoldWireTapes(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteGoldWireTapes(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="goldWireTapesColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: goldWireTapesColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!goldWireTapesDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getGoldWireTapesPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
