import { DatePipe } from '@angular/common';
import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Role, SharedService, SnakbarService } from '../../../shared';
import { Values } from '../../../shared/constants/values.constants';
import { deepCopyFunction } from '../../../shared/utils/utils';
import { SalesAssociate } from '../../dashboard/dashboard.model';
import { ApprovalFormat, CcdcWorkflow, Markings, Quotation, QuoteDefaults, Units } from '../ccdc-model/ccdc.model';
import { ManageUnitsService } from '../manage-units/manage-units.service';
import { SalesOrderSummaryService } from '../summary-sales-order.service';

@Component({
  selector: 'sfl-add-workflow',
  templateUrl: './add-workflow.component.html'
})
export class AddWorkflowComponent implements OnInit, OnDestroy {
  approvalLevels = [];
  approvalFormatList: ApprovalFormat[] = [];
  markingsList: Markings[] = [];
  workFlow = new CcdcWorkflow();
  entryDate: string;
  shipDate: Date;
  subscription = new Subscription();
  jacketGroupId: number;
  measurementUnit = '';
  tempUnit = '';
  quotationId: number;
  _quoteId: number;
  quotation: Quotation;
  productTypes = Values.CCDC_WorkFlow_ProductTypeConst;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  showReloadButton = false;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  salesassociates: SalesAssociate[];
  accountManagers: SalesAssociate[];
  adminRole: SalesAssociate[] = [];

  constructor(
    public workflowDialogRef: MatDialogRef<AddWorkflowComponent>,
    private datePipe: DatePipe,
    private salesOrderSummuryService: SalesOrderSummaryService,
    private sharedService: SharedService,
    private readonly snakbarService: SnakbarService,
    private manageUnitService: ManageUnitsService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketGroupId = data.jacketGroupId;
    this.quotationId = data.quotationId;
    this._quoteId = data.quoteId;
  }

  ngOnInit() {
    this.approvalLevels = deepCopyFunction(Values.approvalLevels); // deep copy values here due to mutation problem
    this.approvalFormatList = deepCopyFunction(Values.approvalFormats); // deep copy values here due to mutation problem
    this.markingsList = deepCopyFunction(Values.markings); // deep copy values here due to mutation problem
    this.getMeasurementUnit();
    this.getWorkflowByJacketGroupId();
    this.quotation = new Quotation();
    this.getSaleAssociate();
    this.getAccountManagerMaster();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.measurementUnit = res.measurementUnit;
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  // used to handle the saving of the workflow info modal
  saveWorkFlow(workflowForm: NgForm, value) {
    this.showLoader = true;
    this.workFlow.quoteId = this._quoteId;
    this.workFlow.jacketGroupId = this.jacketGroupId;
    this.workFlow.dateAppStarted = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphen);
    this.workFlow.approvalFormatList = this.approvalFormatList.filter(format => format.selected === true);
    this.workFlow.markingList = this.markingsList.filter(marking => marking.selected === true);
    this.subscription.add(
      this.salesOrderSummuryService.saveOrUpdateWorkFlow(this.workFlow, this.workFlow.id).subscribe(
        (success: CcdcWorkflow) => {
          if (success) {
            this.sharedService.setProjectName(success.projectName);
            const obj = {data: success, mode: value === 'save' ? 0 : 1};
            workflowForm.reset();
            this.showLoader = false;
            this.workflowDialogRef.close(obj);
          }
        },
        (error) => {
          this.showReloadButton = true;
          this.showLoader = false;
          if (error.applicationStatusCode === 1214 || error.applicationStatusCode === 3001) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  // used to get the workflow info by jacket group id
  getWorkflowByJacketGroupId() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummuryService.getWorkFlowByJacketGroupId(this.jacketGroupId).subscribe(
        (success: CcdcWorkflow) => {
          if (success !== null) {
            this.workFlow = success;
            if (this.workFlow.approvalFormatList.length > 0) {
              for (const aplevel of Object.keys(this.approvalFormatList)) {
                if (this.workFlow.approvalFormatList.find(data => data.approvalLevel === this.approvalFormatList[aplevel].approvalLevel)) {
                  this.approvalFormatList[aplevel].selected = true;
                }
              }
            }
            if (this.workFlow.markingList.length > 0) {
              for (const marking of Object.keys(this.markingsList)) {
                if (this.workFlow.markingList.find(data => data.markings === this.markingsList[marking].markings)) {
                  this.markingsList[marking].selected = true;
                }
              }
            }
            this.showLoader = false;
          } else {
            this.getDefaultValuesForQuotations(this._quoteId);
            this.getDefaultWorkflow(this.quotationId.toString());
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to get and set the account manager and entry date for this quote's workflow
  getDefaultValuesForQuotations(_quoteId: number) {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummuryService.getQuoteDefaultValues(_quoteId).subscribe(
        (response: QuoteDefaults) => {
          if (response) {
            this.workFlow.accountMgrId = response.accountMgrId;
            this.workFlow.entryDate = response.entryDate;
            this.workFlow.assignedAppEngineerId = response.assignedAppEngineerId;
            this.workFlow.dateAppStarted = response.dateAppStarted;
            this.workFlow.dateAppCompleted = response.dateAppCompleted;
            this.workFlow.externalQuoteRequired = response.externalQuoteRequired;
            this.workFlow.customerClarificationRequired = response.customerClarificationRequired;
            this.workFlow.currentStatusComment = response.currentStatusComment;
            this.workFlow.projectName = response.projectTitle;
            this.workFlow.sqtFolderLink = response.sqtFolderLink;
            if (!response.accountMgrId) {
              this.workFlow.accountMgrId = this.accountManagers.find(accMgr => accMgr.firstName === 'Other').id;
            }
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to retrieve values from the Epicore and sets the default values for workflow info (markings)
  getDefaultWorkflow(quotationId: string) {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummuryService.getQuotation(quotationId).subscribe(
        (response: Quotation) => {
          if (response) {
            this.quotation = response;
            if (response.projectName) {
              this.workFlow.projectName = this.quotation.projectName;
            }
            // this.entryDate = Utils.converStringToDate(this.quotation.entryDate);
          }
          this.showLoader = false;
          this.setDefaultValues();
        },
        () => {
          this.showLoader = false;
          // need to set up the default values even if the Epicore fails to respond back with data
          this.setDefaultValues();
        }
      )
    );
  }

  // used to set the default values for the workflow
  setDefaultValues() {
    this.approvalFormatList.forEach(level => (level.approvalLevel === 'PDF' ? (level.selected = true) : (level.selected = false)));
    this.markingsList.forEach(mark => {
      if (mark.markings === 'UL' || mark.markings === 'CE' || mark.markings === 'HL') {
        mark.selected = true;
      }
    });
    this.workFlow.approvalLevel = 'LEVEL3';
  }

  closeDialog() {
    this.workflowDialogRef.close();
  }

  // used to handle the reload of the page
  reloadPage(): void {
    window.location.reload();
  }

  // used to get all the account managers
  getAccountManagerMaster() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.salesOrderSummuryService.getSalesAssociate(false).subscribe(
        (accountManagers: Array<SalesAssociate>) => {
          for (const role of accountManagers) {
            if (role.authorities == Role.ADMIN_ROLE || role.authorities == Role.ENG_ROLE) {
              this.adminRole.push(role);
            }
          }
          this.accountManagers = this.adminRole;
          this.showLoader = false;
          resolve();
        },
        () => {
          this.showLoader = false;
        }
      );
    });
  }

  // used to get all the sales associates
  getSaleAssociate() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.salesOrderSummuryService.getSalesAssociate(false).subscribe(
        (salesAssociates: Array<SalesAssociate>) => {
          this.salesassociates = salesAssociates;
          this.showLoader = false;
          resolve();
        },
        () => {
          this.showLoader = false;
        }
      );
    });
  }

  // used to set the app engg started date when an app engg is assigned/ selected
  appEnggAssigned() {
    this.workFlow.dateAppStarted = this.datePipe.transform(new Date(), Values.dateFormat.format);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
