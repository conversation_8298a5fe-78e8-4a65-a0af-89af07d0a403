export class EcrDto {
  constructor(
    public id: number = null,
    public ecrNo: number = null,
    public ecrDate: string = null,
    public updatedDate: string = null,
    public description: string = null,
    public designReview: string = null,
    public requestorId: number = null,
    public reviewLevel: string = null,
    public designer: number = null,
    public level1Reviewer: number = null,
    public eBom: number = null,
    public finalReviewer: string = null,
    public color: string = null,
    public ecrStatusId: number = null,
    public departmentId: number = null,
    public ecrPartNumbers: EcrPartnumberDto[] = [],
    public ecrAttachments: EcrAttachmentDto[] = [],
  ) {
  }
}

export class EcrPartnumberDto {
  constructor(
    public id: number = null,
    public partNumber: string = null,
    public ecrId: number = null,
    public inActive: boolean = null,
    public onHold: boolean = null,
    public syncStatus: string = null,
    public disable: boolean = false,
    public loading: boolean = false,
    public usOnHold: boolean = null,
    public usHoldReasonCode: string = null,
    public usHoldComments: string = null,
    public usVietnamOnHold: boolean = null,
    public usVietnamHoldReasonCode: string = null,
    public usVietnamHoldComments: string = null,
    public usCostaRicaOnHold: boolean = null,
    public usCostaRicaHoldReasonCode: string = null,
    public usCostaRicaHoldComments: string = null,
    public vietnamOnHold: boolean = null,
    public vietnamHoldReasonCode: string = null,
    public vietnamHoldComments: string = null,
    public costaRicaOnHold: boolean = null,
    public costaRicaHoldReasonCode: string = null,
    public costaRicaHoldComments: string = null
  ) {
  }
}

export class EcrPartNumberExistDTO {
  constructor(
    public message: string,
    public found: boolean,
  ) {
  }
}

export class Departments {
  constructor(
    public id?: number,
    public name?: string,
    public active?: boolean
  ) {
  }
}

export class EcrAttachmentDto {
  constructor(
    public id?: number,
    public blobData?: File,
    public blobDataContentType?: string,
    public ecrId?: number
  ) {
  }
}

export class EcrSearchFilter {
  public ecrNo?: number;
  public status?: number;
  public requestor?: string;
  public partNumber?: string;
}


export class EcrPartNumberDTO {
  public disable?: boolean;
  public ecrId?: number;
  public failedSync?: boolean;
  public id?: number;
  public inActive?: boolean;
  public loading?: boolean;
  public onHold?: boolean;
  public partNumber?: string;
  public syncStatus?: string;
  public ecrNo?: string;
  public ecrStatus?: string;
  public ecrDate: string = null;
}

export class Document {
  constructor(
    public activated?: boolean,
    public filePath?: string,
    public id?: number,
    public name?: string,
    public fileData?: File
  ) {
    this.activated = true;
  }
}

export class EcrPageable {
  constructor(
    public content?: EcrDto[],
    public totalElements?: number,
    public totalPages?: number,
    public number?: number
  ) {
  }
}

export class SelectedStakeHolders {
  constructor(
    public index?: number,
    public stakeHolder?: string
  ) {
  }
}

export class StakeHolders {
  constructor(
    public ecoId: number = null,
    public id: number = null,
    public userId: number = null,
    public userName: string = null
  ) {
  }
}

export class Statuses {
  constructor(
    public id?: number,
    public status?: string,
    public type?: string,
    public active?: boolean,
    public defaultHidden?: boolean,
    public orderNumber?: number,
    public forApproval?: boolean,
    public rowColor?: string,
    public appEngRowColor?: string
  ) {
  }
}

export class AddAttachment {
  constructor(
    public blobData: string = null,
    public blobDataContentType: string = null,
    public ecrId?: 0,
    public fileName: string = null,
    public id: 0 = null
  ) {
  }
}

export class SyncPartNumberDto {
  constructor(
    public ecrPartNumberId?: number,
    public inActive?: boolean,
    public onHold?: boolean,
    public partNumber?: string
  ) {
  }
}
