<h2 mat-dialog-title>Search Part Number
  <hr>
</h2>
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<form #addQuotationForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-card-content>
      <div fxLayout="row wrap">
        <div fxFlex fxLayoutAlign="start center">
          <mat-form-field fxFlex.gt-lg="100%" fxFlex.gt-md="100%" fxFlex.gt-sm="100%" fxFlex.gt-xs="100%">
            <input matInput placeholder="Part Number" ng-trim="true" [(ngModel)]="partNumber"
                   autofocus name="partNumber" #partNumberInput="ngModel" required>
          </mat-form-field>
          <div style="padding-left: 15px" fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-xs="15" fxFlex="100">
            <button mat-raised-button color="warn" type="submit" (click)="searchEcr()"
                    [disabled]="addQuotationForm.invalid">Search
            </button>
          </div>
          <div fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-xs="15" fxFlex="100">
            <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
          </div>
        </div>
      </div>
      <mat-card>
        <h3>SO Number</h3>
        <mat-table [dataSource]="soNumbers">
          <ng-container matColumnDef="quotenumber">
            <mat-header-cell *matHeaderCellDef fxFlex="33%"> Quote Number</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="33%">
              <a class="link" [routerLink]="['/design-eng/jacket-list']" (click)="closeDialog()"
                 [queryParams]="{ quotId: element?.quoteId }">{{
                  element?.quoteNumber
                }}</a>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="33%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="sonumber">
            <mat-header-cell *matHeaderCellDef fxFlex="33%"> SO Number</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="33%">
              {{ element.sonumber != null ? element.sonumber : '-' }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="33%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="revision">
            <mat-header-cell *matHeaderCellDef fxFlex="33%"> Revision</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="33%">
              {{ element.revision != null ? element.revision : '-' }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="33%"></mat-footer-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="searchSOColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: searchSOColumns;"></mat-row>
          <mat-footer-row *matFooterRowDef="searchSOColumns; sticky: true"
                          [ngClass]="{'hide':soNumbers.data.length === 0}"></mat-footer-row>
        </mat-table>
        <div class="no-records" *ngIf="soNumbers.data.length<=0">
          No data found
        </div>
      </mat-card>
      <mat-card>
        <h3>ECR Number</h3>
        <mat-table [dataSource]="ecrPartNumbers">
          <ng-container matColumnDef="ecrnumber">
            <mat-header-cell *matHeaderCellDef fxFlex="33%"> ECR Number</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="33%">
              <a class="link" [routerLink]="['/design-eng/ecr-management']" (click)="closeDialog()"
                 [queryParams]="{ id: element?.ecrId }">{{
                  element?.ecrNo
                }}</a>
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="33%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="status">
            <mat-header-cell *matHeaderCellDef fxFlex="33%"> Status</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="33%">
              {{ element.ecrStatus }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="33%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="ecrdate">
            <mat-header-cell *matHeaderCellDef fxFlex="33%"> Date</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="33%">
              {{ element.ecrDate }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="33%"></mat-footer-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="ecrPartNumColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: ecrPartNumColumns;"></mat-row>
          <mat-footer-row *matFooterRowDef="ecrPartNumColumns; sticky: true"
                          [ngClass]="{'hide':ecrPartNumbers.data.length === 0}"></mat-footer-row>
        </mat-table>
        <div class="no-records" *ngIf="ecrPartNumbers.data.length<=0">
          No data found
        </div>
      </mat-card>
    </mat-card-content>
  </mat-dialog-content>
  <hr>
</form>
