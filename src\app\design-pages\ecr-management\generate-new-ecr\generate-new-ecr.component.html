<div fxLayout="column" fxLayoutAlign="space-between">
  <div class="sfl-loading" *ngIf="showLoader">
    <mat-progress-spinner
    class="sfl-global-spinner-loader"
    [mode]="mode"
    [color]="color"
    [diameter]="spinnerDiameter">
    </mat-progress-spinner>
  </div>

  <h2 mat-dialog-title fxFlex>
    New Engineering Change Request (ECR)
    <hr>
  </h2>

  <mat-dialog-content class="generate-ecr">
    <form class="fields" #ecrFilterForm="ngForm">
      <div fxFlex fxLayout="row" fxLayoutGap="20px">
          <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-xs="50">
            <div fxLayout="row" class="mt-10 mb-20 cust_fields">
              <mat-form-field appearance="outline" >
                <mat-label>Requestor</mat-label>
                <mat-select placeholder="Requestor" [(ngModel)]="ecrDto.requestorId" name="requestor" required>
                  <mat-option *ngFor="let requestor of users" [value]="requestor?.id">
                    {{requestor?.firstName}} {{requestor?.lastName}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline" >
                <mat-label>Department</mat-label>
                <mat-select placeholder="Department" [(ngModel)]="ecrDto.departmentId" name="department" #departmentInput="ngModel" required>
                  <mat-option *ngFor="let department of departments" [value]="department.id">
                    {{department?.name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <div *ngIf="departmentInput.touched && departmentInput.invalid">
                <small class="mat-text-warn" *ngIf="departmentInput?.errors.required">Department is required.</small>
              </div>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>Date</mat-label>
                <input matInput [matDatepicker]="date" placeholder="Date" name="ecrDate" [(ngModel)]="ecrDate" #date="ngModel" autocomplete="off" readonly required>
                <mat-datepicker-toggle matSuffix [for]="date"></mat-datepicker-toggle>
                <mat-datepicker #date></mat-datepicker>
              </mat-form-field>
              <div *ngIf="date.touched && date.invalid">
                <small class="mat-text-warn" *ngIf="date?.errors.required">Date is required.</small>
              </div>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>Design Review</mat-label>
                <mat-select placeholder="Design Review" [(ngModel)]="ecrDto.designReview" name="designReview" #designReviewInput="ngModel" required>
                  <mat-option *ngFor="let review of reviewLevel" [value]="review">
                    {{ review }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <div *ngIf="designReviewInput.touched && designReviewInput.invalid">
                <small class="mat-text-warn" *ngIf="designReviewInput?.errors.required">Design Review is required.</small>
              </div>
            </div>
          </div>
          <div fxFlex.gt-lg="35" fxFlex.gt-md="35" fxFlex.gt-xs="50">
            <span>Part Number(s)</span>
            <div class="mb-10 cust_fields sfl-mat-list mat-list-selected-pn">
              <mat-list role="list" >
                <mat-list-item role="listitem" *ngFor="let pnObj of ecrPartNumbers">
                  <span fxFlex="grow" [ngClass]="{'active-list-item': selectedPartNumber?.partNumber === pnObj?.partNumber}" (click)="selectPartnumber(pnObj)">{{pnObj.partNumber}}</span>
                </mat-list-item>
              </mat-list>
            </div>
            <div fxLayout="row wrap" class="mb-10 cust_fields">
                <div fxFlex fxLayoutAlign="space-between">
                <button mat-raised-button type="button" color="warn" (click)="addPartNumber()">Add Part Number</button>
                <button mat-raised-button type="button" color="warn" [disabled]="!selectedPartNumber?.partNumber" (click)="removePartnumber(selectedPartNumber)" >Remove Part Number</button>
              </div>
            </div>
          </div>
          <div fxFlex.gt-lg="35" fxFlex.gt-md="35" fxFlex.gt-xs="50">
              <span>Attachment(Drawings, Mark-ups, Etc.)</span>
              <div class="mb-10 cust_fields sfl-mat-list mat-list-selected-pn">
                <mat-list role="list">
                  <mat-list-item role="listitem" *ngFor="let attachement of uploadedAttachments">
                    <span [ngClass]="{'active-list-item': selectedAttachment == attachement}" (click)="selectAttachment(attachement)">{{attachement.name}}</span>
                  </mat-list-item>
                </mat-list>
              </div>
              <div fxLayout="row" class="mb-10 cust_fields">
                <div fxFlex fxLayoutAlign="space-between">
                  <button mat-raised-button type="button" color="warn" (click)="addAttachment()">Add Attachment</button>
                  <button mat-raised-button type="button" color="warn" (click)="removeAttachment(selectedAttachment)" [disabled]="!selectedAttachment">Remove Attachement</button>
                </div>
              </div>
          </div>
      </div>
    </form>

    <textarea matInput placeholder="Description of Changes*" [(ngModel)]="ecrDto.description" name="description" rows="10" class="sfl-textarea" #descriptionInput="ngModel" required></textarea>
    <div *ngIf="descriptionInput.touched && descriptionInput.invalid">
      <small class="mat-text-warn" *ngIf="descriptionInput?.errors.required">Description of changes is required.</small>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
        <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
        <button mat-raised-button type="submit" color="warn" [disabled]="!ecrFilterForm.valid" (click)="submitEcr()">Submit ECR</button>
    </div>
  </mat-dialog-actions>
  </div>
