import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { EcoDto, FinalReviewDto } from '../eco-plan.model';
import { Subscription } from 'rxjs';
import { User } from 'src/app/common-pages/users/user.model';
import { EcoPlanService } from '../eco-plan.service';
import { DatePipe } from '@angular/common';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-eco-final-review',
  templateUrl: './eco-final-review.component.html'
})
export class EcoFinalReviewComponent implements OnInit, OnDestroy {

  finalReviewDto: FinalReviewDto = new FinalReviewDto();
  users: User[];

  subscription: Subscription = new Subscription();
  showLoader = false;
  isDataAvailable = false;
  _ecoDto: EcoDto = new EcoDto;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  maxDate = new Date();
  get ecoDto() {
    return this._ecoDto;
  }

  @Input()
  set ecoDto(val) {
    this._ecoDto = val;
  }
  get _users() {
    return this.users;
  }

  @Input()
  set _users(users) {
    this.users = users;
  }

  constructor(private ecoPlanService: EcoPlanService, private datePipe: DatePipe) { }

  ngOnInit() {
    this.getFinalReviewOrCreate();
  }

  // checks if final review exist for eco otherwise creates new
  getFinalReviewOrCreate() {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.getFinalReviewByEcoId(this.ecoDto.id).subscribe((response: FinalReviewDto) => {
      if (response) {
        this.finalReviewDto = response;
        this.showLoader = false;
      } else {
        //  create final review for this eco
        this.finalReviewDto.ecoId = this.ecoDto.id;
        this.subscription.add(this.ecoPlanService.createFinalReviewForEco(this.finalReviewDto).subscribe((finalReview: FinalReviewDto) => {
          this.finalReviewDto = finalReview;
          this.showLoader = false;
        }, error => { this.showLoader = false; }));
      }
    }, error => {this.showLoader = false; }));
  }

  updateFinalReview(updateReviewDate = false) {
    this.showLoader = true;
    if (updateReviewDate && !this.finalReviewDto.reviewDate) {
      this.finalReviewDto.reviewDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
    }
    if (this.finalReviewDto.reviewDate) {
      this.finalReviewDto.reviewDate = this.datePipe.transform(this.finalReviewDto.reviewDate, Values.dateFormat.formatHyphenTimeZone);
    }
    let obj;
    obj = Object.assign({}, this.finalReviewDto);
    obj.reviewDate = this.finalReviewDto.reviewDate;
    this.subscription.add(this.ecoPlanService.updateFinalReviewForEco(obj).subscribe((response: FinalReviewDto) => {
      if (response) {
        obj = response;
        obj.id = response.id;
        this.showLoader = false;
      }
    }, error => { this.showLoader = false; }));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
