import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { PowerCordAmpsMaster, GenericPageable, PowerCordAmpsFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManagePowerCordAmpsComponent } from './manage-power-cord-amps/manage-power-cord-amps.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-power-cord-amps',
  templateUrl: './power-cord-amps.component.html'
})
export class PowerCordAmpsComponent implements OnInit, OnDestroy {
  pageTitle = 'Power Cord AMPS Master';
  powerCordAmps: PowerCordAmpsMaster;
  powerCordAmpsPageable: GenericPageable<PowerCordAmpsMaster>;
  powerCordAmpsDataSource = new MatTableDataSource<PowerCordAmpsMaster>();
  powerCordAmpsColumns = DisplayColumns.Cols.SensorControlTypes;

  dataSource = new MatTableDataSource<PowerCordAmpsMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  powerCordAmpsFilter: PowerCordAmpsFilter = new PowerCordAmpsFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldValue = Values.FilterFields.value;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getPowerCordAmpsMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new Power Cord Amps
  addPowerCordAmps() {
    this.editPowerCordAmps(new PowerCordAmpsMaster());
  }

  // used to add filter to Power Cord Amps listing
  async addFilter() {
    this.filter = this.powerCordAmpsFilter.value === '' ? [] : [{ key: this.filterFieldValue, value: this.powerCordAmpsFilter.value }];
    this.getPowerCordAmpsMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to Power Cord Amps listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldValue,
        value: fieldToClear === this.filterFieldValue ? (this.powerCordAmpsFilter.value = '') : this.powerCordAmpsFilter.value
      }
    ];
    this.getPowerCordAmpsMasterData(this.initialPageIndex, this.pageSize);
  }

  getPowerCordAmpsMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPowerCordAmpsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<PowerCordAmpsMaster>) => {
          this.powerCordAmpsPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPowerCordAmpsTable(this.powerCordAmpsPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createPowerCordAmpsTable(serviceRequestList: GenericPageable<PowerCordAmpsMaster>) {
    this.powerCordAmpsDataSource.data = serviceRequestList.content;
  }

  getPowerCordAmpsPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPowerCordAmpsMasterData(this.pageIndex, this.pageSize);
  }

  getPowerCordAmpsSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPowerCordAmpsMasterData(this.pageIndex, this.pageSize);
  }

  editPowerCordAmps(powerCordAmps: PowerCordAmpsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = powerCordAmps;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-power-cord-amps-master-model';
    const dialogRef = this.matDialog.open(ManagePowerCordAmpsComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          powerCordAmps.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPowerCordAmpsMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deletePowerCordAmps(powerCordAmpsId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePowerCordAmps(powerCordAmpsId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPowerCordAmpsMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
