<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addCurrencyMaterial()">Add New Currency</button>
    </div>

    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="currencyMaterialMasterDataSource"
        (matSortChange)="getCurrencyMaterialMasterSorting($event)"
      >
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.name }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="abbreviation">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Abbreviation </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.abbreviation }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="conversionRateWrtUSD">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Conversion Rate wrt USD </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.conversionRateWrtUSD }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="obsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.obsolete | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="symbol">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Symbol </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.symbol }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editCurrencyMaterial(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="currencyMaterialMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: currencyMaterialMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!currencyMaterialMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getCurrencyPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
