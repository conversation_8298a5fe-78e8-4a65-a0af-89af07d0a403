import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'sfl-non-standard-pn-confirmation-modal',
  templateUrl: './non-standard-pn-confirmation-modal.component.html'
})
export class NonStandardPnConfirmationModalComponent {
  constructor(private dialogRef: MatDialogRef<NonStandardPnConfirmationModalComponent>,
    @Inject(MAT_DIALOG_DATA) data) {
  }

  closeDialog(ans: boolean) {
    this.dialogRef.close(ans);
  }

}
