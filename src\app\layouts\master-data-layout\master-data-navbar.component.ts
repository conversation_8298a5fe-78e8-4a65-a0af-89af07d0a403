import { Component, OnInit } from '@angular/core';
import { MasterDataLayoutNavbarService } from './master-data-layout-navbar.service';
import { Router } from '@angular/router';

@Component({
  selector: 'sfl-master-data-navbar',
  templateUrl: './master-data-navbar.component.html'
})
export class MasterDataNavbarComponent {

  constructor(
    public masterDataNavbarService: MasterDataLayoutNavbarService,
    private router: Router) {
  }

  addMenuItem(): void {
    this.masterDataNavbarService.add({
        state: 'menu',
        name: 'MENU',
        type: 'sub',
        icon: 'trending_flat',
        children: [
            { state: 'menu', name: 'MENU' },
            { state: 'timeline', name: 'MENU' }
        ]
    });
  }

}
