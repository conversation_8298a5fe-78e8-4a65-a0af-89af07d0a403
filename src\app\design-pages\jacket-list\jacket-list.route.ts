import {Routes} from '@angular/router';
import {JacketListComponent} from './jacket-list.component';
import {PatternComponent} from './pattern/pattern.component';
import {Level1ReviewComponent} from './level1-review/level1-review.component';
import {FinalReviewComponent} from './final-review/final-review.component';
import {VietnamConversionComponent} from './vietnam-conversion/vietnam-conversion.component';
import {CoverPageComponent} from './cover-page/cover-page.component';
import {ElementBomComponent} from './element-bom/element-bom.component';
import {PartNumberComponent} from 'src/app/shared/component/Part Number/part-number.component';
import {PNErrorComponent} from 'src/app/shared/component/Part Number/partnumber-error.component';
import {SolidworksDownloadComponent} from 'src/app/shared/component/solidworks-download/solidworks-download.component';
import {NewRevisionComponent} from './new-revision/new-revision.component';
import {CostReportComponent} from './cost-report/cost-report.component';
import {GoldReportComponent} from './gold-report/gold-report.component';
import {TrackerFieldsEditorComponent} from './tracker-fields-editor/tracker-fields-editor.component';
import {LinkDialogComponent} from './link-dialog/link-dialog.component';
import {SimulationComponent} from './simulation/simulation.component';

export const JacketListRoutes: Routes = [
  {
    path: '',
    component: JacketListComponent
  },
  {
    path: 'pattern',
    component: PatternComponent
  },
  {
    path: 'level1-review',
    component: Level1ReviewComponent
  },
  {
    path: 'simulations',
    component: SimulationComponent
  },
  {
    path: 'final-review',
    component: FinalReviewComponent
  },
  {
    path: 'vietnam-conversion',
    component: VietnamConversionComponent
  },
  {
    path: 'cover-page',
    component: CoverPageComponent
  },
  {
    path: 'element-bom',
    component: ElementBomComponent
  },
  {
    path: 'part-number',
    component: PartNumberComponent
  },
  {
    path: 'pn-error',
    component: PNErrorComponent
  },
  {
    path: 'solidworks-download',
    component: SolidworksDownloadComponent
  },
  {
    path: 'new-revision',
    component: NewRevisionComponent
  },
  {
    path: 'cost-report',
    component: CostReportComponent
  },
  {
    path: 'links-dialog',
    component: LinkDialogComponent
  },
  {
    path: 'gold-report',
    component: GoldReportComponent
  },
  {
    path: 'tracker-fields-editor',
    component: TrackerFieldsEditorComponent
  }
];
