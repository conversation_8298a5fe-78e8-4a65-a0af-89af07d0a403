<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<mat-card class="cust_fields">
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxLayout="column" fxFlex.gt-lg="40" fxFlex.gt-md="55">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayout="column" fxFlex.gt-lg="49" class="pr-10" fxFlex.gt-md="59">
          <form name="element-selector" #elementSelector="ngForm">
            <div class="mb-10">
              <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <h4>Basic Info</h4>
                <mat-button-toggle-group (change)="changeElement($event.value)" name="elementNumButtonGroup">
                  <mat-button-toggle [value]="1">1</mat-button-toggle>
                  <mat-button-toggle [value]="2">2</mat-button-toggle>
                  <mat-button-toggle [value]="3">3</mat-button-toggle>
                </mat-button-toggle-group>
              </div>
            </div>
            <mat-form-field>
              <mat-select placeholder="Unit" [(ngModel)]="wireSelector.basicInfoDTO.units" name="unit" required>
                <mat-option value="IN">IN</mat-option>
                <mat-option value="MM">MM</mat-option>
              </mat-select>
            </mat-form-field>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                <input
                  matInput
                  type="number"
                  class="quantity"
                  placeholder="Volts"
                  sflIsDecimal
                  [(ngModel)]="wireSelector.basicInfoDTO.volts"
                  name="volts"
                  (change)="getAmps('volts')"
                  required
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                <mat-select
                  placeholder="Phase"
                  [(ngModel)]="wireSelector.basicInfoDTO.phase"
                  name="phase"
                  (selectionChange)="getAmps()"
                  required
                >
                  <mat-option *ngFor="let data of phaseTypes" [value]="data.value">{{ data?.data }}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32">
                <input
                  matInput
                  placeholder="Watts"
                  type="number"
                  class="quantity"
                  sflIsDecimal
                  [(ngModel)]="wireSelector.basicInfoDTO.watts"
                  name="watts"
                  (change)="getAmps('watts')"
                  required
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32">
                <input
                  matInput
                  placeholder="Amps"
                  type="number"
                  class="quantity"
                  sflIsDecimal
                  name="amps"
                  [(ngModel)]="wireSelector.basicInfoDTO.amps"
                  required
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32">
                <input
                  matInput
                  type="number"
                  placeholder="Op. Temp ({{ tempUnit ? tempUnit : '' }})"
                  class="quantity"
                  sflIsDecimal
                  [(ngModel)]="wireSelector.basicInfoDTO.operatingTemp"
                  name="opTemp"
                  required
                />
              </mat-form-field>
              <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <mat-checkbox
                    name="tajima"
                    [(ngModel)]="wireSelector.tajima"
                    name="tajima"
                    color="warn"
                    (change)="tajimaWireElementTypeUpdated()"
                    >Tajima Wire
                  </mat-checkbox>
                </div>
                <div fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <mat-checkbox color="warn" [(ngModel)]="wireSelector.goldStandard" name="goldStandard">Gold Standard </mat-checkbox>
                </div>
                <div class="mt-10" fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <mat-checkbox color="warn" [(ngModel)]="wireSelector.inVietnam" name="inVietnam">In stock in Vietnam </mat-checkbox>
                </div>
                <div class="mt-10" fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <mat-checkbox color="warn" [(ngModel)]="wireSelector.inCostaRica" name="inCostaRica">In stock in Costa Rica </mat-checkbox>
                </div>
                <div class="mt-10" fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <mat-checkbox color="warn" [(ngModel)]="wireSelector.inEpicor" name="inEpicor">In Epicor</mat-checkbox>
                </div>
                <mat-form-field class="mt-10" fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <mat-select [(ngModel)]="wireSelector.elementInfoDTO.standard" name="standard" required>
                    <mat-option value="std">Standard (+/- 8%)</mat-option>
                    <mat-option value="ser">Series (+/-5%)</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            <br />
            <div class="mb-10">
              <h4>Element Info</h4>
            </div>

            <mat-form-field>
              <mat-select
                placeholder="Element Type"
                [(ngModel)]="wireSelector.elementInfoDTO.elementType"
                (ngModelChange)="emptyFields()"
                name="elementType"
                required
              >
                <mat-option *ngFor="let data of elementsType" [value]="data.value">{{ data?.value }}</mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field>
              <mat-select
                placeholder="Wire Type"
                [(ngModel)]="wireSelector.elementInfoDTO.wireType"
                name="wireType"
                required
                (ngModelChange)="wireTypeUpdated()"
              >
                <mat-option *ngFor="let data of wireType" [value]="data.id">{{ data?.value }}</mat-option>
              </mat-select>
            </mat-form-field>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32">
                <input
                  matInput
                  type="number"
                  placeholder="# of Tapes"
                  [(ngModel)]="wireSelector.elementInfoDTO.noOfTapes"
                  class="quantity"
                  sflIsNumber
                  name="nooftapes"
                  required
                  (change)="noOfTapesUpdated()"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32">
                <input
                  matInput
                  type="number"
                  placeholder="Total length"
                  [(ngModel)]="wireSelector.elementInfoDTO.totalLength"
                  class="quantity"
                  sflIsDecimal
                  name="totallength"
                  required
                  (change)="elementTotalLengthUpdated()"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32">
                <input
                  matInput
                  type="number"
                  placeholder="Tape Width"
                  [(ngModel)]="wireSelector.elementInfoDTO.tapeWidth"
                  class="quantity"
                  sflIsDecimal
                  name="tapewidth"
                  [disabled]="wireSelector.elementInfoDTO.elementType === 'Wire'"
                  [required]="wireSelector.elementInfoDTO.elementType !== 'Wire'"
                  (change)="tapeWidthUpdated()"
                />
              </mat-form-field>
            </div>
            <div>
              <button
                mat-raised-button
                color="warn"
                type="submit"
                class="sfl-mr-2"
                [disabled]="elementSelector.form.invalid"
                (click)="searchWire()"
                id="searchparemeter"
              >
                Search Parameters
              </button>

              <button
                mat-raised-button
                color="warn"
                id="changeparameter"
                type="submit"
                [disabled]="elementSelector.form.invalid"
                (click)="openSearchParameters()"
              >
                Change Parameters
              </button>
            </div>
          </form>
        </div>

        <div fxLayout="column" fxFlex.gt-lg="49" fxFlex.gt-md="41" class="highlight">
          <h4>Element Choices</h4>
          <br />
          <div class="search-result">
            <div class="border-bottom mb-20" *ngFor="let data of wireSelectorResult; let i = index">
              <div class="p-1">
                <label
                  class="font-md open-doc"
                  [ngClass]="{ 'font-bold': elementChoiceSelected[i] }"
                  (click)="getReliabilityTargetingWireInformation(i)"
                >
                  {{ data?.elementChoiceDTO?.searchParameterValue }}
                  {{ data?.elementChoiceDTO?.searchParameterName }} of
                  {{ data?.elementChoiceDTO?.alloyName }}
                  ({{ data?.targetingResultDTO?.errorPercentage }}%)
                  <span class="sfl-vn-stock" *ngIf="data?.wireInformationDTO">
                    {{ data?.wireInformationDTO?.vietnamStock ? 'Wire Stock(VN) -' + data?.wireInformationDTO?.vietnamStock : '' }}
                    {{ data?.tapeInformation?.vietnamStock ? ' Tape Stock(VN) -' + data?.tapeInformation?.vietnamStock : '' }}
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <br />
    <div fxLayout="column" fxFlex.gt-lg="59" fxFlex.gt-md="43">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayout="column" fxFlex="100">
          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <div fxLayout="column">
                <div class="mb-10">
                  <h4>Targeting</h4>
                </div>
                <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <input matInput placeholder="Wattage" [(ngModel)]="targetingResult.targetingWattage" name="targetingWattage" />
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <input matInput placeholder="Error" [(ngModel)]="targetingResult.errorPercentage" name="errorPercentage" />
                </mat-form-field>
              </div>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <div fxLayout="column">
                <div class="mb-10">
                  <h4>Reliablity</h4>
                </div>
                <div fxLayout="row wrap" fxLayoutAlign="space-between">
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" [ngClass]="reliability.elementGrade < 300 ? 'highlight-red' : ''">
                    <input matInput placeholder="Element Grade" [(ngModel)]="reliability.elementGrade" name="elementGrade" />
                  </mat-form-field>
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" [ngClass]="reliability.elementFactor > 10 ? 'highlight-red' : ''">
                    <input matInput placeholder="Element Factor" [(ngModel)]="reliability.elementFactor" name="elementFactor" />
                  </mat-form-field>
                </div>
                <div fxLayout="row wrap" fxLayoutAlign="space-between">
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input matInput placeholder="W / in Wire" [(ngModel)]="reliability.wperInWire" name="wperInWire" />
                  </mat-form-field>
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input matInput placeholder="W / in2" [(ngModel)]="reliability.wperInSquareTape" name="wperInSquareTape" />
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>
          <br />
          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" *ngIf="tapeInformation && tapeInformation.picks != 0">
              <div fxLayout="column">
                <div class="mb-10">
                  <h4>Tape Info</h4>
                </div>
                <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                  <input matInput placeholder="Picks" [(ngModel)]="tapeInformation.picks" name="picks" [disabled]="true" />
                </mat-form-field>
                <div fxLayout="row wrap" fxLayoutAlign="space-between">
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input
                      matInput
                      placeholder="Part Numbers"
                      [(ngModel)]="tapeInformation.partNumber"
                      name="partNumber"
                      (focus)="setOldTapePartNumber()"
                      [disabled]="isTapePartNumberAvailable"
                    />
                  </mat-form-field>
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input
                      matInput
                      placeholder="Ohms / Ft"
                      [(ngModel)]="tapeInformation.ohmsPerFeet"
                      name="ohmsPerFeet"
                      [disabled]="true"
                    />
                  </mat-form-field>
                </div>
              </div>
            </div>

            <div fxFlex.gt-lg="49" fxFlex.gt-md="49">
              <div fxLayout="column">
                <div class="mb-10">
                  <h4>Wire Info</h4>
                </div>
                <div fxLayout="row wrap" fxLayoutAlign="space-between">
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input matInput placeholder="Alloy" [(ngModel)]="wireInformation.alloyName" name="alloyName" />
                  </mat-form-field>
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input matInput placeholder="Part Number" [(ngModel)]="wireInformation.partNumber" name="partNumber" />
                  </mat-form-field>
                </div>
                <div fxLayout="row wrap" fxLayoutAlign="space-between">
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input matInput placeholder="Ohms / Ft" [(ngModel)]="wireInformation.ohmsPerFoot" name="ohmsPerFoot" />
                  </mat-form-field>
                  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
                    <input matInput placeholder="Total Length" [(ngModel)]="wireInformation.length" name="length" />
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>
          <br />

          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="24" fxFlex.gt-md="23" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <mat-form-field>
                <input matInput placeholder="TGT OHMS" [(ngModel)]="elementObject.tgtOhms" name="tgtOhms" [disabled]="true" />
              </mat-form-field>
            </div>
            <div fxFlex.gt-lg="24" fxFlex.gt-md="23" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <mat-form-field>
                <input matInput placeholder="MIN OHMS" [(ngModel)]="elementObject.minOhms" name="minOhms" [disabled]="true" />
              </mat-form-field>
            </div>
            <div fxFlex.gt-lg="24" fxFlex.gt-md="23" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <mat-form-field>
                <input matInput placeholder="MAX OHMS" [(ngModel)]="elementObject.maxOhms" name="maxOhms" [disabled]="true" />
              </mat-form-field>
            </div>
            <div class="d-flex flex-column" fxFlex.gt-lg="24" fxFlex.gt-md="23" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <mat-checkbox color="warn" [(ngModel)]="wireInformation.inEpiCore" name="inEpiCore">In Epicor </mat-checkbox>
              <mat-checkbox color="warn" [(ngModel)]="wireInformation.inActive" name="inActive" [disabled]="true">Inactive </mat-checkbox>
              <mat-checkbox color="warn" [(ngModel)]="wireInformation.onHold" name="onHold" [disabled]="true">Hold </mat-checkbox>
            </div>
          </div>

          <div fxLayout="row wrap" fxLayoutAlign="start center" fxLayoutGap="20px">
            <button
              mat-raised-button
              color="warn"
              [disabled]="
                !(wireSelector.elementInfoDTO.elementType && wireSelector.elementInfoDTO.wireType) || loading || !allowSelectElement
              "
              (click)="saveElement()"
              id="selectelement"
            >
              Select this Element
            </button>
            <button
              mat-raised-button
              color="warn"
              [disabled]="
                !elementObject.id ||
                !(wireSelector.elementInfoDTO.elementType && wireSelector.elementInfoDTO.wireType) ||
                loading ||
                !allowSelectElement
              "
              (click)="clearElement()"
              id="clearElement"
            >
              Clear Element
            </button>
            <button
              mat-raised-button
              color="warn"
              *ngIf="syncRes!==null && (syncRes==='IN_PROGRESS' || isSyncSuccess===false)"
              (click)="retrySync()"
              id="retrySync"
            >
              Resync
            </button>
            <div class="open-doc mt-5" *ngIf="syncStatus !== null">
              <p
                class="bom-sync-status"
                [ngClass]="{ 'sync-error': !isSyncSuccess }"
                matTooltip="Click here to check status"
                (click)="openDMTLog()"
              >
                {{ syncStatus }}
              </p>
            </div>
            <div class="loading-icon">
              <img *ngIf="loading" src="../../../../assets/images/loader.gif" alt="loader" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-card>
