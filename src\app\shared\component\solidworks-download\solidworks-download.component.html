<h2 mat-dialog-title fxFlex>
  Download Solidworks Config file.
  <hr />
</h2>
<mat-dialog-content>
  <mat-tab-group>
    <mat-card-subtitle
      >Download and install this SFL SolidWorks Api Service Installer V_.msi to access solidworks releted features.
    </mat-card-subtitle>
    <mat-tab label="USA">
      <div class="mt-10"></div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="99" fxFlex.gt-md="99">
          <strong>Step to download:</strong>
          <p class="read-text">
            Click on below button to download <strong>Setup file</strong>. Download and install
            <strong>SFL SolidWorks Api Service Installer V_.msi</strong>
            file to install Solidwork service.
          </p>
        </div>
      </div>
      <div></div>
      <div fxLayout="row wrap" fxLayoutAlign="center">
        <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxLayoutAlign="center">
          <a
            mat-raised-button
            color="warn"
            type="submit"
            href="../../../assets/solidwork-file/SFL SolidWorks Api Service Installer v1.3.0.msi"
            >Solidworks Service</a
          >
        </div>
      </div>
    </mat-tab>
    <mat-tab label="Vietnam">
      <div class="mt-10"></div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="99" fxFlex.gt-md="99">
          <strong>Step to download:</strong>
          <p class="read-text">
            Click on below button to download <strong>Setup file</strong>. Download and install
            <strong>SFL SolidWorks Api Service Installer V_.msi</strong>
            file to install Solidworks service.
          </p>
        </div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="center">
        <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxLayoutAlign="center">
          <a
            mat-raised-button
            color="warn"
            type="submit"
            href="../../../assets/solidwork-file/SFL SolidWorks Api Service Installer v1.3.0.msi"
            >Solidworks Service</a
          >
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</mat-dialog-content>
<br />
<hr />
<mat-dialog-actions fxLayoutAlign="end">
  <button mat-raised-button type="submit" (click)="closeDialog()">Close</button>
</mat-dialog-actions>
