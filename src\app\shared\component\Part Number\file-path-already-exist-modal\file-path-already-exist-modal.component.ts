import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'app-file-path-already-exist-modal',
  templateUrl: './file-path-already-exist-modal.component.html',
  styleUrls: ['./file-path-already-exist-modal.component.css']
})
export class FilePathAlreadyExistModalComponent implements OnInit {
  message: string;
  constructor(
    public dialogRef: MatDialogRef<FilePathAlreadyExistModalComponent>,
    @Inject(MAT_DIALOG_DATA) data,
  ) {
    this.message = data;
  }
  ngOnInit() {
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

}
