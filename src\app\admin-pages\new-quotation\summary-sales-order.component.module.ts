import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DragulaModule, DragulaService } from 'ng2-dragula';
import { SharedModule } from '../../shared/shared.module';
import { AccessoriesComponent } from '../accessories/accessories.component';
import { AccessoriesService } from '../accessories/accessories.service';
import { AddAccessoriesComponent } from '../accessories/Add Accessories/add-accessories.component';
import { PowerCordComponent } from '../accessories/Power-Cord/power-cord.component';
import { AddLeadtimeComponent } from '../finalize/add-leadtime/add-leadtime.component';
import { AdditonalAddedValueModalComponent } from '../finalize/additonal-added-value-modal/additonal-added-value-modal.component';
import { FinalizeComponent } from '../finalize/finalize.component';
import { FinalizeService } from '../finalize/finalize.service';
import { ManufacturedInModalComponent } from '../finalize/manufactured-in-modal/manufactured-in-modal.component';
import { MassUpdateJacketModalComponent } from '../finalize/mass-update-jacket-modal/mass-update-jacket-modal.component';
import { AddApplicationComponent } from './Add Applicaton/add-application.component';
import { UpdatePluggingInfoWarningComponent } from './Add Applicaton/update-plugging-info-warning/update-plugging-info-warning.component';
import { AddClosureComponent } from './Add Closure/add-closure.component';
import { AddMaterialComponent } from './Add Material/add-material.component';
import { OtherMaterialComponent } from './Add Material/Other/other-material.component';
import { AddNoteComponent } from './Add Note/add-note.component';
import { AddPluginComponent } from './Add Plugin/add-plugin.component';
import { AddSensorsComponent } from './Add Sensors/add-sensors.component';
import { SensorService } from './Add Sensors/add-sensors.service';
import { AddThermostatsComponent } from './Add Thermostats/add-thermostats.component';
import { AddOthrerThermostatsComponent } from './Add Thermostats/other-termostat/add-other-termostat.component';
import { AddWorkflowComponent } from './Add Workflow/add-workflow.component';
import { AddEntryMethodComponent } from './add-entry-method/add-entry-method.component';
import { FindCustomerComponent } from './Find Customer/find-customer.component';
import { ManageJacketComponent } from './manage-jacketgroups/manage-jacket-groups.component';
import { ManageRevisionsComponent } from './manage-revisions/manage-revisions.component';
import { ManageUnitsComponent } from './manage-units/manage-units.component';
import { ManageUnitsService } from './manage-units/manage-units.service';
import { SummarySalesOrderComponent } from './summary-sales-order.component';
import { SummarySalesOrderRoutes } from './summary-sales-order.route';
import { ImportCcdcModalComponent } from './import-ccdc-modal/import-ccdc-modal.component';
import { ExportCcdcModalComponent } from './export-ccdc-modal/export-ccdc-modal.component';

@NgModule({
  imports: [RouterModule.forChild(SummarySalesOrderRoutes), SharedModule, DragulaModule],
  declarations: [
    SummarySalesOrderComponent,
    AddApplicationComponent,
    FindCustomerComponent,
    ManageJacketComponent,
    ManageRevisionsComponent,
    AddMaterialComponent,
    AddPluginComponent,
    AddSensorsComponent,
    AddNoteComponent,
    AddWorkflowComponent,
    AddAccessoriesComponent,
    AddThermostatsComponent,
    AddClosureComponent,
    AccessoriesComponent,
    FinalizeComponent,
    PowerCordComponent,
    ManageUnitsComponent,
    AddOthrerThermostatsComponent,
    OtherMaterialComponent,
    AddLeadtimeComponent,
    AddEntryMethodComponent,
    MassUpdateJacketModalComponent,
    AdditonalAddedValueModalComponent,
    UpdatePluggingInfoWarningComponent,
    ManufacturedInModalComponent,
    ImportCcdcModalComponent,
    ExportCcdcModalComponent
  ],
  entryComponents: [
    AddEntryMethodComponent,
    MassUpdateJacketModalComponent,
    AdditonalAddedValueModalComponent,
    UpdatePluggingInfoWarningComponent,
    ManufacturedInModalComponent,
    ImportCcdcModalComponent,
    ExportCcdcModalComponent
  ],
  providers: [DragulaService, AccessoriesService, FinalizeService, SensorService, ManageUnitsService],
  exports: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SummarySalesOrderModule { }
