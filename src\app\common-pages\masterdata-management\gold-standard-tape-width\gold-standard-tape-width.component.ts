import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { GoldStandardTapeWidthMaster, GenericPageable, GoldStandardTapeWidthFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { ManageGoldStandardTapeWidthComponent } from './manage-gold-standard-tape-width/manage-gold-standard-tape-width.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-gold-standard-tape-width',
  templateUrl: './gold-standard-tape-width.component.html'
})
export class GoldStandardTapeWidthComponent implements OnInit, OnDestroy {
  pageTitle = 'Gold Standard Tape Width';
  goldStandardTapeWidth: GoldStandardTapeWidthMaster;
  goldStandardTapeWidthPageable: GenericPageable<GoldStandardTapeWidthMaster>;
  goldStandardTapeWidthDataSource = new MatTableDataSource<GoldStandardTapeWidthMaster>();
  goldStandardTapeWidthColumns = DisplayColumns.Cols.GoldStandardTapeWidthMaster;
  goldStandardTapeWidthFilter: GoldStandardTapeWidthFilter = new GoldStandardTapeWidthFilter();

  dataSource = new MatTableDataSource<GoldStandardTapeWidthMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  length: number;

  showLoader = false;
  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldTapeType = Values.FilterFields.tapeType;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getGoldStandardTapeWidth(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new gold standard tape width
  addGoldStandardTapeWidth() {
    this.editGoldStandardTapeWidth(new GoldStandardTapeWidthMaster());
  }
  // used to add filter to gold standard tape width listing
  async addFilter() {
    this.filter =
      this.goldStandardTapeWidthFilter.tapeType === ''
        ? []
        : [{ key: this.filterFieldTapeType, value: this.goldStandardTapeWidthFilter.tapeType }];
    this.getGoldStandardTapeWidth(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to gold standard tape width listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldTapeType,
        value:
          fieldToClear === this.filterFieldTapeType
            ? (this.goldStandardTapeWidthFilter.tapeType = '')
            : this.goldStandardTapeWidthFilter.tapeType
      }
    ];
    this.getGoldStandardTapeWidth(this.initialPageIndex, this.pageSize);
  }

  getGoldStandardTapeWidth(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getGoldStandardTapeWidthMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<GoldStandardTapeWidthMaster>) => {
          this.goldStandardTapeWidthPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createGoldStandardTapeWidthTable(this.goldStandardTapeWidthPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.goldStandardTapeWidthDataSource.data = [];
        }
      )
    );
  }

  createGoldStandardTapeWidthTable(serviceRequestList: GenericPageable<GoldStandardTapeWidthMaster>) {
    this.goldStandardTapeWidthDataSource.data = serviceRequestList.content;
  }

  getGoldStandardTapeWidthPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getGoldStandardTapeWidth(this.pageIndex, this.pageSize);
  }

  getGoldStandardTapeWidthSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getGoldStandardTapeWidth(this.pageIndex, this.pageSize);
  }

  editGoldStandardTapeWidth(goldStandardTapeWidth: GoldStandardTapeWidthMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = goldStandardTapeWidth;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-gold-standard-tape-width-model';
    const dialogRef = this.matDialog.open(ManageGoldStandardTapeWidthComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          goldStandardTapeWidth.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getGoldStandardTapeWidth(this.pageIndex, this.pageSize);
      }
    });
  }

  async deleteGoldStandardTapeWidth(goldStandardTapeWidthId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteGoldStandardTapeWidth(goldStandardTapeWidthId).subscribe(
        res => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getGoldStandardTapeWidth(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
