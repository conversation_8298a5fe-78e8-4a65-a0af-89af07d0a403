import { Injectable } from '@angular/core';
import { NavigationEnd, Route, Router } from '@angular/router';
import { Role } from '../constants/user-roles.constants';
import { SharedService } from './shared.service';

@Injectable()
export class AuthGuardService {
  constructor(private sharedService: SharedService, private router: Router) { }
  canActivate(route: Route): boolean {
    if (this.sharedService.isLoggedIn()) {
      const currentUserRole = this.sharedService.getRole();
      if (route.data && route.data.roles && route.data.roles.indexOf(currentUserRole) === -1) {
        this.router.navigate(['/unauthorized']);
        return false;
      }
      this.router.events.subscribe(event => {
        if (event instanceof NavigationEnd) {
          if (event.url === '/' || event.url === '/login') {
            if (this.sharedService.getRole() === Role.ADMIN_ROLE) {
              this.router.navigate(['quote-tracker/dashboard']);
            } else if (this.sharedService.getRole() === Role.ENG_ROLE) {
              this.router.navigate(['app-eng/dashboard']);
            } else if (this.sharedService.getRole() === Role.DESIGN_ROLE) {
              this.router.navigate(['design-eng/dashboard']);
            } else if (this.sharedService.getRole() === Role.SALES_ROLE) {
              this.router.navigate(['sales/dashboard']);
            }
          }
        }
      });
      return true;
    } else {
      this.sharedService.logout(route.path);
      return false;
    }
  }
}

@Injectable()
export class NonAuthGuardService {
  constructor(private readonly sharedService: SharedService, private readonly router: Router) { }

  canActivate(): boolean {
    if (this.sharedService.isLoggedIn() === false) {
      return true;
    }
    if (this.sharedService.getRole() === Role.ADMIN_ROLE) {
      this.router.navigate(['quote-tracker/dashboard']);
    } else if (this.sharedService.getRole() === Role.ENG_ROLE) {
      this.router.navigate(['app-eng/dashboard']);
    } else if (this.sharedService.getRole() === Role.DESIGN_ROLE) {
      this.router.navigate(['design-eng/dashboard']);
    } else if (this.sharedService.getRole() === Role.SALES_ROLE) {
      this.router.navigate(['sales/dashboard']);
    }
    return false;
  }
}
