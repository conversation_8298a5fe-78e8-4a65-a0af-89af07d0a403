import { Location } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { AddApplicationService } from 'src/app/admin-pages/new-quotation/Add Applicaton/add-application.service';
import { AddClosureService } from 'src/app/admin-pages/new-quotation/Add Closure/add-closure.service';
import {
  ApplicationInfo,
  CcdcWorkflow,
  ClosureInformation,
  ClosureMaterial,
  CustomerDTO,
  LeadPlug,
  MaterialInfoReq,
  MaterialLayers,
  Notes,
  Plug,
  PluggingInformation,
  Quotation,
  SensorInformationObject,
  ThermostatInfo,
  Units,
  ViewCCDCJacketGroup
} from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { JacketGroupService } from 'src/app/admin-pages/new-quotation/manage-jacketgroups/manage-jacket-groups.service';
import { JacketGroup } from 'src/app/shared/component/geometry/geometry.model';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { SharedService } from 'src/app/shared/service/shared.service';
import { SensorService } from '../../admin-pages/new-quotation/Add Sensors/add-sensors.service';
import { ManageUnitsService } from '../../admin-pages/new-quotation/manage-units/manage-units.service';
import { SalesOrderSummaryService } from '../../admin-pages/new-quotation/summary-sales-order.service';
import { SelectJacketGroupsComponent } from './select-jacket-groups/select-jacket-groups.component';

@Component({
  selector: 'sfl-view-ccdc',
  templateUrl: './view-ccdc.component.html'
})
export class ViewccdcComponent implements OnInit, OnDestroy {
  quotID: number;
  jacketGroupId: number;
  tempUnit = '';
  measurementUnit = '';
  quotation: Quotation;
  material: MaterialLayers[];
  materials: ClosureMaterial[];
  subscription = new Subscription();
  jacketTypes = Values.JacketTypeConst;
  phaseTypes = Values.PhaseTypeConst;
  contentMotions = Values.ContentMotionsConst;
  productTypes = Values.ProductTypeConst;
  jacketGroup = new JacketGroup();
  jacketGroups: JacketGroup[] = [];
  availableJacketGroups: JacketGroup[] = [];
  revisionId: number;
  plugs: Plug[] = [];
  jacketGroupIds = [];
  viewCCDCJacketGroups: ViewCCDCJacketGroup[] = [];

  constructor(
    private readonly salesOrderSummaryService: SalesOrderSummaryService,
    private readonly addApplicationService: AddApplicationService,
    private readonly sharedService: SharedService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly addClosureService: AddClosureService,
    private readonly sensorService: SensorService,
    private readonly location: Location,
    private readonly titleService: Title,
    private readonly jacketGroupService: JacketGroupService,
    private manageUnitService: ManageUnitsService,
    private readonly matDialog: MatDialog,
    private summarySalesOrderService: SalesOrderSummaryService
  ) {
  }

  ngOnInit() {
    this.titleService.setTitle('Print CCDC - App Eng');
    this.activatedRoute.queryParams.subscribe(params => {
      this.quotID = params['quotId'];
      this.revisionId = params['revisionId'];
    });
    this.quotation = new Quotation();
    this.quotation.customerDTO = new CustomerDTO();
    this.getMeasurementUnit();
    this.getClosureMaterialList();
    this.getJacketGroups(this.revisionId);
  }


  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnitByQuotationId(this.quotID).subscribe(
        (res: Units) => {
          if (res) {
            this.measurementUnit = res.measurementUnit;
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  // gets the jacket groups based on revision number
  getJacketGroups(revisionId: number): void {
    this.jacketGroupService.getJacketGroupsByRevisionId(revisionId).subscribe((retrievedJacketGroups: JacketGroup[]) => {
      if (retrievedJacketGroups.length) {
        this.availableJacketGroups = retrievedJacketGroups;
        this.selectJacketGroupToPrint();
      }
    });
  }

  // prompts user to select jacket groups for printing
  selectJacketGroupToPrint() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {
      jacketGroups: this.availableJacketGroups,
      revisionId: this.revisionId
    };
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(SelectJacketGroupsComponent, matDataConfig)
      .afterClosed()
      .subscribe((selectedJacketGroupsForPrinting: number[]) => {
        if (selectedJacketGroupsForPrinting.length) {
          this.jacketGroupIds = selectedJacketGroupsForPrinting.sort();
          this.getAllData();
        } else {
          window.close();
        }
      });
  }

  // gets all the quoatation details
  getQuotDetails() {
    this.subscription.add(
      this.salesOrderSummaryService.getQUotById(this.quotID).subscribe((res: Quotation) => {
        this.quotation = res;
      })
    );
  }

  getJacketGroup(): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.jacketGroupIds.forEach(data => {
        this.jacketGroupService.getJacketGroupById(data).subscribe({
          next: async (res: JacketGroup) => {
            let viewCCDCJacketGroup = new ViewCCDCJacketGroup();
            viewCCDCJacketGroup.id = res.id;
            viewCCDCJacketGroup.name = res.name;
            this.viewCCDCJacketGroups.push(viewCCDCJacketGroup);
            await this.getWorkFlowByJacketGroupId(viewCCDCJacketGroup);
            await this.getApplicationInfo(viewCCDCJacketGroup);
            await this.getPluggingInformationByJacketGroupId(viewCCDCJacketGroup);
            await this.getMaterialByJacketGroupId(viewCCDCJacketGroup);
            await this.getClosureInfo(viewCCDCJacketGroup);
            await this.getSensorsListByJacketGroupId(viewCCDCJacketGroup);
            await this.getThermostatByJacketGroupId(viewCCDCJacketGroup);
            await this.getNotesByJacketGroupId(viewCCDCJacketGroup);
            resolve(); // Resolve the promise after data is set
          },
          error: (err) => {
            resolve(); // Reject the promise if there's an error
          }
        });
      });
    });
  }


  // gets the work flow details
  getWorkFlowByJacketGroupId(viewCCDCJacketGroup): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.salesOrderSummaryService.getWorkFlowByJacketGroupId(viewCCDCJacketGroup.id).subscribe({
          next: (res: CcdcWorkflow) => {
            if (res != null) {
              viewCCDCJacketGroup.ccdcWorkflow = res;
            }
            resolve(); // Resolve after the data is pushed
          },
          error: (err) => {
            resolve(); // Resolve after the data is pushed
          }
        })
      );
    });
  }


  // gets the application info details
  getApplicationInfo(viewCCDCJacketGroup: ViewCCDCJacketGroup): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.addApplicationService.getApplicationInfoByJacketGroup(viewCCDCJacketGroup.id).subscribe({
          next: (res: ApplicationInfo) => {
            if (res) {
              let appInfo: ApplicationInfo = res;

              if (appInfo.contentMotion) {
                appInfo.contentMotion = this.contentMotions.find(e => e.id === appInfo.contentMotion).value;
              }

              if (appInfo.jacketType) {
                appInfo.jacketType = this.jacketTypes.find(e => e.id === appInfo.jacketType).value;
              }

              if (appInfo.phase) {
                appInfo.phase = this.phaseTypes.find(e => e.id === appInfo.phase).value;
              }

              viewCCDCJacketGroup.appInfo = appInfo;

              // Handle the nested subscription for getting plugs
              this.summarySalesOrderService.getPlugsByJacketType(appInfo.jacketType).subscribe({
                next: (success: Plug[]) => {
                  this.plugs = success;
                  resolve(); // Resolve only after plugs are successfully fetched
                },
                error: (err) => {
                  resolve(); // Reject in case of an error in the plugs API call
                }
              });
            } else {
              resolve(); // Resolve if no result is returned
            }
          },
          error: (err) => {
            resolve(); // Reject in case of an error in the main API call
          }
        })
      );
    });
  }


  // gets the plugging info details
  getPluggingInformationByJacketGroupId(viewCCDCJacketGroup: ViewCCDCJacketGroup): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.salesOrderSummaryService.getPluggingInformationByJacketGroupId(viewCCDCJacketGroup.id).subscribe({
          next: (res: PluggingInformation) => {
            viewCCDCJacketGroup.pluggingInformation = res;
            if (viewCCDCJacketGroup.pluggingInformation != null && viewCCDCJacketGroup.pluggingInformation.leadPlugDTO !== undefined && viewCCDCJacketGroup.pluggingInformation.leadPlugDTO !== null) {
              let leadPlug: LeadPlug = this.plugs.filter(plug => plug.id === viewCCDCJacketGroup.pluggingInformation.leadPlugDTO.plugId)[0];
              if (leadPlug) {
                viewCCDCJacketGroup.pluggingInformation.leadPlugDTO.partNumber = leadPlug.partNumber;
              }
            }
            resolve(); // Resolve when processing is done
          },
          error: (err) => {
            resolve(); // Reject in case of an error
          }
        })
      );
    });
  }

  // gets the material info details
  getMaterialByJacketGroupId(viewCCDCJacketGroup: ViewCCDCJacketGroup): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.salesOrderSummaryService.getAllMaterial(viewCCDCJacketGroup.id).subscribe({
          next: (res: MaterialInfoReq) => {
            viewCCDCJacketGroup.materialDatas = res;
            resolve(); // Resolve after data is pushed into the materialDatas array
          },
          error: (err) => {
            resolve(); // Reject in case of an error
          }
        })
      );
    });
  }


  // gets the closure material details
  getClosureMaterialList(): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.addClosureService.getClosureMaterialList().subscribe({
          next: (res: ClosureMaterial[]) => {
            this.materials = res;
            resolve();
          },
          error: (err) => {
            resolve(); // Reject in case of an error in the main API call
          }
        })
      );
    });
  }


  // gets the closure info details
  getClosureInfo(viewCCDCJacketGroup: ViewCCDCJacketGroup): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.addClosureService.getClosureInfoByJacketGroup(viewCCDCJacketGroup.id).subscribe({
          next: (res: ClosureInformation) => {
            viewCCDCJacketGroup.closureInfo = res;
            if (this.materials.length > 0) {
              if (viewCCDCJacketGroup.closureInfo != null && !viewCCDCJacketGroup.closureInfo !== undefined && viewCCDCJacketGroup.closureInfo.closureMaterialId) {
                let closureMaterial: ClosureMaterial = this.materials.find(x => x.id === viewCCDCJacketGroup.closureInfo.closureMaterialId);
                if (closureMaterial) {
                  viewCCDCJacketGroup.closureMaterial = closureMaterial;
                }
              }
            }
            resolve(); // Resolve when all data processing is done
          },
          error: (err) => {
            resolve(); // Reject in case of an error
          }
        })
      );
    });
  }


  // gets the sensor details
  getSensorsListByJacketGroupId(viewCCDCJacketGroup: ViewCCDCJacketGroup): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.sensorService.getSensorList(viewCCDCJacketGroup.id).subscribe({
          next: (res: SensorInformationObject) => {
            if (res) {
              viewCCDCJacketGroup.sensorInfoObjects = res;
            }
            resolve(); // Resolve after processing the data
          },
          error: (err) => {
            resolve(); // Reject in case of an error
          }
        })
      );
    });
  }


  getNotesByJacketGroupId(viewCCDCJacketGroup: ViewCCDCJacketGroup): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.salesOrderSummaryService.getNotesByJacketGroupId(viewCCDCJacketGroup.id).subscribe({
          next: (res: Notes) => {
            viewCCDCJacketGroup.notes = res;
            resolve(); // Resolve after the data is processed
          },
          error: (err) => {
            resolve(); // Reject in case of an error
          }
        })
      );
    });
  }


  // gets the thermostat details
  getThermostatByJacketGroupId(viewCCDCJacketGroup: ViewCCDCJacketGroup): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.subscription.add(
        this.salesOrderSummaryService.getThermostatByJacketGroupId(viewCCDCJacketGroup.id).subscribe({
          next: (res: ThermostatInfo) => {
            viewCCDCJacketGroup.thermostatInfos = res;
            resolve(); // Resolve after data is added
          },
          error: (err) => {
            resolve(); // Reject in case of an error
          }
        })
      );
    });
  }


  async getAllData() {
    this.getQuotDetails(); // Ensure this method returns a Promise if it is asynchronous
    await Promise.resolve(this.getJacketGroup());
    this.viewCCDCJacketGroups.sort((a, b) => a.id - b.id);
    setTimeout(() => {
      this.print();
    }, 5000); // B will print after 1 second, asynchronously.
  }

  // used to gets the HTML elements using it's id and prints
  print() {
    let printableCCDC = '';
    for (let index = 0; index < this.viewCCDCJacketGroups.length; index++) {
      const custmerinfo = document.getElementById('custmerinfo' + index).innerHTML || '';
      const workflowinfo = document.getElementById('workflowinfo' + index).innerHTML || '';
      const appinfo = document.getElementById('appinfo' + index).innerHTML || '';
      const plugginginfo = document.getElementById('plugginginfo' + index).innerHTML || '';
      const materialinfo = document.getElementById('materialinfo' + index).innerHTML || '';
      const closureinfo = document.getElementById('closureinfo' + index).innerHTML || '';
      const sensorsinfo = document.getElementById('sensorsinfo' + index).innerHTML || '';
      const thermostatsinfo = document.getElementById('thermostatsinfo' + index).innerHTML || '';
      const note = document.getElementById('note' + index).innerHTML || '';
      const pageBreak = '<p style="page-break-after: always"></p>';

      printableCCDC += custmerinfo + workflowinfo + appinfo + plugginginfo + materialinfo + closureinfo + sensorsinfo + thermostatsinfo + note + pageBreak;

    }
    document.body.innerHTML = printableCCDC;
    const originalContents = document.body.innerHTML;

    window.print();
    window.close();

    document.body.innerHTML = originalContents;
  }
  

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
