<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #closureMaterialForm="ngForm" (ngSubmit)="updateMaterialProperty()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Closure Material Name"
            [(ngModel)]="closureMaterial.name"
            name="closureMaterialName"
            #closureMaterialNameInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="closureMaterialNameInput.touched && closureMaterialNameInput.invalid">
          <small class="mat-text-warn" *ngIf="closureMaterialNameInput?.errors?.required">Closure material name is required.</small>
          <small class="mat-text-warn" *ngIf="closureMaterialNameInput?.errors?.whitespace && !closureMaterialNameInput?.errors?.required">
            Invalid closure material name.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number 1"
            [(ngModel)]="closureMaterial.partNumber1"
            name="partNumber1"
            #partNumber1Input="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partNumber1Input.touched && partNumber1Input.invalid">
          <small class="mat-text-warn" *ngIf="partNumber1Input?.errors?.required">Part number 1 is required.</small>
          <small class="mat-text-warn" *ngIf="partNumber1Input?.errors?.whitespace && !partNumber1Input?.errors?.required">
            Invalid Part number 1.
          </small>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number 2"
            [(ngModel)]="closureMaterial.partNumber2"
            name="partNumber2"
            #partNumber2Input="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partNumber2Input.touched && partNumber2Input.invalid">
          <small class="mat-text-warn" *ngIf="partNumber2Input?.errors?.required">Part number 2 is required.</small>
          <small class="mat-text-warn" *ngIf="partNumber2Input?.errors?.whitespace && !partNumber2Input?.errors?.required">
            Invalid Part number 2.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Cost Per Sq"
            [(ngModel)]="closureMaterial.costPerSq"
            name="costPerSq"
            #costPerSqInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="costPerSqInput.touched && costPerSqInput.invalid">
          <small class="mat-text-warn" *ngIf="costPerSqInput?.errors?.required">Cost Per Sq is required.</small>
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Max Temperature"
            [(ngModel)]="closureMaterial.maxTemp"
            name="maxTemp"
            #maxTempInput="ngModel"
            required
            sflIsDecimal
            (change)="convertTeperature('celcius')"
          />
        </mat-form-field>
        <div *ngIf="maxTempInput.touched && maxTempInput.invalid">
          <small class="mat-text-warn" *ngIf="maxTempInput?.errors?.required">Max temperature is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Select UL Part Number" name="fastener" [(ngModel)]="closureMaterial.fastenerCode"
            #fasetenerCodeInput="ngModel" required>
            <mat-option *ngFor="let fastnerCodeId of fastnerCodeValues" [value]="fastnerCodeId?.id">
              {{ fastnerCodeId?.id }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="fasetenerCodeInput.touched && fasetenerCodeInput.invalid">
          <small class="mat-text-warn" *ngIf="fasetenerCodeInput?.errors?.required">UL Part Number is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Max Temperature Fahrenheit"
            [(ngModel)]="closureMaterial.maxTempF"
            name="maxTempF"
            #maxTempFInput="ngModel"
            required
            sflIsDecimal
            (change)="convertTeperature('fahrenheit')"
          />
        </mat-form-field>
        <div *ngIf="maxTempFInput.touched && maxTempFInput.invalid">
          <small class="mat-text-warn" *ngIf="maxTempFInput?.errors?.required">Max Temperature fahrenheit is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="closureMaterial.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <span>Closure Material Image</span>
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <img class="jumper-product-img" src="{{ imageUrl }}" alt="Closure Material Image" />
        </div>
      </div>
      <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <div class="mb-10" fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" (click)="addAttachment()">Add Image</button>
        </div>
        <div fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" [disabled]="!closureMaterial?.imageUrl" (click)="removeAttachment()">
            Remove Image
          </button>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!closureMaterialForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
