<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Part Number</mat-label>
          <input matInput [(ngModel)]="thermostatFilter.partNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldPartNumber)"
            *ngIf="thermostatFilter.partNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addThermostatMaster()">Add New Thermostat</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        class="thermostat-table-scrollable"
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="thermostatMasterDataSource"
        (matSortChange)="getThermostatMasterSorting($event)"
      >
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.partNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="amps">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> AMPs </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.amps }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="closeTemp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Close Temp </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.closeTemp }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="closeTempF">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Close Temp F </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.closeTempF }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="closeTolC">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Close Tol C </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.closeTolC }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="closeTolF">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Close Tol F </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.closeTolF }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="cost">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Cost </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.cost }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="manualReset">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Manual Reset </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.manualReset | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="manufacturer">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Manufacturer </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.manufacturer }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="openOnRise">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Open On Rise </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.openOnRise | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="openTemp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Open Temp </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.openTemp }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="openTempF">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Open Temp F </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.openTempF }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="adjustable">
          <mat-header-cell *matHeaderCellDef mat-sort-header mat-sort-header fxFlex="10"> Adjustable </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.adjustable | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tolerance">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Tolerance </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.tolerance }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="toleranceF">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Tolerance F </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.toleranceF }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="type">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.type }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="volts">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="7"> Volts </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="7"> {{ element?.volts }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editThermostatMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteThermostatMaster(element.thermostatListId)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="thermostatMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: thermostatMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!thermostatMasterDataSource.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getThermostatMasterPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
