<h2 mat-dialog-title>
  Power Cord
  <hr />
</h2>
<mat-dialog-content class="cust_table">
  <form>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <input matInput placeholder="Part Number" [(ngModel)]="partNumber" name="partNumber" />
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <mat-select placeholder="Material" (selectionChange)="onOptionChange($event.value, 'material')">
          <mat-option *ngFor="let material of materialList" [value]="material.powerCordMaterialId">
            {{ material?.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <mat-select placeholder="Voltage" (selectionChange)="onOptionChange($event.value, 'voltage')">
          <mat-option *ngFor="let voltage of voltageList" [value]="voltage.id">
            {{ voltage?.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100"></div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="start space-between">
      <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <mat-select placeholder="Amps" (selectionChange)="onOptionChange($event.value, 'amps')">
          <mat-option *ngFor="let amp of ampsList" [value]="amp.id">
            {{ amp?.value }}
          </mat-option>
        </mat-select> </mat-form-field
      >&nbsp;
      <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <input
          matInput
          placeholder="Cord Length"
          [(ngModel)]="powerCordLength"
          sflIsNumber
          name="length"
          (ngModelChange)="onOptionChange($event.value, 'length')"
          maxlength="2"
        />
        <div matSuffix>Feet</div>
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="59" fxFlex.gt-md="59" fxFlex.gt-sm="59" fxFlex.gt-xs="100">
        <mat-select placeholder="Conn. to Jacket" (selectionChange)="onOptionChange($event.value, 'connector')">
          <mat-option *ngFor="let conn of connectorList" [value]="conn.value"> {{ conn.name }} ({{ conn.partNumber }}) </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="Sex">
          <mat-option *ngFor="let sex of sexList" [value]="sex.id">
            {{ sex.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="UL">
          <mat-option *ngFor="let ul of ulList" [value]="ul.id">
            {{ ul.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="CSA">
          <mat-option *ngFor="let ul of ulList" [value]="ul.id">
            {{ ul.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="CE">
          <mat-option *ngFor="let ul of ulList" [value]="ul.id">
            {{ ul.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="59" fxFlex.gt-md="59" fxFlex.gt-sm="59" fxFlex.gt-xs="100">
        <mat-select placeholder="Plug to Jacket" (selectionChange)="onOptionChange($event.value, 'plug')">
          <mat-option *ngFor="let plug of plugList" [value]="plug.value"> {{ plug?.name }} ({{ plug?.partNumber }}) </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="Sex">
          <mat-option *ngFor="let sex of sexList" [value]="sex.id">
            {{ sex.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="UL">
          <mat-option *ngFor="let ul of ulList" [value]="ul.id">
            {{ ul.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="CSA">
          <mat-option *ngFor="let ul of ulList" [value]="ul.id">
            {{ ul.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="9" fxFlex.gt-md="9" fxFlex.gt-sm="9" fxFlex.gt-xs="100">
        <mat-select placeholder="CE">
          <mat-option *ngFor="let ul of ulList" [value]="ul.id">
            {{ ul.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select placeholder="Options" (selectionChange)="onOptionChange($event.value, 'options')">
          <mat-option *ngFor="let opt of optionsList" [value]="opt.powerCordConstantId">
            {{ opt?.value }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </form>
</mat-dialog-content>
<hr />
<mat-dialog-actions>
  <button mat-raised-button color="warn" type="submit" (click)="closeDialog()">Add</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
