<mat-nav-list sfl-appAccordion class="navigation">
    <mat-list-item sfl-appAccordionLink *ngFor="let menuitem of adminNavbarService.getAll()">
        <a sfl-appAccordionToggle class="relative" routerLink={{router.parseUrl(menuitem.state)}} *ngIf="menuitem.type === 'link'">
            <mat-icon>{{ menuitem.icon }}</mat-icon>
            <span>{{ menuitem.name }}</span>
            <span fxFlex></span>
            <span class="menu-badge mat-{{ badge.type }}" *ngFor="let badge of menuitem.badge">{{ badge.value }}</span>
        </a>
        <a sfl-appAccordionToggle class="relative" href="{{menuitem.state}}" *ngIf="menuitem.type === 'extLink'">
            <mat-icon>{{ menuitem.icon }}</mat-icon>
            <span>{{ menuitem.name}}</span>
            <span fxFlex></span>
            <span class="menu-badge mat-{{ badge.type }}" *ngFor="let badge of menuitem.badge">{{ badge.value }}</span>
        </a>
        <a sfl-appAccordionToggle class="relative" href="{{menuitem.state}}" target="_blank" *ngIf="menuitem.type === 'extTabLink'">
            <mat-icon>{{ menuitem.icon }}</mat-icon>
            <span>{{ menuitem.name}}</span>
            <span fxFlex></span>
            <span class="menu-badge mat-{{ badge.type }}" *ngFor="let badge of menuitem.badge">{{ badge.value }}</span>
        </a>
        <a sfl-appAccordionToggle class="relative" href="javascript:;" *ngIf="menuitem.type === 'sub'">
            <mat-icon>{{ menuitem.icon }}</mat-icon>
            <span>{{ menuitem.name}}</span>
            <span fxFlex></span>
            <span class="menu-badge mat-{{ badge.type }}" *ngFor="let badge of menuitem.badge">{{ badge.value }}</span>
            <mat-icon class="menu-caret">arrow_drop_down</mat-icon>
        </a>
        <mat-nav-list class="sub-menu" *ngIf="menuitem.type === 'sub'">
            <mat-list-item *ngFor="let childitem of menuitem.children" routerLinkActive="open">
                <a *ngIf="childitem.type !== 'link'" [routerLink]="['/', menuitem.state, childitem.state ]" class="child-nav">{{ childitem.name}}</a>
                <a *ngIf="childitem.type === 'link'" [routerLink]="[childitem.state ]" class="child-nav">{{ childitem.name}}</a>
            </mat-list-item>
        </mat-nav-list>
    </mat-list-item>
</mat-nav-list>

<hr>
<div class="main-title">
  Recently Visited orders
</div>

<div class="top-five-SO" *ngFor="let topFive of topFiveQuotation">
 <strong> SO#: {{topFive?.quotation?.salesOrderNumber}}</strong> <br>
 <b><a class="link" [routerLink]="['/app-eng/ccdc']"
  [queryParams]="{ quotId: topFive?.quotation?.id }"> Quote#: {{topFive?.quotation?.quotationNumber}}</a></b>
</div>
