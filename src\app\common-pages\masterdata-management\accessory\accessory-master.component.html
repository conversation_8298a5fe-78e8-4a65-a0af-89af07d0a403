<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Part Number</mat-label>
          <input matInput [(ngModel)]="accessoryFilter.partNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldPartNumber)"
            *ngIf="accessoryFilter.partNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Controller Name</mat-label>
          <input matInput [(ngModel)]="accessoryFilter.controllerName" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldControllerName)"
            *ngIf="accessoryFilter.controllerName"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addAccessory()">Add New Accessory</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="accessoryDataSource" (matSortChange)="getAccessorySorting($event)">
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.partNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Description </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.description }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="controllerName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Controller Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.controllerName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="listPrice">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> List Price </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.listPrice }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="usCost">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> US Cost </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.usCost }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editAccessory(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteAccessory(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="accessoryColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: accessoryColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!accessoryDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getAccessoryPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
