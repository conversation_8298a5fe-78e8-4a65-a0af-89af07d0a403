import { Component, OnInit, OnD<PERSON>roy, ViewChild } from '@angular/core';
import { PQPFamilyMaster, PQPFamilyMasterPageable, PQPFamilyFilter, GenericPageable } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManagePqpFamilyComponent } from './manage-pqp-family/manage-pqp-family.component';
import { Values } from 'src/app/shared/constants/values.constants';
@Component({
  selector: 'sfl-pqp-family',
  templateUrl: './pqp-family.component.html'
})
export class PqpFamilyComponent implements OnInit, OnDestroy {
  pageTitle = 'PQP Family';
  pqpFamily: PQPFamilyMaster;
  pqpFamilyPageable: GenericPageable<PQPFamilyMaster>;
  pqpFamilyDataSource = new MatTableDataSource<PQPFamilyMaster>();
  pqpFamilyColumns = DisplayColumns.Cols.PQPFamily;
  pqpFamilyFilter: PQPFamilyFilter = new PQPFamilyFilter();

  dataSource = new MatTableDataSource<PQPFamilyMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  length: number;

  showLoader = false;
  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldName = Values.FilterFields.name;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getPQPMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new PQP Family
  addPQPFamily() {
    this.editPQPFamily(new PQPFamilyMaster());
  }
  // used to add filter to PQP Family listing
  async addFilter() {
    this.filter = this.pqpFamilyFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.pqpFamilyFilter.name }];
    this.getPQPMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of PQP Family listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.pqpFamilyFilter.name = '') : this.pqpFamilyFilter.name
      }
    ];
    this.getPQPMasterData(this.initialPageIndex, this.pageSize);
  }

  getPQPMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPQPFamilyMasterData(this.filter, pageable).subscribe(
        (res: GenericPageable<PQPFamilyMaster>) => {
          this.pqpFamilyPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPQPFamilyTable(this.pqpFamilyPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.pqpFamilyDataSource.data = [];
        }
      )
    );
  }

  createPQPFamilyTable(serviceRequestList: GenericPageable<PQPFamilyMaster>) {
    this.pqpFamilyDataSource.data = serviceRequestList.content;
  }

  getPQPFamilyPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPQPMasterData(this.pageIndex, this.pageSize);
  }

  getPQPFamilySorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPQPMasterData(this.pageIndex, this.pageSize);
  }

  editPQPFamily(pqpFamily: PQPFamilyMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = pqpFamily;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-pqp-family-model';
    const dialogRef = this.matDialog.open(ManagePqpFamilyComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          pqpFamily.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPQPMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  async deletePQPFamily(pqpFamilyId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePQPFamily(pqpFamilyId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPQPMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
