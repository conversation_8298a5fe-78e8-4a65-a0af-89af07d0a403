import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { MatDialogRef, MatOption, MatSelect, MAT_DIALOG_DATA } from '@angular/material';
import { SnakbarService, Messages } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { BomEditorService } from '../bom-editor.service';
import { Jacket } from '../../../jacket-list/jacket-list.model';
import { FormControl } from '@angular/forms';
import { DuplicateBomDTO } from '../bom-editor.model';
import { Values } from 'src/app/shared/constants/values.constants';
import { ViewChild } from '@angular/core';

@Component({
  selector: 'sfl-copy-bom',
  templateUrl: './copy-bom.component.html',
  styleUrls: ['./copy-bom.component.css']
})
export class CopyBomComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('select') select: MatSelect;
  title = 'Copy this BOM to different Jacket';
  jacketId: number;
  revisionId: number;
  allSelected = false;
  type: string;
  jacketListInfo: Jacket[];
  noOtherJacketsAvailable: boolean;
  sections: string;
  duplicateBomDTO = new DuplicateBomDTO;
  sectionListArray =  Values.sectionList;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  showLoader = false;
  sourceJacket: Jacket = new Jacket();
  targetJacket: number;
  subscription = new Subscription();
  constructor(
    public dialogRef: MatDialogRef<CopyBomComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly snakbarService: SnakbarService,
    private readonly bomEditorService: BomEditorService
  ) {
    this.jacketId = data.jacketId;
    this.type = data.type;
    this.revisionId = data.revisionId;
  }

  ngOnInit() {
    this.getAllJacketsByRevId();
  }
  // gets the all available jackets in specified revision
  getAllJacketsByRevId(): void {
    this.showLoader = true;
    this.subscription.add(
      this.bomEditorService.getJacketsByRevId(this.revisionId).subscribe(
        (jacketList: Jacket[]) => {
          this.jacketListInfo = jacketList.filter(jacket => jacket.id !== this.jacketId);
          this.jacketListInfo.length ? (this.noOtherJacketsAvailable = false) : (this.noOtherJacketsAvailable = true);
          this.sourceJacket = jacketList.find(jacket => jacket.id === this.jacketId);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      )
    );
  }

  // takes the source and the target jacket id, type [usa/VN] and
  copyJacketBOM(): void {
    this.showLoader = true;
    this.duplicateBomDTO.type = this.type;
    this.duplicateBomDTO.sourceJacketId = this.sourceJacket.id;
    this.duplicateBomDTO.destinationJacketId = this.targetJacket;
    this.duplicateBomDTO.selectedSections = this.sections;
    this.subscription.add(
      this.bomEditorService.copyBOMToTargetJacket(this.duplicateBomDTO).subscribe(
        res => {
          if (res) {
            this.snakbarService.success(Messages.COPY_BOM.Copy_Success);
            this.showLoader = false;
            this.dialogRef.close(true);
          } else {
            this.snakbarService.error(Messages.COPY_BOM.Copy_Error);
            this.showLoader = false;
          }
        },
        () => {
          this.showLoader = false;
        }
      )
    );
  }

  toggleAllSelection() {
      this.select.options.forEach((item: MatOption) => {
        if (this.allSelected) {
          item.select();
        } else {
          item.deselect();
        }
      });
    }

  // closes the modal
  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
