import { Component, OnInit, <PERSON><PERSON>hild, OnDestroy } from '@angular/core';
import {
  ProductGroupCoverPageMaster,
  ProductGroupCoverPageMasterPageable,
  ProductGroupFilter,
  GenericPageable
} from '../masterdata-management.model';
import { MatTableDataSource, MatSort, MatPaginator, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-product-group',
  templateUrl: './product-group.component.html'
})
export class ProductGroupComponent implements OnInit, OnDestroy {
  pageTitle = 'Product Group Master';
  productGroup: ProductGroupCoverPageMaster;
  productGroupPageable: GenericPageable<ProductGroupCoverPageMaster>;
  productGroupDataSource = new MatTableDataSource<ProductGroupCoverPageMaster>();
  productGroupColumns = DisplayColumns.Cols.ProductGroupCoverPage;
  productGroupFilter: ProductGroupFilter = new ProductGroupFilter();
  dataSource = new MatTableDataSource<ProductGroupCoverPageMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  showLoader = true;
  filterFieldName = Values.FilterFields.name;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(private masterDataService: MasterdataManagementService) {}

  ngOnInit() {
    this.getProductGroupMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add filter to Product group listing
  async addFilter() {
    this.filter = this.productGroupFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.productGroupFilter.name }];
    this.getProductGroupMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of Product group listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.productGroupFilter.name = '') : this.productGroupFilter.name
      }
    ];
    this.getProductGroupMasterData(this.initialPageIndex, this.pageSize);
  }

  getProductGroupMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getProductGroupMasterData(this.filter, pageable).subscribe(
        (res: GenericPageable<ProductGroupCoverPageMaster>) => {
          this.productGroupPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.createProductGroupTable(this.productGroupPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.productGroupDataSource.data = [];
        }
      )
    );
  }

  createProductGroupTable(serviceRequestList: GenericPageable<ProductGroupCoverPageMaster>) {
    this.productGroupDataSource.data = serviceRequestList.content;
  }

  getProductGroupPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getProductGroupMasterData(this.pageIndex, this.pageSize);
  }

  getProductGroupSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getProductGroupMasterData(this.pageIndex, this.pageSize);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
