import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';
import { Mat<PERSON><PERSON>og, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { GenericPageable, ProductFilter, ProductGroupMaster } from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { ManageProductGroupComponent } from './manage-product-group/manage-product-group.component';

@Component({
  selector: 'app-product-group',
  templateUrl: './product-group.component.html',
  styleUrls: ['./product-group.component.css']
})
export class ProductGroupComponent implements OnInit, OnDestroy{

  pageTitle = 'Product Group Master';
   productGroup: ProductGroupMaster;
   productGroupPageable: GenericPageable<ProductGroupMaster>;
   productGroupDataSource = new MatTableDataSource<ProductGroupMaster>();
   productGroupColumns = DisplayColumns.Cols.ProductGroup;
   productFilter: ProductFilter = new ProductFilter();
   dataSource = new MatTableDataSource<ProductGroupMaster>();
   pageSizeOptions = Variable.pageSizeOptions;
   initialPageIndex = Variable.activePage;
   initialPageSize = Variable.tenItemsPerPage;
 
   length: number;
   pageIndex = Variable.activePage;
   pageSize = Variable.tenItemsPerPage;
   sortOrder = Variable.defaultSortOrderDescending;
   sortField = Variable.defaultSortById;
   ascSort = Variable.sortAscending;
   numberOfElements: number;
   filter = [];
   showLoader = true;
   filterFieldName = Values.FilterFields.name;
 
   subscription = new Subscription();
   @ViewChild(MatPaginator) paginator: MatPaginator;
   @ViewChild(MatSort) sort: MatSort;
 
   constructor(private masterDataService: MasterdataManagementService, private readonly matDialog: MatDialog, private readonly snakbarService: SnakbarService, private readonly sweetAlertService: SweetAlertService,) {}
 
   ngOnInit() {
     this.getProductGroupMasterData(this.initialPageIndex, this.initialPageSize);
   }
 
   // used to add filter to Product group listing
   async addFilter() {
     this.filter = this.productFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.productFilter.name }];
     this.getProductGroupMasterData(this.initialPageIndex, this.pageSize);
   }
   // used to clear filter of Product group listing
   clearFilter(fieldToClear: string) {
     this.filter = [
       {
         key: this.filterFieldName,
         value: fieldToClear === this.filterFieldName ? (this.productFilter.name = '') : this.productFilter.name,
       },
     ];
     this.getProductGroupMasterData(this.initialPageIndex, this.pageSize);
   }
 
   getProductGroupMasterData(pageIndex, pageSize) {
     this.showLoader = true;
     const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
     this.subscription.add(
       this.masterDataService.getProductGroupMasterData(this.filter, pageable).subscribe(
         (res: GenericPageable<ProductGroupMaster>) => {
           this.productGroupPageable = res;
           this.length = res.totalElements;
           this.pageIndex = res.number;
           this.numberOfElements = res.numberOfElements;
           this.createProductGroupTable(this.productGroupPageable);
           this.showLoader = false;
         },
         (error) => {
           this.showLoader = false;
           this.productGroupDataSource.data = [];
         },
       ),
     );
   }
 
   createProductGroupTable(serviceRequestList: GenericPageable<ProductGroupMaster>) {
     this.productGroupDataSource.data = serviceRequestList.content;
   }
 
   getProductGroupPagination(event) {
     this.pageIndex = event.pageIndex;
     this.pageSize = event.pageSize;
     this.getProductGroupMasterData(this.pageIndex, this.pageSize);
   }
 
   getProductGroupSorting(event) {
     this.pageIndex = this.initialPageIndex;
     event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
     this.sortField = event.active;
     this.getProductGroupMasterData(this.pageIndex, this.pageSize);
   }
 
   addProductGroup() {
     this.editProductGroup(new ProductGroupMaster());
   }
 
   editProductGroup(productGroups: ProductGroupMaster) {
     const matDataConfig = new MatDialogConfig();
     matDataConfig.data = productGroups;
     matDataConfig.width = PopupSize.size.popup_md;
     matDataConfig.panelClass = 'sfl-product-group-master-model';
     const dialogRef = this.matDialog.open(ManageProductGroupComponent, matDataConfig);
     dialogRef.afterClosed().subscribe((res) => {
       if (res) {
         this.snakbarService.success(
           productGroups.id ? 'Product Group' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success : 'Product Group' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success,
         );
         this.getProductGroupMasterData(this.pageIndex, this.pageSize);
       }
     });
   }
 
   async deleteProductGroup(departmentsId: number) {
     if (await this.sweetAlertService.deleteAlert()) {
       this.showLoader = true;
       this.masterDataService.deleteProductGroup(departmentsId).subscribe(
         () => {
           this.snakbarService.success('Product Group' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
           this.getProductGroupMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
           this.showLoader = false;
         },
         () => {
           this.showLoader = false;
         }
       );
     }
   }
 
   ngOnDestroy() {
     if (this.subscription) {
       this.subscription.unsubscribe();
     }
   }

}
