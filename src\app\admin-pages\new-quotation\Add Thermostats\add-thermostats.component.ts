import {Component, Inject, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
  MatPaginator,
  MatTableDataSource
} from '@angular/material';
import {Messages, SharedService, SnakbarService, SweetAlertService} from '../../../shared';
import {SalesOrderSummaryService} from '../summary-sales-order.service';
import {
  ThermostatInfo,
  ThermostatInstallationMethod,
  ThermostatList,
  ThermostatType,
  ThermostatTypesMaster,
  Units
} from '../ccdc-model/ccdc.model';
import {Subscription} from 'rxjs';
import {DisplayColumns} from 'src/app/shared/constants/displayColName.constants';
import {PopupSize} from '../../../shared/constants/popupsize.constants';
import {NgForm} from '@angular/forms';
import {AddOthrerThermostatsComponent} from './other-termostat/add-other-termostat.component';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {ManageUnitsService} from '../manage-units/manage-units.service';

@Component({
  selector: 'sfl-add-thermostats',
  templateUrl: './add-thermostats.component.html'
})
export class AddThermostatsComponent implements OnInit, OnDestroy {
  thermostatList: ThermostatList[];
  selectedThermostat = [];
  thermostatInfo: ThermostatInfo;
  jacketGroupId: number;
  searchThermostate: ThermostatList;
  thermostatType: ThermostatTypesMaster[];
  thermostatInstallationMethod: ThermostatInstallationMethod[];

  @ViewChild(MatPaginator) paginator: MatPaginator;

  thermostatsdisplayedColumns = DisplayColumns.Cols.thermostatsdisplayedColumns;
  thermostatdataSource = new MatTableDataSource<ThermostatList>();

  selecetdthermostatsdisplayedColumns = DisplayColumns.Cols.thermostatsdisplayedColumns;
  selecetdthermostatdataSource = new MatTableDataSource<ThermostatList>();

  tempUnit = '';
  isShowThermostat: boolean;
  isAdded: boolean;
  subscription: Subscription = new Subscription();
  pageSize = PopupSize.size.pageSize;
  others: ThermostatList[];
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  defaultSelected = true;

  constructor(
    private snakbarService: SnakbarService,
    public thermostatDialogRef: MatDialogRef<AddThermostatsComponent>,
    private salesOrderSummaryService: SalesOrderSummaryService,
    private matDialog: MatDialog,
    private sharedService: SharedService,
    private manageUnitService: ManageUnitsService,
    private sweetAlertService: SweetAlertService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketGroupId = data.jacketGroupId;
  }

  ngOnInit() {
    this.getMeasurementUnit();
    this.searchThermostate = new ThermostatList();
    this.isShowThermostat = false;
    this.isAdded = false;
    this.thermostatInfo = new ThermostatInfo();
    this.thermostatInfo.thermostatInformationDTOList = [];
    this.selectedThermostat = new Array<ThermostatInfo>();
    this.getThermostats();
    this.getThermostatByJacketGroupId();
    this.getThermostatTypes();
    this.getInstallationMethod();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  // used to get the thermostats
  getThermostats() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatsList().subscribe(
        (res: ThermostatList[]) => {
          this.thermostatList = res;
          this.thermostatdataSource = new MatTableDataSource<ThermostatList>(this.thermostatList);
          this.thermostatdataSource.paginator = this.paginator;
          this.showLoader = false;
        },
        (error) => {
          if (error.applicationStatusCode === 1235) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false;
        }
      )
    );
  }

  // used to handle adding new thermostats to the list
  addThermostat(element) {
    if (element.thermostatType && element.installationMethodDTO) {
      this.thermostatInfo.thermostatInformationDTOList.push(element);
      this.selecetdthermostatdataSource.data = this.thermostatInfo.thermostatInformationDTOList;
    } else {
      this.snakbarService.error(Messages.Quotation.select_thermostat);
    }
  }

  // used to reset the thermostat adding form
  reset(thermostateForm: NgForm) {
    this.getThermostats();
    thermostateForm.reset();
  }

  // used to handle the filter of the thermostat list
  searchThermosateList(thermostateForm: NgForm) {
    this.showLoader = true;
    this.searchThermostate.tempUnit = this.tempUnit;
    this.searchThermostate.openOnRise = this.defaultSelected;
    this.subscription.add(
      this.salesOrderSummaryService.searchThermostate(this.searchThermostate).subscribe(
        (res: ThermostatList[]) => {
          if (res.length > 0) {
            this.thermostatList = res;
            this.thermostatdataSource = new MatTableDataSource<ThermostatList>(this.thermostatList);
            this.thermostatdataSource.paginator = this.paginator;
          } else {
            this.snakbarService.error(Messages.error_msg.nodata);
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to remove the thermostat from the list
  async remove(element) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      if (element.id) {
        this.subscription.add(
          this.salesOrderSummaryService.deleteThermostate(element.id).subscribe(
            res => {
              this.selecetdthermostatdataSource.data.splice(this.selecetdthermostatdataSource.data.indexOf(element), 1);
              this.selecetdthermostatdataSource._updateChangeSubscription();
              this.showLoader = false;
            },
            () => (this.showLoader = false)
          )
        );
      } else {
        this.selecetdthermostatdataSource.data.splice(this.selecetdthermostatdataSource.data.indexOf(element), 1);
        this.selecetdthermostatdataSource._updateChangeSubscription();
        this.showLoader = false;
      }
    }
  }

  // used to save the thermostat
  saveThermostat(value) {
    this.showLoader = true;
    const thermostatInfoReq = [];
    for (let i = 0; i < this.thermostatInfo.thermostatInformationDTOList.length; i++) {
      const obj = {
        id: this.thermostatInfo.thermostatInformationDTOList[i].id,
        thermostatType: this.thermostatInfo.thermostatInformationDTOList[i].thermostatType,
        thermostatListId: this.thermostatInfo.thermostatInformationDTOList[i].thermostatListId,
        jacketGroupId: this.jacketGroupId,
        otherThermostatType: this.thermostatInfo.thermostatInformationDTOList[i].otherThermostatType,
        otherThermostatPartNumber: this.thermostatInfo.thermostatInformationDTOList[i].otherThermostatPartNumber,
        otherThermostatCost: this.thermostatInfo.thermostatInformationDTOList[i].otherThermostatCost,
        installationMethodDTO: this.thermostatInfo.thermostatInformationDTOList[i].installationMethodDTO
      };
      thermostatInfoReq.push(obj);
    }
    this.thermostatInfo.thermostatInformationDTOList = thermostatInfoReq;
    this.subscription.add(
      this.salesOrderSummaryService.saveThermostatsInfo(this.thermostatInfo, this.jacketGroupId).subscribe(
        (res: ThermostatInfo) => {
          if (res.thermostatInformationDTOList.length) {
            const obj = {data: res, mode: value === 'save' ? 0 : 1};
            this.thermostatDialogRef.close(obj);
          }
          this.showLoader = false;
        },
        (error) => {
          this.showLoader = false;
          if (error.applicationStatusCode === 3001) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  // used to get the list of thermostat by jacket group id
  getThermostatByJacketGroupId() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatByJacketGroupId(this.jacketGroupId).subscribe(
        (res: ThermostatInfo) => {
          this.thermostatInfo = res;
          this.selecetdthermostatdataSource.data = this.thermostatInfo.thermostatInformationDTOList;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog() {
    this.thermostatDialogRef.close();
  }

  // used to handle adding of other thermostat using a modal popup
  openOtherThermostat() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {};
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(AddOthrerThermostatsComponent, matDataConfig)
      .afterClosed()
      .subscribe((result: ThermostatList) => {
        if (result) {
          result.thermostatType = null;
          this.thermostatInfo.thermostatInformationDTOList = this.thermostatInfo.thermostatInformationDTOList.concat(result);
          this.selecetdthermostatdataSource.data = this.thermostatInfo.thermostatInformationDTOList;
        }
      });
  }

  // used to get the thermostat types
  getThermostatTypes() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatTypeList().subscribe(
        (res: ThermostatType[]) => {
          this.thermostatType = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to get the list of installtion methods
  getInstallationMethod() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getInstallationMethod().subscribe(
        (installationMethod: ThermostatInstallationMethod[]) => {
          this.thermostatInstallationMethod = installationMethod;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
