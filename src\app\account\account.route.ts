import { Routes } from '@angular/router';

import {
    LoginComponent,
    ForgotPasswordComponent,
} from '.';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { SolidworksDownloadComponent } from '../shared/component/solidworks-download/solidworks-download.component';
import { SetInitialNameComponent } from './login/set-initial-name/set-initial-name.component';

export const accountRoutes: Routes = [
    {
        path: '',
        component: LoginComponent
    },
    {
        path: 'login',
        component: LoginComponent
    },
    {
        path: 'forgot-password',
        component: ForgotPasswordComponent
    },
    {
        path: 'reset/finish',
        component: ResetPasswordComponent
    },
    {
        path: 'solidworks-download',
        component: SolidworksDownloadComponent
    },
    {
        path: 'initial-name',
        component: SetInitialNameComponent
    },
];
