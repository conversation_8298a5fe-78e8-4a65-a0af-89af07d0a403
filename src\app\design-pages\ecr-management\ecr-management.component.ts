import { DatePipe } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { User } from '../../common-pages/users/user.model';
import { AddAttachmentComponent } from './add-attachment/add-attachment.component';
import { AddPartnumberComponent } from './add-partnumber/add-partnumber.component';
import {
  AddAttachment,
  EcrDto,
  EcrPageable,
  EcrPartnumberDto,
  EcrSearchFilter,
  Statuses,
  SyncPartNumberDto
} from './ecr-management.model';
import { EcrManagementService } from './ecr-management.service';
import { GenerateNewEcrComponent } from './generate-new-ecr/generate-new-ecr.component';

@Component({
  selector: 'sfl-ecr-management',
  templateUrl: './ecr-management.component.html',
  styleUrls: ['./ecr-management.component.scss'],
})
export class EcrManagementComponent implements OnInit, OnDestroy {

  ecrSearchFilter = new EcrSearchFilter();

  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  sortField = Variable.defaultSortByEcrLog;
  length: number;

  ecrPageable = new EcrPageable();
  statuses: Statuses[];
  ecrDto: EcrDto = new EcrDto();
  addAttachmentDto: AddAttachment = new AddAttachment();
  requesters: any[];
  attachments: any[];
  description: string;
  displayedColumns = DisplayColumns.Cols.ECRManagement;
  displayedECRPartColumns = DisplayColumns.Cols.ECRPartManagement;

  subscription: Subscription = new Subscription();
  documentDataSource = new MatTableDataSource<Document>();
  formData = new FormData();

  ecrNumber: string;
  status: string;
  selectedPartNumber: string;
  selectedAttachment: string;
  isEcropen: boolean;
  attachmentList = false;
  showLoader = false;
  viewEcr = true;
  viewEco = false;
  btnSwitchView = Variable.ecoPageTitle;
  title = Variable.ecrPageTitle;

  users: User[];
  dataUsers: User[];

  sizeLG = 90;
  sizeMD = 85;
  sizeSM = 49;
  sizeXS = 100;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  ecrDataSource = new MatTableDataSource();
  mock: any[];
  isEcrSelected = false;
  reviewLevel = Variable.ecrReviewLevel; // designReview
  colors = Variable.ecrColor;
  inactive: boolean;
  onhold: boolean;
  syncInProcess = false;
  lastSyncStatus: string;
  isSyncSuccess = true;
  loading = false;
  syncPartNumberDTO: SyncPartNumberDto = new SyncPartNumberDto();

  isNoDataFound = this.ecrDataSource.connect().pipe(map(data => data.length === 0));

  constructor(private route: ActivatedRoute, private titleService: Title, private matDialog: MatDialog, private ecrManagementService: EcrManagementService, private snakbarService: SnakbarService, private sweetAlertService: SweetAlertService, private datePipe: DatePipe) {
  }

  async openECR(ecrRow) {
    return new Promise((resolve) => {
      this.showLoader = true;
      this.selectedRow = ecrRow.id;
      this.subscription.add(this.ecrManagementService.getEcrById(ecrRow.id).subscribe((response: EcrDto) => {
        if (response) {
          this.ecrDto = response;
          this.showLoader = false;
          this.isEcrSelected = true;
        }
        resolve();
      }, error => {
        this.showLoader = false;
        this.isEcrSelected = false;
      }));
    });
  };

  selectPartnumber(partNumber) {
    this.selectedPartNumber = this.selectedPartNumber === partNumber ? '' : partNumber;
  }

  selectAttachment(attachment) {
    this.selectedAttachment = this.selectedAttachment === attachment ? '' : attachment;
  }

  ngOnInit() {
    this.titleService.setTitle('ECR Management - Design Eng');
    this.preLoadingData();
  }

  async preLoadingData() {
    try {
      const id: string = this.route.snapshot.queryParams['id'];
      await this.getEcrLog(this.initialPageIndex, this.initialPageSize);
      this.getAllUsers();
      this.getECRStatuses();

      if (this.ecrPageable.content.length > 0) {
        let row: any = this.ecrPageable.content[0];

        if (id !== null && id !== undefined) {
          let tempRaw = this.ecrPageable.content.filter((data: EcrDto) => data.id.toString() === id);

          if (tempRaw !== null && tempRaw !== undefined && tempRaw.length > 0) {
            row = tempRaw[0];
            this.selectedRow = row.id;
            await this.openECR(row).then(res => {
              this.openEco();
            });

          } else {
            this.selectedRow = row.id;
          }
        } else {
          this.selectedRow = row.id;
          await this.openECR(row);
        }
      }
    } catch (error) {
      // Handle errors here
      console.error(error);
    }
  }

  getAllUsers() {
    // getUsers
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.getSalesAssociate(true).subscribe((res: User[]) => {
      if (res) {
        this.users = res;
        this.dataUsers = res;
        this.showLoader = false;
      }
    }, error => {
      this.showLoader = false;
    }));
  }

  getECRStatuses() {
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.getStatuses().subscribe((statuses: Statuses[]) => {
      if (statuses) {
        this.statuses = statuses;
        this.showLoader = false;
      }
    }, error => {
      this.showLoader = false;
      if (error.applicationStatusCode === 1238) {
        this.snakbarService.error(error.message);
      }
    }));
  }

  getEcrLog(pageIndex, pageSize) {
    const pageable = {
      page: pageIndex,
      size: pageSize,
      sort: this.sortField + ',' + this.sortOrder,
      direction: this.sortOrder
    };
    this.showLoader = true;
    return new Promise((resolve, reject) => {
      this.subscription.add(this.ecrManagementService.getEcrLog(this.ecrSearchFilter, pageable).subscribe((res: EcrPageable) => {
        if (res) {
          this.ecrPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.createEcrTable(this.ecrPageable);
          this.showLoader = false;
          resolve();
        } else {
          this.ecrDataSource.data = [];
          this.showLoader = false;
          resolve();
        }
      }, error => {
        this.ecrDataSource.data = [];
        this.showLoader = false;
        reject();
      }));
    });
  }

  async deleteOperation(element) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.subscription.add(
        this.ecrManagementService.deleteECR(element.id).subscribe(
          () => {
            this.snakbarService.success('ECR' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
            this.ecrDataSource.data.splice(
              this.ecrDataSource.data.indexOf(element),
              1
            );
            if (this.ecrPageable.content.length > 0) {
              let row: any = this.ecrPageable.content[0];
              this.openECR(row);
            } else {
              this.isEcrSelected = false;
            }
            this.ecrDataSource._updateChangeSubscription();
            this.showLoader = false;
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  createEcrTable(serviceRequestList: EcrPageable) {
    this.ecrDataSource.data = serviceRequestList.content;
  }

  async addFilter() {
    this.ecrSearchFilter.ecrNo = this.ecrSearchFilter.ecrNo === null ? null : this.ecrSearchFilter.ecrNo;
    this.ecrSearchFilter.partNumber = this.ecrSearchFilter.partNumber === '' ? null : this.ecrSearchFilter.partNumber;
    this.ecrSearchFilter.requestor = this.ecrSearchFilter.requestor === '' ? null : this.ecrSearchFilter.requestor;
    this.ecrSearchFilter.status = this.ecrSearchFilter.status === null ? null : this.ecrSearchFilter.status;

    await this.getEcrLog(this.initialPageIndex, this.pageSize);
    if (this.ecrPageable.content.length > 0) {
      let row: any = this.ecrPageable.content[0];
      this.openECR(row);
    } else {
      this.isEcrSelected = false;
    }
  }

  async resetFilter() {
    this.ecrSearchFilter.ecrNo = null;
    this.ecrSearchFilter.partNumber = null;
    this.ecrSearchFilter.requestor = null;
    this.ecrSearchFilter.status = null;
    await this.getEcrLog(this.initialPageIndex, this.pageSize);
    if (this.ecrPageable.content.length > 0) {
      let row: any = this.ecrPageable.content[0];
      this.openECR(row);
    } else {
      this.isEcrSelected = false;
    }
  }

  async generateNewEcr() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xlg;
    matDataConfig.panelClass = 'sfl-generate-ecr-model';
    const dialogRef = this.matDialog.open(GenerateNewEcrComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(async res => {
      if (res) {
        await this.getEcrLog(this.initialPageIndex, this.initialPageSize);
        if (this.ecrPageable.content.length > 0) {
          let row: any = this.ecrPageable.content[0];
          this.openECR(row);
        } else {
          this.isEcrSelected = false;
        }
      }
    });
  }

  addPartNumber() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_sm;
    matDataConfig.panelClass = 'sfl-add-partnumber-model';
    const dialogRef = this.matDialog.open(AddPartnumberComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((addedPartnumber: EcrPartnumberDto) => {
      if (addedPartnumber.partNumber) {
        if (!this.ecrDto.ecrPartNumbers.find(element => element['partNumber'] === addedPartnumber.partNumber)) {
          addedPartnumber.ecrId = this.ecrDto.id;
          this.subscription.add(this.ecrManagementService.addPartNumber(addedPartnumber).subscribe((res: EcrPartnumberDto) => {
            this.ecrDto.ecrPartNumbers.push(res);
            this.ecrDataSource.data.forEach((ele: EcrDto) => {
              if (ele.id === this.ecrDto.id) {
                ele.ecrPartNumbers = [...ele.ecrPartNumbers, res];
              }
            });
          }));
        } else {
          this.snakbarService.error('Part number ' + addedPartnumber.partNumber + ' already exist!');
        }
      }
    });
  }

  addAttachment(ecrId) {
    this.showLoader = true;
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-add-partnumber-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      if (uploadedAttachment !== undefined) {
        this.addAttachmentDto.ecrId = ecrId;
        this.formData = new FormData();
        this.formData.append('file', uploadedAttachment, uploadedAttachment.name);
        this.formData.append('document', JSON.stringify(this.addAttachmentDto));
        this.subscription.add(this.ecrManagementService.addAttachment(this.formData).subscribe(res => {
          this.ecrDto.ecrAttachments.push(res);
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
        }));
      } else {
        this.showLoader = false;
      }
    });
  }

  removePartnumber(partNumberId) {
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.deletePartNumber(partNumberId).subscribe(res => {
      this.ecrDto.ecrPartNumbers.splice(this.ecrDto.ecrPartNumbers.map(x => x.id).indexOf(partNumberId), 1);
      this.ecrDataSource.data.forEach((ele: EcrDto) => {
        if (ele.id === this.ecrDto.id) {
          ele.ecrPartNumbers.splice(ele.ecrPartNumbers.map(x => x.id).indexOf(partNumberId), 1);
          // referesh the ecr datasource to update the view datatable
          ele.ecrPartNumbers = [...ele.ecrPartNumbers];
        }
      });
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  removeAttachment(fileId) {
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.deleteAttachment(fileId).subscribe(() => {
      this.ecrDto.ecrAttachments.splice(this.ecrDto.ecrAttachments.map(x => x.id).indexOf(fileId), 1);
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  async confirmDelete(itemValue, toBeDeleted) {
    if (await this.sweetAlertService.deleteAlert()) {
      toBeDeleted === 'partNumber' ? this.removePartnumber(itemValue) : this.removeAttachment(itemValue);
    }
  }

  openDoclist() {
    this.attachmentList = !this.attachmentList;
    if (!this.attachmentList) {
      this.sizeLG = 90;
      this.sizeMD = 85;
      this.sizeSM = 49;
      this.sizeXS = 100;
    } else {
      this.sizeLG = 69;
      this.sizeMD = 65;
      this.sizeSM = 50;
      this.sizeXS = 100;
    }
  }

  openDoc(blob) {
    this.subscription.add(this.ecrManagementService.getDocument(blob.id).subscribe((success) => {
        const url = URL.createObjectURL(success);
        window.open(url, '_target');
      },
      error => {
        if (error.applicationStatusCode === 1240) {
          this.snakbarService.error(error.message);
        }
      }
    ));
  }

  getPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getEcrLog(this.pageIndex, this.pageSize);
  }

  getSorting(event) {
    this.sortField = event.active;
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    this.getEcrLog(this.pageIndex, this.pageSize);
  }

  openEco() {
    this.viewEco = !this.viewEco;
    this.viewEcr = !this.viewEcr;
    this.btnSwitchView = this.viewEco ? Variable.ecrPageTitle : Variable.ecoPageTitle;
    this.title = this.viewEco ? Variable.ecoPageTitle : Variable.ecrPageTitle;
  }

  selectedRow(row) {
    this.selectedRow = row.id;
  }

  updateEcr(isStatusUpdate = false) {
    this.showLoader = true;
    if (isStatusUpdate) {
      this.ecrDto.updatedDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphen);
    }
    this.subscription.add(this.ecrManagementService.updateEcr(this.ecrDto).subscribe((response: EcrDto) => {
      this.ecrDto = response;
      this.ecrDataSource.data.forEach((ele: EcrDto) => {
        if (ele.id === this.ecrDto.id) {
          ele.ecrNo = this.ecrDto.ecrNo;
          ele.ecrStatusId = this.ecrDto.ecrStatusId;
          ele.updatedDate = this.ecrDto.updatedDate;
          ele.description = this.ecrDto.description;
        }
      });
      this.showLoader = false;
    }, error => {
      this.openECR(this.ecrDto);
      this.showLoader = false;
      if (error.applicationStatusCode === 3007) {
        this.snakbarService.error(error.message);
      }
    }));
  }

  syncOnhold(partNumberDto: EcrPartnumberDto,syncType:string) {
    // call epicore
    return new Promise(resolve => {
      partNumberDto.syncStatus = 'IN_PROGRESS';
      partNumberDto.disable = true;
      this.subscription.add(this.ecrManagementService.isPartnumberExistInEpicore(partNumberDto.partNumber).subscribe((res) => {
        if (res) {
          partNumberDto.syncStatus = 'IN_PROGRESS';
          partNumberDto.disable = true;
          // pn exist call epicore to sync
          this.subscription.add(this.ecrManagementService.syncPartNumberToEpicore(partNumberDto,syncType).subscribe(() => {
          }));
        } else {
          partNumberDto.syncStatus = 'FAILED';
          this.snakbarService.error(Messages.ECR_MANAGEMENT.Partnumber_Not_Exist);
        }
      }, (error) => {
        partNumberDto.syncStatus = 'FAILED';
        return false;
      }));
    });
  }

  getECRPartSyncStatus(partNumberDto: EcrPartnumberDto){
    this.ecrManagementService.getPartNumber(partNumberDto.id).subscribe((res) => {
      if(res && res!=undefined && res!=null){
        partNumberDto.syncStatus = res.syncStatus;
        partNumberDto.usOnHold = res.usOnHold;
        partNumberDto.usHoldReasonCode = res.usHoldReasonCode;
        partNumberDto.usHoldComments = res.usHoldComments;
        partNumberDto.usVietnamOnHold = res.usVietnamOnHold;
        partNumberDto.usVietnamHoldReasonCode = res.usVietnamHoldReasonCode;
        partNumberDto.usVietnamHoldComments = res.usVietnamHoldComments;
        partNumberDto.usCostaRicaOnHold = res.usCostaRicaOnHold;
        partNumberDto.usCostaRicaHoldReasonCode = res.usCostaRicaHoldReasonCode;
        partNumberDto.usCostaRicaHoldComments = res.usCostaRicaHoldComments;
        partNumberDto.vietnamOnHold = res.vietnamOnHold;
        partNumberDto.vietnamHoldReasonCode = res.vietnamHoldReasonCode;
        partNumberDto.vietnamHoldComments = res.vietnamHoldComments;
        partNumberDto.costaRicaOnHold = res.costaRicaOnHold;
        partNumberDto.costaRicaHoldReasonCode = res.costaRicaHoldReasonCode;
        partNumberDto.costaRicaHoldComments = res.costaRicaHoldComments;
      }
    })
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
