<div class="row g-4 mx-2">
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Quotation Status</h3>
      <mat-card-content>
        <div>
          <p>Update or add Quotation Status related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageQuotStatus()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Features</h3>
      <mat-card-content>
        <div>
          <p>Update or add Features related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageFeatures()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>PQP Family</h3>
      <mat-card-content>
        <div>
          <p>Update or add PQP Family related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="managePQPFamily()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Gold Standard Tape Width</h3>
      <mat-card-content>
        <div>
          <p>Update or add Gold Standard Tape Width related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageGoldStandardTapeWidth()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Alloy</h3>
      <mat-card-content>
        <div>
          <p>Update or add Alloy related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageAlloys()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>BHX Gold Wire Tape</h3>
      <mat-card-content>
        <div>
          <p>Update or add BHX Gold Wire Tape related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageGoldWireTapes()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Heating Tapes</h3>
      <mat-card-content>
        <div>
          <p>Update or add Heating Tapes related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageHeatingTapes()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>BHX Material</h3>
      <mat-card-content>
        <div>
          <p>Update or add BHX Material related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageBHXMaterial()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Departments</h3>
      <mat-card-content>
        <div>
          <p>Update or add Departments related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageDepartments()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>ECR Statuses</h3>
      <mat-card-content>
        <div>
          <p>Update or add ECR Statuses related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageECRStatuses()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Warps</h3>
      <mat-card-content>
        <div>
          <p>Update or add Warps related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageWarps()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Label Configuration Master</h3>
      <mat-card-content>
        <div>
          <p>Update or add Label Config related master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageLeadMaster()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>SolidWorks Block Master</h3>
      <mat-card-content>
        <div>
          <p>Update or add SolidWorks Block Master data here</p>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageSolidWorksMaster()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Eco Line Revision Master</h3>
      <mat-card-content>
        <p>Update or add Eco Line Revision related master data here</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageEcoLineRevisionMaster()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
  <div class="col-xl-3 col-md-4 col-sm-6 col-12 p-2">
    <mat-card class="master-data-card w-100 h-100 m-0">
      <h3>Estimated Engineering Release Date Master</h3>
      <mat-card-content>
        <p>Update or add estimated engineering release date master data here</p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button type="button" color="warn" (click)="manageEstimatedEngRelDate()">Manage</button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
