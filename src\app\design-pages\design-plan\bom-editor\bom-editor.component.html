<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<mat-card class="cust_fields">
  <div fxFLex="100">
    <div class="highlight">
      <div fxLayout="row wrap">
        <h4 fxFlex *ngIf="this.type === 'Usa'">
          Part {{ part?.jacketPartNumber ? ' - (' + part?.jacketPartNumber + ')' : '' }}</h4>
        <h4 fxFlex *ngIf="this.type === 'Vietnam'">
          Part {{ part?.jacketPartNumber ? ' (' + part?.jacketPartNumber + 'V)' : '' }}</h4>
        <h4 fxFlex *ngIf="this.type === 'Costa Rica'">
          Part {{ part?.jacketPartNumber ? ' (' + part?.jacketPartNumber + 'R)' : '' }}</h4>
        <div fxLayoutAlign="end">
          <div class="open-doc mt-5" *ngIf="lastSyncStatus !== null">
            <p
              class="bom-sync-status"
              [ngClass]="{ 'sync-error': !isSyncSuccess, 'sync-progress': syncInProcess }"
              matTooltip="Click here to check status"
              (click)="openDMTLog('All')"
            >
              {{ lastSyncStatus }}
            </p>
          </div>
          <div class="mr-20">
            <button mat-raised-button color="warn" *ngIf="checkDMTStatus" (click)="openDMTLog('All')">Check Status</button>
          </div>
          <div class="mr-20">
            <button mat-raised-button color="warn" (click)="openCopyBOMDialog()" [disabled]="syncInProcess">Copy BOM
            </button>
          </div>
          <div class="mr-20">
            <button mat-raised-button color="warn" (click)="syncToEpicor('All')" [disabled]="syncInProcess">Sync to Epicor
            </button>
          </div>
          <div class="mr-20">
            <button mat-raised-button color="warn" (click)="searchMfgPart()" [disabled]="syncInProcess">Search Mfg Part
            </button>
          </div>
          <div class="mr-20">
            <button mat-raised-button color="warn" (click)="exportBOMToExcel()" id="generate-excel">
              Export to Excel
            </button>
          </div>
          <div class="mr-20">
            <button mat-raised-button color="warn" (click)="refreshPartAndBomDataByJacketId()"
                    [disabled]="syncInProcess" id="refresh">
              Reset BOM
            </button>
          </div>
          <mat-form-field class="mt-10 mr-20">
            <mat-select placeholder="Made In" [(ngModel)]="type" (selectionChange)="onEpicorTypeChanged()">
              <mat-option value="Usa"> USA</mat-option>
              <mat-option value="Vietnam"> VIETNAM</mat-option>
              <mat-option value="Costa Rica"> Costa Rica</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <input matInput placeholder="Type" name="type" [(ngModel)]="part.type" readonly/>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <input matInput placeholder="Rev" name="revisionName" [(ngModel)]="part.revisionName" readonly/>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <mat-select placeholder="Product Group" [(ngModel)]="part.className"
                      (selectionChange)="updatePartListPrice(part)">
            <mat-option *ngFor="let class of partClass" [value]="class.type">
              {{ class?.type }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <!-- <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24">
          <input matInput placeholder="Class" name="className" [(ngModel)]="part.className" readonly />
        </mat-form-field> -->
        <mat-form-field fxFlex.gt-lg="12" fxFlex.gt-md="12" *ngIf="type!=='Costa Rica'">
          <input
            class="quantity"
            type="number"
            step="any"
            matInput
            placeholder="List Price"
            name="listPrice"
            [(ngModel)]="part.listPrice"
            (change)="updatePartListPrice(part)"
          />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="12" fxFlex.gt-md="12" *ngIf="type==='Costa Rica'">
          <input
            class="quantity"
            type="number"
            step="any"
            matInput
            placeholder="List Price"
            name="listPrice"
            value="0"
            (change)="updatePartListPrice(part)"
          />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="12" fxFlex.gt-md="12"  *ngIf="type!=='Costa Rica'">
          <input
            class="quantity"
            type="number"
            step="any"
            matInput
            placeholder="Net Price"
            name="netPrice"
            [(ngModel)]="part.netPrice"
            (change)="updatePartNetPrice(part)"
          />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="12" fxFlex.gt-md="12"  *ngIf="type==='Costa Rica'">
          <input
            class="quantity"
            type="number"
            step="any"
            matInput
            placeholder="Net Price"
            name="netPrice"
            value="0"
            (change)="updatePartNetPrice(part)"
          />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <textarea
            matInput
            placeholder="Description"
            cols="3"
            name="description"
            [(ngModel)]="part.description"
            (change)="updatePartDescription(part)"
          ></textarea>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
          <textarea matInput placeholder="Internal Cross References" cols="3" [(ngModel)]="part.internalCrossRef"
                    readonly></textarea>
        </mat-form-field>
      </div>
    </div>

    <div class="highlight-mat-table">
      <div fxLayout="row wrap">
        <h4 fxFlex class="header-text">Operations</h4>
        <div fxLayoutAlign="end center">
          <div class="ml-3">
            <div matSuffix>
              <mat-icon class="open-doc" (click)="resetBomBySection('Operations')" matTooltip="Reset Operations">
                cached
              </mat-icon>
            </div>
          </div>
          <mat-icon class="open-doc" matTooltip="Add Operation" (click)="openOperations()">add</mat-icon>
        </div>
      </div>

      <div class="example-container">
        <mat-table>
          <ng-container matColumnDef="sequence">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Seq.</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="opNumber">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Op. Num</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="operationName">
            <mat-header-cell class="desc-header" *matHeaderCellDef fxFlex="30%"> Operation</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="prodHrs">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Prod Hrs</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="setupHrs">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Setup Hrs</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="update">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Edit</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="delete">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Delete</mat-header-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="operationsdisplayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: operationsdisplayedColumns"></mat-row>
        </mat-table>
        <div class="example-container" dragula="OPERATION" [(dragulaModel)]="operationsDataSource">
          <div #table class="mat-table" *ngFor="let operation of operationsDataSource">
            <ng-container matColumnDef="sequence">
              <mat-cell fxFlex="15%" class="column">
                {{ operation.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="opNumber">
              <mat-cell fxFlex="15%" class="column">{{ operation.opNumber }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="operationName">
              <mat-cell fxFlex="30%" class="desc-column">{{ operation.operationName }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="prodHrs">
              <mat-cell fxFlex="15%" class="column">
                {{ operation.prodHrs }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="setupHrs">
              <mat-cell fxFlex="15%" class="column">
                {{ operation.setupHrs }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="update">
              <mat-cell fxFlex="5%" class="column">
                <mat-icon class="open-doc" matTooltip="Edit Operation" (click)="openEditDialog('operation',operation)">
                  edit
                </mat-icon>
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="delete">
              <mat-cell fxFlex="5%" class="column">
                <mat-icon class="open-doc" matTooltip="Delete Operation" (click)="deleteOperation(operation)">delete
                </mat-icon>
              </mat-cell>
            </ng-container>
          </div>
        </div>
      </div>
    </div>

    <div class="highlight-mat-table">
      <div fxLayout="row wrap">
        <h4 fxFlex class="header-text">Facing / Liner / Closure</h4>
        <div fxLayoutAlign="end center">
          <div class="ml-3">
            <div matSuffix>
              <mat-icon class="open-doc" (click)="resetBomBySection('FacingLinerClosure')"
                        matTooltip="Reset Facing/Liner/Closure"
              >cached
              </mat-icon>
            </div>
          </div>
          <mat-icon class="open-doc" matTooltip="Add Facing / Liner / Closure" (click)="openMaterial(3)">add</mat-icon>
        </div>
      </div>
      <div class="example-container">
        <mat-table>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Part number</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="description">
            <mat-header-cell class="desc-header" *matHeaderCellDef fxFlex="30%"> Description</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="qty">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Qty</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="uom">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> UOM</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="relOperation">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Rel. Op.</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="update">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Edit</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="delete">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Delete</mat-header-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="facingLinerClosureDisplayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: facingLinerClosureDisplayedColumns;"></mat-row>
        </mat-table>
        <div class="example-container" [(dragulaModel)]="facingLinerClosureDataSource"
             dragula="FACING">
          <div #table class="mat-table" *ngFor="let element of facingLinerClosureDataSource">
            <ng-container matColumnDef="partNumber">
              <mat-cell class="column" fxFlex="15%" [ngClass]="{partNumColor: (!element.partIncluded)}"> {{ element.partNumber }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-cell class="desc-column" fxFlex="30%"> {{ element.description }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="qty">
              <mat-cell class="column" fxFlex="15%"> {{ element.qty }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-cell class="column" fxFlex="15%"> {{ element.uom }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOperation">
              <mat-cell class="column" fxFlex="15%"> {{ element.relOperation }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="update">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Edit Material" (click)="openEditDialog('facing',element)">
                  edit
                </mat-icon>
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="delete">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Delete Material" (click)="deleteFacingLinerClosures(element)">
                  delete
                </mat-icon>
              </mat-cell>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
    <div class="highlight-mat-table">
      <div fxLayout="row wrap">
        <h4 fxFlex class="header-text">Elements</h4>
        <div fxLayoutAlign="end center">
          <div class="ml-3">
            <div matSuffix>
              <mat-icon class="open-doc" (click)="resetBomBySection('Elements')" matTooltip="Reset Element & Sensors"
              >cached
              </mat-icon>
            </div>
          </div>
          <mat-icon class="open-doc" matTooltip="Add Elements & Sensors" (click)="openMaterial(6)">add</mat-icon>
        </div>
      </div>
      <div class="example-container">
        <mat-table>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Part number</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="description">
            <mat-header-cell class="desc-header" *matHeaderCellDef fxFlex="30%"> Description</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="qty">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Qty</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="uom">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> UOM</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="relOperation">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Rel. Op.</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="update">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Edit</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="delete">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Delete</mat-header-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="facingLinerClosureDisplayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: facingLinerClosureDisplayedColumns;"></mat-row>
        </mat-table>
        <div class="example-container" dragula="ELEMENT" [(dragulaModel)]="elementsOnlyDataSource">
          <div #table class="mat-table" *ngFor="let element of elementsOnlyDataSource">
            <ng-container matColumnDef="partNumber">
              <mat-cell fxFlex="15%" class="column" [ngClass]="{partNumColor: (!element.partIncluded)}">
                {{ element.partNumber }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-cell fxFlex="30%" class="desc-column">
                {{ element.description }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="qty">
              <mat-cell fxFlex="15%" class="column">
                {{ element.qty }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-cell fxFlex="15%" class="column">
                {{ element.uom }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOperation">
              <mat-cell fxFlex="15%" class="column">
                {{ element.relOperation }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="update">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Edit Material" (click)="openEditDialog('sensors',element)">
                  edit
                </mat-icon>
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="delete">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Delete Material" (click)="deleteElement(element)">
                  delete
                </mat-icon>
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="elementsSensorsDisplayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: elementsSensorsDisplayedColumns"></mat-row>
          </div>
        </div>
      </div>
    </div>

    <div class="highlight-mat-table">
      <div fxLayout="row wrap">
        <h4 fxFlex class="header-text">Sensors</h4>
        <div fxLayoutAlign="end center">
          <div class="ml-3">
            <div matSuffix>
              <mat-icon class="open-doc" (click)="resetBomBySection('Sensors')" matTooltip="Reset Element & Sensors"
              >cached
              </mat-icon>
            </div>
          </div>
          <mat-icon class="open-doc" matTooltip="Add Elements & Sensors" (click)="openMaterial(2)">add</mat-icon>
        </div>
      </div>
      <div class="example-container">
        <mat-table>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Part Number</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="description">
            <mat-header-cell class="desc-header" *matHeaderCellDef fxFlex="30%"> Description</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="qty">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Qty</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="uom">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> UOM</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="relOperation">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Rel. Op.</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="update">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Edit</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="delete">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Delete</mat-header-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="sensorsDisplayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: sensorsDisplayedColumns"></mat-row>
        </mat-table>
        <!--        Header Started Ended-->
        <div class="example-container" dragula="SENSORS" [(dragulaModel)]="sensorsOnlyDataSource">
          <div class="mat-table" *ngFor="let element of sensorsOnlyDataSource">
            <ng-container matColumnDef="partNumber">
              <mat-cell fxFlex="15%" class="column" [ngClass]="{partNumColor: (!element.partIncluded)}">
                {{ element.partNumber }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-cell fxFlex="30%" class="desc-column">
                {{ element.description }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="qty">
              <mat-cell fxFlex="15%" class="column">
                {{ element.qty }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-cell fxFlex="15%" class="column">
                {{ element.uom }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOperation">
              <mat-cell fxFlex="15%" class="column">
                {{ element.relOperation }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="update">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Edit Material" (click)="openEditDialog('element',element)">
                  edit
                </mat-icon>
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="delete">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Delete Material" (click)="deleteSensors(element)">
                  delete
                </mat-icon>
              </mat-cell>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
    <div class="highlight-mat-table">
      <div fxLayout="row wrap">
        <h4 fxFlex class="header-text">Wiring / Plugging</h4>
        <div fxLayoutAlign="end center">
          <div class="ml-3">
            <div matSuffix>
              <mat-icon class="open-doc" (click)="resetBomBySection('WirePlugging')"
                        matTooltip="Reset Wiring/Plugging">
                cached
              </mat-icon>
            </div>
          </div>
          <mat-icon class="open-doc" matTooltip="Add Wiring / Plugging" (click)="openMaterial(4)">add</mat-icon>
        </div>
      </div>
      <div class="example-container">
        <mat-table>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Part Number</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="description">
            <mat-header-cell class="desc-header" *matHeaderCellDef fxFlex="30%"> Description</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="qty">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Qty</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="uom">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> UOM</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="relOperation">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Rel. Op.</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="update">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Edit</mat-header-cell>
          </ng-container>
          <ng-container matColumnDef="delete">
            <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Delete</mat-header-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="wiringPluggingDisplayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: wiringPluggingDisplayedColumns"></mat-row>
        </mat-table>
        <div class="example-container" dragula="WIRE" [(dragulaModel)]="wiringPluggingDataSource">
          <div class="mat-table" *ngFor="let element of wiringPluggingDataSource">
            <ng-container matColumnDef="partNumber">
              <mat-cell class="column" fxFlex="15%" [ngClass]="{partNumColor: (!element.partIncluded)}"> {{ element.partNumber }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-cell class="desc-column" fxFlex="30%"> {{ element.description }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="qty">
              <mat-cell class="column" fxFlex="15%"> {{ element.qty }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-cell class="column" fxFlex="15%"> {{ element.uom }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOperation">
              <mat-cell class="column" fxFlex="15%"> {{ element.relOperation }}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="update">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Edit Material" (click)="openEditDialog('wiring',element)">
                  edit
                </mat-icon>
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="delete">
              <mat-cell class="column" fxFlex="5%">
                <mat-icon class="open-doc" matTooltip="Delete Material" (click)="deleteWirePluggings(element)">
                  delete
                </mat-icon>
              </mat-cell>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="highlight-mat-table">
    <div fxLayout="row wrap">
      <h4 fxFlex class="header-text">Labels</h4>
      <div fxLayoutAlign="end center">
        <div class="ml-3">
          <div matSuffix>
            <mat-icon class="open-doc" (click)="resetBomBySection('Labels')" matTooltip="Reset Labels">cached
            </mat-icon>
          </div>
        </div>
        <mat-icon class="open-doc" matTooltip="Add Labels" (click)="openMaterial(1)">add</mat-icon>
      </div>
    </div>
    <div class="cust_table">
      <mat-table>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Part number</mat-header-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell class="desc-header" *matHeaderCellDef fxFlex="30%"> Description</mat-header-cell>
        </ng-container>
        <ng-container matColumnDef="qty">
          <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Qty</mat-header-cell>
        </ng-container>
        <ng-container matColumnDef="uom">
          <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> UOM</mat-header-cell>
        </ng-container>
        <ng-container matColumnDef="relOperation">
          <mat-header-cell class="header" *matHeaderCellDef fxFlex="15%"> Rel. Op.</mat-header-cell>
        </ng-container>
        <ng-container matColumnDef="update">
          <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Edit</mat-header-cell>
        </ng-container>
        <ng-container matColumnDef="delete">
          <mat-header-cell class="header" *matHeaderCellDef fxFlex="5%"> Delete</mat-header-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="labelsDisplayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: labelsDisplayedColumns"></mat-row>
      </mat-table>
      <div class="example-container" dragula="LABELS" [(dragulaModel)]="labelsDataSource">
        <div class="mat-table" *ngFor="let element of labelsDataSource">
          <ng-container matColumnDef="partNumber">
            <mat-cell class="column" fxFlex="15%" [ngClass]="{partNumColor: (!element.partIncluded)}"> {{ element.partNumber }}</mat-cell>
          </ng-container>
          <ng-container matColumnDef="description">
            <mat-cell class="desc-column" fxFlex="30%"> {{ element.description }}</mat-cell>
          </ng-container>
          <ng-container matColumnDef="qty">
            <mat-cell class="column" fxFlex="15%"> {{ element.qty }}</mat-cell>
          </ng-container>
          <ng-container matColumnDef="uom">
            <mat-cell class="column" fxFlex="15%"> {{ element.uom }}</mat-cell>
          </ng-container>
          <ng-container matColumnDef="relOperation">
            <mat-cell class="column" fxFlex="15%"> {{ element.relOperation }}</mat-cell>
          </ng-container>
          <ng-container matColumnDef="update">
            <mat-cell class="column" fxFlex="5%">
              <mat-icon class="open-doc" matTooltip="Edit Material" (click)="openEditDialog('labels',element)">
                edit
              </mat-icon>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="delete">
            <mat-cell class="column" fxFlex="5%">
              <mat-icon class="open-doc" matTooltip="Delete Material" (click)="deleteLabels(element)">
                delete
              </mat-icon>
            </mat-cell>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</mat-card>
<div>
  <!-- Labels Entry -->
  <mat-card class="cust_fields">
    <div class="highlight-mat-table">
      <div fxLayout="row wrap">
        <mat-card-title fxFlex>Labels Entry</mat-card-title>
        <div fxLayoutAlign="end center">
          <p
            *ngIf="lastSyncStatusLab !== null && lastSyncStatusLab!==undefined && lastSyncStatusLab!==''"
            class="bom-sync-status"
            [ngClass]="{ 'sync-error': !isSyncSuccessLab }"
            matTooltip="Click here to check status"
            (click)="openDMTLog('Lab')"
          >
            {{ lastSyncStatusLab }}
          </p>
          <mat-icon class="open-doc" (click)="resetlabelConfig()" matTooltip="Reset Labels Entry">cached</mat-icon>
        </div>
      </div>
      <div class="cust_table">
        <mat-table mat-table matSort matSortDisableClear [dataSource]="labelDetailedEntryDataSource">
          <ng-container matColumnDef="update">
            <mat-header-cell class="header-label" *matHeaderCellDef> Update</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              <mat-icon class="open-doc" matTooltip="Edit Material"
                        (click)="openEditDialog('labels-entry',label)">
                edit
              </mat-icon>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="image">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> Image</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              <button (click)="viewImage(label.imageUrl)">
                <mat-icon *ngIf="label.imageUrl;else elsePart" class='image'>image</mat-icon>
              </button>
              <ng-template #elsePart></ng-template>
              <!-- {{ !label.imageUrl ? <mat-icon class="image"></mat-icon> : null }} -->
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="toSync">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> To Sync</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              <mat-checkbox color="warn" class="effect quantity" [(ngModel)]="label.toSync"
                            (change)="checkIfAddOrEdited()"></mat-checkbox>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="labelPartNumber">
            <mat-header-cell class="header-label" *matHeaderCellDef> Label Part Number</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.labelPartNumber }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="format">
            <mat-header-cell class="header-label" *matHeaderCellDef> Format</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.format }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="volts">
            <mat-header-cell class="header-label" *matHeaderCellDef> Volts</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.volts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="watts">
            <mat-header-cell class="header-label" *matHeaderCellDef> Watts</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.watts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="amps">
            <mat-header-cell class="header-label" *matHeaderCellDef> Amps</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.amps }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="phase">
            <mat-header-cell class="header-label" *matHeaderCellDef> Phase</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.phase }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="size">
            <mat-header-cell class="header-label" *matHeaderCellDef> Size</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.size }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="modelNumber">
            <mat-header-cell class="header-label" *matHeaderCellDef> Model Number</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.modelNumber }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open1">
            <mat-header-cell class="header-label" *matHeaderCellDef> Open 1</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.open1 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open2">
            <mat-header-cell class="header-label" *matHeaderCellDef> Open 2</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.open2 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open3">
            <mat-header-cell class="header-label" *matHeaderCellDef> Open 3</mat-header-cell>
            <mat-cell class="column-label" *matCellDef="let label">
              {{ label.open3 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="width">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> Width</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.width }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="length">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> Length</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.length }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csv">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> CS Volts</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.csv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csa">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> CS Amps</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.csa }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhlv">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> MHL Volts</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.mhlv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhla">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> MHL Amps</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.mhla }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="tempRange">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> Temp Range</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.tempRange }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="lowT">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> Low Temp</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.lowT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="highT">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> High Temp</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.highT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="jacketPartNumber">
            <mat-header-cell class="header-label-width" *matHeaderCellDef> Part Number</mat-header-cell>
            <mat-cell class="column-label-width" *matCellDef="let label">
              {{ label.partNumber }}
            </mat-cell>
          </ng-container>
          <mat-header-row class="header-row" *matHeaderRowDef="labelsColumns; sticky: true"></mat-header-row>
          <mat-row class="row" *matRowDef="let row; columns: labelsColumns"></mat-row>
        </mat-table>
        <div class="no-records" *ngIf="!labelsDataSource?.length">No data found</div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="end">
      <mat-action-row>
        <div class="mr-20">
          <button color="warn" type="button" mat-raised-button (click)="syncToEpicor('Lab')">Sync Labels</button>
        </div>
      </mat-action-row>
      <mat-action-row>
        <div class="mr-20">
          <button color="warn" type="button" mat-raised-button (click)="updatelabelConfig()">Update</button>
        </div>
      </mat-action-row>
    </div>
  </mat-card>
</div>
