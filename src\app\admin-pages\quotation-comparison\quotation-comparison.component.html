<div class="filter less-peding">
  <mat-card>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxLayoutAlign="start" fxLayoutGap="10px">
        <div class="sub-heder" fxLayoutAlign="start center" fxLayoutGap="10px">
          <span><strong>Customer:</strong> {{quotation?.customerDTO?.name}}</span>
          <span><strong>Quotation# :</strong> {{quotation?.quotationNumber}}</span>
          <span><strong>SO # :</strong>{{quotation?.salesOrderNumber}}</span>
        </div>
        <div class="cust_fields" fxLayoutAlign="start center">
          <mat-form-field appearance="outline">
            <mat-label>Revision</mat-label>
            <mat-select [(ngModel)]="revisionId" (selectionChange)="onRevisionChanged($event.value)">
              <mat-option *ngFor="let rev of revisionList" [value]="rev.id">
                {{ rev.revisionName}}
                <span [ngClass]="{'lbl-red': rev.activeRevision === true}">{{rev.activeRevision === true ? 'Active'
                    : ''}}</span>
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div fxLayoutAlign="end">
        <div>
          <button mat-raised-button color="warn" (click)="goBack()"> Back to Quotation </button>&nbsp;
        </div>
      </div>
    </div>
  </mat-card>
</div>
<div class="mb-20"></div>
<div class="less-peding">
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxFlex.gt-lg="15" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="20">
      <mat-card class="comparions-lbl" fxLayoutAlign="center center">
        <mat-lable>Fields</mat-lable>
      </mat-card>
      <mat-card>
        <div class="side-lbl">
          <span>Workflow</span>
        </div>
        <div class="comparions-lbl comparions-workflow" fxLayout="column wrap" fxLayoutAlign="center end">
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.workflow?.approvalLevel, quotationComparison[1]?.workflow?.approvalLevel, quotationComparison[2]?.workflow?.approvalLevel]">
              Approval
              Level</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.workflow?.entryDate, quotationComparison[1]?.workflow?.entryDate, quotationComparison[2]?.workflow?.entryDate]">
              Entry
              Date</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.workflow?.shipDate, quotationComparison[1]?.workflow?.shipDate, quotationComparison[2]?.workflow?.shipDate]">
              Ship
              Date</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.workflow?.approvalFormatList | approvalFormats, quotationComparison[1]?.workflow?.approvalFormatList | approvalFormats, quotationComparison[2]?.workflow?.approvalFormatList | approvalFormats]">
              Approval
              Format</mat-label>
          </div>
          <div>
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.workflow?.markingList | markings, quotationComparison[1]?.workflow?.markingList | markings, quotationComparison[2]?.workflow?.markingList | markings]">
              Markings</mat-label>
          </div>
        </div>
      </mat-card>
      <mat-card>
        <div class="side-lbl">
          <span>App Info</span>
        </div>
        <div class="comparions-lbl comparions-appinfo" fxLayout="column wrap" fxLayoutAlign="center end">
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.jacketType, quotationComparison[1]?.appInfo?.jacketType, quotationComparison[2]?.appInfo?.jacketType]">
              Element
              Type</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.voltage, quotationComparison[1]?.appInfo?.voltage, quotationComparison[2]?.appInfo?.voltage]">
              Voltage</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.phase, quotationComparison[1]?.appInfo?.phase, quotationComparison[2]?.appInfo?.phase]">
              Phase</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.operatingTemp, quotationComparison[1]?.appInfo?.operatingTemp, quotationComparison[2]?.appInfo?.operatingTemp]">
              Operating
              Temp &nbsp;({{(tempUnit)?tempUnit : ''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.maxExposureTemp, quotationComparison[1]?.appInfo?.maxExposureTemp, quotationComparison[2]?.appInfo?.maxExposureTemp]">
              Max
              Exposure Temp&nbsp;({{(tempUnit)?tempUnit : ''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.minAmbientTemp, quotationComparison[1]?.appInfo?.minAmbientTemp, quotationComparison[2]?.appInfo?.minAmbientTemp]">
              Min
              Ambient Temp&nbsp;({{(tempUnit)?tempUnit : ''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.heatupFrom, quotationComparison[1]?.appInfo?.heatupFrom, quotationComparison[2]?.appInfo?.heatupFrom]">
              Heat
              up From&nbsp;({{(tempUnit)?tempUnit : ''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.heatupTo, quotationComparison[1]?.appInfo?.heatupTo, quotationComparison[2]?.appInfo?.heatupTo]">
              Heat
              up To&nbsp;({{(tempUnit)?tempUnit : ''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.heatupIn, quotationComparison[1]?.appInfo?.heatupIn, quotationComparison[2]?.appInfo?.heatupIn]">
              In
              (Hours)</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.pipeThickness, quotationComparison[1]?.appInfo?.pipeThickness, quotationComparison[2]?.appInfo?.pipeThickness]">
              Pipe
              Thickness &nbsp;({{(measurementUnit)?measurementUnit : ''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.contentMotion, quotationComparison[1]?.appInfo?.contentMotion, quotationComparison[2]?.appInfo?.contentMotion]">
              Content
              Motion</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.contentMotionFlowingRate, quotationComparison[1]?.appInfo?.contentMotionFlowingRate, quotationComparison[2]?.appInfo?.contentMotionFlowingRate]">
              Flowing
              Rate</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.materialName, quotationComparison[1]?.appInfo?.materialName, quotationComparison[2]?.appInfo?.materialName]">
              Pipe
              Material</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.materialSpecificHeat || quotationComparison[0]?.appInfo?.otherMaterialHeat, quotationComparison[1]?.appInfo?.materialSpecificHeat || quotationComparison[1]?.appInfo?.otherMaterialHeat, quotationComparison[2]?.appInfo?.materialSpecificHeat || quotationComparison[2]?.appInfo?.otherMaterialHeat]">
              Specific
              Heat (BTU/lb &nbsp;({{(tempUnit)?tempUnit : ''}}))</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.materialDensity || quotationComparison[0]?.appInfo?.otherMaterialDensity, quotationComparison[1]?.appInfo?.materialDensity || quotationComparison[1]?.appInfo?.otherMaterialDensity, quotationComparison[2]?.appInfo?.materialDensity || quotationComparison[2]?.appInfo?.otherMaterialDensity]">
              Density
              (lb/ft3)</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.contentName, quotationComparison[1]?.appInfo?.contentName, quotationComparison[2]?.appInfo?.contentName]">
              Pipe
              Contents</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.contentSpecificHeat || quotationComparison[0]?.appInfo?.otherContentHeat, quotationComparison[1]?.appInfo?.contentSpecificHeat || quotationComparison[1]?.appInfo?.otherContentHeat, quotationComparison[2]?.appInfo?.contentSpecificHeat || quotationComparison[2]?.appInfo?.otherContentHeat]">
              Specific
              Heat (BTU/lb &nbsp;({{(tempUnit)?tempUnit : ''}}))</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.contentDensity || quotationComparison[0]?.appInfo?.otherContentDensity, quotationComparison[1]?.appInfo?.contentDensity || quotationComparison[1]?.appInfo?.otherContentDensity, quotationComparison[2]?.appInfo?.contentDensity || quotationComparison[2]?.appInfo?.otherContentDensity]">
              Density
              (lb/ft3)</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.controlType, quotationComparison[1]?.appInfo?.controlType, quotationComparison[2]?.appInfo?.controlType]">
              Control
              Type</mat-label>
          </div>
          <div>
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.appInfo?.productType, quotationComparison[1]?.appInfo?.productType, quotationComparison[2]?.appInfo?.productType]">
              Product
              Type</mat-label>
          </div>
        </div>
      </mat-card>
      <mat-card>
        <div class="side-lbl">
          <span>Plugging</span>
        </div>
        <div class="comparions-lbl comparions-pluginfo" fxLayout="column wrap" fxLayoutAlign="center end">
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.sleevingTypeName != 'Other' ? quotationComparison[0]?.plugginginfo?.sleevingTypeName : quotationComparison[0]?.plugginginfo?.otherSleevingType, quotationComparison[1]?.plugginginfo?.sleevingTypeName != 'Other' ? quotationComparison[1]?.plugginginfo?.sleevingTypeName : quotationComparison[1]?.plugginginfo?.otherSleevingType, quotationComparison[2]?.plugginginfo?.sleevingTypeName != 'Other' ? quotationComparison[2]?.plugginginfo?.sleevingTypeName : quotationComparison[2]?.plugginginfo?.otherSleevingType]">
              Sleeving
              Type</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.strainReliefName != 'Other' ? quotationComparison[0]?.plugginginfo?.strainReliefName : quotationComparison[0]?.plugginginfo?.otherStrainRelief, quotationComparison[1]?.plugginginfo?.strainReliefName != 'Other' ? quotationComparison[1]?.plugginginfo?.strainReliefName : quotationComparison[1]?.plugginginfo?.otherStrainRelief, quotationComparison[2]?.plugginginfo?.strainReliefName != 'Other' ?quotationComparison[2]?.plugginginfo?.strainReliefName : quotationComparison[2]?.plugginginfo?.otherStrainRelief]">
              Strain
              Relief</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.greenLightName != 'Other' ? quotationComparison[0]?.plugginginfo?.greenLightName : quotationComparison[0]?.plugginginfo?.otherGreenLight , quotationComparison[1]?.plugginginfo?.greenLightName != 'Other' ? quotationComparison[1]?.plugginginfo?.greenLightName : quotationComparison[1]?.plugginginfo?.otherGreenLight , quotationComparison[2]?.plugginginfo?.greenLightName != 'Other' ? quotationComparison[2]?.plugginginfo?.greenLightName : quotationComparison[2]?.plugginginfo?.otherGreenLight]">
              Green
              Light</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.redLightName  != 'Other' ? quotationComparison[0]?.plugginginfo?.redLightName : quotationComparison[0]?.plugginginfo?.otherRedLight , quotationComparison[1]?.plugginginfo?.redLightName  != 'Other' ? quotationComparison[1]?.plugginginfo?.redLightName : quotationComparison[1]?.plugginginfo?.otherRedLight, quotationComparison[2]?.plugginginfo?.redLightName  != 'Other' ? quotationComparison[2]?.plugginginfo?.redLightName : quotationComparison[2]?.plugginginfo?.otherRedLight]">
              Red
              Light</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.leadPlugDTO.plugName != 'Other' ? quotationComparison[0]?.plugginginfo?.leadPlugDTO.plugName : quotationComparison[0]?.plugginginfo?.otherPlug, quotationComparison[1]?.plugginginfo?.leadPlugDTO.plugName != 'Other' ? quotationComparison[1]?.plugginginfo?.leadPlugDTO.plugName : quotationComparison[1]?.plugginginfo?.otherPlug, quotationComparison[2]?.plugginginfo?.leadPlugDTO.plugName != 'Other' ? quotationComparison[2]?.plugginginfo?.leadPlugDTO.plugName : quotationComparison[2]?.plugginginfo?.otherPlug]">
              Plug</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.jumperPlugDTO.plugName != 'Other' ? quotationComparison[0]?.plugginginfo?.jumperPlugDTO.plugName : quotationComparison[0]?.plugginginfo?.otherConnector, quotationComparison[1]?.plugginginfo?.jumperPlugDTO.plugName != 'Other' ? quotationComparison[1]?.plugginginfo?.jumperPlugDTO.plugName : quotationComparison[1]?.plugginginfo?.otherConnector, quotationComparison[2]?.plugginginfo?.jumperPlugDTO.plugName != 'Other' ? quotationComparison[2]?.plugginginfo?.jumperPlugDTO.plugName : quotationComparison[2]?.plugginginfo?.otherConnector]">
              Connector</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.leadPlugDTO.leadLength, quotationComparison[1]?.plugginginfo?.leadPlugDTO.leadLength, quotationComparison[2]?.plugginginfo?.leadPlugDTO.leadLength]">
              Lead
              Length({{(measurementUnit)? measurementUnit:''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.jumperPlugDTO.jumperLength, quotationComparison[1]?.plugginginfo?.jumperPlugDTO.jumperLength, quotationComparison[2]?.plugginginfo?.jumperPlugDTO.jumperLength]">
              Jumper
              Length
              ({{(measurementUnit)? measurementUnit:''}})</mat-label>
          </div>
          <div>
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.plugginginfo?.jumperPlugDTO.quantity, quotationComparison[1]?.plugginginfo?.jumperPlugDTO.quantity, quotationComparison[2]?.plugginginfo?.jumperPlugDTO.quantity]">
              Jumper
              Qty</mat-label>
          </div>
        </div>
      </mat-card>
      <mat-card>
        <div class="side-lbl">
          <span>Closure</span>
        </div>
        <div class="comparions-lbl comparions-closure" fxLayout="column wrap" fxLayoutAlign="center end">
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.closureInfo?.closureMaterialName != 'Other' ? quotationComparison[0]?.closureInfo?.closureMaterialName : quotationComparison[0]?.closureInfo?.otherClosure, quotationComparison[1]?.closureInfo?.closureMaterialName != 'Other' ? quotationComparison[1]?.closureInfo?.closureMaterialName : quotationComparison[1]?.closureInfo?.otherClosure, quotationComparison[2]?.closureInfo?.closureMaterialName != 'Other' ? quotationComparison[2]?.closureInfo?.closureMaterialName : quotationComparison[2]?.closureInfo?.otherClosure]">
              Closure</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.closureInfo?.extendedFlap, quotationComparison[1]?.closureInfo?.extendedFlap, quotationComparison[2]?.closureInfo?.extendedFlap]">
              Extended
              Flap</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.closureInfo?.cat5Tunnel, quotationComparison[1]?.closureInfo?.cat5Tunnel, quotationComparison[2]?.closureInfo?.cat5Tunnel]">
              Cat
              5 Tunnel</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.closureInfo?.estSurfaceTemp, quotationComparison[1]?.closureInfo?.estSurfaceTemp, quotationComparison[2]?.closureInfo?.estSurfaceTemp]">
              Est
              Surface Temp ({{(tempUnit)? tempUnit : ''}})</mat-label>
          </div>
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.closureInfo?.closureMaterialMaxTemp, quotationComparison[1]?.closureInfo?.closureMaterialMaxTemp, quotationComparison[2]?.closureInfo?.closureMaterialMaxTemp]">
              Max
              Temp ({{(tempUnit)? tempUnit : ''}})</mat-label>
          </div>
          <div>
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.closureInfo?.otherClosureCost ? quotationComparison[0]?.closureInfo?.otherClosureCost : quotationComparison[0]?.closureInfo?.closureMaterialCostPerSq, quotationComparison[1]?.closureInfo?.otherClosureCost ? quotationComparison[1]?.closureInfo?.otherClosureCost : quotationComparison[1]?.closureInfo?.closureMaterialCostPerSq, quotationComparison[2]?.closureInfo?.otherClosureCost ? quotationComparison[2]?.closureInfo?.otherClosureCost : quotationComparison[2]?.closureInfo?.closureMaterialCostPerSq]">
              Cost
              per foot</mat-label>
          </div>
        </div>
      </mat-card>
      <mat-card>
        <div class="comparions-lbl comparions-thermosate" fxLayout="column wrap" fxLayoutAlign="center end">
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare : [quotationComparison[0]?.thermostatInfo | thermostatType, quotationComparison[1]?.thermostatInfo | thermostatType, quotationComparison[2]?.thermostatInfo | thermostatType]">
              Thermostats</mat-label>
          </div>
        </div>
      </mat-card>
      <mat-card id="lblSensor">
        <div class="comparions-lbl  comparions-sensors" fxLayout="column wrap" fxLayoutAlign="center end">
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare: [quotationComparison[0]?.sensorInfo | sensors, quotationComparison[1]?.sensorInfo | sensors, quotationComparison[2]?.sensorInfo | sensors]">
              Sensors</mat-label>
          </div>
          <span>Sensor Type, Location, Connector, Lead Length</span>
        </div>
      </mat-card>
      <mat-card id="lblMaterial">
        <div class="comparions-lbl comparions-material" fxLayout="column wrap" fxLayoutAlign="center end">
          <div class="mb-10">
            <mat-label
              [ngClass]="'' | compare: [quotationComparison[0]?.materialinfo | material, quotationComparison[1]?.materialinfo | material, quotationComparison[2]?.materialinfo | material]">
              Materials</mat-label>
          </div>
          <span>Layer, Material</span>
        </div>
      </mat-card>
    </div>
    <div fxLayout="row wrap" fxFlex.gt-lg="85" fxFlex.gt-md="80">
      <div *ngFor="let info of quotationComparison; let i = index" fxFlex.gt-lg="33" fxFlex.gt-md="33">
        <div>
          <mat-card>
            <div class="side-remove-icon">
              <mat-icon class="open-doc" (click)="remove(i)">clear</mat-icon>
            </div>
            <mat-select placeholder="Jacket Group" name="info.jacketGroupId" [ngModel]="info.jacketGroupId"
              (selectionChange)="onJacketGroupChanged($event.value,i)">
              <mat-option *ngFor="let data of jacketGroup" [value]="data.id">
                {{ data.name }}
              </mat-option>
            </mat-select>
          </mat-card>
          <mat-card>
            <div class="comparions-workflow" fxLayout="column wrap" fxLayoutAlign="center center">
              <div class="mb-10" [ngClass]="{'under-line': info?.workflow?.approvalLevel !== null}">
                <mat-label>{{info?.workflow?.approvalLevel == undefined ? "-" : info?.workflow?.approvalLevel |
                  approvalLevel}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.workflow?.entryDate !== null}">
                <mat-label>{{info?.workflow?.entryDate ? info?.workflow?.entryDate: "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.workflow?.shipDate !== null}">
                <mat-label>{{info?.workflow?.shipDate ? info?.workflow?.shipDate: "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.workflow?.approvalFormatList !== null}">
                <mat-label>{{info?.workflow?.approvalFormatList.length
                  > 0 ? (info?.workflow?.approvalFormatList | approvalFormats) : "-"}}</mat-label>
              </div>
              <div [ngClass]="{'under-line': info?.workflow?.markingList !== null}">
                <mat-label>{{ info?.workflow?.markingList.length > 0 ? (info?.workflow?.markingList |
                  markings) : "-"}}</mat-label>
              </div>
            </div>
          </mat-card>
          <mat-card>
            <div class="comparions-appinfo" fxLayout="column wrap" fxLayoutAlign="center center">
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.jacketType !== null}">
                <mat-label>{{info?.appInfo?.jacketType ? info?.appInfo?.jacketType : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.voltage !== null}">
                <mat-label>{{info?.appInfo?.voltage ? info?.appInfo?.voltage : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.phase !== null}">
                <mat-label>{{info?.appInfo?.phase ? info?.appInfo?.phase : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.operatingTemp !== null}">
                <mat-label>{{info?.appInfo?.operatingTemp ? info?.appInfo?.operatingTemp:"-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.maxExposureTemp !== null}">
                <mat-label>{{info?.appInfo?.maxExposureTemp ? info?.appInfo?.maxExposureTemp : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.minAmbientTemp !== null}">
                <mat-label>{{info?.appInfo?.minAmbientTemp ? info?.appInfo?.minAmbientTemp: "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.heatupFrom !== null}">
                <mat-label>{{info?.appInfo?.heatupFrom ? info?.appInfo?.heatupFrom : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.heatupTo !== null}">
                <mat-label>{{info?.appInfo?.heatupTo ? info?.appInfo?.heatupTo : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.heatupIn !== null}">
                <mat-label>{{info?.appInfo?.heatupIn ? info?.appInfo?.heatupIn : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.pipeThickness !== null}">
                <mat-label>{{info?.appInfo?.pipeThickness ? info?.appInfo?.pipeThickness : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.contentMotion !== null}">
                <mat-label>{{info?.appInfo?.contentMotion ? info?.appInfo?.contentMotion : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.contentMotionFlowingRate !== null}">
                <mat-label>{{info?.appInfo?.contentMotionFlowingRate ?
                  info?.appInfo?.contentMotionFlowingRate : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.materialName !== null}">
                <mat-label>{{info?.appInfo?.materialName ? info?.appInfo?.materialName : "-"}}</mat-label>
              </div>
              <div class="mb-10"
                [ngClass]="{'under-line': info?.appInfo?.materialSpecificHeat !== null || info?.appInfo?.otherMaterialHeat !== null}">
                <mat-label>{{info?.appInfo?.materialSpecificHeat != null ? info?.appInfo?.materialSpecificHeat :
                  info?.appInfo?.otherMaterialHeat != null ? info?.appInfo?.otherMaterialHeat : "-"}}</mat-label>
              </div>
              <div class="mb-10"
                [ngClass]="{'under-line': info?.appInfo?.materialDensity !== null || info?.appInfo?.otherMaterialDensity !== null}">
                <mat-label>{{info?.appInfo?.materialDensity != null ? info?.appInfo?.materialDensity :
                  info?.appInfo?.otherMaterialDensity != null ? info?.appInfo?.otherMaterialDensity : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.contentName !== null}">
                <mat-label>{{info?.appInfo?.contentName ? info?.appInfo?.contentName : "-"}}</mat-label>
              </div>
              <div class="mb-10"
                [ngClass]="{'under-line': info?.appInfo?.contentSpecificHeat !== null || info?.appInfo?.otherContentHeat !== null}">
                <mat-label>{{info?.appInfo?.contentSpecificHeat != null ? info?.appInfo?.contentSpecificHeat :
                  info?.appInfo?.otherContentHeat != null ? info?.appInfo?.otherContentHeat : "-"}}</mat-label>
              </div>
              <div class="mb-10"
                [ngClass]="{'under-line': info?.appInfo?.contentDensity !== null || info?.appInfo?.otherContentDensity !== null}">
                <mat-label>{{info?.appInfo?.contentDensity != null ? info?.appInfo?.contentDensity :
                  info?.appInfo?.otherContentDensity != null ? info?.appInfo?.otherContentDensity : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.appInfo?.controlType !== null}">
                <mat-label>
                  {{info?.appInfo?.controlType != 'Other' ? info?.appInfo?.controlType : info?.appInfo?.otherControlType != null ? info?.appInfo?.otherControlType : "-"}}
                </mat-label>
              </div>
              <div [ngClass]="{'under-line': info?.appInfo?.productType}">
                <mat-label>{{info?.appInfo?.productType ? info?.appInfo?.productType : "-"}}</mat-label>
              </div>
            </div>
          </mat-card>
          <mat-card>
            <div class="comparions-pluginfo" fxLayout="column wrap" fxLayoutAlign="center center">
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.sleevingType !== null}">
                <mat-label>{{info?.plugginginfo?.sleevingTypeName != "Other" ? info?.plugginginfo?.sleevingTypeName :
                  info?.plugginginfo?.otherSleevingType != null ? info?.plugginginfo?.otherSleevingType : "-"}}
                </mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.strainRelief !== null}">
                <mat-label>{{info?.plugginginfo?.strainReliefName != "Other" ? info?.plugginginfo?.strainReliefName :
                  info?.plugginginfo?.otherStrainRelief != null ? info?.plugginginfo?.otherStrainRelief : "-"}}
                </mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.greenLightName !== null}">
                <mat-label>{{info?.plugginginfo?.greenLightName != "Other" ? info?.plugginginfo?.greenLightName :
                  info?.plugginginfo?.otherGreenLight != null ? info?.plugginginfo?.otherGreenLight : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.redLightName !== null}">
                <mat-label>{{info?.plugginginfo?.redLightName != "Other" ? info?.plugginginfo?.redLightName :
                  info?.plugginginfo?.otherRedLight != null ? info?.plugginginfo?.otherRedLight : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.leadPlugDTO.plugName !== null}">
                <mat-label>
                  {{info?.plugginginfo?.leadPlugDTO.plugName != "Other" ? info?.plugginginfo?.leadPlugDTO.plugName : info?.plugginginfo?.otherPlug != null ? info?.plugginginfo?.otherPlug  : "-"}}
                </mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.jumperPlugDTO.plugName !== null}">
                <mat-label>
                  {{info?.plugginginfo?.jumperPlugDTO.plugName != "Other" ? info?.plugginginfo?.jumperPlugDTO.plugName : info?.plugginginfo?.otherConnector != null ? info?.plugginginfo?.otherConnector : "-"}}
                </mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.leadPlugDTO.leadLength !== null}">
                <mat-label>{{info?.plugginginfo?.leadPlugDTO.leadLength ?
                  info?.plugginginfo?.leadPlugDTO.leadLength : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.plugginginfo?.jumperPlugDTO.jumperLength !== null}">
                <mat-label>{{info?.plugginginfo?.jumperPlugDTO.jumperLength ?
                  info?.plugginginfo?.jumperPlugDTO.jumperLength : "-"}}</mat-label>
              </div>
              <div [ngClass]="{'under-line': info?.plugginginfo?.jumperPlugDTO.quantity !== null}">
                <mat-label>{{info?.plugginginfo?.jumperPlugDTO.quantity ?
                  info?.plugginginfo?.jumperPlugDTO.quantity : "-"}}</mat-label>
              </div>
            </div>
          </mat-card>
          <mat-card>
            <div class="comparions-closure" fxLayout="column wrap" fxLayoutAlign="center center">
              <div class="mb-10" [ngClass]="{'under-line': info?.closureInfo?.closureMaterialName !== null}">
                <mat-label>{{info?.closureInfo?.closureMaterialName != "Other" ? info?.closureInfo?.closureMaterialName :
                  info?.closureInfo?.otherClosure != null ? info?.closureInfo?.otherClosure : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.closureInfo?.extendedFlap !== null}">
                <mat-label>{{info?.closureInfo?.extendedFlap == true ? "Yes" : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.closureInfo?.cat5Tunnel !== null}">
                <mat-label>{{info?.closureInfo?.cat5Tunnel == true ? "Yes" : "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.closureInfo?.estSurfaceTemp !== null}">
                <mat-label>{{info?.closureInfo?.estSurfaceTemp ? info?.closureInfo?.estSurfaceTemp :
                  "-"}}</mat-label>
              </div>
              <div class="mb-10" [ngClass]="{'under-line': info?.closureInfo?.closureMaterialMaxTemp !== null}">
                <mat-label>{{info?.closureInfo?.closureMaterialMaxTemp ?
                  info?.closureInfo?.closureMaterialMaxTemp : "-"}}</mat-label>
              </div>
              <div [ngClass]="{'under-line': info?.closureInfo?.closureMaterialCostPerSq !== null}">
                <mat-label>
                  {{(info?.closureInfo?.otherClosureCost) ? info?.closureInfo?.otherClosureCost : info?.closureInfo?.closureMaterialCostPerSq ? info?.closureInfo?.closureMaterialCostPerSq : "-"}}
                </mat-label>
              </div>
            </div>
          </mat-card>
          <mat-card>
            <div class="comparions-thermosate" fxLayout="column wrap" fxLayoutAlign="center center">
              <div class="mb-10" [ngClass]="{'under-line': info.thermostatInfo}">
                <mat-label>{{info?.thermostatInfo | thermostatType}}</mat-label>
              </div>
            </div>
          </mat-card>
          <mat-card>
            <div class="comparions-sensors" fxLayout="column wrap" fxLayoutAlign="center start">
              <div class="mb-10" [ngClass]="{'under-line': info.sensorInfo}"
                *ngFor="let data of info?.sensorInfo | sensors; let i = index">
                <mat-label><strong *ngIf="info?.sensorInfo.length > 0">{{i+1}})</strong>{{data}}</mat-label>
              </div>
            </div>
          </mat-card>
          <mat-card>
            <div class="comparions-material" fxLayout="column wrap" fxLayoutAlign="center start">
              <div class="mb-10" [ngClass]="{'under-line': info.materialinfo}"
                *ngFor="let data of info?.materialinfo | material; let i = index">
                <mat-label><strong *ngIf="info?.materialinfo.length > 0">{{i+1}})</strong>{{data}}</mat-label>
              </div>
            </div>
          </mat-card>
        </div>
      </div>
    </div>
  </div>
</div>