import { Component, OnInit, OnD<PERSON>roy, ViewChild } from '@angular/core';
import {
  PowerCordConnectorMaster,
  PowerCordConnectorPageable,
  PowerCordConnectorFilter,
  GenericPageable
} from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManagePowerCordConnectorComponent } from './manage-power-cord-connector/manage-power-cord-connector.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-power-cord-connectors',
  templateUrl: './power-cord-connectors.component.html'
})
export class PowerCordConnectorsComponent implements OnInit, OnDestroy {
  pageTitle = 'Power Cord Connector';
  powerCordConnector: PowerCordConnectorMaster;
  powerCordConnectorPageable: GenericPageable<PowerCordConnectorMaster>;
  powerCordConnectorDataSource = new MatTableDataSource<PowerCordConnectorMaster>();
  powerCordConnectorColumns = DisplayColumns.Cols.PowerCordConnector;
  powerCordConnectorFilter: PowerCordConnectorFilter = new PowerCordConnectorFilter();

  dataSource = new MatTableDataSource<PowerCordConnectorsComponent>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  length: number;

  showLoader = false;
  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldpartNumber = Values.FilterFields.partNumber;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getPowerCordMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new Power Cord Connector
  addPowerCordConnector() {
    this.editPowerCordConnector(new PowerCordConnectorMaster());
  }
  // used to add filter to Power Cord Connector listing
  async addFilter() {
    this.filter =
      this.powerCordConnectorFilter.partNumber === ''
        ? []
        : [{ key: this.filterFieldpartNumber, value: this.powerCordConnectorFilter.partNumber }];
    this.getPowerCordMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to Power Cord Connector listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldpartNumber,
        value:
          fieldToClear === this.filterFieldpartNumber
            ? (this.powerCordConnectorFilter.partNumber = '')
            : this.powerCordConnectorFilter.partNumber
      }
    ];
    this.getPowerCordMasterData(this.initialPageIndex, this.pageSize);
  }

  getPowerCordMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPowerCordConnectorMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<PowerCordConnectorMaster>) => {
          this.powerCordConnectorPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPowerCordConnectorTable(this.powerCordConnectorPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.powerCordConnectorDataSource.data = [];
        }
      )
    );
  }

  createPowerCordConnectorTable(serviceRequestList: GenericPageable<PowerCordConnectorMaster>) {
    this.powerCordConnectorDataSource.data = serviceRequestList.content;
  }

  getPowerCordConnectorPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPowerCordMasterData(this.pageIndex, this.pageSize);
  }

  getPowerCordConnectorSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPowerCordMasterData(this.pageIndex, this.pageSize);
  }

  editPowerCordConnector(powerCordConnector: PowerCordConnectorMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = powerCordConnector;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-power-cord-connector-model';
    const dialogRef = this.matDialog.open(ManagePowerCordConnectorComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          powerCordConnector.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPowerCordMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  async deletePowerCordConnector(powerCordConeectorId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePowerCordConnector(powerCordConeectorId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPowerCordMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
