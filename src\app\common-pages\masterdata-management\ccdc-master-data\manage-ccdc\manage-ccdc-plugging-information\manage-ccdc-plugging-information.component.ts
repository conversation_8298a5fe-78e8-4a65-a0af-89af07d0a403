import { Component, OnInit, Inject, OnD<PERSON>roy, Input } from '@angular/core';
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material';

import { FormGroup, NgForm } from '@angular/forms';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SharedService } from 'src/app/shared/service/shared.service';
import { GreenLight, JumperPlug, LeadPlug, LeadType, Plug, PluggingInformation, RedLight, SleevingType, StrainRelief } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { Values } from 'src/app/shared/constants/values.constants';
import { SalesOrderSummaryService } from 'src/app/admin-pages/new-quotation/summary-sales-order.service';
import { PluggingInformationTemplateDTO } from '../manage-ccdc.model';

@Component({
  selector: 'app-manage-ccdc-plugging-information',
  templateUrl: './manage-ccdc-plugging-information.component.html',
  styleUrls: ['./manage-ccdc-plugging-information.component.css']
})
export class ManageCcdcPluggingInformationComponent implements OnInit {
  @Input("pluggingInfoDto")
  pluggingInfoDto: PluggingInformationTemplateDTO;
  @Input("tempUnit")
  tempUnit: string;
  @Input("measureUnit")
  measureUnit: string;
  sleevingTypes: SleevingType[] = [];
  strainReliefs: StrainRelief[] = [];
  greenLights: GreenLight[] = [];
  redLights: RedLight[] = [];
  plugs: Plug[] = [];
  leadPlug = new Plug();
  jumperPlug = new Plug();
  subscription = new Subscription();
  isOtherLeadPlug = false;
  isOtherJumperPlug = false;
  isOtherSleevingType = false;
  isOtherStrainRelief = false;
  isOtherGreenLight = false;
  isOtherRedLight = false;
  leadPlugImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  jumperConnectorImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  leadTypesList: LeadType[] = [];
  leadType = new LeadType();
  activeJacketType: string;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  showReloadButton = false;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  constructor(
    private summarySalesOrderService: SalesOrderSummaryService
  ) { }

  async ngOnInit() {
    if(!this.pluggingInfoDto) {
    this.pluggingInfoDto = new PluggingInformationTemplateDTO();
    }
    this.getLeadTypesMaster();
    this.getPluginMasterData();
    await this.getPlugsByJacketType();
    this.getPluggingInformation();
  }

  // used to get master data for plugging
  getPluginMasterData() {
    this.showLoader = true;
    this.subscription.add(
      this.summarySalesOrderService.getPluginMasterData().subscribe(
        success => {
          this.sleevingTypes = success.sleevingTypes;
          this.strainReliefs = success.strainReliefs;
          this.redLights = success.redLights;
          this.greenLights = success.greenLights;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // gets the plug by currently active jacket type
  getPlugsByJacketType() {
    this.showLoader = true;
    return new Promise(resolve => {
    this.subscription.add(
      this.summarySalesOrderService.getPlugsByJacketType(this.activeJacketType).subscribe(
        (success: Plug[]) => {
          this.plugs = success;
          this.showLoader = false;
          resolve();
        },
        () => (this.showLoader = false)
      )
    );
      });
  }

  onSleevingTypeChanged(sleeveType) {
    this.isOtherSleevingType = false;
    if (sleeveType.source.triggerValue === Values.Other) {
      this.isOtherSleevingType = true;
    } else {
      this.pluggingInfoDto.otherSleevingType = null;
      this.pluggingInfoDto.otherSleevingTypePartNumber = null;
      this.pluggingInfoDto.otherSleevingTypeCost = null;
    }
  }

  onStrainReliefChanged(strainRelief) {
    this.isOtherStrainRelief = false;
    if (strainRelief.source.triggerValue === Values.Other) {
      this.isOtherStrainRelief = true;
    } else {
      this.pluggingInfoDto.otherStrainRelief = null;
      this.pluggingInfoDto.otherStrainReliefPartNumber = null;
      this.pluggingInfoDto.otherStrainReliefCost = null;
    }
  }

  onGreenLightChanged(greenLight) {
    this.isOtherGreenLight = false;
    if (greenLight.source.triggerValue === Values.Other) {
      this.isOtherGreenLight = true;
    } else {
      this.pluggingInfoDto.otherGreenLight = null;
      this.pluggingInfoDto.otherGreenLightPartNumber = null;
      this.pluggingInfoDto.otherGreenLightCost = null;
    }
  }

  onRedLightChanged(redLight) {
    this.isOtherRedLight = false;
    if (redLight.source.triggerValue === Values.Other) {
      this.isOtherRedLight = true;
    } else {
      this.pluggingInfoDto.otherRedLight = null;
      this.pluggingInfoDto.otherRedLightPartNumber = null;
      this.pluggingInfoDto.otherRedLightCost = null;
    }
  }

  // used to handle the lead plug change event
  onLeadPlugChanged(value) {
    this.isOtherLeadPlug = false;
    this.pluggingInfoDto.leadPlugDTO.plugId = value;
    const leadPlug = this.plugs.find(data => data.id === value);
    if (leadPlug) {
      this.leadPlug.id = leadPlug.id;
      this.pluggingInfoDto.leadPlugDTO.plugName = leadPlug.plugName;
      if (leadPlug.plugName === Values.Other) {
        this.isOtherLeadPlug = true;
      } else {
        this.leadPlug.partNumber = leadPlug.partNumber;
        this.leadPlug.maxAmps = leadPlug.maxAmps;
        this.leadPlug.maxVolts = leadPlug.maxVolts;
        this.leadPlug.plugCost = leadPlug.plugCost;
        this.leadPlug.imageUrl = leadPlug.imageUrl;
        this.pluggingInfoDto.otherPlug = null;
        this.pluggingInfoDto.otherPlugPartNumber = null;
        this.pluggingInfoDto.otherPlugCost = null;
      }

      if (leadPlug.imageUrl) {
        this.leadPlugImageUrl = environment.IMAGES_URL + this.leadPlug.imageUrl;
      } else {
        this.leadPlugImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
      }
    }
  }

  // used to handle the jumper plug change event
  onJumperPlugChanged(value) {
    this.isOtherJumperPlug = false;
    this.pluggingInfoDto.jumperPlugDTO.plugId = value;
    const jumperPlug = this.plugs.find(data => data.id === value);
    if (jumperPlug) {
      this.jumperPlug.id = jumperPlug.id;
      this.pluggingInfoDto.jumperPlugDTO.plugName = jumperPlug.plugName;
      if (jumperPlug.plugName === Values.Other) {
        this.isOtherJumperPlug = true;
      } else {
        this.jumperPlug.jumperPartNumber = jumperPlug.jumperPartNumber;
        this.jumperPlug.maxAmps = jumperPlug.maxAmps;
        this.jumperPlug.maxVolts = jumperPlug.maxVolts;
        this.jumperPlug.plugCost = jumperPlug.plugCost;
        this.jumperPlug.imageUrl = jumperPlug.imageUrl;
        this.pluggingInfoDto.otherConnector = null;
        this.pluggingInfoDto.otherConnectorPartNumber = null;
        this.pluggingInfoDto.otherConnectorCost = null;
      }

      if (jumperPlug.imageUrl) {
        this.jumperConnectorImageUrl = environment.IMAGES_URL + this.jumperPlug.imageUrl;
      } else {
        this.jumperConnectorImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
      }
    }
  }

  // used to get the plugging info by jacket group id
  getPluggingInformation() {
          if (this.pluggingInfoDto !== null) {
            if (this.pluggingInfoDto.leadPlugDTO !== null) {
              this.onLeadPlugChanged(this.pluggingInfoDto.leadPlugDTO.plugId);
            }
            if (this.pluggingInfoDto.jumperPlugDTO !== null) {
              this.onJumperPlugChanged(this.pluggingInfoDto.jumperPlugDTO.plugId);
            }
            if (this.pluggingInfoDto.sleevingTypeName === Values.Other) {
              this.isOtherSleevingType = true;
            }
            if (this.pluggingInfoDto.strainReliefName === Values.Other) {
              this.isOtherStrainRelief = true;
            }
            if (this.pluggingInfoDto.greenLightName === Values.Other) {
              this.isOtherGreenLight = true;
            }
            if (this.pluggingInfoDto.redLightName === Values.Other) {
              this.isOtherRedLight = true;
            }
            if (this.pluggingInfoDto.leadTypeDTO !== null) {
              this.onLeadTypeChanged(this.pluggingInfoDto.leadTypeDTO.id);
            }
          }
        }

  // retrieve all the master leadtypes
  getLeadTypesMaster() {
    this.showLoader = true;
    this.subscription.add(
      this.summarySalesOrderService.getLeadTypeMaster().subscribe(
        (leadTypes: LeadType[]) => {
          this.leadTypesList = leadTypes;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to handle the lead type change
  onLeadTypeChanged(leadTypeId) {
    const leadType = this.leadTypesList.find(data => data.id === leadTypeId);
    if (leadType) {
      this.leadType.id = leadType.id;
      this.pluggingInfoDto.leadTypeDTO = leadType;
      this.leadType.partNumber = leadType.partNumber;
      this.leadType.maxVolts = leadType.maxVolts;
      this.leadType.maxTemp = leadType.maxTemp;
      this.leadType.costPerFoot = leadType.costPerFoot;
    }
  }

  // used to handle page reload
  reloadPage(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

