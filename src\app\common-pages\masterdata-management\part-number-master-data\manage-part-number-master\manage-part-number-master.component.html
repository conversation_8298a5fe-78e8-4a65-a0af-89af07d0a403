<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #partNumberControllerForm="ngForm" (ngSubmit)="updateMasterPartNumber()">
  <mat-dialog-content>
    <div fxLayout="column" fxLayoutAlign="space-between">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-select placeholder="Facings"  [(ngModel)]="masterPartNumber.facings" name="FacingsIdentifierType"  required multiple>
            <mat-option #matOptionFacings (click)="facingIdentifierType(identifierTypes,matOptionFacings.selected)" *ngFor="let identifierTypes of identifierList?.uniqueIdentifiers" [value]="identifierTypes">
             {{identifierTypes}}
            </mat-option>
          </mat-select>
          <mat-error>This field is required</mat-error>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-select placeholder="Insulation" [(ngModel)]="masterPartNumber.insulations" name="InsulationsIdentifierType" required multiple>
            <mat-option #matOptionInsulations (click)="insulationIdentifierType(identifierTypes,matOptionInsulations.selected)" *ngFor="let identifierTypes of identifierList?.uniqueIdentifiers" [value]="identifierTypes">
              {{ identifierTypes }}
            </mat-option>
          </mat-select>
          <mat-error>This field is required</mat-error>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-select placeholder="Liner" [(ngModel)]="masterPartNumber.liners" name="LinersIdentifierType" required multiple>
            <mat-option #matOptionLiners (click)="linerIdentifierType(identifierTypes,matOptionLiners.selected)" *ngFor="let identifierTypes of identifierList?.uniqueIdentifiers" [value]="identifierTypes">
              {{ identifierTypes }}
            </mat-option>
          </mat-select>
          <mat-error>This field is required</mat-error>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Number" name="number" [(ngModel)]="masterPartNumber.number" #numberInput="ngModel" required />
          <mat-error>This field is required</mat-error>
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" [disabled]="partNumberControllerForm.invalid" color="warn">Submit</button>
    </div>
  </mat-dialog-actions>
</form>