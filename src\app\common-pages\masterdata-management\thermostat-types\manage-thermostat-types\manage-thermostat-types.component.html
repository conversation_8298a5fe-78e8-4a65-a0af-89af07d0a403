<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #thermostatTypeForm="ngForm" (ngSubmit)="updateThermostatType()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Thermostat Type"
            [(ngModel)]="thermostatType.id"
            name="thermostatType"
            #thermostatTypeInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="thermostatTypeInput.touched && thermostatTypeInput.invalid">
          <small class="mat-text-warn" *ngIf="thermostatTypeInput?.errors?.required">Thermostat Type is required.</small>
          <small class="mat-text-warn" *ngIf="thermostatTypeInput?.errors?.whitespace && !thermostatTypeInput?.errors?.required"
            >Invalid Thermostat Type.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="thermostatType.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!thermostatTypeForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
