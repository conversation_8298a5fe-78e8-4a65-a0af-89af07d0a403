
import {catchError, map} from 'rxjs/operators';
import { AccessoriesData } from './Add Accessories/add-accessories.model';
import { Injectable } from '@angular/core';
import { AppConfig } from '../../app.config';
import { utils } from '../../shared/helpers/app.helper';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class AccessoriesService {
  constructor(private readonly http: HttpClient) {}

  getAllJacketsAccessories(revisionId) {
    return this.http
      .get(AppConfig.GET_ALL_JACKETS_ACCESSORIES + revisionId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getAccessoriesList() {
    return this.http.get(AppConfig.GET_ALL_ACCESSORIES_LIST).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  saveAllAccesories(accessoriesData: AccessoriesData[], revisionId) {
    return this.http
      .post(AppConfig.SAVE_ACCESSORIES_LIST + '/saveAll/' + revisionId, accessoriesData).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  updateAccessory(accessoriesData: AccessoriesData) {
    return this.http.put(AppConfig.SAVE_ACCESSORIES_LIST, accessoriesData).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getAccessoriesByRevisionId(revisionId) {
    return this.http
      .get(AppConfig.SAVE_ACCESSORIES_LIST.concat('/revision/').concat(revisionId)).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteAccessory(id) {
    return this.http.delete(AppConfig.SAVE_ACCESSORIES_LIST.concat('/').concat(id)).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  applyDiscountToAllAccessory(requestObject) {
    return this.http
      .post(AppConfig.SAVE_ACCESSORIES_LIST + '/applyDiscount', requestObject).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getAccessoryControllers() {
    return this.http.get(AppConfig.GET_ALL_ACCESSORY_CONTROLLERS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  applyJacketDiscountToAllJacket(revisionId, jacketDiscount) {
    return this.http
      .put(AppConfig.JACKET_DISCOUNT + revisionId + '/discount/' + jacketDiscount, null).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getPowerCordConnectors() {
    return this.http.get(AppConfig.POWER_CORD_CONNECTORS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  updateRevOrder(accessories: AccessoriesData[]) {
    return this.http.put(AppConfig.UPDATE_REV_ORDER, accessories).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getPowerCordMaterialsList() {
    return this.http.get(AppConfig.POWER_CORD_MATERIALS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getPowerCordVoltage() {
    return this.http.get(AppConfig.POWER_CORD_VOLTAGES).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getPowerCordAmps() {
    return this.http.get(AppConfig.POWER_CORD_AMPS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getPowerCordOptions() {
    return this.http.get(AppConfig.POWER_CORD_OPTIONS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
}
