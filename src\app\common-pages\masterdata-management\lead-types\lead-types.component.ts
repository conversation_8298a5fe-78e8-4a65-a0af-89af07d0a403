import { Component, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { LeadTypesMaster, GenericPageable, LeadTypesMasterFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SnakbarService, SweetAlertService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageLeadTypesComponent } from './manage-lead-types/manage-lead-types.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-lead-types',
  templateUrl: './lead-types.component.html'
})
export class LeadTypesComponent implements OnInit, OnDestroy {
  pageTitle = 'Lead Type';
  leadTypeMaster: LeadTypesMaster;
  leadTypeMasterPageable: GenericPageable<LeadTypesMaster>;
  leadTypeMasterDataSource = new MatTableDataSource<LeadTypesMaster>();
  leadTypeMasterMasterColumns = DisplayColumns.Cols.LeadTypeMaster;
  leadTypeFilter: LeadTypesMasterFilter = new LeadTypesMasterFilter();

  dataSource = new MatTableDataSource<LeadTypesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  showLoader = false;
  numberOfElements: number;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldPartNumber = Values.FilterFields.partNumber;
  filterFieldLeadName = Values.FilterFields.leadName;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) {}

  ngOnInit() {
    this.getLeadTypeMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // Handles the adding of new lead type master data. calls editLeadType() expects new object of LeadTypesMaster to create new else existing LeadTypesMaster to update
  addLeadType(): void {
    this.editLeadType(new LeadTypesMaster());
  }

  // sets the filter for leads master data table. using partnumber field to filter the data
  async addFilter() {
    this.filter = [
      { key: this.filterFieldPartNumber, value: !this.leadTypeFilter.partNumber ? '' : this.leadTypeFilter.partNumber },
      { key: this.filterFieldLeadName, value: !this.leadTypeFilter.leadName ? '' : this.leadTypeFilter.leadName }
    ];
    this.getLeadTypeMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear the filter for lead type listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber,
        value: fieldToClear === this.filterFieldPartNumber ? (this.leadTypeFilter.partNumber = '') : this.leadTypeFilter.partNumber
      },
      {
        key: this.filterFieldLeadName,
        value: fieldToClear === this.filterFieldLeadName ? (this.leadTypeFilter.leadName = '') : this.leadTypeFilter.leadName
      }
    ];
    this.getLeadTypeMasterData(this.initialPageIndex, this.pageSize);
  }

  // gets the lead type master data as pageable
  getLeadTypeMasterData(pageIndex, pageSize): void {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getLeadTypesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<LeadTypesMaster>) => {
          this.leadTypeMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createLeadTypeTable(this.leadTypeMasterPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.leadTypeMasterDataSource.data = [];
        }
      )
    );
  }

  // used to create pageable data table with provided master data list in serviceRequestList
  createLeadTypeTable(serviceRequestList: GenericPageable<LeadTypesMaster>): void {
    this.leadTypeMasterDataSource.data = serviceRequestList.content;
  }

  // provids the pagination, applies when user traverse back and forth using pagination
  getLeadTypeMasterPagination(event): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getLeadTypeMasterData(this.pageIndex, this.pageSize);
  }

  // provids the sorting, applies when user sorts any fields which is sorting enabled
  getLeadTypeMasterSorting(event): void {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getLeadTypeMasterData(this.pageIndex, this.pageSize);
  }

  // used to handle the edit/ update of any lead type master data, opens up ManageLeadTypesComponent in modal where user can update the record
  editLeadType(leadType: LeadTypesMaster): void {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = leadType;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-lead-types-model';
    const dialogRef = this.matDialog.open(ManageLeadTypesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          leadType.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getLeadTypeMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  // used to handle the deletion of the master data record
  async deleteLeadType(leadTypeId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteLeadType(leadTypeId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getLeadTypeMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
