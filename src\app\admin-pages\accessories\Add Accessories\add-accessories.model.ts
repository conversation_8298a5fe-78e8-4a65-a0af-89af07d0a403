export class AccessoriesData {
    constructor(
        public id?: number,
        public partNumber?: string,
        public description?: string,
        public listPrice?: number,
        public controllerName?: string,
        public discount?: number,
        public revisionId?: number
    ) { }
}

export class RevisionAccessoriesData {
    constructor(
        public accessoryId?: number,
        public discount?: number,
        public extendedNetPrice?: number,
        public id?: number,
        public marginpublic?: number,
        public netPrice?: number,
        public qty?: number,
        public revisionId?: number,
        public listPrice?: number,
        public description?: string,
        public partNumber?: string,
        public accessoryIndex?: number
    ) { }
}

export class AccessoryControllers {
    constructor(
        public id?: number,
        public name?: string
    ) { }
}
