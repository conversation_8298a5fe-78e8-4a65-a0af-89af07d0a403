<mat-toolbar class="main-header bg-dark">
  <button (click)="toggleSidenav.emit()" mat-icon-button>
    <mat-icon>menu</mat-icon>
  </button>
  <div class="branding">
    <div (click)="goToDashboard()" class="logo open-doc"></div>
  </div>
  <samp fxFlex></samp>
  <button *ngIf="!isFullScreen" (click)="fullScreenToggle()" mat-icon-button>
    <mat-icon>fullscreen</mat-icon>
  </button>
  <button *ngIf="isFullScreen" (click)="fullScreenToggle()" mat-icon-button>
    <mat-icon>fullscreen_exit</mat-icon>
  </button>
  <button [matMenuTriggerFor]="user" mat-icon-button class="ml-xs">
    <mat-icon>person</mat-icon>
  </button>
  <mat-menu #user="matMenu" x-position="before">
    <button mat-menu-item id="switchtosuperadmin" (click)="switchToSuperAdmin()">
      <mat-icon>supervisor_account</mat-icon>
      Super Admin
    </button>
    <button mat-menu-item id="switchtoappadmin" (click)="switchToAppAdmin()">
      <mat-icon>supervisor_account</mat-icon>
      Switch to App.Eng
    </button>
    <button mat-menu-item id="switchtodesign" (click)="switchToDesign()">
      <mat-icon>supervisor_account</mat-icon>
      Switch to Design
    </button>
    <button mat-menu-item id="switchtoquotetracker" (click)="switchToQuoteTracker()">
      <mat-icon>timeline</mat-icon>
      Trackers
    </button>
    <a href="/view-jacket-reference" routerLink="/view-jacket-reference" target="_blank">
      <button mat-menu-item id="switchtojacketreference">
        <mat-icon>timeline</mat-icon>
        BOM Reference
      </button>
    </a>
    <button mat-menu-item id="logout" (click)="logout()">
      <mat-icon>exit_to_app</mat-icon>
      Log Out
    </button>
  </mat-menu>
</mat-toolbar>
