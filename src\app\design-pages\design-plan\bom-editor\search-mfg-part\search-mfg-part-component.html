<h2 mat-dialog-title>Search Manufacturing Part Number
  <hr>
</h2>
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<form #addQuotationForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-card-content>
      <div fxLayout="row wrap">
        <div fxFlex fxLayoutAlign="start center">
          <mat-form-field fxFlex.gt-lg="100%" fxFlex.gt-md="100%" fxFlex.gt-sm="100%" fxFlex.gt-xs="100%">
            <input matInput placeholder="Manufacturing Part Number" ng-trim="true" [(ngModel)]="partNumber"
                   autofocus name="partNumber" #partNumberInput="ngModel" required>
          </mat-form-field>
          <div style="padding-left: 15px" fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-xs="15" fxFlex="100">
            <button mat-raised-button color="warn" type="submit" (click)="searchEcr()"
                    [disabled]="addQuotationForm.invalid">Search
            </button>
          </div>
          <div fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-xs="15" fxFlex="100">
            <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
          </div>
        </div>
      </div>
      <mat-card>
        <mat-table [dataSource]="mfgParts">
          <ng-container matColumnDef="part-number">
            <mat-header-cell *matHeaderCellDef fxFlex="10%">Part Number</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10%">
              {{ element?.partNum }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="10%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="name">
            <mat-header-cell *matHeaderCellDef fxFlex="20%"> Manufacturer</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20%">
              {{ element?.name }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="20%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="uom">
            <mat-header-cell *matHeaderCellDef fxFlex="5%"> UOM</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="5%">
              {{ element?.uom }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="5%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="part-desc">
            <mat-header-cell *matHeaderCellDef fxFlex="35%"> Description</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="35%">
              {{ element?.partDescription }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="35%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="desc">
            <mat-header-cell *matHeaderCellDef fxFlex="20%"> Material Class</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20%">
              {{ element?.description }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="20%"></mat-footer-cell>
          </ng-container>
          <ng-container matColumnDef="company">
            <mat-header-cell *matHeaderCellDef fxFlex="10%"> Company</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10%">
              {{ element?.company }}
            </mat-cell>
            <mat-footer-cell *matFooterCellDef fxFlex="10%"></mat-footer-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="searchMFGPartsColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: searchMFGPartsColumns;"></mat-row>
          <mat-footer-row *matFooterRowDef="searchMFGPartsColumns; sticky: true"
                          [ngClass]="{'hide':mfgParts.data.length === 0}"></mat-footer-row>
        </mat-table>
        <div class="no-records" *ngIf="mfgParts.data.length<=0">
          No data found
        </div>
      </mat-card>
    </mat-card-content>
  </mat-dialog-content>
  <hr>
</form>
