import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AppEnggQuoteTrackerRowColourComponent } from './app-engg-quote-tracker-row-colour.component';

describe('AppEnggQuoteTrackerRowColourComponent', () => {
  let component: AppEnggQuoteTrackerRowColourComponent;
  let fixture: ComponentFixture<AppEnggQuoteTrackerRowColourComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AppEnggQuoteTrackerRowColourComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppEnggQuoteTrackerRowColourComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
