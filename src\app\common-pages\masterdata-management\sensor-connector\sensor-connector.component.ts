import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { SensorConnectorsAndTypesMaster, GenericPageable, SensorConnectorsAndTypesFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { ManageSensorConnectorComponent } from './manage-sensor-connector/manage-sensor-connector.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-sensor-connector',
  templateUrl: './sensor-connector.component.html'
})
export class SensorConnectorComponent implements OnInit, OnDestroy {
  pageTitle = 'Sensor Connector Master';
  sensConnector: SensorConnectorsAndTypesMaster;
  sensConnectorPageable: GenericPageable<SensorConnectorsAndTypesMaster>;
  sensConnectorDataSource = new MatTableDataSource<SensorConnectorsAndTypesMaster>();
  sensConnectorColumns = DisplayColumns.Cols.SensorConnectorsAndTypes;

  dataSource = new MatTableDataSource<SensorConnectorsAndTypesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  sensConnectorFilter: SensorConnectorsAndTypesFilter = new SensorConnectorsAndTypesFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldId = Values.FilterFields.id;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getSensConnectorsMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new sensor connectors
  addSensorConnector() {
    this.editSensConnectors(new SensorConnectorsAndTypesMaster());
  }

  // used to add filter to sensor connectors listing
  async addFilter() {
    this.filter = this.sensConnectorFilter.id === '' ? [] : [{ key: this.filterFieldId, value: this.sensConnectorFilter.id }];
    this.getSensConnectorsMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of sensor connectors listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldId,
        value: fieldToClear === this.filterFieldId ? (this.sensConnectorFilter.id = '') : this.sensConnectorFilter.id
      }
    ];
    this.getSensConnectorsMasterData(this.initialPageIndex, this.pageSize);
  }

  getSensConnectorsMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getSensorConnectorsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<SensorConnectorsAndTypesMaster>) => {
          this.sensConnectorPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createSensConnectorsTable(this.sensConnectorPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createSensConnectorsTable(serviceRequestList: GenericPageable<SensorConnectorsAndTypesMaster>) {
    this.sensConnectorDataSource.data = serviceRequestList.content;
  }

  getSensConnectorsPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getSensConnectorsMasterData(this.pageIndex, this.pageSize);
  }

  getSensConnectorsSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getSensConnectorsMasterData(this.pageIndex, this.pageSize);
  }

  editSensConnectors(sensorConnector: SensorConnectorsAndTypesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = sensorConnector;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-sens-connector-master-model';
    const dialogRef = this.matDialog.open(ManageSensorConnectorComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          sensorConnector.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getSensConnectorsMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteSensConnectors(sensorConnectorId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteSensorConnectors(sensorConnectorId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getSensConnectorsMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
