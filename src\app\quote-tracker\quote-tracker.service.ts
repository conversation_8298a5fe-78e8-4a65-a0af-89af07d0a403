import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import 'rxjs/add/operator/catch';
import 'rxjs/add/operator/map';
import { AppConfig } from '../app.config';
import { createRequestOption } from '../shared';
import { utils } from '../shared/helpers';
import {
  AppEngineeringQuoteTracker,
  DesignEngineeringQuoteTracker,
  FilterDateRange,
  FilterPageable,
  FilterQuoteTracker,
  GenericPageable,
  QuoteCompletedDetailed
} from './quote-tracker.model';
@Injectable({
  providedIn: 'root'
})
export class QuoteTrackerService {
  getQuoteIdObs$: BehaviorSubject<DesignEngineeringQuoteTracker> = new BehaviorSubject(new DesignEngineeringQuoteTracker());
  constructor(private readonly http: HttpClient) {}

  //for design quote tracker row highlighting

  getRowQuoteId(): Observable<DesignEngineeringQuoteTracker> {
    return this.getQuoteIdObs$.asObservable();
  }

  setRowQuoteId(designRowHighlighting: DesignEngineeringQuoteTracker) {
    this.getQuoteIdObs$.next(designRowHighlighting);
  }

  designRowHighlighting(id: number, colorValue: string, quoteStatusId) {
    return this.http.put(AppConfig.DESIGN_ROW_HIGHLIGHTING + id + '/' + colorValue + '/' + quoteStatusId, {}).map(utils.extractData).catch(utils.handleError);
  }

  designColumnHighlighting(id: number, columnName: string, colorValue: string) {
    return this.http.put(AppConfig.DESIGN_COLUMN_HIGHLIGHTING + id + '/' + columnName + '/' + colorValue, {}).map(utils.extractData).catch(utils.handleError);
  }

  appRowHighlighting(id: number, colorValue: string, quoteStatusId) {
    return this.http.put(AppConfig.APP_ROW_HIGHLIGHTING + id + '/' + colorValue + '/' + quoteStatusId, {}).map(utils.extractData).catch(utils.handleError);
  }

  appColumnHighlighting(id: number, columnName: string, colorValue: string) {
    return this.http.put(AppConfig.APP_COLUMN_HIGHLIGHTING + id + '/' + columnName + '/' + colorValue, {}).map(utils.extractData).catch(utils.handleError);
  }

  // used to get the SO in design and its dollar value
  getSoInDesign(soInDesignFilter) {
    return this.http.post(AppConfig.SO_IN_DESIGN_COUNT_SUMMARY, soInDesignFilter).map(utils.extractData).catch(utils.handleError);
  }

  // used to get the SO in design details by provided type and within date range
  getSoInDesignByType(filter) {
    return this.http.post(AppConfig.SO_IN_DESIGN_DETAILED, filter).map(utils.extractData).catch(utils.handleError);
  }

  // used to get the counter numbers for all the quotes
  getQuoteStatusCounts() {
    return this.http.get(AppConfig.STATUS_COUNT).map(utils.extractData).catch(utils.handleError);
  }

  // used to get the available sales associates
  getSalesAssociate(isGlobalSearch: boolean) {
    return this.http
      .get(AppConfig.SalesAssociate + '/' + isGlobalSearch)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  // used to get the completed quotes and its dollar value
  getQuoteCompleted(filter) {
    return this.http.post(AppConfig.QUOTES_COMPLETED, filter).map(utils.extractData).catch(utils.handleError);
  }
  // used to get the completed quotes and its dollar value
  getQuoteCompletedDetailed(
    pageableObject: FilterPageable,
    filter: FilterDateRange,
    productType: string
  ): Observable<GenericPageable<QuoteCompletedDetailed>> {
    return this.http
      .post(AppConfig.QUOTES_COMPLETED_DETAILED + `${productType}`, filter, {
        params: createRequestOption(pageableObject)
      })
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  // used to geT the counter of quotes statuses based on the date range selected
  getQuotesCounterBasedOnTheDateRange(filter) {
    return this.http.post(AppConfig.QUOTES_STATUS_COUNT_BY_DATE, filter).map(utils.extractData).catch(utils.handleError);
  }

  // used to get the app engg dashboard listing
  getAppEngQuoteTracker(
    pageableObject: FilterPageable,
    filterDTO: FilterQuoteTracker
  ): Observable<GenericPageable<AppEngineeringQuoteTracker>> {
    return this.http
      .post(AppConfig.APP_ENGG_QUOTE_TRACKER_DASHBOARD, filterDTO, {
        params: createRequestOption(pageableObject)
      })
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  // used to get the app engg dashboard listing
  getDesignEngQuoteTracker(
    pageableObject: FilterPageable,
    filterDTO: FilterQuoteTracker
  ): Observable<GenericPageable<DesignEngineeringQuoteTracker>> {
    return this.http
      .post(AppConfig.DESIGN_ENGG_QUOTE_TRACKER_DASHBOARD, filterDTO, {
        params: createRequestOption(pageableObject)
      })
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  downloadAppEnggQuoteExcel(filter){
      return this.http.post(AppConfig.EXPORT_TO_EXCEL,filter, { 'responseType': 'blob' })
          .map(utils.extractData).catch(utils.handleError);
  }

  downloadDesignQuoteTrackerExcel(filter){
    return this.http.post(AppConfig.EXPORT_TO_EXCEL_DESIGN_QUOTE,filter, { 'responseType': 'blob' })
        .map(utils.extractData).catch(utils.handleError);
}

  getAccountManagers() {
    return this.http.get(AppConfig.ACCOUNT_MANAGERS).map(utils.extractData).catch(utils.handleError);
  }

  updateColumnValues(data): Observable<any> {
    return this.http.patch(AppConfig.UPDATE_QUOTE_TRACKER_COLUMNS, data);
  }
}
