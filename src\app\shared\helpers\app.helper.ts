
import {throwError as observableThrowError,  Observable } from 'rxjs';
import { Response } from '@angular/http';

export const utils: any = {
    handleError: (error: Response | any) => {
        let errMsg: string;
        if (error instanceof Response) {
            const body = error.json() || '';
            const err = body['error'] || JSON.stringify(body);
            errMsg = `${error.status} - ${error.statusText || ''} ${err}`;
        } else {
            errMsg = error.message ? error.message : error.toString();
        }
        return observableThrowError(errMsg);
    },
    extractData: (res: any) => {
        return res;
    },
};

