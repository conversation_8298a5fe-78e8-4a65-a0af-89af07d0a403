import {SensorService} from './add-sensors.service';
import {FormControl, NgForm} from '@angular/forms';
import {Values} from './../../../shared/constants/values.constants';
import {SensorConnector, SensorInformation, SensorInformationObject, Units} from './../ccdc-model/ccdc.model';
import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef, MatTableDataSource} from '@angular/material';
import {Messages, SharedService, SnakbarService, SweetAlertService} from '../../../shared';
import {Observable, Subscription} from 'rxjs';
import {
  SensorConnectorsMaster,
  SensorTypesMaster,
  LaborMaster
} from 'src/app/common-pages/masterdata-management/masterdata-management.model';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {map, startWith} from 'rxjs/operators';
import {ManageUnitsService} from '../manage-units/manage-units.service';

@Component({
  selector: 'sfl-add-sensor',
  templateUrl: './add-sensors.component.html'
})
export class AddSensorsComponent implements OnInit, OnDestroy {
  jacketGroupId: number;
  sensorInfos: SensorInformation[];
  sensorInfo: SensorInformation;
  sensorInfoObject: SensorInformationObject;
  sensorTypes: SensorTypesMaster[];
  sensorTypesDetails: SensorTypesMaster;
  sensorTypesObservable$: Observable<SensorTypesMaster[]>;
  sensorTypesControl = new FormControl();
  sensorLocations: object = Values.SensorLocation;
  sensorConnectors: SensorConnectorsMaster[];
  sensorConnectorsDetails: SensorConnectorsMaster;
  sensorConnectorsObservable$: Observable<SensorConnectorsMaster[]>;
  sensorConnectorsControl = new FormControl();
  subscription: Subscription = new Subscription();
  sensorsType: LaborMaster[];
  SensorsdisplayedColumns = ['sensorType', 'sensorLocation', 'sensorConnector', 'sensorLeadLength', 'sensorTempType', 'action'];
  sensorsDataSource = new MatTableDataSource<SensorInformation>();
  measureUnit = '';
  isOtherSensorType = false;
  isOtherSensorLocation = false;
  isOtherSensorConnector = false;
  controlType = '';
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  lastFilter = '';
  selection: string;

  constructor(
    private snakbarService: SnakbarService,
    public sensorsDialogRef: MatDialogRef<AddSensorsComponent>,
    public sensorService: SensorService,
    private sharedService: SharedService,
    private sweetAlertService: SweetAlertService,
    private manageUnitService: ManageUnitsService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketGroupId = data.jacketGroupId;
    this.controlType = data.controlType;
  }

  ngOnInit() {
    this.getMeasurementUnit();
    this.sensorInfo = new SensorInformation();
    this.sensorInfos = new Array<SensorInformation>();
    this.sensorInfoObject = new SensorInformationObject();
    this.getSensorsList();
    this.getSensorsConnectors();
    this.getSensorsTypes();
    this.sensorInfo.sensorLocation = 'Inside Jacket Through Liner';
    this.getThermostatTypes();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.measureUnit = res.measurementUnit;
          }
        }
      )
    );
  }

  // used to get the sensors listing
  getSensorsList() {
    this.showLoader = true;
    this.subscription.add(
      this.sensorService.getSensorList(this.jacketGroupId).subscribe(
        (res: SensorInformationObject) => {
          if (res) {
            this.sensorInfoObject = res;
            this.sensorsDataSource.data = this.sensorInfoObject.sensorsInformationDTOList;
            this.sensorInfos = this.sensorInfoObject.sensorsInformationDTOList;
            this.showLoader = false;
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to get the sensors conntectors
  getSensorsConnectors() {
    this.showLoader = true;
    this.subscription.add(
      this.sensorService.getSensorConnectorsList().subscribe(
        (sensorConnector: SensorConnectorsMaster[]) => {
          if (sensorConnector) {
            this.sensorConnectors = sensorConnector;
            this.sensorConnectorsObservable$ = this.sensorConnectorsControl.valueChanges.pipe(
              startWith<string | SensorConnectorsMaster[]>(''),
              map(value => (typeof value === 'string' ? value : this.lastFilter)),
              map(filter => this.filterSensorConnector(filter)),
              map(values => values.sort(function (a, b) {
                return a.id > b.id ? 1 : -1;
              }))
            );
          }
          this.showLoader = false;
        },
        error => {
          this.sensorConnectors = [];
          this.showLoader = false;
        }
      )
    );
  }

  // used to get listing of sensors types
  getSensorsTypes() {
    this.showLoader = true;
    this.subscription.add(
      this.sensorService.getSensorTypesList().subscribe(
        (sensorTypes: SensorTypesMaster[]) => {
          if (sensorTypes) {
            this.sensorTypes = sensorTypes;
            this.sensorTypesObservable$ = this.sensorTypesControl.valueChanges.pipe(
              startWith<string | SensorTypesMaster[]>(''),
              map(value => (typeof value === 'string' ? value : this.lastFilter)),
              map(filter => this.filterSensorTypes(filter)),
              map(values => values.sort(function (a, b) {
                return a.id > b.id ? 1 : -1;
              }))
            );
          }
          this.showLoader = false;
        },
        error => {
          this.sensorConnectors = [];
          this.showLoader = false;
        }
      )
    );
  }

  displaySensorTypes(value: SensorTypesMaster[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          material.id;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  displaySensorConnectors(value: SensorConnectorsMaster[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          material.id;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  onSelectionSensorTypes(data: SensorTypesMaster) {
    if (data) {
      this.sensorTypesDetails = data;
      this.sensorTypesControl.setValue(this.sensorTypesDetails.id);
    }
  }

  onSelectionSensorConnectors(data: SensorConnectorsMaster) {

    if (data.id !== Values.Other) {
      this.sensorInfo.sensorConnector.id = data.id;
      this.sensorConnectorsControl.setValue(this.sensorInfo.sensorConnector.id);
    } else {
      this.isOtherSensorConnector = true;
      this.sensorInfo.sensorConnector.id = data.id;
    }
  }


  filterSensorTypes(filter: string): SensorTypesMaster[] {
    this.lastFilter = filter;
    if (filter) {
      return this.sensorTypes.filter(option => {
        if (option.id !== null) {
          return (
            option.id.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.sensorTypes ? this.sensorTypes.slice() : [];
    }
  }


  filterSensorConnector(filter: string): SensorConnectorsMaster[] {
    this.lastFilter = filter;
    if (filter) {
      return this.sensorConnectors.filter(option => {
        if (option.id !== null) {
          return (
            option.id.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.sensorConnectors ? this.sensorConnectors.slice() : [];
    }
  }

  // used to get listing of thermostat types
  getThermostatTypes() {
    this.showLoader = true;
    this.subscription.add(
      this.sensorService.getThermostatTypeList().subscribe(
        (res: LaborMaster[]) => {
          this.sensorsType = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to handle the sensor type change event
  onSensorTypeChanged(sensorType) {
    this.isOtherSensorType = false;
    if (sensorType === Values.Other) {
      this.isOtherSensorType = true;
    }
  }

  // used to get listing of sensors types
  onSensorLocationChanged(sensorLocation) {
    this.isOtherSensorLocation = false;
    if (sensorLocation === Values.Other) {
      this.isOtherSensorLocation = true;
    }
  }

  // used to handle the sensor connector change event
  onSensorConnectorChanged(sensorConnector) {
    this.isOtherSensorConnector = false;
    if (sensorConnector === Values.Other) {
      this.isOtherSensorConnector = true;
    }
  }

  // used to handle the save/ update of the sensor info modal
  saveSensor(value) {
    this.showLoader = true;
    this.sensorInfoObject.sensorsInformationDTOList = this.sensorInfos;
    this.sensorInfoObject.sensorsInformationDTOList.forEach(sensorInfo => {
      sensorInfo.jacketGroupId = this.jacketGroupId;
      if (sensorInfo.sensorType && sensorInfo.sensorType !== Values.Other) {
        sensorInfo.otherSensorType = null;
      }
      if (sensorInfo.sensorLocation && sensorInfo.sensorLocation !== Values.Other) {
        sensorInfo.otherSensorLocation = null;
      }
      if (sensorInfo.sensorConnector && !sensorInfo.sensorConnector.id) {
        sensorInfo.sensorConnector = null;
      }
      if (sensorInfo.sensorConnector && sensorInfo.sensorConnector.id !== Values.Other) {
        sensorInfo.otherSensorConnector = null;
      }
    });
    this.subscription.add(
      this.sensorService.saveSensorInfo(this.sensorInfoObject, this.jacketGroupId).subscribe(
        (res: SensorInformationObject) => {
          if (res.sensorsInformationDTOList.length) {
            this.sensorInfos = res.sensorsInformationDTOList;
            const obj = {data: res, mode: value === 'save' ? 0 : 1};
            this.sensorsDialogRef.close(obj);
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to add selected senser to the listing
  addToSensor(form: NgForm) {
    let tempTypeCount = false;
    this.sensorsDataSource.data.forEach(sensor => {
      if (sensor.sensorTempType.toLowerCase() === 'control') {
        tempTypeCount = true;
      }
    });
    if (this.controlType.toLowerCase() === 'lynx' && this.sensorInfo.sensorTempType.toLowerCase() === 'control' && tempTypeCount) {
      this.snakbarService.error(Messages.SENSOR_INFO.Sensor_Temp_Type, 3000);
    } else {
      this.sensorInfos.push(this.sensorInfo);
      this.sensorsDataSource.data = this.sensorInfos;
      this.sensorInfo = new SensorInformation();
      this.sensorInfo.sensorType.id = '';
      this.sensorInfo.sensorConnector.id = '';
      form.resetForm();
    }
  }

  // used to handle the updation of the sensor
  editFromSensor(sensorInfo: SensorInformation) {
    this.isOtherSensorType = false;
    this.isOtherSensorLocation = false;
    this.isOtherSensorConnector = false;
    this.sensorsDataSource.data.splice(this.sensorsDataSource.data.indexOf(sensorInfo), 1);
    this.sensorsDataSource._updateChangeSubscription();
    this.sensorInfo = sensorInfo;
    if (this.sensorInfo.sensorType === Values.Other) {
      this.isOtherSensorType = true;
    }
    if (this.sensorInfo.sensorLocation === Values.Other) {
      this.isOtherSensorLocation = true;
    }
    if (this.sensorInfo.sensorConnector && this.sensorInfo.sensorConnector.id === Values.Other) {
      this.isOtherSensorConnector = true;
    }
    if (!this.sensorInfo.sensorConnector) {
      this.sensorInfo.sensorConnector = new SensorConnector();
    }
  }

  // used to handle
  async removeFromSensor(ele) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      if (ele.id) {
        this.subscription.add(
          this.sensorService.deleteSensorInfo(ele.id).subscribe(res => {
            this.sensorsDataSource.data.splice(this.sensorsDataSource.data.indexOf(ele), 1);
            this.sensorsDataSource._updateChangeSubscription();
          })
        );
        this.showLoader = false;
      } else {
        this.sensorsDataSource.data.splice(this.sensorsDataSource.data.indexOf(ele), 1);
        this.sensorsDataSource._updateChangeSubscription();
        this.showLoader = false;
      }
    }
  }

  closeDialog() {
    this.sensorsDialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
