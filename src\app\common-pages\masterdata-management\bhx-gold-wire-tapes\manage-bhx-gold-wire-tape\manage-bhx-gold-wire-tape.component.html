<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #goldWireTapeForm="ngForm" (ngSubmit)="updateGoldWireTape()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Alloy Name" [(ngModel)]="goldWireTape.alloyName" name="alloyName" #alloyNameInput="ngModel" required>
            <mat-option *ngFor="let alloy of alloys" [value]="alloy.alloyName">
              {{ alloy.alloyName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="alloyNameInput.touched && alloyNameInput.invalid">
          <small class="mat-text-warn" *ngIf="alloyNameInput?.errors?.required">Alloy name is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <div>
          <button mat-stroked-button type="button" color="warn" (click)="manageAlloys()">Manage Alloys</button>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Type" [(ngModel)]="goldWireTape.type" name="type" #typeInut="ngModel" required>
            <mat-option *ngFor="let type of types" [value]="type">
              {{ type }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="typeInut.touched && typeInut.invalid">
          <small class="mat-text-warn" *ngIf="typeInut?.errors?.required">Type is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            type="number"
            placeholder="TPI STR"
            [(ngModel)]="goldWireTape.tpiStr"
            name="tpiStr"
            #tpiStrInput="ngModel"
            sflIsDecimal
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            type="number"
            placeholder="Picks"
            [(ngModel)]="goldWireTape.picks"
            name="picks"
            #picksInput="ngModel"
            sflIsDecimal
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Wire Type" [(ngModel)]="goldWireTape.wireType" name="wireType" #wireTypeInut="ngModel">
            <mat-option *ngFor="let wireType of wireTypes" [value]="wireType">
              {{ wireType }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="goldWireTape.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!goldWireTapeForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
