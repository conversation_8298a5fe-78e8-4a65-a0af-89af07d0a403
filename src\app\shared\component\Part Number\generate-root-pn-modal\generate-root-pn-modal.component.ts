import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { MatDialogRef } from '@angular/material';
import { Values } from 'src/app/shared/constants/values.constants';
import { SnakbarService } from 'src/app/shared/service/snakbar.service';
import { Messages } from 'src/app/shared/constants/messages.constants';
import {RootPNModel} from './rootPNModel';

@Component({
  selector: 'sfl-generate-root-pn-modal',
  templateUrl: './generate-root-pn-modal.component.html'
})
export class GenerateRootPNModalComponent implements OnInit, OnDestroy {

  rootPN: string;
  constructor(private dialogRef: MatDialogRef<GenerateRootPNModalComponent>, private snakbarService: SnakbarService) { }

  ngOnInit() {
  }

  createRootPN() {
    const rootPNModel = new RootPNModel();
    rootPNModel.rootPN = this.rootPN;
    rootPNModel.openModel='createRootPN';
    this.dialogRef.close(rootPNModel);
  }

  generateNonStandardPN() {
    const rootPNModel = new RootPNModel();
    rootPNModel.rootPN = this.rootPN;
    rootPNModel.openModel='generateNonStandardPN';
    this.dialogRef.close(rootPNModel);
  }

  closeDialog() {
    this.dialogRef.close();
  }

  ngOnDestroy() { }
}
