import { Component, OnInit, Inject, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';

@Component({
  selector: 'sfl-prompt-update-all',
  templateUrl: './prompt-update-all.component.html'
})
export class PromptUpdateAllComponent implements OnInit, OnDestroy {

  showLoader = false;
  oppositeType: string;
  currentType: string;

  constructor(public dialogRef: MatDialogRef<PromptUpdateAllComponent>, @Inject(MAT_DIALOG_DATA) data) {
    this.oppositeType = data.oppositeType;
    this.currentType = data.currentType;
  }

  ngOnInit() {
  }

  updateResponse(updateAll: boolean) {
    this.dialogRef.close(updateAll);
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() { }

}
