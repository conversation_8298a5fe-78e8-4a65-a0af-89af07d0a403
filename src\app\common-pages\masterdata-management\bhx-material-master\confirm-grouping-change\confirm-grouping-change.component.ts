import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material';

@Component({
  selector: 'sfl-confirm-grouping-change',
  templateUrl: './confirm-grouping-change.component.html'
})
export class ConfirmGroupingChangeComponent {
  title = 'Attention! Confirm Grouping Change';
  constructor(public readonly dialogRef: MatDialogRef<ConfirmGroupingChangeComponent>) {}

  changeGrouping() {
    this.dialogRef.close(true);
  }
  closeDialog(): void {
    this.dialogRef.close(false);
  }
}
