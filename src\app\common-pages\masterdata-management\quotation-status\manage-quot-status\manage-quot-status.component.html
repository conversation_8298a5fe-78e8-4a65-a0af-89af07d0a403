<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #quotationStatusForm="ngForm" (ngSubmit)="updateQuotStatus()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            sflNoWhiteSpaces
            placeholder="Quotation Status"
            [(ngModel)]="quotStatus.status"
            name="quotstatus"
            #quotstatusInput="ngModel"
            [disabled]="this._data.isEdit"
            required
          />
        </mat-form-field>
        <div *ngIf="quotstatusInput.touched && quotstatusInput.invalid">
          <small class="mat-text-warn" *ngIf="quotstatusInput?.errors?.required">Quotation Status is required.</small>
          <small class="mat-text-warn" *ngIf="quotstatusInput?.errors?.whitespace && !quotstatusInput?.errors?.required"
            >Invalid Quotation Status.</small
          >
        </div>
      </div>
      <div fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="50" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Type" [(ngModel)]="quotStatus.type" name="quottype" #quotTypeInut="ngModel" required>
            <mat-option *ngFor="let type of quotTypes" [value]="type.id">
              {{ type.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="quotTypeInut.touched && quotTypeInut.invalid">
          <small class="mat-text-warn" *ngIf="quotTypeInut?.errors.required">Quotation Status Type is required.</small>
        </div>
      </div>

    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="35" fxFlex.gt-md="35" fxFlex.gt-sm="35" fxFlex.gt-xs="35">
        <mat-form-field>
          <mat-select placeholder="Select Design Eng Row Colour" [(ngModel)]="quotStatus.rowColor" name ="designColorSelect" #designSelectedColour>
            <mat-option *ngFor="let color of highlightedColors" [value]="color?.value" [ngStyle]="{'background': color?.value}">
              {{ color?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="35" fxFlex.gt-md="35" fxFlex.gt-sm="35" fxFlex.gt-xs="35">
        <mat-form-field>
          <mat-select placeholder="Select App Eng Row Colour" [(ngModel)]="quotStatus.appEngRowColor" name ="appColorSelect" #appSelectedColour>
            <mat-option *ngFor="let color of highlightedColors" [value]="color?.value" [ngStyle]="{'background': color?.value}">
              {{ color?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
        <mat-form-field>
          <input 
          matInput 
          sflNoWhiteSpaces
          placeholder="Order Number" 
          [(ngModel)]="quotStatus.orderNumber" 
          name="orderNumber" 
          #orderNumberInput="ngModel" 
          required 
          />
        </mat-form-field>
        <div *ngIf="orderNumberInput.touched && orderNumberInput.invalid">
          <small class="mat-text-warn" *ngIf="orderNumberInput?.errors?.required">Order Number is required.</small>
          <small class="mat-text-warn" *ngIf="orderNumberInput?.errors?.whitespace && !orderNumberInput?.errors?.required">Invalid Order Number.</small>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
        <mat-checkbox name="forApproval" fxLayoutAlign="space-between" color="warn" [(ngModel)]="quotStatus.forApproval">For Approval</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" fxLayoutAlign="space-between" color="warn" [(ngModel)]="quotStatus.isObsolete">Is Obsolete</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
        <mat-checkbox name="defaultHidden" fxLayoutAlign="space-between" color="warn" [(ngModel)]="quotStatus.defaultHidden">Default Hidden</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!quotationStatusForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
