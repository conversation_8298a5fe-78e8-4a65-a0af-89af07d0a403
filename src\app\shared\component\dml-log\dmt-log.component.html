<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div fxLayout="row">
  <div fxFlex>
    <h2 mat-dialog-title>
      {{ title }}
      <hr />
    </h2>
  </div>
  <mat-icon class="open-doc" (click)="refreshDMTLog()" id="refresh">cached</mat-icon>
</div>
<mat-dialog-content>
  <div *ngFor="let data of dmtResMap | keyvalue; let lastIndex = last">
    <h1>
      <strong>{{ data.key }}</strong>
    </h1>
    <pre class="dmtlog">{{ data.value }}</pre>
    <ng-container *ngIf="!lastIndex">
      <hr />
    </ng-container>
  </div>
</mat-dialog-content>
<hr />
<mat-dialog-actions>
  <button mat-raised-button type="submit" (click)="closeDialog()">Close</button>
</mat-dialog-actions>
