<!-- So In Design Detailed view Content for Grid -->
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayoutAlign="start center">
      <div>
        <mat-card-title>{{ quoteCompletedTitle }}</mat-card-title>
      </div>
      <div fxLayoutAlign="end" style="margin-left: auto;">
        <div class="box-layout">
          <h4>Total Number of Design: {{ totalNoOfDesign }}</h4>
        </div>
        <div class="box-layout">
          <h4>Total Dollar Value: {{ totalDollarValue | currency: 'USD':'symbol':'1.2-2' }}</h4>
        </div>
      </div>
    </div>
    <!-- SO in Design Detailed Table View -->
    <div class="cust_table">
      <mat-table mat-table matSort [dataSource]="quoteCompletedDetailedDataSource" (matSortChange)="getSorting($event)">
        <ng-container matColumnDef="salesOrderNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="35"> SO Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.salesOrderNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="quotationNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="35"> Quote Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.quotationNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="noOfDesigns">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Number Of Design </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.noOfDesigns }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="dollarValue">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Dollar Value($) </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.dollarValue | currency: 'USD':'symbol':'1.2-2' }} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="quoteCompletedDetailedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: quoteCompletedDetailedColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!quoteCompletedDetailedDataSource?.data?.length && !showLoader">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
