import { SweetAlertService } from './../../shared/service/sweet-alert.service';
import { UserService } from './user.service';
import { User, UserFilter, UserPageable } from './user.model';
import { Messages } from './../../shared/constants/messages.constants';
import { SnakbarService } from './../../shared/service/snakbar.service';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { MatSort, MatTableDataSource, MatDialog, MatPaginator, MatDialogConfig } from '@angular/material';
import { AddUserComponent } from './add-user/add-user.component';
import { HttpResponse } from '@angular/common/http';
import { DisplayColumns } from '../../shared/constants/displayColName.constants';
import { PopupSize } from '../../shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Title } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';

@Component({
  selector: 'sfl-users',
  templateUrl: './users.component.html'
})
export class UsersComponent implements OnInit, OnDestroy {
  users: User[];
  userPageable: UserPageable = new UserPageable();
  noOfRecord: number = Values.pageSize;
  displayedColumns = DisplayColumns.Cols.UserDisplayColumns;
  dataSource = new MatTableDataSource<User>();

  subscription = new Subscription();
  showLoader = false;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  length: number;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortByUserMaster;
  userFilter = new UserFilter();
  totalPages: number;

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    private matDialog: MatDialog,
    private userService: UserService,
    private sweetAlertService: SweetAlertService,
    private snakbarService: SnakbarService,
    private titleService: Title
  ) {}

  ngOnInit() {
    this.titleService.setTitle('User Management - Super Admin');
    this.loadAll(this.initialPageIndex, this.initialPageSize);
  }

  applyFilter() {
    if (this.userFilter.name && this.userFilter.name.length) {
      this.userFilter.name = this.userFilter.name.trim();
      this.loadAll(this.initialPageIndex, this.pageSize);
    }
  }

  async addFilter() {
    this.userFilter.name = this.userFilter.name === '' ? undefined : this.userFilter.name;
    this.loadAll(this.initialPageIndex, this.pageSize);
  }
  async resetFilter() {
    this.userFilter = new UserFilter();
    this.loadAll(this.initialPageIndex, this.pageSize);
  }

  loadAll(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, sort: this.sortField + ',' + this.sortOrder, direction: this.sortOrder };
    this.userFilter.name = this.userFilter.name === '' ? undefined : this.userFilter.name;
    this.userService.filterUsers(this.userFilter, pageable).subscribe((userPageable: UserPageable) => {
      if (userPageable) {
        this.userPageable = userPageable;
        this.length = userPageable.totalElements;
        this.totalPages = userPageable.totalPages;
        this.pageIndex = userPageable.number;
        this.createMatTable(this.userPageable);
        this.showLoader = false;
      } else {
        this.dataSource.data = [];
        this.showLoader = false;
      }
    });
  }

  createMatTable(serviceRequestList: UserPageable) {
    this.dataSource.data = serviceRequestList.content;
  }

  editUser(username) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { username: username === undefined ? '' : username };
    dialogConfig.width = PopupSize.size.popup_md;
    const dialogRef = this.matDialog
      .open(AddUserComponent, dialogConfig)
      .afterClosed()
      .subscribe(result => {
        this.loadAll(this.initialPageIndex, this.pageSize);
      });
  }

  addUser() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { username: '' };
    dialogConfig.width = PopupSize.size.popup_md;
    const dialogRef = this.matDialog
      .open(AddUserComponent, dialogConfig)
      .afterClosed()
      .subscribe(result => {
        this.loadAll(this.initialPageIndex, this.pageSize);
      });
  }

  async deleteUser(email) {
    this.sweetAlertService.deleteAlert();
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.userService.delete(email).subscribe(
        response => {
          this.updateList();
          this.showLoader = false;
        },
        () => {
          this.snakbarService.error(Messages.User.error_delete);
          this.showLoader = false;
        }
      );
    }
  }

  updateList() {
    this.loadAll(this.initialPageIndex, this.pageSize);
  }

  private onSuccess(data, headers) {
    this.users = data;
    this.dataSource = new MatTableDataSource<User>(this.users);
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  getSorting(event) {
    this.sortField = event.active;
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    this.loadAll(this.initialPageIndex, this.pageSize);
  }

  getPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadAll(this.pageIndex, this.pageSize);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
