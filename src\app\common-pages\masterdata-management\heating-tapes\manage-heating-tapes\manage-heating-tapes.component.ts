import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { HeatingTapesMaster, AlloyMaster, TapePartNumberInfoDTO } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';
import { Router } from '@angular/router';
import { SnakbarService, Messages } from 'src/app/shared';

@Component({
  selector: 'sfl-manage-heating-tapes',
  templateUrl: './manage-heating-tapes.component.html'
})
export class ManageHeatingTapesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  heatingTape: HeatingTapesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  tapeTypes = Values.HeatingTapesTypes;
  wireTypes = Values.HeatingTapesWireTypes;
  alloys: AlloyMaster[];
  constructor(
    public readonly dialogRef: MatDialogRef<ManageHeatingTapesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly router: Router,
    private readonly snakbarService: SnakbarService
  ) {
    this.heatingTape = data;
  }

  ngOnInit() {
    this.heatingTape = this.heatingTape.id ? Object.assign({}, this.heatingTape) : new HeatingTapesMaster();
    this.heatingTape.id ? (this.title = 'Update Heating Tape') : (this.title = 'Add Heating Tape');
    this.getAlloys();
  }

  getAlloys() {
    this.subscription.add(
      this.masterDataService.getAlloysList().subscribe(
        (alloys: AlloyMaster[]) => {
          this.alloys = alloys;
          this.showLoader = false;
        },
        error => {
          if (error.applicationStatusCode === 1211) {
            this.snakbarService.error(error.message);
          }
          this.alloys = [];
          this.showLoader = false;
        }
      )
    );
  }

  manageAlloys() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/alloys']);
  }
  updateHeatingTapes() {
    this.showLoader = true;
    if (this.heatingTape && !this.heatingTape.tapePartNumber.match(Values.Tape_Part_Number_Regex)) {
      this.snakbarService.error(Messages.error.tape_invalid_partnumber);
      this.showLoader = false;
    } else {
      if (this.heatingTape.id) {
        this.subscription.add(
          this.masterDataService.updateHeatingTapes(this.heatingTape).subscribe(
            () => {
              this.showLoader = false;
              this.dialogRef.close(true);
            },
            () => {
              this.showLoader = false;
            }
          )
        );
      } else {
        this.subscription.add(
          this.masterDataService.addHeatingTapes(this.heatingTape).subscribe(
            () => {
              this.showLoader = false;
              this.dialogRef.close(true);
            },
            () => {
              this.showLoader = false;
            }
          )
        );
      }
    }
  }

  // if user changes the tape type tape part number needs to reset and re-generate
  changeInTapeType() {
    this.heatingTape.tapePartNumber = '';
  }
  // create system generated tape p/n
  generateTapePartNumber() {
    this.showLoader = true;
    if (!this.heatingTape.tapeType) {
      this.snakbarService.error(Messages.error.select_tape_type_first);
      this.showLoader = false;
    } else {
      this.subscription.add(
        this.masterDataService.generateHeatingTapePartNumber(this.heatingTape.tapeType).subscribe(
          (tapePartNumberDto: TapePartNumberInfoDTO) => {
            if (tapePartNumberDto) {
              this.heatingTape.tapePartNumber = tapePartNumberDto.tapePartNumber;
              this.showLoader = false;
            }
          },
          () => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  // validates the entered Heating Tape Part number
  validateTapePartNumber() {
    if (!this.heatingTape.tapePartNumber.match(Values.Tape_Part_Number_Regex)) {
      this.snakbarService.error(Messages.error.tape_invalid_partnumber);
    } else if (this.heatingTape.tapePartNumber) {
      this.showLoader = true;
      this.subscription.add(
        this.masterDataService.validateHeatingTapePartNumber(this.heatingTape.tapePartNumber).subscribe(
          isValid => {
            if (isValid) {
              this.snakbarService.error(Messages.error.tape_partnumber_already_exist);
              this.heatingTape.tapePartNumber = '';
            }
            this.showLoader = false;
          },
          () => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
