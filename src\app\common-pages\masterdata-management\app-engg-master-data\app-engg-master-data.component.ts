import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'sfl-app-engg-master-data',
  templateUrl: './app-engg-master-data.component.html'
})
export class AppEnggMasterDataComponent implements OnInit {
  constructor(private router: Router) {}

  ngOnInit() {}

  manageQuotStatus() {
    this.router.navigate(['master-data/management/quotation-status']);
  }
  manageAccessory() {
    this.router.navigate(['master-data/management/accessory']);
  }
  manageMaterial() {
    this.router.navigate(['master-data/management/material']);
  }
  manageMaterialProperties() {
    this.router.navigate(['master-data/management/material-properties']);
  }
  manageClosureMaterial() {
    this.router.navigate(['master-data/management/closure-material']);
  }
  managePlug() {
    this.router.navigate(['master-data/management/plug']);
  }
  manageThermostat() {
    this.router.navigate(['master-data/management/thermostat']);
  }
  manageFeatures() {
    this.router.navigate(['master-data/management/features']);
  }
  manageAccessoryController() {
    this.router.navigate(['master-data/management/accessory-controller']);
  }
  manageProductTypeCoverPage() {
    this.router.navigate(['master-data/management/product-type']);
  }
  managePowerCordConnector() {
    this.router.navigate(['master-data/management/power-cord-connector']);
  }
  manageSleevingTypes() {
    this.router.navigate(['master-data/management/sleeving-types']);
  }
  manageStrainReliefs() {
    this.router.navigate(['master-data/management/strain-reliefs']);
  }
  managePlugLights() {
    this.router.navigate(['master-data/management/plug-lights']);
  }
  manageDepartments() {
    this.router.navigate(['master-data/management/departments']);
  }
  manageSensorConnectors() {
    this.router.navigate(['master-data/management/sensor-connectors']);
  }
  managePowerCordMaterial() {
    this.router.navigate(['master-data/management/power-cord-materials']);
  }
  managePowerCordVoltages() {
    this.router.navigate(['master-data/management/power-cord-voltages']);
  }
  managePowerCordAmps() {
    this.router.navigate(['master-data/management/power-cord-amps']);
  }
  managePowerCordOptions() {
    this.router.navigate(['master-data/management/power-cord-options']);
  }
  manageSensorTypes() {
    this.router.navigate(['master-data/management/sensor-types']);
  }
  // manageSensorControlTypes() {
  //   this.router.navigate(['master-data/management/sensor-control-types']);
  // }
  manageThermostatTypes() {
    this.router.navigate(['master-data/management/thermostat-types']);
  }
  manageLeadTypes() {
    this.router.navigate(['master-data/management/lead-types']);
  }

  manageCurrency() {
    this.router.navigate(['master-data/management/currency-master']);
  }

  manageCCDC() {
    this.router.navigate(['master-data/management/ccdc-master-data']);
  }

  managePartNumber() {
    this.router.navigate(['master-data/management/part-number-master-data']);
  }

  manageLabor() {
    this.router.navigate(['master-data/management/labor-master-data']);
  }

  editCCDC() {
    this.router.navigate(['master-data/management/manage-ccdc']);
  }

}
