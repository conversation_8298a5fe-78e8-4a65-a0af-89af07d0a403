<h2 mat-dialog-title>Add Quotation
  <hr>
</h2>
<form #addQuotationForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-card-content>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <input matInput placeholder="Quote Number" ng-trim="true" [(ngModel)]="quoteNumber" pattern="^[A-Za-z0-9]+(?: +[A-Za-z0-9]+)*$"
            autofocus name="quoteNumber" #quoteNumberInput="ngModel" required>
          <mat-error *ngIf="!quoteNumber">This field is required</mat-error>
          <mat-error *ngIf="quoteNumber && quoteNumber.length > 0">No special charecters or spaces allowed</mat-error>
        </mat-form-field>
      </div>
    </mat-card-content>
  </mat-dialog-content>
  <hr>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveQuotation()" [disabled]="addQuotationForm.invalid">Search</button>
    <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</form>
