<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<sfl-pre-loader *ngIf="!isDataAvailable"></sfl-pre-loader>
<div *ngIf="isDataAvailable">

  <!-- More Actions required section -->
  <div class="pb-5" *ngFor="let moreAction of designActivityData['Drawing and Design Files']; let i = index;">
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">{{moreAction.title}}</strong>
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="moreAction.status" [attr.name]="'approv'+i"
        (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)"> {{moreAction.description}} </mat-checkbox>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Designed By</strong>
      <mat-form-field appearance="outline">
        <mat-select placeholder="Designed By" [(ngModel)]="moreAction.designedBy" name="{{moreAction.description}}"
          [disabled]="!moreAction.status" (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
          <mat-option *ngFor="let designedBy of users" [value]="designedBy?.id">
            {{designedBy.firstName}} {{designedBy.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Date</strong>
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="compByDateM" [max]="maxDate" placeholder="Date" name="compByDateM"
        [(ngModel)]="moreAction.designDate" #compByDateM="ngModel" autocomplete="off"
          [disabled]="!moreAction.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
        <mat-datepicker-toggle matSuffix [for]="compByDateM"></mat-datepicker-toggle>
        <mat-datepicker #compByDateM></mat-datepicker>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new-review" *ngIf="i == 0">Reviewed By</strong>
      <mat-form-field appearance="outline">
        <mat-select placeholder="Reviewed By" [(ngModel)]="moreAction.reviewedBy" name="{{moreAction.description}}"
          [disabled]="!moreAction.status" (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
          <mat-option *ngFor="let actionRevBy of users" [value]="actionRevBy?.id">
            {{actionRevBy.firstName}} {{actionRevBy.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Date</strong>
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="reviewByDateM" [max]="maxDate" placeholder="Date" name="reviewByDateM"
        [(ngModel)]="moreAction.reviewDate" #reviewByDateM="ngModel" autocomplete="off"
          [disabled]="!moreAction.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
        <mat-datepicker-toggle matSuffix [for]="reviewByDateM"></mat-datepicker-toggle>
        <mat-datepicker #reviewByDateM></mat-datepicker>
      </mat-form-field>
    </div>
</div>

    <div class="pb-5" *ngFor="let moreActionCheckBox of moreAction?.childDesignActivity">
      <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5 sub-action">
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="moreActionCheckBox.status" [attr.name]="'approv'+i"
      (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)" [attr.name]="'approv'+i"> {{moreActionCheckBox?.description}}
      </mat-checkbox><br>
      </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-select placeholder="Designed By" [(ngModel)]="moreActionCheckBox.designedBy" name="{{moreActionCheckBox.description}}"
              [disabled]="!moreActionCheckBox.status" (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
              <mat-option *ngFor="let designedBy of users" [value]="designedBy?.id">
                {{designedBy.firstName}} {{designedBy.lastName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-label>Date</mat-label>
            <input matInput [matDatepicker]="compByDateM" [max]="maxDate" placeholder="Date" name="compByDateM"
            [(ngModel)]="moreActionCheckBox.designDate" #compByDateM="ngModel" autocomplete="off"
              [disabled]="!moreActionCheckBox.status" (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
            <mat-datepicker-toggle matSuffix [for]="compByDateM"></mat-datepicker-toggle>
            <mat-datepicker #compByDateM></mat-datepicker>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-select placeholder="Reviewed By" [(ngModel)]="moreActionCheckBox.reviewedBy" name="{{moreActionCheckBox.description}}"
              [disabled]="!moreActionCheckBox.status" (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
              <mat-option *ngFor="let actionRevBy of users" [value]="actionRevBy?.id">
                {{actionRevBy.firstName}} {{actionRevBy.lastName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-label>Date</mat-label>
            <input matInput [matDatepicker]="reviewByDateM" [max]="maxDate" placeholder="Date" name="reviewByDateM"
            [(ngModel)]="moreActionCheckBox.reviewDate" #reviewByDateM="ngModel" autocomplete="off"
              [disabled]="!moreActionCheckBox.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
            <mat-datepicker-toggle matSuffix [for]="reviewByDateM"></mat-datepicker-toggle>
            <mat-datepicker #reviewByDateM></mat-datepicker>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div><br><br>

  <div class="pb-5" *ngFor="let moreAction of designActivityData['Heater Man']; let i = index;">
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div fxLayout="column"  fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">{{moreAction.title}}</strong>
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="moreAction.status" [attr.name]="'approv'+i"
        (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)"> {{moreAction.description}} </mat-checkbox>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Designed By</strong>
      <mat-form-field appearance="outline">
        <mat-select placeholder="Designed By" [(ngModel)]="moreAction.designedBy" name="{{moreAction.description}}"
          [disabled]="!moreAction.status" (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
          <mat-option *ngFor="let designedBy of users" [value]="designedBy?.id">
            {{designedBy.firstName}} {{designedBy.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Date</strong>
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="compByDateM" [max]="maxDate" placeholder="Date" name="compByDateM"
        [(ngModel)]="moreAction.designDate"  #compByDateM="ngModel" autocomplete="off"
          [disabled]="!moreAction.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
        <mat-datepicker-toggle matSuffix [for]="compByDateM"></mat-datepicker-toggle>
        <mat-datepicker #compByDateM></mat-datepicker>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Reviewed By</strong>
      <mat-form-field appearance="outline">
        <mat-select placeholder="Reviewed By" [(ngModel)]="moreAction.reviewedBy" name="{{moreAction.description}}"
          [disabled]="!moreAction.status" (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
          <mat-option *ngFor="let actionRevBy of users" [value]="actionRevBy?.id">
            {{actionRevBy.firstName}} {{actionRevBy.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Date</strong>
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="reviewByDateM" [max]="maxDate" placeholder="Date" name="reviewByDateM"
        [(ngModel)]="moreAction.reviewDate"  #reviewByDateM="ngModel" autocomplete="off"
          [disabled]="!moreAction.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
        <mat-datepicker-toggle matSuffix [for]="reviewByDateM"></mat-datepicker-toggle>
        <mat-datepicker #reviewByDateM></mat-datepicker>
      </mat-form-field>
    </div>
    </div>

    <div class="pb-5" *ngFor="let moreActionCheckBox of moreAction?.childDesignActivity">
      <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="moreActionCheckBox.status" [attr.name]="'approv'+i"
      (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)" [attr.name]="'approv'+i"> {{moreActionCheckBox?.description}}
      </mat-checkbox><br>
    </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-select placeholder="Designed By" [(ngModel)]="moreActionCheckBox.designedBy" name="{{moreActionCheckBox.description}}"
              [disabled]="!moreActionCheckBox.status" (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
              <mat-option *ngFor="let designedBy of users" [value]="designedBy?.id">
                {{designedBy.firstName}} {{designedBy.lastName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-label>Date</mat-label>
            <input matInput [matDatepicker]="compByDateM" [max]="maxDate" placeholder="Date" name="compByDateM"
            [(ngModel)]="moreActionCheckBox.designDate" #compByDateM="ngModel" autocomplete="off"
              [disabled]="!moreActionCheckBox.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
            <mat-datepicker-toggle matSuffix [for]="compByDateM"></mat-datepicker-toggle>
            <mat-datepicker #compByDateM></mat-datepicker>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-select placeholder="Reviewed By" [(ngModel)]="moreActionCheckBox.reviewedBy" name="{{moreActionCheckBox.description}}"
              [disabled]="!moreActionCheckBox.status" (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
              <mat-option *ngFor="let actionRevBy of users" [value]="actionRevBy?.id">
                {{actionRevBy.firstName}} {{actionRevBy.lastName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-label>Date</mat-label>
            <input matInput [matDatepicker]="reviewByDateM" [max]="maxDate" placeholder="Date" name="reviewByDateM"
            [(ngModel)]="moreActionCheckBox.reviewDate" #reviewByDateM="ngModel" autocomplete="off"
              [disabled]="!moreActionCheckBox.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
            <mat-datepicker-toggle matSuffix [for]="reviewByDateM"></mat-datepicker-toggle>
            <mat-datepicker #reviewByDateM></mat-datepicker>
          </mat-form-field>
        </div>
    </div>
    </div>
  </div><br><br>

  <div class="pb-5" *ngFor="let moreAction of designActivityData['Epicor']; let i = index;">
    <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div fxLayout="column"  fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">{{moreAction.title}}</strong>
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="moreAction.status" [attr.name]="'approv'+i"
        (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)"> {{moreAction.description}} </mat-checkbox>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Designed By</strong>
      <mat-form-field appearance="outline">
        <mat-select placeholder="Designed By" [(ngModel)]="moreAction.designedBy" name="{{moreAction.description}}"
          [disabled]="!moreAction.status" (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
          <mat-option *ngFor="let designedBy of users" [value]="designedBy?.id">
            {{designedBy.firstName}} {{designedBy.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Date</strong>
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="compByDateM" [max]="maxDate" placeholder="Date" name="compByDateM"
        [(ngModel)]="moreAction.designDate"  #compByDateM="ngModel" autocomplete="off"
          [disabled]="!moreAction.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
        <mat-datepicker-toggle matSuffix [for]="compByDateM"></mat-datepicker-toggle>
        <mat-datepicker #compByDateM></mat-datepicker>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Reviewed By</strong>
      <mat-form-field appearance="outline">
        <mat-select placeholder="Reviewed By" [(ngModel)]="moreAction.reviewedBy" name="{{moreAction.description}}"
          [disabled]="!moreAction.status" (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
          <mat-option *ngFor="let actionRevBy of users" [value]="actionRevBy?.id">
            {{actionRevBy.firstName}} {{actionRevBy.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
      <strong class="pb-15 new" *ngIf="i == 0">Date</strong>
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="reviewByDateM" [max]="maxDate" placeholder="Date" name="reviewByDateM"
        [(ngModel)]="moreAction.reviewDate" #reviewByDateM="ngModel" autocomplete="off"
          [disabled]="!moreAction.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreAction, i)">
        <mat-datepicker-toggle matSuffix [for]="reviewByDateM"></mat-datepicker-toggle>
        <mat-datepicker #reviewByDateM></mat-datepicker>
      </mat-form-field>
    </div>
    </div>

    <div class="pb-5" *ngFor="let moreActionCheckBox of moreAction?.childDesignActivity">
      <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
      <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
        <mat-checkbox color="warn" class="critical-check" [(ngModel)]="moreActionCheckBox.status" [attr.name]="'approv'+i"
        (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)" [attr.name]="'approv'+i"> {{moreActionCheckBox?.description}}
        </mat-checkbox><br>
      </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-select placeholder="Designed By" [(ngModel)]="moreActionCheckBox.designedBy" name="{{moreActionCheckBox.description}}"
              [disabled]="!moreActionCheckBox.status" (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
              <mat-option *ngFor="let designedBy of users" [value]="designedBy?.id">
                {{designedBy.firstName}} {{designedBy.lastName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-label>Date</mat-label>
            <input matInput [matDatepicker]="compByDateM" [max]="maxDate" placeholder="Date" name="compByDateM"
            [(ngModel)]="moreActionCheckBox.designDate" #compByDateM="ngModel" autocomplete="off"
              [disabled]="!moreActionCheckBox.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
            <mat-datepicker-toggle matSuffix [for]="compByDateM"></mat-datepicker-toggle>
            <mat-datepicker #compByDateM></mat-datepicker>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-select placeholder="Reviewed By" [(ngModel)]="moreActionCheckBox.reviewedBy" name="{{moreActionCheckBox.description}}"
              [disabled]="!moreActionCheckBox.status" (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
              <mat-option *ngFor="let actionRevBy of users" [value]="actionRevBy?.id">
                {{actionRevBy.firstName}} {{actionRevBy.lastName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="column" fxFlex.gt-lg="20" fxFlex.gt-md="20">
          <mat-form-field appearance="outline">
            <mat-label>Date</mat-label>
            <input matInput [matDatepicker]="reviewByDateM" [max]="maxDate" placeholder="Date" name="reviewByDateM"
            [(ngModel)]="moreActionCheckBox.reviewDate" #reviewByDateM="ngModel" autocomplete="off"
              [disabled]="!moreActionCheckBox.status" readonly (ngModelChange)="updateEcoDesignActivityMain(moreActionCheckBox, i)">
            <mat-datepicker-toggle matSuffix [for]="reviewByDateM"></mat-datepicker-toggle>
            <mat-datepicker #reviewByDateM></mat-datepicker>
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>

  <!-- Inform Quality section -->
  <div fxLayout="row wrap" class="pb-5"
    *ngFor="let infoQuality of designActivityData['Inform Quality(by email) so PQP Files Can Be Updated']; let i = index;">
    <div fxLayout="column" fxFlex.gt-lg="25" fxFlex.gt-md="25">
      <strong class="pb-15">{{infoQuality.title}}</strong>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="15" fxFlex.gt-md="15">
      <mat-form-field appearance="outline">
        <mat-select placeholder="Reviewed By" [(ngModel)]="infoQuality.informQualityEmail" name="infoQualityReviewedBy"
          (ngModelChange)="updateInformQuality(infoQuality)">
          <mat-option *ngFor="let user of users" [value]="user?.id">
            {{user.firstName}} {{user.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="15" fxFlex.gt-md="15">
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="infoQualityReviewedByByDateM" [max]="maxDate" placeholder="Date"
          name="infoQualityReviewedByByDateM" [(ngModel)]="infoQuality.informDate"
          #infoQualityReviewedByByDateM="ngModel" autocomplete="off" (ngModelChange)="updateInformQuality(infoQuality)">
        <mat-datepicker-toggle matSuffix [for]="infoQualityReviewedByByDateM"></mat-datepicker-toggle>
        <mat-datepicker #infoQualityReviewedByByDateM></mat-datepicker>
      </mat-form-field>
    </div>
  </div>

  <!-- Comments section -->
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5"
    *ngFor="let comments of designActivityData['Comments']; let i = index;">
    <div fxLayout="column" fxFlex.gt-lg="100" fxFlex.gt-md="100">
      <strong class="pb-15">{{comments.title}}</strong>
      <textarea matInput placeholder="Comments" [(ngModel)]="comments.comment" name="designActivityComments" rows="6"
        class="sfl-textarea" (change)="updateComments(comments)"></textarea>
    </div>
  </div>
</div>
