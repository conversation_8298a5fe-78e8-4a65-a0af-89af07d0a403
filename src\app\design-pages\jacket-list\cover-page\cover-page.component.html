<h2 mat-dialog-title>Cover Page
    <hr>
</h2>
<mat-dialog-content>
    <form>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field color="warn" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <mat-select placeholder="Product Type(s)" [formControl]="products" multiple>
                    <mat-option *ngFor="let product of productTypeList" [value]="product.id">{{product?.name}}</mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <mat-select placeholder="Anticipated Volume" name="anticipatedVolume" #anticipatedVolumeSelect="ngModel"
                    [(ngModel)]="coverPage.anticipatedVolume">
                    <mat-option *ngFor="let anticipated of anticipatedOptions" [value]="anticipated.id">{{anticipated.value}}</mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput [matDatepicker]="meetingDate" placeholder="Meeting Date" name="meetingDate"
                    #meetingDateDate="ngModel" [(ngModel)]="coverPageMeetingDate" autocomplete="off">
                <mat-datepicker-toggle matSuffix [for]="meetingDate"></mat-datepicker-toggle>
                <mat-datepicker #meetingDate></mat-datepicker>
            </mat-form-field>
        </div>
        <div>
            <mat-form-field>
                <textarea matInput placeholder="Note" name="note" [(ngModel)]="coverPage.note" #noteInput="ngModel"></textarea>
            </mat-form-field>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="mt-10" fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="reviewMeeting" [(ngModel)]="coverPage.reviewMeeting"
                    #reviewMeetingInput="ngModel">Review Meeting Required</mat-checkbox>
            </div>
            <mat-form-field *ngIf="coverPage.reviewMeeting" fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32"
                fxFlex.gt-xs="100">
                <input matInput [matDatepicker]="reviewMeetingDate" placeholder="Review Meeting Date" name="reviewMeetingDate"
                    #reviewMeetingDateDate="ngModel" [(ngModel)]="coverPageReviewMeetingDate" autocomplete="off">
                <mat-datepicker-toggle matSuffix [for]="reviewMeetingDate"></mat-datepicker-toggle>
                <mat-datepicker #reviewMeetingDate></mat-datepicker>
            </mat-form-field>
            <div fxFlex.gt-lg="40" fxFlex.gt-md="40" fxFlex.gt-sm="53" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="mt-10" fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="specialRequirement" [(ngModel)]="coverPage.specialRequirement"
                    #specialRequirementInput="ngModel">Special Requirements</mat-checkbox>
            </div>
            <mat-form-field *ngIf="coverPage.specialRequirement" fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32"
                fxFlex.gt-xs="100">
                <input matInput placeholder="Note" name="specialRequirementNote" [(ngModel)]="coverPage.specialRequirementNote"
                    #specialRequirementNoteInput="ngModel">
            </mat-form-field>
            <div fxFlex.gt-lg="40" fxFlex.gt-md="40" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
            </div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="mt-10" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="newMaterial" [(ngModel)]="coverPage.newMaterial"
                    #newMaterialInput="ngModel">New Materials / Parts</mat-checkbox>
            </div>
            <mat-form-field *ngIf="coverPage.newMaterial" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33"
                fxFlex.gt-xs="100">
                <input matInput placeholder="New Material Comments" name="newMaterialComments" [(ngModel)]="coverPage.newMaterialComments"
                    #newMaterialCommentsInput="ngModel">
            </mat-form-field>
            <div class="mt-10" fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="msds" [(ngModel)]="coverPage.msds" #msdsCheckbox="ngModel">MSDS</mat-checkbox>
            </div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="mt-10" fxFlex.gt-lg="15" fxFlex.gt-md="15" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="qualificationTestingRequirement" [(ngModel)]="coverPage.qualificationTestingRequirement"
                    #qualificationTestingRequirementInput="ngModel">Qualification Testing Req</mat-checkbox>
            </div>
            <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="100">
                <mat-form-field *ngIf="coverPage.qualificationTestingRequirement">
                    <mat-select placeholder="Testing Department" name="testingDept" #testingDeptSelect="ngModel"
                        [(ngModel)]="coverPage.qualificationTestingDept">
                        <mat-option *ngFor="let testingDept of testingDeptOptions" [value]="testingDept.id">{{testingDept.value}}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div fxFlex.gt-lg="40" fxFlex.gt-md="20" fxFlex.gt-sm="40" fxFlex.gt-xs="100"></div>
        </div>
        <div class="mb-10">
            <div class="mt-10" *ngIf="coverPage.qualificationTestingRequirement" fxFlex.gt-lg="48" fxFlex.gt-md="48"
                fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="contactedReliability" [(ngModel)]="coverPage.contactedReliability"
                    #contactedReliabilityInput="ngModel">Contacted Reliability Eng. or V.P of Engineering via Email
                    with Testing
                    Details
                </mat-checkbox>
            </div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <input matInput placeholder="General Comments" name="generalComments" [(ngModel)]="coverPage.generalComments"
                    #generalCommentsInput="ngModel">
            </mat-form-field>
            <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="greenSheetRequired" [(ngModel)]="coverPage.greenSheetRequired"
                    #greenSheetRequiredInput="ngModel">Green Sheet Required</mat-checkbox>
            </div>
            <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-checkbox color="warn" name="criticalPart" [(ngModel)]="coverPage.criticalPart" #criticalPartInput="ngModel">Critical
                    Part</mat-checkbox>
            </div>
        </div>
        <div *ngIf="coverPage.criticalPart">
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <input matInput placeholder="MTBF Goal" name="mtbfgoal" [(ngModel)]="coverPage.mtbfgoal"
                        #mtbfgoalInput="ngModel">
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <input matInput placeholder="Years at" name="yearsAt" [(ngModel)]="coverPage.yearsAt" #yearsAtInput="ngModel">
                    <div matSuffix>% confidence</div>
                </mat-form-field>
                <mat-form-field color="warn" fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <mat-select placeholder="PQP Family(s)" name="coverPagePqpFamilies" [formControl]="families"
                        multiple>
                        <mat-option *ngFor="let pqp of pqpFamilyList" [value]="pqp.id">{{pqp?.name}}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <mat-checkbox color="warn" name="newPQPFamily" [(ngModel)]="coverPage.newPQPFamily"
                        #newPQPFamilyInput="ngModel">New PQP Family</mat-checkbox>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <mat-checkbox color="warn" name="contactedQc" [(ngModel)]="coverPage.contactedQc" #contactedQcInput="ngModel">Contacted
                        QC</mat-checkbox>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <mat-checkbox color="warn" name="stayAsNonPQP" [(ngModel)]="coverPage.stayAsNonPQP"
                        #stayAsNonPQPInput="ngModel">Stay as Non-PQP</mat-checkbox>
                </div>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                    <textarea matInput placeholder="PQP Comments" name="pqpComments" [(ngModel)]="coverPage.pqpComments"
                        #pqpCommentsInput="ngModel"></textarea>
                </mat-form-field>
            </div>
        </div>
    </form>
</mat-dialog-content>
<hr>
<mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveCoverPage()">Save</button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>