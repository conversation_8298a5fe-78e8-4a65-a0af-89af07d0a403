
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../app.config';
import { IUser, UserFilter, PageableQuery } from './user.model';
import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { createRequestOption } from 'src/app/shared';
import { utils } from 'src/app/shared/helpers';


@Injectable({ providedIn: 'root' })
export class UserService {
  private resourceUrl = AppConfig.USER_API;

  constructor(private http: HttpClient) {}

  create(user: IUser): Observable<HttpResponse<IUser>> {
    return this.http.post<IUser>(this.resourceUrl, user, {
      observe: 'response'
    });
  }

  update(user: IUser): Observable<HttpResponse<IUser>> {
    return this.http.put<IUser>(this.resourceUrl, user, {
      observe: 'response'
    });
  }

  find(login: string): Observable<HttpResponse<IUser>> {
    return this.http.get<IUser>(`${this.resourceUrl}/${login}`, {
      observe: 'response'
    });
  }

  query(userSearchFilter: UserFilter, pageableObject: PageableQuery) {
    return this.http.get<IUser[]>(this.resourceUrl, {
        params: createRequestOption(pageableObject)
      }).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
}

  delete(login: string): Observable<HttpResponse<any>> {
    return this.http.delete(`${this.resourceUrl}/${login}`, {
      observe: 'response'
    });
  }

  sendForgotPasswordEmail(email: String) {
    return this.http.post(
        AppConfig.RESET_PASSWORD_INIT_API,
        email
    );
  }

  filterUsers(userFilter: UserFilter, pageableObject: PageableQuery) {
    return this.http.post(AppConfig.FILTER_USERS, userFilter, {
      params: createRequestOption(pageableObject)
    }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
