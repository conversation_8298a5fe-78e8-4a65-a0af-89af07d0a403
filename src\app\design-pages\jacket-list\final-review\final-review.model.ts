export class FinalReviewModel {
  constructor(
    public bomReviewed?: boolean,
    public checked?: boolean,
    public comments?: string,
    public customerPDFUpdated?: boolean,
    public designReleased?: boolean,
    public elementDesignReviewed?: boolean,
    public engineeringCostSheet?: boolean,
    public finalReviewDate?: any,
    public goldReportRun?: boolean,
    public heaterManuallyVerified?: boolean,
    public id?: number,
    public jacketId?: number,
    public labelReviewed?: boolean,
    public name?: string,
    public partsActiveInEpicor?: boolean,
    public sensorLengthReviewed?: boolean,
    public tsNotInterfere?: boolean
  ) { }
}

export class ChecklistFinalReviewDTO {
  public finalReviewDTOs: FinalReviewModel[] = [];
  public quotationID: number;
}

