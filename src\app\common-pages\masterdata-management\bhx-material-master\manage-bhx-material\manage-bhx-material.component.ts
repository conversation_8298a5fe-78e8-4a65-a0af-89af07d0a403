import {Component, Inject, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Subscription} from 'rxjs';
import {
  AccessoryControllerMaster,
  BHXMaterialMaster,
  ClosureMaterialMaster,
  InstallationMethod,
  LeadTypesMaster,
  MaterialMaster,
  PlugMaster,
  SensorConnectorsAndTypesMaster,
  SleevingTypesAndStrainReliefsMaster,
  StrainRelief
} from '../../masterdata-management.model';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {MAT_DIALOG_DATA, MatDialog, MatDialogConfig, MatDialogRef} from '@angular/material';
import {MasterdataManagementService} from '../../masterdata-management.service';
import {Values} from 'src/app/shared/constants/values.constants';
import {Router} from '@angular/router';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';
import {Messages, SnakbarService} from 'src/app/shared';
import {ConfirmGroupingChangeComponent} from '../confirm-grouping-change/confirm-grouping-change.component';
import {EquationFormulaGeneratorComponent} from '../../equation-formula-generator/equation-formula-generator.component';

@Component({
  selector: 'sfl-manage-bhx-material',
  templateUrl: './manage-bhx-material.component.html',
  styleUrls: ['./manage-bhx-material.component.css']
})
export class ManageBhxMaterialComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  bhxMaterial: BHXMaterialMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  groupings = Values.GroupingsBHXMaterial;
  uomMaster = Values.UOMBHXMaterial;
  elementTypes = Values.BHXMaterialElementType;
  elementType = [];
  wireTypes = Values.wireTypesBHXMaterialMaster;
  wireType = [];
  jacketTypes: object = Values.JacketTypeConst;
  jacketType = [];
  targetCountryTypes = Values.TargetCountryTypes;
  connectors = Values.Connectors;
  greenLights = [];
  operations = Values.Operations;

  materialMaster: MaterialMaster;
  sensorsConnectors: SensorConnectorsAndTypesMaster;
  sensConn = [];
  sensorTypes: SensorConnectorsAndTypesMaster;
  sensorType = [];
  selectedInstallationMethods = [];
  selectedStrainReliefs = [];
  selectedController = [];
  selectedLayeredMaterial = [];
  selectedClosuredMaterial = [];
  selectedSleeving = [];
  selectedGreenLights = [];
  selectedPlugs = [];
  selectedConnectors = [];
  selectedPhase = [];
  selectedJacketTypes = [];
  selectedProductTypes = [];
  leadTypes: LeadTypesMaster[];
  selectedLeadTypes = [];

  sleevingTypes: SleevingTypesAndStrainReliefsMaster[];
  plugs: PlugMaster;
  closureMaterial: ClosureMaterialMaster;
  currentGrouping: number;
  strFormula = '';
  noConnector = false;
  noPlug = false;
  disFormulaBtn = false;
  disQty = false;

  productTypes: object = Values.ProductTypeConst;
  installationMethods: InstallationMethod[];
  strainReliefs: StrainRelief[];
  accessoryControllers: AccessoryControllerMaster[];
  phaseTypes: object = Values.PhaseTypeConst;
  checkBoxYesLabel = Values.CheckboxLabels.YES;
  checkBoxNoLabel = Values.CheckboxLabels.NO;
  checkBoxNullLabel = Values.CheckboxLabels.NULL;
  ClothCECheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.ClothCE;
  ClothULCheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.ClothUL;
  HazardousCheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.Hazardous;
  ThermostatCheckBoxTitle = Values.BHXMaterial_CheckBox_Titles.Thermostat;
  ManualResetTSCheckBox = Values.BHXMaterial_CheckBox_Titles.ManualReset;
  PrivateLabelCheckBox = Values.BHXMaterial_CheckBox_Titles.PrivateLabel;

  constructor(
    public readonly dialogRef: MatDialogRef<ManageBhxMaterialComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly router: Router,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) {
    this.bhxMaterial = data;
    this.currentGrouping = this.bhxMaterial.grouping;
  }
  ngOnInit() {
    this.bhxMaterial = this.bhxMaterial.id ? Object.assign({}, this.bhxMaterial) : new BHXMaterialMaster();
    if (this.bhxMaterial.id) {
      this.title = 'Update BHX Material';

      this.strFormula = this.bhxMaterial.formula ? this.bhxMaterial.formula : '';
      this.jacketType = this.bhxMaterial.jacketType ? this.bhxMaterial.jacketType.split(',') : [];
      this.elementType = this.bhxMaterial.elementType ? this.bhxMaterial.elementType.split(',') : [];
      this.wireType = this.bhxMaterial.wireType ? this.bhxMaterial.wireType.split(',') : [];
      this.sensConn = this.bhxMaterial.sensConn ? this.bhxMaterial.sensConn.split(',') : [];
      this.sensorType = this.bhxMaterial.sensorType ? this.bhxMaterial.sensorType.split(',') : [];
      this.selectedPhase = this.bhxMaterial.phase ? this.bhxMaterial.phase.split(',') : [];
      this.selectedProductTypes = this.bhxMaterial.productType ? this.bhxMaterial.productType.split(',') : [];
      this.selectedStrainReliefs = this.bhxMaterial.strainRelief ? this.bhxMaterial.strainRelief.split(',') : [];
      this.selectedConnectors = this.bhxMaterial.connector ? this.bhxMaterial.connector.split(',') : [];
      this.selectedController = this.bhxMaterial.controller ? this.bhxMaterial.controller.split(',') : [];
      this.selectedInstallationMethods = this.bhxMaterial.installationMethod ? this.bhxMaterial.installationMethod.split(',') : [];
      this.selectedJacketTypes = this.bhxMaterial.jacketType ? this.bhxMaterial.jacketType.split(',') : [];
      this.selectedLayeredMaterial = this.bhxMaterial.layered ? this.bhxMaterial.layered.split(',') : [];
      this.selectedClosuredMaterial = this.bhxMaterial.closure ? this.bhxMaterial.closure.split(',') : [];
      this.selectedPlugs = this.bhxMaterial.plug ? this.bhxMaterial.plug.split(',') : [];
      this.selectedGreenLights = this.bhxMaterial.greenLight ? this.bhxMaterial.greenLight.split(',') : [];
      this.selectedSleeving = this.bhxMaterial.sleeving ? this.bhxMaterial.sleeving.split(',') : [];
      this.selectedLeadTypes = this.bhxMaterial.leadType ? this.bhxMaterial.leadType.split(',') : [];


      if (this.bhxMaterial.plug) {
        this.noPlug = false;
        this.noConnector = true;
      } else if (this.bhxMaterial.connector) {
        this.noPlug = true;
        this.noConnector = false;
      } else {
        this.noPlug = false;
        this.noConnector = false;
      }
    } else {
      this.title = 'Add BHX Material';
    }
    this.getMaterialList();
    this.getSensorsConnectors();
    this.getSensorsTypes();
    this.getSleevingTypes();
    this.getPlugsAndConnectors();
    this.getClosureMaterials();
    this.getInstallationMethods();
    this.getStrainReliefs();
    this.getControlerTypeMasterData();
    this.getPluginMasterData();
    this.getLeadTypesMasterData();
  }

  getMaterialList() {
    this.subscription.add(
      this.masterDataService.getMaterialsList().subscribe((materialList: MaterialMaster) => {
        this.materialMaster = materialList;
      })
    );
  }
  getSensorsConnectors() {
    this.subscription.add(
      this.masterDataService.getSensorConnectorsList().subscribe((sensorsConnectors: SensorConnectorsAndTypesMaster) => {
        this.sensorsConnectors = sensorsConnectors;
      })
    );
  }
  getSensorsTypes() {
    this.subscription.add(
      this.masterDataService.getSensorTypesList().subscribe((sensorTypes: SensorConnectorsAndTypesMaster) => {
        this.sensorTypes = sensorTypes;
      })
    );
  }
  getSleevingTypes() {
    this.subscription.add(
      this.masterDataService.getSleevingTypesList().subscribe((sleevingTypes: SleevingTypesAndStrainReliefsMaster[]) => {
        this.sleevingTypes = sleevingTypes;
      })
    );
  }
  // gets the plug by currently active jacket type, if we switch jacket type from UI it will set plug and connector to null
  getPlugsAndConnectors() {
    this.subscription.add(
      this.masterDataService.getPlugList().subscribe((plugs: PlugMaster) => {
        this.plugs = plugs;
      },
      (error) => {
        if (error.applicationStatusCode === 1230) {
          this.snakbarService.error(error.message);
          }
        }
      )
    );
  }
  getClosureMaterials() {
    this.subscription.add(
      this.masterDataService.getClosureMaterial().subscribe((closureMaterial: ClosureMaterialMaster) => {
        this.closureMaterial = closureMaterial;
      }, error => {
        if (error.applicationStatusCode === 1215) {
          this.snakbarService.error(error.message);
        }
      }
      )
    );
  }

  // gets the available installation methods
  getInstallationMethods() {
    this.subscription.add(
      this.masterDataService.getInstallationMethods().subscribe((installationMethods: InstallationMethod[]) => {
        this.installationMethods = installationMethods;
      })
    );
  }
  // gets the available strain reliefs
  getStrainReliefs() {
    this.subscription.add(
      this.masterDataService.getStrainReliefs().subscribe((strainReliefs: StrainRelief[]) => {
        this.strainReliefs = strainReliefs;
      },
      (error) => {
        if (error.applicationStatusCode === 1234) {
          this.snakbarService.error(error.message);
          }
        }
      )
    );
  }
  // gets the available control types
  getControlerTypeMasterData() {
    this.subscription.add(
      this.masterDataService.getAccessoryControllersList().subscribe((accessoryControllers: AccessoryControllerMaster[]) => {
        this.accessoryControllers = accessoryControllers;
      })
    );
  }
  // gets the available plugging light master data [we need only green lights for BHX material]
  getPluginMasterData() {
    this.subscription.add(
      this.masterDataService.getPluginMasterData().subscribe((plugLights: any) => {
        this.greenLights = plugLights.greenLights;
      })
    );
  }

  // gets the Lead type master data
  getLeadTypesMasterData() {
    this.subscription.add(
      this.masterDataService.getLeadTypeMaster().subscribe((leadTypes: LeadTypesMaster[]) => {
        this.leadTypes = leadTypes;
      })
    );
  }

  manageMaterial() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/material']);
  }
  manageSensorConnectors() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/sensor-connectors']);
  }
  manageSensorTypes() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/sensor-types']);
  }
  manageSleevingTypes() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/sleeving-types']);
  }
  managePlugs() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/plug']);
  }

  // used to redirect to lead type master data screen
  manageLeadTypes() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/lead-types']);
  }


  // triggers to disable connector when user selects the plug. Both plug and connector will be available if user selects null. At a time
  changePlugData(plugName: String[]) {
    if (plugName.length && !plugName.includes('none')) {
      this.noPlug = false;
      this.noConnector = true;
    } else {
      this.selectedPlugs = [];
      this.noPlug = false;
      this.noConnector = false;
    }
  }
  // triggers to disable plugs when user selects the connector. Both plug and connector will be available if user selects null. At a time
  changeConnectorData(connectorName: String[]) {
    if (connectorName.length && !connectorName.includes('none')) {
      this.noPlug = true;
      this.noConnector = false;
    } else {
      this.selectedConnectors = [];
      this.noPlug = false;
      this.noConnector = false;
    }
  }

  // from multi select drop down we get array of selected items, so we need to extract values in comma seperated string as we store string in db
  prepareBHXMaterialObject(): Promise<boolean> {
    return new Promise(resolve => {
      this.bhxMaterial.formula = this.strFormula ? this.masterDataService.equationFormatterFromFullNameToConst(this.strFormula) : '';
      this.bhxMaterial.formula = this.bhxMaterial.formula.replace(/\s/g, '');
      this.bhxMaterial.elementType = this.elementType.join();
      this.bhxMaterial.wireType = this.wireType.join();
      this.bhxMaterial.sensConn = this.sensConn.join();
      this.bhxMaterial.sensorType = this.sensorType.join();
      this.bhxMaterial.installationMethod = this.selectedInstallationMethods.join();
      this.bhxMaterial.strainRelief = this.selectedStrainReliefs.join();
      this.bhxMaterial.controller = this.selectedController.join();
      this.bhxMaterial.layered = this.selectedLayeredMaterial.join();
      this.bhxMaterial.closure = this.selectedClosuredMaterial.join();
      this.bhxMaterial.sleeving = this.selectedSleeving.join();
      this.bhxMaterial.greenLight = this.selectedGreenLights.join();
      this.bhxMaterial.plug = this.selectedPlugs.join();
      this.bhxMaterial.connector = this.selectedConnectors.join();
      this.bhxMaterial.phase = this.selectedPhase.join();
      this.bhxMaterial.jacketType = this.selectedJacketTypes.join();
      this.bhxMaterial.productType = this.selectedProductTypes.join();
      this.bhxMaterial.leadType = this.selectedLeadTypes.join();

      // if values are empty string `""` set it to `null`
      // Object.entries get us the array of this.bhxMaterial then we can iterate over it and get the key value pair as array[0] and array[1] respectively
      Object.entries(this.bhxMaterial).forEach(property => {
        if (typeof property[1] === 'string' && property[1] === '') {
          this.bhxMaterial[property[0]] = null;
        }
      });
      resolve();
    });
  }

  // add/ update bhx material. if Id is present we update it else we add it as a new.
  async updateBHXMaterial() {
    this.showLoader = true;
    await this.prepareBHXMaterialObject();
    if (this.bhxMaterial.id) {
      this.subscription.add(
        this.masterDataService.updateBHXMaterial(this.bhxMaterial).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addBHXMaterial(this.bhxMaterial).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  // if user changes the grouping when updating material we need to get confirmation that the current grouping values will be removed.
  checkMaterialGroupingData() {
    // If bhx material id is present in the data then get confirmation from user before allowing the change that this action will remove existing grouping data
    if (this.bhxMaterial.id && !this.bhxMaterial.allFieldsOptionChecked) {
      const matDataConfig = new MatDialogConfig();
      matDataConfig.width = PopupSize.size.popup_md;
      matDataConfig.panelClass = 'sfl-confirm-grouping-change-model';
      const dialogRef = this.matDialog.open(ConfirmGroupingChangeComponent, matDataConfig);
      dialogRef.afterClosed().subscribe((changeGrouping: boolean) => {
        if (changeGrouping) {
          // nullify the grouping data
          this.resetBHXMaterialOBject(this.currentGrouping);
        } else {
          // if no reset the grouping to the existing
          this.bhxMaterial.grouping = this.currentGrouping;
        }
      });
    } else if (!this.bhxMaterial.allFieldsOptionChecked) {
      // this will execute the default case in reset BHX Material method
      this.resetBHXMaterialOBject(0);
    }
  }

  // takes the current grouping id [1,2,3,4,5] and resets the BHX Material object as grouping is being changed.
  resetBHXMaterialOBject(currentGrouping: number) {
    // We need to only preserve the Common values rest will be set to null
    // common value are:: Part number, type, description, rel.Op., UOM, Quantity, Quantity Identity/ Formula, Blocked, Customer. So we are not touching that

    switch (currentGrouping) {
      case 1:
        // 1 grouping label values are:: max diameter, min diameter, jacket type
        this.bhxMaterial.maxDiameter = null;
        this.bhxMaterial.minDiameter = null;
        break;
      case 2:
        // 2 grouping element sensor values are:: element type, wire type, sens. conn, sens. type, max len, min len, min jump, sleeving, thermostat
        this.bhxMaterial.elementType = null;
        this.bhxMaterial.wireType = null;
        this.elementType = [];
        this.wireType = [];
        this.bhxMaterial.sensConn = null;
        this.bhxMaterial.sensorType = null;
        this.sensConn = [];
        this.sensorType = [];
        this.bhxMaterial.maxLength = null;
        this.bhxMaterial.minLength = null;
        this.bhxMaterial.minJumpers = null;
        this.bhxMaterial.sleeving = null;
        this.bhxMaterial.thermostat = null;
        break;
      case 3:
        // 3 grouping Facing liner closure values are:: layered material, closure, max diameter, min diameter, sen. conn
        this.bhxMaterial.layered = null;
        this.bhxMaterial.closure = null;
        break;
      case 4:
        // 4 grouping Wire plugging values are:: ele type, wire type, plug, connectors, sens. conn, sleeving, green light
        this.bhxMaterial.plug = null;
        this.bhxMaterial.connector = null;
        this.bhxMaterial.greenLight = null;
        break;
      case 5:
        // 5 grouping Operation values are:: operation name, sequence, op. numeber, prod hours, setup hours
        this.bhxMaterial.operationName = null;
        this.bhxMaterial.sequence = null;
        this.bhxMaterial.opNumber = null;
        this.bhxMaterial.prodHrs = null;
        this.bhxMaterial.setupHrs = null;
        break;
      default:
        // clean all grouping when user is creating a new material at that time if user switches the grouping we need to clean all the object
        this.bhxMaterial.maxDiameter = null;
        this.bhxMaterial.minDiameter = null;

        this.bhxMaterial.elementType = null;
        this.bhxMaterial.wireType = null;
        this.elementType = [];
        this.wireType = [];
        this.bhxMaterial.sensConn = null;
        this.bhxMaterial.sensorType = null;
        this.sensConn = [];
        this.sensorType = [];
        this.bhxMaterial.maxLength = null;
        this.bhxMaterial.minLength = null;
        this.bhxMaterial.minJumpers = null;
        this.bhxMaterial.sleeving = null;
        this.bhxMaterial.thermostat = null;

        this.bhxMaterial.layered = null;
        this.bhxMaterial.closure = null;

        this.bhxMaterial.plug = null;
        this.bhxMaterial.connector = null;
        this.bhxMaterial.greenLight = null;

        this.bhxMaterial.operationName = null;
        this.bhxMaterial.sequence = null;
        this.bhxMaterial.opNumber = null;
        this.bhxMaterial.prodHrs = null;
        this.bhxMaterial.setupHrs = null;
        break;
    }
  }

  addFormula() {
    const matDataConfig = new MatDialogConfig();
    let formula = this.masterDataService.equationFormatterFromFullNameToConst(this.strFormula);
    formula = formula ? formula.replace(/\s/g, '') : '';
    matDataConfig.data = { formula: formula, strFormula: this.strFormula };
    matDataConfig.width = PopupSize.size.popup_xxlg;
    matDataConfig.panelClass = 'sfl-formula-generator-model';
    const dialogRef = this.matDialog.open(EquationFormulaGeneratorComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res.success) {
        this.strFormula = res.strExpression;
        this.bhxMaterial.formula = res.expression.join('');
        this.snakbarService.success('BHX Material Formula' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success);
        this.checkQuantityAndFormula();
      }
    });
  }
  clearFormula() {
    this.strFormula = '';
    this.bhxMaterial.formula = '';
    this.checkQuantityAndFormula();
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  checkQuantityAndFormula() {
    if (this.bhxMaterial.qty) {
      this.disFormulaBtn = true;
      this.disQty = false;
    } else if (this.bhxMaterial.formula) {
      this.disFormulaBtn = false;
      this.disQty = true;
    } else {
      this.disFormulaBtn = false;
      this.disQty = false;
    }
  }

  // triggers when user checks the show all fields check box
  toggleShowAllFields(showAll: boolean) {
    // if check box is checked then we are showing all the fields and we need to load all the required master data
    if (showAll) {
      this.getMaterialList();
      this.getSensorsConnectors();
      this.getSensorsTypes();
      this.getSleevingTypes();
      this.getPlugsAndConnectors();
      this.getClosureMaterials();
    }
    // at any point if user wishish to toggle all fields visibility we need to nullify all the fields so unwanted values would not be stored in. Passing 0 to resetBHXMaterialOBject() will trigger the default case
    this.resetBHXMaterialOBject(0);
  }

  // the method is used to set the specified field will be set to Null, True or False
  setCheckBoxTriStateValues(fieldValue: boolean, field: string) {
    switch (fieldValue) {
      case true: {
        this.checkFieldWhichNeedsToBeUpdated(field, false);
        break;
      }
      case false: {
        this.checkFieldWhichNeedsToBeUpdated(field, null);
        break;
      }
      case null: {
        this.checkFieldWhichNeedsToBeUpdated(field, true);
        break;
      }
    }
  }

  // Takes the field and it's value to be updated with, switching between Cloth Ce, Cloth Ul, Hazardous and Thermostat fields and sets the Null, False or True supplied as `valueToUpdate`
  checkFieldWhichNeedsToBeUpdated(field: string, valueToUpdate: boolean) {
    switch (field) {
      case this.ClothCECheckBoxTitle:
        this.bhxMaterial.ce = valueToUpdate;
        break;
      case this.ClothULCheckBoxTitle:
        this.bhxMaterial.ul = valueToUpdate;
        break;
      case this.HazardousCheckBoxTitle:
        this.bhxMaterial.hazardous = valueToUpdate;
        break;
      case this.ThermostatCheckBoxTitle:
        this.bhxMaterial.thermostat = valueToUpdate;
        break;
      case this.ManualResetTSCheckBox:
          this.bhxMaterial.manualResetThermostat = valueToUpdate;
        break;
      case this.PrivateLabelCheckBox:
          this.bhxMaterial.privateLabel = valueToUpdate;
        break;
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
