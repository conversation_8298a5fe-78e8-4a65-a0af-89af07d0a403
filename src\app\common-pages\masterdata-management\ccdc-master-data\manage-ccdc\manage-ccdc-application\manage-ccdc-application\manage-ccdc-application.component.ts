import { Component, Input, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AccessoriesService } from 'src/app/admin-pages/accessories/accessories.service';
import { AccessoryControllers } from 'src/app/admin-pages/accessories/Add Accessories/add-accessories.model';
import { AddApplicationService } from 'src/app/admin-pages/new-quotation/Add Applicaton/add-application.service';
import { Material } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SnakbarService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { ApplicationInfoTemplateDTO } from '../../manage-ccdc.model';

@Component({
  selector: 'app-manage-ccdc-application',
  templateUrl: './manage-ccdc-application.component.html',
  styleUrls: ['./manage-ccdc-application.component.css']
})
export class ManageCcdcApplicationComponent implements OnInit {
  @Input("applicationInfoDto")
  applicationInfoDto: ApplicationInfoTemplateDTO;
  @Input("measureUnit")
  measureUnit: string;
  @Input("tempUnit")
  tempUnit: string;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  subscription = new Subscription();
  pipeMaterial: Material[] = [];
  pipeContent: Material[] = [];
  accessoryControllers: AccessoryControllers[];
  appMaterial: Material;
  appContent: Material;
  jacketTypes: object = Values.JacketTypeConst;
  phaseTypes: object = Values.PhaseTypeConst;
  contentMotions: object = Values.ContentMotionsConst;
  productTypes: object = Values.ProductTypeConst;
  fitTypes: object = Values.FitTypeConst;
  isOtherContent = false;
  isOtherMaterial = false;
  isOther = false;
  selectedJacketType: string;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  showReloadButton = false;

  constructor(
    private addApplicationService: AddApplicationService,
    private accessoriesService: AccessoriesService,
    private readonly snakbarService: SnakbarService,
  ) { }

  ngOnInit() {
    this.appMaterial = new Material();
    this.appContent = new Material();
    this.getMaterialList();
    this.getControlerTypeMasterData();
    this.getApplicationInfo();
  }

  // used to get the controller types master data
  getControlerTypeMasterData() {
    this.showLoader = true;
    this.subscription.add(
      this.accessoriesService.getAccessoryControllers().subscribe(
        (res: AccessoryControllers[]) => {
          this.accessoryControllers = res;
          this.showLoader = false;
        },
        (error) => {
          if (error.applicationStatusCode === 1225) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false
        }
      )
    );
  }

  // used to get the material listings
  getMaterialList() {
    this.showLoader = true;
    this.subscription.add(
      this.addApplicationService.getMaterialList().subscribe(
        (res: Material[]) => {
          res.forEach(element => {
            if (element.materialType.toLowerCase() === 'material') {
              this.pipeMaterial.push(element);
            } else if (element.materialType.toLowerCase() === 'content') {
              this.pipeContent.push(element);
            }
          });
          this.getApplicationInfo();
          this.showLoader = false;
        },
        (error) => {
          this.showLoader = false;
          if (error.applicationStatusCode === 1225) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }


  // used to get the application info
  getApplicationInfo() {
    if (this.applicationInfoDto) {
      if (this.applicationInfoDto.materialId) {
        this.setMaterialValues(this.applicationInfoDto.materialId);
      }
      if (this.applicationInfoDto.contentId) {
        this.setContentValues(this.applicationInfoDto.contentId);
      }
      if (this.applicationInfoDto.controlType === Values.Other) {
        this.isOther = true;
      }
      this.selectedJacketType = this.applicationInfoDto.jacketType;
    }
    else {
      this.applicationInfoDto = new ApplicationInfoTemplateDTO();
    }
  }

  onPipeContentChange(value) {
    if (value) {
      this.applicationInfoDto.contentId = value;
      this.setContentValues(value);
    }
  }

  onFitTypeChange(value) {
    if (value) {
      this.applicationInfoDto.fitTypeEnum = value;
    }
  }


  onPipeMaterialChange(value) {
    if (value) {
      this.applicationInfoDto.materialId = value;
      this.setMaterialValues(value);
    }
  }

  setMaterialValues(value) {
    this.isOtherMaterial = false;
    this.appMaterial = this.pipeMaterial.find(x => x.id === value);
    if (this.appMaterial) {
      if (this.appMaterial.name === Values.Other) {
        this.isOtherMaterial = true;
      } else {
        this.applicationInfoDto.materialHeat = this.appMaterial.specificHeat;
        this.applicationInfoDto.materialDensity = this.appMaterial.density;
        this.applicationInfoDto.otherMaterialDensity = null;
        this.applicationInfoDto.otherMaterialHeat = null;
      }
    }
  }

  setContentValues(value) {
    this.isOtherContent = false;
    this.appContent = this.pipeContent.find(x => x.id === value);
    if (this.appContent) {
      if (this.appContent.name === Values.Other) {
        this.isOtherContent = true;
      } else {
        this.applicationInfoDto.contentHeat = this.appContent.specificHeat;
        this.applicationInfoDto.contentDensity = this.appContent.density;
        this.applicationInfoDto.otherContentHeat = null;
        this.applicationInfoDto.otherContentDensity = null;
      }
    }
  }

  onControlTypeChanged(value) {
    this.isOther = false;
    if (value === Values.Other) {
      this.isOther = true;
    } else {
      this.applicationInfoDto.otherControlType = null;
    }
  }

  // used to reload the page
  reloadPage(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

