import {JacketGroup} from '../ccdc-model/ccdc.model';
import {Subscription} from 'rxjs';
import {JacketGroupService} from './manage-jacket-groups.service';
import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef, MatTableDataSource} from '@angular/material';
import {SnakbarService, SweetAlertService} from '../../../shared';
import {SummarySalesOrderComponent} from '../summary-sales-order.component';

@Component({
  selector: 'sfl-manage-jacket',
  templateUrl: './manage-jacket-groups.component.html'
})
export class ManageJacketComponent implements OnInit, OnDestroy {
  addNew = false;
  isSave = false;
  isCopy = false;
  quotationId: number;
  revisionId: number;
  jacketName: string;
  subscription: Subscription = new Subscription();
  jacketGroup: JacketGroup;
  jacketGroupList: JacketGroup[] = new Array<JacketGroup>();
  jacketGroupId = null;
  displayedColumns = ['jacket', 'action'];
  dataSource = new MatTableDataSource<JacketGroup>();

  constructor(
    private snakbarService: SnakbarService,
    public dialogRef: MatDialogRef<SummarySalesOrderComponent>,
    public jacketGroupService: JacketGroupService,
    private sweetAlertService: SweetAlertService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.quotationId = data.quotationId;
    this.revisionId = data.revisionId;
  }

  ngOnInit() {
    this.jacketGroup = new JacketGroup();
    this.getJacketsGroupByQuotation(this.revisionId);
  }

  getJacketsGroupByQuotation(quotationId) {
    this.subscription.add(this.jacketGroupService.getJacketGroupList(quotationId).subscribe((res: JacketGroup[]) => {
      this.jacketGroupList = res;
      this.dataSource.data = res;
    }));
  }

  newJacket() {
    this.jacketGroup = new JacketGroup();
    this.jacketName = '';
    this.jacketGroupId = null;
    this.addNew = true;
    this.isSave = true;
  }

  editJacketGroup(jacketGroup) {
    this.addNew = true;
    this.isSave = true;
    this.jacketGroup = jacketGroup;
    this.jacketName = this.jacketGroup.name;
  }

  copyJacketGroup(jacketGroup: JacketGroup) {
    this.isCopy = true;
    this.jacketGroupId = jacketGroup.id;
    this.jacketName = jacketGroup.name + ' - Duplicate';;
  }

  async deleteJacketGroup(id) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.subscription.add(this.jacketGroupService.deleteJacketGroup(id).subscribe(() => {
        this.getJacketsGroupByQuotation(this.revisionId);
      }));
    }
  }

  copyJacket(){
    this.subscription.add(this.jacketGroupService.copyJacketGroup(this.jacketGroupId,this.jacketName).subscribe(() => {
      this.getJacketsGroupByQuotation(this.revisionId);
      this.isCopy = false;
    }));
  }

  saveJacket() {
      this.jacketGroup.revisionId = this.revisionId;
      this.jacketGroup.name = this.jacketName;
      this.jacketGroup.quotationId = this.quotationId;
      this.subscription.add(this.jacketGroupService.saveJacket(this.jacketGroup).subscribe((res: JacketGroup[]) => {
        this.isSave = false;
        this.addNew = false;
        this.getJacketsGroupByQuotation(this.revisionId);
        this.jacketName = '';
      }));

  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
