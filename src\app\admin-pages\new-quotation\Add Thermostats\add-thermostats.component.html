<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Thermostat Information
  <hr />
</h2>
<mat-dialog-content>
  <div fxLayout="column">
  <form #thermostateForm="ngForm">

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
        <input
          matInput
          type="number"
          placeholder="Desired Temperature ({{ tempUnit ? tempUnit : '' }})"
          [(ngModel)]="searchThermostate.openTemp"
          name="desiredTemp"
        />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="10" fxFlex.gt-md="10" fxFlex.gt-sm="10" fxFlex.gt-xs="100">
        <mat-select placeholder="Open On Rise" [(ngModel)]="defaultSelected" name="openonrise">
          <mat-option [value]="true">Yes</mat-option>
          <mat-option [value]="false">No</mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
        <input
          matInput
          type="text"
          placeholder="Search by Part Number"
          [(ngModel)]="searchThermostate.partNumber"
          name="partNumber"
        />
      </mat-form-field>
      <div class="mt-10" fxFlex.gt-lg="10" fxFlex.gt-md="10" fxFlex.gt-sm="10" fxFlex.gt-xs="100">
        <mat-checkbox color="warn" [(ngModel)]="searchThermostate.manualReset" name="manualreset">Manual Reset</mat-checkbox>&nbsp;&nbsp;
      </div>
      <div fxFlex.gt-lg="4" fxFlex.gt-md="7" fxFlex.gt-sm="10" fxFlex.gt-xs="100">
        <button mat-raised-button color="warn" type="submit" (click)="searchThermosateList(thermostateForm)">Search</button>
      </div>
      <div fxFlex.gt-lg="4" fxFlex.gt-md="7" fxFlex.gt-sm="10" fxFlex.gt-xs="100">
        <button mat-raised-button type="submit" (click)="reset(thermostateForm)">Reset</button>
      </div>
      <div fxFlex.gt-lg="27" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-xs="100">
        <button mat-raised-button type="submit" (click)="openOtherThermostat()">Other Thermostat</button>
      </div>
    </div>
  </form>
  <div class="highlight-mat-table cust_table">
    <mat-table [dataSource]="thermostatdataSource">
      <ng-container matColumnDef="openTemp">
        <mat-header-cell *matHeaderCellDef>Action Temp Open/Close ({{ tempUnit ? tempUnit : '' }}) </mat-header-cell>
        <span *ngIf="tempUnit === '°C'">
        <mat-cell *matCellDef="let element">{{ element?.openOnRise === true ? element?.openTemp : element?.closeTemp }}</mat-cell>
        </span>
        <span *ngIf="tempUnit === '°F'">
        <mat-cell *matCellDef="let element">{{ element?.openOnRise === true ? element?.openTempF : element?.closeTempF }}</mat-cell>
        </span>
      </ng-container>
      <!-- <ng-container matColumnDef="closeTemp"> keeping this, it might be needed in future
        <mat-header-cell *matHeaderCellDef> Action Temp Close ({{ tempUnit ? tempUnit : '' }}) </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ tempUnit === '°F' ? element?.closeTempF : element?.closeTemp }}</mat-cell>
      </ng-container> -->
      <ng-container matColumnDef="tolerance">
        <mat-header-cell *matHeaderCellDef fxFlex="8"> Tolerance </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ tempUnit === '°F' ? element?.toleranceF : element?.tolerance }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="amps">
        <mat-header-cell *matHeaderCellDef> AMPS </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.amps }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="type">
        <mat-header-cell *matHeaderCellDef> Type </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.type }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="partNumber">
        <mat-header-cell *matHeaderCellDef> Part Number </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.partNumber }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="cost">
        <mat-header-cell *matHeaderCellDef> Cost </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.cost }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="openOnRise">
        <mat-header-cell *matHeaderCellDef> Open On Rise </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.openOnRise | convertToYesNo }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="manualReset">
        <mat-header-cell *matHeaderCellDef> Manual Reset </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.manualReset | convertToYesNo }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="volts">
        <mat-header-cell *matHeaderCellDef> Volts </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.volts }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="thermostatType">
        <mat-header-cell *matHeaderCellDef fxFlex="10"> Thermostat Type </mat-header-cell>
        <mat-cell *matCellDef="let element">
          <mat-select placeholder="Select Type" name="thermostatType" [(ngModel)]="element.thermostatType">
            <mat-option *ngFor="let type of thermostatType" [value]="type">
              {{ type?.id }}
            </mat-option>
          </mat-select>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="installationMethod">
        <mat-header-cell *matHeaderCellDef fxFlex="7"> Installation Method </mat-header-cell>
        <mat-cell *matCellDef="let element">
          <mat-select placeholder="Installation Method" name="installationMethod" [(ngModel)]="element.installationMethodDTO">
            <mat-option *ngFor="let installationMethod of thermostatInstallationMethod" [value]="installationMethod">
              {{ installationMethod?.methodName }}
            </mat-option>
          </mat-select>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="usaStock">
        <mat-header-cell *matHeaderCellDef> USA Stock </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.usaStock }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="vietnamStock">
        <mat-header-cell *matHeaderCellDef> Vietnam Stock </mat-header-cell>
        <mat-cell *matCellDef="let element">{{ element?.vietnamStock }}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="action">
        <mat-header-cell *matHeaderCellDef> Action </mat-header-cell>
        <mat-cell *matCellDef="let element">
          <mat-icon class="open-doc" (click)="addThermostat(element)">add</mat-icon>
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="thermostatsdisplayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: thermostatsdisplayedColumns"></mat-row>
    </mat-table>
    <mat-paginator [pageSize]="10" [pageSizeOptions]="pageSize" showFirstLastButtons></mat-paginator>
  </div>
  <div *ngIf="selecetdthermostatdataSource.data.length > 0" class="mt-10">
    <h2 mat-dialog-title>
      Selected Thermostat
      <hr />
    </h2>
    <div class="highlight-mat-table cust_table">
      <mat-table [dataSource]="selecetdthermostatdataSource">
        <ng-container matColumnDef="openTemp">
          <mat-header-cell *matHeaderCellDef> Open Temp ({{ tempUnit ? tempUnit : '' }}) </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ tempUnit === '°F' ? element?.openTempF : element?.openTemp }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="closeTemp">
          <mat-header-cell *matHeaderCellDef> Close Temp ({{ tempUnit ? tempUnit : '' }}) </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ tempUnit === '°F' ? element?.closeTempF : element?.closeTemp }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="tolerance">
          <mat-header-cell *matHeaderCellDef> Tolerance </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ tempUnit === '°F' ? element?.toleranceF : element?.tolerance }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="amps">
          <mat-header-cell *matHeaderCellDef> AMPS </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.amps }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="type">
          <mat-header-cell *matHeaderCellDef> Type </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.type ? element?.type : 'Other' }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element">{{
            element?.partNumber ? element?.partNumber : element?.otherThermostatPartNumber
          }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="cost">
          <mat-header-cell *matHeaderCellDef> Cost </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.cost ? element?.cost : element.otherThermostatCost }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="openOnRise">
          <mat-header-cell *matHeaderCellDef> Open On Rise </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.openOnRise | convertToYesNo }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="manualReset">
          <mat-header-cell *matHeaderCellDef> Manual Reset </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.manualReset | convertToYesNo }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="volts">
          <mat-header-cell *matHeaderCellDef> Volts </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.volts }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="thermostatType">
          <mat-header-cell *matHeaderCellDef> Thermostat Type </mat-header-cell>
          <mat-cell *matCellDef="let element">
            {{ element?.thermostatType?.id ? element?.thermostatType?.id : element?.otherThermostatType?.id }}</mat-cell
          >
        </ng-container>
        <ng-container matColumnDef="installationMethod">
          <mat-header-cell *matHeaderCellDef> Installation Method </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{ element?.installationMethodDTO?.methodName }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="usaStock">
          <mat-header-cell *matHeaderCellDef> USA Stock </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.usaStock }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="vietnamStock">
          <mat-header-cell *matHeaderCellDef> Vietnam Stock </mat-header-cell>
          <mat-cell *matCellDef="let element">{{ element?.vietnamStock }}</mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef> Action </mat-header-cell>
          <mat-cell *matCellDef="let element">
            <mat-icon class="open-doc" (click)="remove(element)">delete</mat-icon>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="selecetdthermostatsdisplayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: selecetdthermostatsdisplayedColumns"></mat-row>
      </mat-table>
    </div>

</div>
  <div fxFlex="100" class="mt-10">
    <mat-form-field>
      <textarea matInput placeholder="Notes" rows="5" [(ngModel)]="thermostatInfo.notes"></textarea>
    </mat-form-field>
  </div>
</div>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button color="warn" type="submit" (click)="saveThermostat('save')" name="save">Save</button>
    <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="submit" (click)="saveThermostat('saveandnext')" name="saveandnext">Save And Next</button>
  </div>
</mat-dialog-actions>
