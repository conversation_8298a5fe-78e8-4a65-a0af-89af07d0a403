<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addEstEngRelDate()">Add New Release Date</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="estRelDateDataSource" (matSortChange)="getEstEngRelMasterSorting($event)">
        <ng-container matColumnDef="prodType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Product Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.prodType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="quoteStatus">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Quote Status </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.quoteStatus }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="noOfDays">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Estimated Eng. Release Date </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30"> {{'Today + '+ element?.noOfDays+ ' Business Days'}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editEstEngRelDate(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteEstEngRel(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="estRelDateColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: estRelDateColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!estRelDateDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getEstEngRelMasterPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
