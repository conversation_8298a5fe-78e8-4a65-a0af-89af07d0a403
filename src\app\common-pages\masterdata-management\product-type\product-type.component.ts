import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Messages } from 'src/app/shared/constants/messages.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SnakbarService } from 'src/app/shared/service/snakbar.service';
import {
  GenericPageable,
  ProductFilter,
  ProductTypeCoverPageMaster
} from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { ManageProductTypeComponent } from './manage-product-type/manage-product-type.component';

@Component({
  selector: 'sfl-product-type',
  templateUrl: './product-type.component.html',
})
export class ProductTypeComponent implements OnInit, OnDestroy {
  pageTitle = 'Product Type Master';
  productType: ProductTypeCoverPageMaster;
  productTypePageable: GenericPageable<ProductTypeCoverPageMaster>;
  productTypeDataSource = new MatTableDataSource<ProductTypeCoverPageMaster>();
  productTypeColumns = DisplayColumns.Cols.ProductTypeCoverPage;
  productFilter: ProductFilter = new ProductFilter();
  dataSource = new MatTableDataSource<ProductTypeCoverPageMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  numberOfElements: number;
  filter = [];
  showLoader = true;
  filterFieldName = Values.FilterFields.name;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(private masterDataService: MasterdataManagementService, private readonly matDialog: MatDialog, private readonly snakbarService: SnakbarService, private readonly sweetAlertService: SweetAlertService,) {}

  ngOnInit() {
    this.getProductTypeMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add filter to Product type listing
  async addFilter() {
    this.filter = this.productFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.productFilter.name }];
    this.getProductTypeMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of Product type listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.productFilter.name = '') : this.productFilter.name,
      },
    ];
    this.getProductTypeMasterData(this.initialPageIndex, this.pageSize);
  }

  getProductTypeMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getProductTypeMasterData(this.filter, pageable).subscribe(
        (res: GenericPageable<ProductTypeCoverPageMaster>) => {
          this.productTypePageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createProductTypeTable(this.productTypePageable);
          this.showLoader = false;
        },
        (error) => {
          this.showLoader = false;
          this.productTypeDataSource.data = [];
        },
      ),
    );
  }

  createProductTypeTable(serviceRequestList: GenericPageable<ProductTypeCoverPageMaster>) {
    this.productTypeDataSource.data = serviceRequestList.content;
  }

  getProductTypePagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getProductTypeMasterData(this.pageIndex, this.pageSize);
  }

  getProductTypeSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getProductTypeMasterData(this.pageIndex, this.pageSize);
  }

  addProductType() {
    this.editProductType(new ProductTypeCoverPageMaster());
  }

  editProductType(productTypes: ProductTypeCoverPageMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = productTypes;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-product-type-master-model';
    const dialogRef = this.matDialog.open(ManageProductTypeComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((res) => {
      if (res) {
        this.snakbarService.success(
          productTypes.id ? 'Product type' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success : 'Product type' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success,
        );
        this.getProductTypeMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  async deleteProductType(departmentsId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteProductType(departmentsId).subscribe(
        () => {
          this.snakbarService.success('Product type' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getProductTypeMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
