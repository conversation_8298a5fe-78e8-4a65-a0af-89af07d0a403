import { Component, OnInit, <PERSON><PERSON>hil<PERSON>, OnDestroy } from '@angular/core';
import {
  ProductTypeCoverPageMaster,
  ProductTypeCoverPageMasterPageable,
  ProductFilter,
  GenericPageable
} from '../masterdata-management.model';
import { MatTableDataSource, MatSort, MatPaginator, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-product-type',
  templateUrl: './product-type.component.html'
})
export class ProductTypeComponent implements OnInit, OnDestroy {
  pageTitle = 'Product Type Master';
  productType: ProductTypeCoverPageMaster;
  productTypePageable: GenericPageable<ProductTypeCoverPageMaster>;
  productTypeDataSource = new MatTableDataSource<ProductTypeCoverPageMaster>();
  productTypeColumns = DisplayColumns.Cols.ProductTypeCoverPage;
  productFilter: ProductFilter = new ProductFilter();
  dataSource = new MatTableDataSource<ProductTypeCoverPageMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  showLoader = true;
  filterFieldName = Values.FilterFields.name;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(private masterDataService: MasterdataManagementService) {}

  ngOnInit() {
    this.getProductTypeMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add filter to Product type listing
  async addFilter() {
    this.filter = this.productFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.productFilter.name }];
    this.getProductTypeMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of Product type listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.productFilter.name = '') : this.productFilter.name
      }
    ];
    this.getProductTypeMasterData(this.initialPageIndex, this.pageSize);
  }

  getProductTypeMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getProductTypeMasterData(this.filter, pageable).subscribe(
        (res: GenericPageable<ProductTypeCoverPageMaster>) => {
          this.productTypePageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.createProductTypeTable(this.productTypePageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.productTypeDataSource.data = [];
        }
      )
    );
  }

  createProductTypeTable(serviceRequestList: GenericPageable<ProductTypeCoverPageMaster>) {
    this.productTypeDataSource.data = serviceRequestList.content;
  }

  getProductTypePagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getProductTypeMasterData(this.pageIndex, this.pageSize);
  }

  getProductTypeSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getProductTypeMasterData(this.pageIndex, this.pageSize);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
