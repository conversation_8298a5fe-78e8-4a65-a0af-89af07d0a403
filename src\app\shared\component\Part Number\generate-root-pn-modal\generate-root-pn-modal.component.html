<h2 mat-dialog-title>
  Please add Root Part Number
  <hr>
</h2>
<mat-dialog-content>
  <ul class="error-msg rootPN-message">
    <li>If you want auto generated Root P/N please cancel this prompt and click Regenerate Part Number button</li>
    <li>Or you can even create Root P/N here also.</li>
    <li> In order to create Standard or Non-Standard P/N you need to create Root P/N first.</li>
  </ul>
  <hr>
  <div class="mt-10" fxFlex="30">
    <mat-form-field>
      <input matInput placeholder="Root P/N" name="rootPN" [(ngModel)]="rootPN">
    </mat-form-field>
  </div>
</mat-dialog-content>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button type="submit" (click)="closeDialog()" >Cancel</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button color="warn" type="submit" (click)="generateNonStandardPN()" [disabled]="!rootPN">Generate Non Standard P/N</button>
    <button mat-raised-button type="submit" color="warn" (click)="createRootPN()" [disabled]="!rootPN">Create Root P/N</button>
  </div>



</mat-dialog-actions>
