import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { QuoteCompleted, QuoteCompletedDetailed, GenericPageable, FilterPageable, FilterDateRange } from '../../quote-tracker.model';
import { Variable } from '../../../shared/constants/Variable.constants';
import { MatTableDataSource } from '@angular/material';
import { DisplayColumns } from '../../../shared/constants/displayColName.constants';
import { QuoteTrackerService } from '../../quote-tracker.service';
import { DatePipe } from '@angular/common';
import { Values } from '../../../shared/constants/values.constants';

@Component({
  selector: 'sfl-quote-completed-detailed',
  templateUrl: './quote-completed-detailed.component.html'
})
export class QuoteCompletedDetailedComponent implements OnInit, OnDestroy {
  quoteCompletedTitle = 'Quote Completed Detailed';
  totalNoOfDesign: number;
  totalDollarValue: number;
  subscription = new Subscription();

  @Input() dataSource: QuoteCompleted;
  @Input() fromDate: Date;
  @Input() toDate: Date;
  quoteCompletedDetailedDataSource = new MatTableDataSource<QuoteCompletedDetailed>();
  quoteCompletedDetailedColumns = DisplayColumns.Cols.QuoteCompletedDetailedColumn;
  quoteCompletedDetailedPageable: GenericPageable<QuoteCompletedDetailed>;

  showLoader = false;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.sortByQuotation_number;
  ascSort = Variable.sortAscending;
  numberOfElements: number;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  constructor(private readonly quoteTrackerService: QuoteTrackerService, private readonly datePipe: DatePipe) {}

  ngOnInit() {
    this.quoteCompletedTitle = 'Quote Completed for ' + this.dataSource.classification;
    this.totalNoOfDesign = this.dataSource.noOfDesign;
    this.totalDollarValue = this.dataSource.dollarValue;
    this.getDetailedQuoteCompletedByType(this.initialPageIndex, this.pageSize);
  }

  // used to get the details of quotes which are completed based on the provided product type
  getDetailedQuoteCompletedByType(pageIndex, pageSize) {
    this.showLoader = true;
    // const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    const pageable: FilterPageable = { page: pageIndex, size: pageSize, sort: this.sortField + ',' + this.sortOrder };
    const dateFilter: FilterDateRange = {
      startDate: this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen),
      endDate: this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen)
    };
    this.subscription.add(
      this.quoteTrackerService.getQuoteCompletedDetailed(pageable, dateFilter, this.dataSource.classification.toUpperCase()).subscribe(
        (res: GenericPageable<QuoteCompletedDetailed>) => {
          this.quoteCompletedDetailedPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createTable(this.quoteCompletedDetailedPageable);
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  createTable(serviceRequestList: GenericPageable<QuoteCompletedDetailed>) {
    this.quoteCompletedDetailedDataSource.data = serviceRequestList.content;
  }
  getPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getDetailedQuoteCompletedByType(this.pageIndex, this.pageSize);
  }
  getSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortOrder = event.direction;
    this.sortField = event.active;
    this.getDetailedQuoteCompletedByType(this.pageIndex, this.pageSize);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
