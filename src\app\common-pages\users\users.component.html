<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div class="mb-10 cust_fields d-flex justify-content-between align-items-center flex-wrap">
      <div>
        <mat-card-title>User List</mat-card-title>
      </div>
      <div>
        <form #searchUser="ngForm" class="d-flex">
          <mat-form-field>
            <mat-label>Search for User</mat-label>
            <input matInput [(ngModel)]="userFilter.name" name="username" /> </mat-form-field
          >&nbsp;
          <mat-actions-row class="d-flex align-items-baseline">
            <button mat-raised-button color="warn" type="submit" (click)="applyFilter()" [disabled]="!userFilter?.name">Search</button
            >&nbsp; <button mat-raised-button color="warn" (click)="resetFilter()">Reset</button>&nbsp;
            <button mat-raised-button color="warn" (click)="addUser()">New User</button>
          </mat-actions-row>
        </form>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        aria-describedby="users listing table"
        class="w-auto"
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="dataSource"
        (matSortChange)="getSorting($event)"
      >
        <ng-container matColumnDef="firstName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.firstName }} {{ element?.lastName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="email">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="35"> Email </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.email }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="login">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Username </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.login }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="activated">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Status </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.activated ? 'Active' : 'Disable' }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="authorities">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Role </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15">
            {{
              element?.authorities == 'ROLE_DES_ENG'
                ? 'Design Eng'
                : element?.authorities == 'ROLE_ADMIN'
                ? 'Admin'
                : element?.authorities == 'ROLE_APP_ENG'
                ? 'Application Eng'
                : element?.authorities == 'ROLE_SALES'
                ? 'Sales'
                : ''
            }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="country">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Country </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.country | uppercase }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="initialName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Initial Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.initialName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5" class="mw-fit"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5" class="mw-fit">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editUser(element.login)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteUser(element.login)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </mat-table>
    </div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
