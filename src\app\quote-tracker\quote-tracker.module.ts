import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { VirtualScrollerModule } from 'ngx-virtual-scroller';
import { SharedModule } from '../shared/shared.module';
import { AnimateCounterComponent } from './animate-counter/animate-counter.component';
import { AppEnggQuoteTrackerComponent } from './app-engg-quote-tracker/app-engg-quote-tracker.component';
import { DesignEnggQuoteTrackerComponent } from './design-engg-quote-tracker/design-engg-quote-tracker.component';
import { QuoteCompletedDetailedComponent } from './quote-completed/quote-completed-detailed/quote-completed-detailed.component';
import { QuoteCompletedComponent } from './quote-completed/quote-completed.component';
import { QuoteTrackerComponent } from './quote-tracker.component';
import { QuoteTrackerRoutes } from './quote-tracker.route';
import { SoInDesignDetailedComponent } from './so-in-design-grid/so-in-design-detailed/so-in-design-detailed.component';
import { SoInDesignGridComponent } from './so-in-design-grid/so-in-design-grid.component';
import { DesignEnggQuoteTrackerColumnColorComponent } from './design-engg-quote-tracker/design-engg-quote-tracker-column-color/design-engg-quote-tracker-column-color.component';
import { AppEnggQuoteTrackerRowColourComponent } from './app-engg-quote-tracker/app-engg-quote-tracker-row-colour/app-engg-quote-tracker-row-colour.component';
import { AppEnggQuoteTrackerColumnColourComponent } from './app-engg-quote-tracker/app-engg-quote-tracker-column-colour/app-engg-quote-tracker-column-colour.component';

@NgModule({
  imports: [RouterModule.forChild(QuoteTrackerRoutes), SharedModule, VirtualScrollerModule],
  declarations: [QuoteTrackerComponent, AnimateCounterComponent, SoInDesignGridComponent, SoInDesignDetailedComponent, QuoteCompletedComponent, QuoteCompletedDetailedComponent, AppEnggQuoteTrackerComponent, DesignEnggQuoteTrackerComponent, DesignEnggQuoteTrackerColumnColorComponent, AppEnggQuoteTrackerRowColourComponent, AppEnggQuoteTrackerColumnColourComponent],
  entryComponents: [DesignEnggQuoteTrackerColumnColorComponent, AppEnggQuoteTrackerRowColourComponent, AppEnggQuoteTrackerColumnColourComponent],
})
export class QuoteTrackerModule { }
