import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { catchError, map } from 'rxjs/operators';


import { BehaviorSubject } from 'rxjs';
import { TitleBlockHighlighterDTO } from 'src/app/design-pages/design-plan/title-block-editor/title-block-editor.model';
import { QuotationSearchFilter, Status } from '../../admin-pages/dashboard/dashboard.model';
import { AppConfig } from '../../app.config';
import {
  ConnectionInfoForSharedDriveUSA,
  ConnectionInfoForSharedDriveVietnam
} from '../constants/shared-drive-config.constants';
import { utils } from '../helpers/app.helper';

@Injectable()
export class SharedService {
  public currentFeatureForAutoFocus: number;

  private readonly dataSource = new BehaviorSubject<boolean>(true);
  isCurrentRevisionActive = this.dataSource.asObservable();

  private readonly designSearchResult = new BehaviorSubject<QuotationSearchFilter>(new QuotationSearchFilter());
  refreshDesignSearchResult = this.designSearchResult.asObservable();

  private readonly searchResult = new BehaviorSubject<QuotationSearchFilter>(new QuotationSearchFilter());
  refreshResult = this.searchResult.asObservable();

  constructor(
    private readonly $localStorage: LocalStorageService,
    private readonly $sessionStorage: SessionStorageService,
    private readonly http: HttpClient,
    private readonly router: Router,
    private readonly cookieService: CookieService
  ) { }

  // Set and Get Role...
  storeRole(role, rememberMe) {
    if (rememberMe) {
      this.$localStorage.store('role', role);
    } else {
      this.$sessionStorage.store('role', role);
    }
  }

  getRole() {
    return this.$localStorage.retrieve('role') || this.$sessionStorage.retrieve('role');
  }

  getUserToken() {
    return this.$localStorage.retrieve('access_token');
  }

  getUserName() {
    return this.$localStorage.retrieve('userName');
  }

  getUserId() {
    return this.$localStorage.retrieve('userId');
  }
  getObject(objectKey: string) {
    const res = JSON.parse(localStorage.getItem(objectKey));
    return res;
  }

  setUserRole(role: string) {
    this.$localStorage.store('role', role);
  }

  setUserToken(token: string) {
    this.$localStorage.store('access_token', token);
  }

  setUserId(userId: string) {
    this.$localStorage.store('userId', userId);
  }

  getProjectName() {
    return this.$localStorage.retrieve('projectName');
  }
  setProjectName(projectName: string) {
    this.$localStorage.store('projectName', projectName);
  }

  setObject(objectKey: string, objectValue: any) {
    localStorage.setItem(objectKey, typeof objectValue === 'object' ? JSON.stringify(objectValue) : objectValue);
  }

  setUserName(username: string) {
    this.$localStorage.store('userName', username);
  }

  // used to set the user's country location
  setUsersCountry(country: string) {
    this.$localStorage.store('country', country);
  }

  // used to return the country location of the logged in user
  getUsersCountry(): string {
    return this.$localStorage.retrieve('country');
  }

  // used to return the shared drive configuration based on the user's location
  getSharedDriveServerConfigurations(): ConnectionInfoForSharedDriveVietnam | ConnectionInfoForSharedDriveUSA {
    if ('Vietnam' === this.getUsersCountry()) {
      return new ConnectionInfoForSharedDriveVietnam();
    } else {
      // if user's location is other than Vietnam set it to USA, so by default UAS config would apply if user's location is null
      return new ConnectionInfoForSharedDriveUSA();
    }
  }

  isLoggedIn() {
    const token = this.getUserToken();
    if (token != null) {
      return true;
    }
    return false;
  }

  getAllStatus() {
    return this.getAllStatusAPI();
  }

  getAllStatusByType(type:string,statusId:number) {
    return this.getAllStatusAPIByType(type,statusId);
  }

  getAllStatusAPIByType(type:string,statusId:number): Promise<Status[]> {
    return new Promise(resolve => {
      this.getAllStatusesByTypeFromApi(type,statusId).subscribe((res: Status[]) => {
        resolve(res);
      });
    });
  }

  getAllStatusAPI(): Promise<Status[]> {
    return new Promise(resolve => {
      this.getAllStatusesFromApi().subscribe((res: Status[]) => {
        resolve(res);
      });
    });
  }

  getStatuesByTypeOrderByOrderNumber() {
    return this.http.get(AppConfig.STATUS_LIST).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getAllStatusesFromApi() {
    return this.http.get(AppConfig.STATUS_LIST).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getAllStatusesByTypeFromApi(type:string,statusId:number) {
    return this.http.get(AppConfig.STATUS_LIST_BY_TYPE+type+'/'+statusId).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  setDiameter(revisionId: number, diameter: number) {
    const diameterValue = new Map(JSON.parse(this.$localStorage.retrieve('diameter')));
    diameterValue.set(revisionId, diameter);
    this.$localStorage.store('diameter', JSON.stringify(Array.from(diameterValue)));
  }

  getDiameter(revisionId: number) {
    const diameterValue = new Map(JSON.parse(this.$localStorage.retrieve('diameter')));
    return diameterValue.get(revisionId) as number;
  }

  setJacketGroup(revisionId: number, jacketGroupId: number) {
    const jacketGroupMap = new Map(JSON.parse(this.$localStorage.retrieve('jacketGroup')));
    jacketGroupMap.set(revisionId, jacketGroupId);
    this.$localStorage.store('jacketGroup', JSON.stringify(Array.from(jacketGroupMap)));
  }

  getJacketGroup(revisionId: number) {
    const jacketGroupMap = new Map(JSON.parse(this.$localStorage.retrieve('jacketGroup')));
    return jacketGroupMap.get(revisionId) as number;
  }

  setCurrentRevisionActive(active: boolean) {
    this.dataSource.next(active);
  }

  setDesignQuotationSearchObject(searchObject) {
    this.designSearchResult.next(searchObject);
  }
  setQuotationList(searchResult) {
    this.searchResult.next(searchResult);
  }

  highlightTitleBlockFields(titleBlockHighlighterDTO: TitleBlockHighlighterDTO) {
    return this.http.post(AppConfig.HIGHLIGHT_TITLE_BLOCK, titleBlockHighlighterDTO).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  setFilterItem(value: any) {
    this.$localStorage.store('tableFilters', JSON.stringify(value));
  }

  getFilterItem(): any {
    const value = this.$localStorage.retrieve('tableFilters');
    return value ? JSON.parse(value) : null;
  }

  removeFilterItem() {
    this.$localStorage.clear('tableFilters');
  }

  // Logout...
  public logout(stateUrl: string) {
    this.$localStorage.clear('role');
    this.$localStorage.clear('access_token');
    this.$localStorage.clear('objectKey');
    this.$localStorage.clear('token');
    this.$localStorage.clear('statuses');
    this.$localStorage.clear('userName');
    this.$localStorage.clear('jacketGroup');
    this.$localStorage.clear('projectName');
    this.$localStorage.clear('currentUser');
    this.$localStorage.clear('measureUnit');
    this.$localStorage.clear('tempUnit');
    this.$localStorage.clear('diameter');
    this.$localStorage.clear('userId');
    this.$localStorage.clear('country');
    this.cookieService.delete('access_token');
    this.cookieService.delete('session_token');
    this.setQuotationList(new QuotationSearchFilter());
    this.setDesignQuotationSearchObject(new QuotationSearchFilter());

    this.router.navigate(['/login'], { queryParams: { returnUrl: stateUrl } });
  }
}
