import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { PowerCordOptionsMaster, GenericPageable, PowerCordOptionsFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { ManagePowerCordOptionsComponent } from './manage-power-cord-options/manage-power-cord-options.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-power-cord-options',
  templateUrl: './power-cord-options.component.html'
})
export class PowerCordOptionsComponent implements OnInit, OnDestroy {
  pageTitle = 'Power Cord Options Master';
  powerCordOptions: PowerCordOptionsMaster;
  powerCordOptionsPageable: GenericPageable<PowerCordOptionsMaster>;
  powerCordOptionsDataSource = new MatTableDataSource<PowerCordOptionsMaster>();
  powerCordOptionsColumns = DisplayColumns.Cols.PowerCordOptionsCols;

  dataSource = new MatTableDataSource<PowerCordOptionsMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  powerCordOptionsFilter: PowerCordOptionsFilter = new PowerCordOptionsFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldValue = Values.FilterFields.value;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getPowerCordOptionsMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new Power Cord Options
  addPowerCordOptions() {
    this.editPowerCordOptions(new PowerCordOptionsMaster());
  }

  // used to add filter to Power Cord Options listing
  async addFilter() {
    this.filter =
      this.powerCordOptionsFilter.value === '' ? [] : [{ key: this.filterFieldValue, value: this.powerCordOptionsFilter.value }];
    this.getPowerCordOptionsMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of Power Cord Options listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldValue,
        value: fieldToClear === this.filterFieldValue ? (this.powerCordOptionsFilter.value = '') : this.powerCordOptionsFilter.value
      }
    ];
    this.getPowerCordOptionsMasterData(this.initialPageIndex, this.pageSize);
  }

  getPowerCordOptionsMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPowerCordOptionsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<PowerCordOptionsMaster>) => {
          this.powerCordOptionsPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPowerCordOptionsTable(this.powerCordOptionsPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createPowerCordOptionsTable(serviceRequestList: GenericPageable<PowerCordOptionsMaster>) {
    this.powerCordOptionsDataSource.data = serviceRequestList.content;
  }

  getPowerCordOptionsPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPowerCordOptionsMasterData(this.pageIndex, this.pageSize);
  }

  getPowerCordOptionsSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPowerCordOptionsMasterData(this.pageIndex, this.pageSize);
  }

  editPowerCordOptions(powerCordOptions: PowerCordOptionsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = powerCordOptions;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-power-cord-Options-master-model';
    const dialogRef = this.matDialog.open(ManagePowerCordOptionsComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          powerCordOptions.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPowerCordOptionsMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deletePowerCordOptions(powerCordOptionsId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePowerCordOptions(powerCordOptionsId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPowerCordOptionsMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
