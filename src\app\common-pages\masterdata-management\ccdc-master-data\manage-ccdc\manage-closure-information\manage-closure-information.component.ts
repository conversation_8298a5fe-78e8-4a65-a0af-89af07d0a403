import { Component, Input, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AddClosureService } from 'src/app/admin-pages/new-quotation/Add Closure/add-closure.service';
import { ClosureMaterial } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { environment } from 'src/environments/environment';
import { ClosureTemplateDTO } from '../manage-ccdc.model';

@Component({
  selector: 'app-manage-closure-information',
  templateUrl: './manage-closure-information.component.html',
  styleUrls: ['./manage-closure-information.component.css']
})
export class ManageClosureInformationComponent implements OnInit {
  @Input("closureTemplatedto")
  closureTemplatedto: ClosureTemplateDTO;
  closureMaterial: ClosureMaterial;
  @Input("tempUnit")
  tempUnit: string;
  @Input("measureUnit")
  measureUnit: string;
  materials: ClosureMaterial[];
  material: ClosureMaterial;
  subscription: Subscription = new Subscription();
  isOther = false;
  imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  showReloadButton = false;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public addClosureService: AddClosureService
  ) { }

  ngOnInit() {
    if (!this.closureTemplatedto) {
      this.closureTemplatedto = new ClosureTemplateDTO();
    }
    this.material = new ClosureMaterial();
    this.getMaterialList();
  }

  getClosure() {
    if (this.closureTemplatedto) {
      this.closureTemplatedto = this.closureTemplatedto;
      if (this.closureTemplatedto.closureMaterialId) {
        this.material = this.materials.find(x => x.id === this.closureTemplatedto.closureMaterialId);
        if (this.closureTemplatedto.otherClosure) {
          this.isOther = true;
        }
        if (this.closureTemplatedto.closureMaterialImageUrl) {
          this.imageUrl = environment.IMAGES_URL + this.closureTemplatedto.closureMaterialImageUrl;
        }
      }
    }

  }


  // used to get the materials listing
  getMaterialList() {
    this.showLoader = true;
    this.subscription.add(
      this.addClosureService.getClosureMaterialList().subscribe(
        (res: ClosureMaterial[]) => {
          this.materials = res;
          this.showLoader = false;
          this.getClosure();
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to handle the material change
  onMaterialChange(id) {
    this.isOther = false;
    this.material = this.materials.find(x => x.id === id);
    if (this.material.name === Values.Other) {
      this.isOther = true;
    } else {
      this.closureTemplatedto.otherClosure = null;
      this.closureTemplatedto.otherClosurePartNumber = null;
      this.closureTemplatedto.otherClosureCost = null;
    }
    this.closureTemplatedto.closureMaterialId = id;
    if (this.material.imageUrl) {
      this.imageUrl = environment.IMAGES_URL + this.material.imageUrl;
    } else {
      this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
    }
  }

  // used to reload the page
  reloadPage(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
