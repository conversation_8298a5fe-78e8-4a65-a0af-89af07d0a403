<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding" [ngStyle]="{ overflow: 'visible !important' }">
  <mat-card class="cust_table bhx-material-tbl-header">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>

      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="5px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Label Part Number</mat-label>
          <input matInput [(ngModel)]="partNumberFilter.labelPartNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldKeyName)"
            *ngIf="partNumberFilter.labelPartNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addLabelMaster()">Add New Label Master Configuration</button>
      </div>
    </div>
    <div class="cust_table label-material-tbl">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="labelMasterDataSource"
        (matSortChange)="getLabelMaterialMasterSorting($event)"
      >
        <ng-container matColumnDef="labelPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Label Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.labelPartNumber }}">
            {{ element?.labelPartNumber }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="format">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Format </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.format }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.format }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="volts">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Volts </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.volts }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.volts }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="watts">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Watts </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.watts }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.watts }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="amps">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Amps </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.amps }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.amps }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="phase">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Phase </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.phase }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.phase }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="size">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Size </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.size }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.size }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="width">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Width </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.width }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.width }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="length">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Length </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.length }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.length }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="csv">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> CS Volts </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.csv }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.csv }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="csa">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> CS Amps </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.csa }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.csa }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="mhlv">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> MHL Volts </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.mhlv }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.mhlv }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="mhla">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> MHL Amps </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.mhla }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.mhla }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="tempRange">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Temp Range </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.tempRange }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.tempRange }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="lowT">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Low Temp </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.lowT }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.lowT }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="highT">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> High Temp </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.highT }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.highT }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="modelNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Model Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.modelNumber }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.modelNumber }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="imageUrl">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Image URL </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.imageUrl }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.imageUrl }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="open1">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Open 1 </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.open1 }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.open1 }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="open2">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Open 2 </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.open2 }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.open2 }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="open3">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="30"> Open 3 </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30" matTooltip="{{ element?.open3 }}" matTooltipClass="sfl-formula-tooltip">
            {{ element?.open3 }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="20"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editLabelMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteLabelMaster(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="ccdcMaterialMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: ccdcMaterialMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!labelMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getLabelMaterialMasterPagination($event)"
      showFirstLastButtons
      class="bhx-material-paginator"
    >
    </mat-paginator>
  </mat-card>
</div>
