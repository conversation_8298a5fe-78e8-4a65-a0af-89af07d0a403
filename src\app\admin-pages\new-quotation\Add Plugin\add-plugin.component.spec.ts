import { SalesOrderSummaryService } from '../summary-sales-order.service';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { inject, TestBed, async } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/catch';
import { PluggingInformation } from '../ccdc-model/ccdc.model';

describe('PLUIGIN INFORMATION TESTCASE', () => {

    const jgId = 3008;
    const pgId = null;

    const jumperPlug = {
        'id': null,
        'jumperLength': 10,
        'quantity': 1,
        'plugId': 4,
        'plugName': null
    };

    const leadPlug = {
        'id': null,
        'leadLength': 10,
        'plugId': 3,
        'plugName': null
    };

    const dummyPlugin = {
        'id': null,
        'jacketGroupId': 3008,
        'notes': null,
        'greenLightId': 2,
        'greenLightName': null,
        'otherGreenLight': null,
        'otherGreenLightPartNumber': null,
        'otherGreenLightCost': null,
        'redLightName': null,
        'redLightId': 5,
        'otherRedLight': null,
        'otherRedLightPartNumber': null,
        'otherRedLightCost': null,
        'sleevingTypeId': 2,
        'sleevingTypeName': null,
        'otherSleevingType': null,
        'otherSleevingTypePartNumber': null,
        'otherSleevingTypeCost': null,
        'strainReliefId': 2,
        'strainReliefName': null,
        'otherStrainRelief': null,
        'otherStrainReliefPartNumber': null,
        'otherStrainReliefCost': null,
        'otherPlug': null,
        'otherPlugPartNumber': null,
        'otherPlugCost': null,
        'otherConnector': null,
        'otherConnectorPartNumber': null,
        'otherConnectorCost': null,
        'leadPlugDTO': leadPlug,
        'jumperPlugDTO': jumperPlug,
    };



    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                HttpClientModule,
                HttpClientTestingModule
            ]
        });
    });

    it('Should add a pluging information when savePluggingInformation() call', async(inject([HttpClient], (http: HttpClient) => {
        const service = new SalesOrderSummaryService(http);
        service.savePluggingInformation(this.dummyPlugin, pgId);

        expect(service.savePluggingInformation(this.dummyPlugin, pgId).subscribe((res: PluggingInformation) => {
            expect(res).toEqual(this.dummyPlugin);
        }));
    })));

    it('Should get a plugin information when getPluggingInformationByJacketGroupId() call', async(inject([HttpClient], (http: HttpClient) => {
        const service = new SalesOrderSummaryService(http);

        expect(service.getPluggingInformationByJacketGroupId(jgId).subscribe((res: PluggingInformation) => {
            expect(res).toEqual(this.dummyPlugin);
        }));
    })));

});
