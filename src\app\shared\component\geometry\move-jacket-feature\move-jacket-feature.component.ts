import { ChangeDetectorRef, Component, Inject, Input, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { Jacket } from 'src/app/design-pages/jacket-list/jacket-list.model';
import { JacketListService } from 'src/app/design-pages/jacket-list/jacket-list.service';
import { Messages, SnakbarService } from 'src/app/shared';
import { MoveJacketFeaturesDTO } from '../geometry.model';

@Component({
  selector: 'app-move-jacket-feature',
  templateUrl: './move-jacket-feature.component.html',
  styleUrls: ['./move-jacket-feature.component.css']
})
export class MoveJacketFeatureComponent implements OnInit {
  subscription: Subscription = new Subscription();
  newFeatureId: [];
  _revisionId: number;
  moveJacketFeaturesDTO: MoveJacketFeaturesDTO = new MoveJacketFeaturesDTO()
  newJacketList: Jacket[];
  jacketID: number;
  selectedJacket: number;
  jacketGroupId: number;
  reference: string;
  discount: number;
  buttonDisabled: boolean;

  constructor(
    private readonly jacketListService: JacketListService,
    private cdr: ChangeDetectorRef,
    private snakbarService: SnakbarService,
    public dialogRef: MatDialogRef<MoveJacketFeatureComponent>,
    @Inject(MAT_DIALOG_DATA) data

  ) {
    this.newFeatureId = data.newFeatureId
    this._revisionId = data._revisionId;
    this.jacketID = data.jacketID;
    this.jacketGroupId = data.jacketGroupId;
    this.reference = data.reference;
    this.discount = data.discount;
  }

  ngOnInit() {
    this.getJacketListDropdown();
  }

  getJacketListDropdown() {
    this.subscription.add(
      this.jacketListService.getJacketListDropdownByRevId(this._revisionId).subscribe(res => {
        if (res) {
          this.newJacketList = res;
        }
      }
     ));
  }

  selectJacket(value) {
    this.selectedJacket = value;
    if(value) {
      this.buttonDisabled = true;
    } else {
      this.buttonDisabled = false;
    }
    if(value === 'New Jacket') {
      this.moveJacketFeaturesDTO.newJacket = true;
      this.moveJacketFeaturesDTO.jacketGroupId = null;
      this.moveJacketFeaturesDTO.revisionId = null;
      this.moveJacketFeaturesDTO.jacketId = this.jacketID;
      this.moveJacketFeaturesDTO.revisionId = this._revisionId;
      this.moveJacketFeaturesDTO.jacketGroupId = this.jacketGroupId;
    } else {
      this.moveJacketFeaturesDTO.newJacket = false;
      this.moveJacketFeaturesDTO.jacketGroupId = this.jacketGroupId;
      this.moveJacketFeaturesDTO.revisionId = this._revisionId;
      this.moveJacketFeaturesDTO.jacketId = this.selectedJacket;
    }
    this.moveJacketFeaturesDTO.jacketFeatureIdList = this.newFeatureId;
    this.moveJacketFeaturesDTO.reference = this.reference;
    this.moveJacketFeaturesDTO.discount = this.discount;
    this.moveJacketFeaturesDTO.centerLine = true;
  }


  moveFeaturesToJacket() {
    this.subscription.add(
      this.jacketListService.moveJacketFeatures(this.moveJacketFeaturesDTO).subscribe(res => {
        if (res) {
          this.cdr.detectChanges();
          this.closeDialog();
          this.snakbarService.success(Messages.FEATURE.features_moved);
        }
      }
     ));

  }

  closeDialog(): void {
    this.dialogRef.close();
}


}
