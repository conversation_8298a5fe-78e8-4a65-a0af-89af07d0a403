<h2 mat-dialog-title>Add Accessories
  <hr>
</h2>
<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
      <mat-select placeholder="Select Controller" name="controller" [(ngModel)]="selectedController"
        #controllerSelect="ngModel" (selectionChange)="onControllerChange($event.value)">
        <mat-option *ngFor="let controller of accessoryControllers" [value]="controller.name">
          {{ controller?.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
      <input type="text" placeholder="Search Accessories Name" aria-label="Search Accessories Name" matInput [(ngModel)]="filterData.partNumber" [matAutocomplete]="autoAccessories"
      [formControl]="accessoriesControl" />
    </mat-form-field>
    <mat-autocomplete #autoAccessories="matAutocomplete" [displayWith]="displayAccessories" (optionSelected)="onSelectionChangesAccessories($event.option.value)">
      <mat-option *ngFor="let data of accessoriesObservable$ | async; let i = index" [value]="data">
        {{ data?.partNumber }}
      </mat-option>
    </mat-autocomplete>
    <button mat-raised-button color="warn" (click)="resetFilter()">Reset</button>
  </div><br>
  <div class="cust_table">
    <mat-table [dataSource]="dataSource">
      <ng-container matColumnDef="select">
        <mat-header-cell *matHeaderCellDef fxFlex="7">
          <mat-checkbox color="warn" (change)="$event ? masterToggle() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()">
          </mat-checkbox>
        </mat-header-cell>
        <mat-cell *matCellDef="let row" fxFlex="7">
          <mat-checkbox color="warn" (click)="$event.stopPropagation()"
            (change)="onAccessorySelectionChange($event, row)" [checked]="selection.isSelected(row)">
          </mat-checkbox>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="partNumber">
        <mat-header-cell *matHeaderCellDef fxFlex="15"> Accessories Name </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="15">{{element?.partNumber}}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="controllertype">
        <mat-header-cell *matHeaderCellDef fxFlex="10"> Controller </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="10">{{element?.controllerName}}</mat-cell>
      </ng-container>
      <ng-container matColumnDef="description">
        <mat-header-cell *matHeaderCellDef fxFlex="70"> Accessories Description </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="70">
          {{element?.description}}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="listPrice">
        <mat-header-cell *matHeaderCellDef fxFlex="10"> List Price </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex="10">{{element?.listPrice}}</mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
    </mat-table>
    <mat-paginator [pageSize]="10" [pageSizeOptions]="pageSize" showFirstLastButtons></mat-paginator>
  </div>
</mat-dialog-content>
<hr>
<mat-dialog-actions>
  <button mat-raised-button color="warn" type="submit" (click)="saveAccessories()">Add</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
