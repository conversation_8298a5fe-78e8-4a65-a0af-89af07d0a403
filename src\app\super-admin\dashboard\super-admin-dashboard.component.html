<div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="50" fxFlex.gt-xs="100">
        <mat-card class="mat-card-yellow card-widget">
            <div mat-card-widget>
                <div mat-card-float-icon>
                    <mat-icon class="material-icons_md-48">check_circle</mat-icon>
                </div>
                <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
                    <h2 mat-card-widget-title>200</h2>
                    <p>Active User</p>
                </div>
            </div>
        </mat-card>
    </div>
    <div fxFlex.gt-lg="50" fxFlex.gt-md="50" fxFlex.gt-sm="50" fxFlex.gt-xs="100">
        <mat-card class="mat-card-purple card-widget">
            <div mat-card-widget>
                <div mat-card-float-icon>
                    <mat-icon class="material-icons_md-48">cancel</mat-icon>
                </div>
                <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
                    <h2 mat-card-widget-title>10</h2>
                    <p>Deactive User</p>
                </div>
            </div>
        </mat-card>
    </div>
</div>