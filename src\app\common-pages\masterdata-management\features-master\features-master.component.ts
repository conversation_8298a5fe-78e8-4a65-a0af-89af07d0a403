import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { FeaturesMaster, FeaturesMasterPageable, FeaturesFilter, GenericPageable } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-features-master',
  templateUrl: './features-master.component.html'
})
export class FeaturesMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Features Master';
  featuresMaster: FeaturesMaster;
  featuresMasterPageable: GenericPageable<FeaturesMaster>;
  featuresMasterDataSource = new MatTableDataSource<FeaturesMaster>();
  featuresMasterMasterColumns = DisplayColumns.Cols.FeaturesMaster;
  featuresFilter: FeaturesFilter = new FeaturesFilter();
  dataSource = new MatTableDataSource<FeaturesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  filterFieldName = Values.FilterFields.name;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(private readonly masterDataService: MasterdataManagementService) {}

  ngOnInit() {
    this.getFeaturesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add filter to features listing
  async addFilter() {
    this.filter = this.featuresFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.featuresFilter.name }];
    this.getFeaturesMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to features listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.featuresFilter.name = '') : this.featuresFilter.name
      }
    ];
    this.getFeaturesMasterData(this.initialPageIndex, this.pageSize);
  }

  getFeaturesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getFeatures(this.filter, pageable).subscribe(
        (res: GenericPageable<FeaturesMaster>) => {
          this.featuresMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.createFeaturesTable(this.featuresMasterPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.featuresMasterDataSource.data = [];
        }
      )
    );
  }

  createFeaturesTable(serviceRequestList: GenericPageable<FeaturesMaster>) {
    this.featuresMasterDataSource.data = serviceRequestList.content;
  }

  getFeaturesMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getFeaturesMasterData(this.pageIndex, this.pageSize);
  }

  getFeaturesMasterSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getFeaturesMasterData(this.pageIndex, this.pageSize);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
