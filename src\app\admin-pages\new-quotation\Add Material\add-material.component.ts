import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogConfig, MatDialogRef, MatTableDataSource } from '@angular/material';
import { Observable, Subscription } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { environment } from 'src/environments/environment';
import { SharedService, SnakbarService, SweetAlertService } from '../../../shared';
import { PopupSize } from '../../../shared/constants/popupsize.constants';
import { Values } from '../../../shared/constants/values.constants';
import { MaterialInfo, MaterialInfoReq, MaterialLayers, Units } from '../ccdc-model/ccdc.model';
import { ManageUnitsService } from '../manage-units/manage-units.service';
import { SalesOrderSummaryService } from '../summary-sales-order.service';
import { OtherMaterialComponent } from './Other/other-material.component';

@Component({
  selector: 'sfl-add-material',
  templateUrl: './add-material.component.html'
})
export class AddMaterialComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  materialLayerObject: MaterialLayers;
  selectedMaterialLayerObject: MaterialLayers;
  materialLayersList: MaterialLayers[];
  materialLayersDetails: MaterialLayers;
  materialLayersControl = new FormControl();
  materialLayersObservable$: Observable<MaterialLayers[]>;
  materialLayerObjectList: MaterialLayers[];
  materialLayersRequestObject: MaterialInfoReq;
  notes: string;
  jacketGroupId: number;
  lastFilter = '';
  selection: string;
  tempUnit = '';
  imageUrl = '..//..//..//assets//images//ft-logo.png';
  materialdisplayedColumns = ['layerName', 'material', 'partNumber', 'maxTemp', 'costPerSq', 'action', 'order'];
  materialDataSource = new MatTableDataSource<MaterialLayers>();

  layers = [
    {layerId: 1, name: 'Facing', value: 'Facing'},
    {layerId: 2, name: 'Insulation', value: 'Insulation'},
    {layerId: 3, name: 'Liner', value: 'Liner'}
  ];
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public materislDialogRef: MatDialogRef<AddMaterialComponent>,
    private matDialog: MatDialog,
    private salesOrderSummaryService: SalesOrderSummaryService,
    private sharedService: SharedService,
    private sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
    private manageUnitService: ManageUnitsService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketGroupId = data.jacketGroupId;
  }

  ngOnInit() {
    this.materialLayerObject = new MaterialLayers();
    this.materialLayerObjectList = new Array<MaterialLayers>();
    this.materialLayersRequestObject = new MaterialInfoReq();
    this.materialDataSource = new MatTableDataSource<MaterialLayers>();
    this.getMeasurementUnit();
    this.getMaterialLayersList();
    this.getMaterialLayersByJacketGroupId();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  // used to get the material layer list
  getMaterialLayersList() {
    this.showLoader = true;
    this.salesOrderSummaryService.getMaterialLayer().subscribe(
      (res: MaterialLayers[]) => {
        this.materialLayersList = res;
        this.materialLayersObservable$ = this.materialLayersControl.valueChanges.pipe(
          startWith<string | MaterialLayers[]>(''),
          map(value => (typeof value === 'string' ? value : this.lastFilter)),
          map(filter => this.filterMaterial(filter)),
          map(values => values.sort(function (a, b) {
            return a.material > b.material ? 1 : -1;
          }))
        );
        this.showLoader = false;
      },
      (error) => {
        this.showLoader = false;
        if (error.applicationStatusCode === 1226) {
          this.snakbarService.error(error.message);
        }
      }
    );
  }

  filterMaterial(filter: string): MaterialLayers[] {
    this.lastFilter = filter;
    if (filter) {
      return this.materialLayersList.filter(option => {
        if (option.materialId !== null && option.material !== null) {
          return (
            option.material.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.materialLayersList ? this.materialLayersList.slice() : [];
    }
  }

  displayMaterials(value: MaterialLayers[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          material.material;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  onSelectionMaterial(material: MaterialLayers) {
    if (material) {
      this.materialLayerObject.materialId = material.materialId;
      this.materialLayerObject.material = material.material;
      this.setMaterialLayerInfo(material.materialId);
    }
  }

  // used to handle the change of materials
  onChangeMaterial(data) {
    this.setMaterialLayerInfo(data);
  }

  // used to set the material layer info
  setMaterialLayerInfo(data) {
    this.selectedMaterialLayerObject = this.materialLayersList.find(s => s.materialId === data);
    this.materialLayerObject.material = this.selectedMaterialLayerObject.material;
    this.materialLayersControl.setValue(this.materialLayerObject.material);
    if (this.selectedMaterialLayerObject.material === Values.Other) {
      this.addOtherMaterial();
    } else {
      this.materialLayerObject.partNumber = this.selectedMaterialLayerObject.partNumber;
      this.materialLayerObject.material = this.selectedMaterialLayerObject.material;
      this.materialLayerObject.maxTempF = this.selectedMaterialLayerObject.maxTempF;
      this.materialLayerObject.maxTemp = this.selectedMaterialLayerObject.maxTemp;
      this.materialLayerObject.costPerSq = this.selectedMaterialLayerObject.costPerSq;
      this.materialLayerObject.inventory = this.selectedMaterialLayerObject.inventory;
    }
    if (this.selectedMaterialLayerObject.imageUrl) {
      this.imageUrl = environment.IMAGES_URL + this.selectedMaterialLayerObject.imageUrl;
    } else {
      this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
    }
  }

  // used to add new layer
  addLayer() {
    this.materialLayerObject.jacketGroupId = this.jacketGroupId;
    this.materialLayerObjectList.push(this.materialLayerObject);
    this.materialLayerObject = new MaterialLayers();
    this.materialDataSource.data = this.materialLayerObjectList;
    this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
    this.materialLayerObject.material = '';
  }

  // used to save the material info
  saveMaterial(value) {
    this.showLoader = true;
    this.materialLayersRequestObject.notes = this.notes;
    this.materialLayersRequestObject.materialInfoDTOList = this.materialDataSource.data;
    for (let i = 0; i < this.materialLayersRequestObject.materialInfoDTOList.length; i++) {
      this.materialLayersRequestObject.materialInfoDTOList[i].materialInfoIndex = i;
    }
    this.subscription = this.salesOrderSummaryService.saveMaterial(this.materialLayersRequestObject, this.jacketGroupId).subscribe(
      (res: MaterialInfo) => {
        const obj = {data: res, mode: value === 'save' ? 0 : 1};
        this.materialDataSource._updateChangeSubscription();
        this.materislDialogRef.close(obj);
        this.showLoader = false;
      },
      error => {
        this.showLoader = false;
        if (error.applicationStatusCode === 3001) {
          this.snakbarService.error(error.message);
        }
      }
    );
  }

  // used to remove the material from the list
  async remove(element) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      if (element.id) {
        this.salesOrderSummaryService.deleteMaterial(element.id).subscribe(
          () => {
            this.materialDataSource.data.splice(this.materialDataSource.data.indexOf(element), 1);
            this.materialDataSource._updateChangeSubscription();
            this.saveMaterial('save');
            this.showLoader = false;
          },
          () => (this.showLoader = false)
        );
      } else {
        this.materialDataSource.data.splice(this.materialDataSource.data.indexOf(element), 1);
        this.materialDataSource._updateChangeSubscription();
        this.showLoader = false;
      }
    }
  }

  closeDialog() {
    this.materislDialogRef.close();
  }

  // used to handle the Other Material option shows modal
  addOtherMaterial() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {materialObj: this.materialLayerObject};
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(OtherMaterialComponent, matDataConfig)
      .afterClosed()
      .subscribe((result: MaterialLayers) => {
        if (result) {
          result.jacketGroupId = this.jacketGroupId;
          this.materialLayerObjectList.push(result);
          this.materialLayerObject = new MaterialLayers();
          this.materialDataSource.data = this.materialLayerObjectList;
        } else {
          this.materialLayerObject = new MaterialLayers();
        }
      });
    this.ngOnInit();
  }

  // used to get the material layer by jacket group id
  getMaterialLayersByJacketGroupId() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getAllMaterial(this.jacketGroupId).subscribe(
        (res: MaterialInfoReq) => {
          this.materialLayerObjectList = res.materialInfoDTOList;
          this.notes = res.notes;
          this.materialDataSource.data = res.materialInfoDTOList;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to handle material list movement towards up side
  up(i) {
    if (i > 0) {
      const new_index = i - 1;
      this.materialDataSource.data.splice(new_index, 0, this.materialDataSource.data.splice(i, 1)[0]);
      this.materialDataSource._updateChangeSubscription();
    }
  }

  // used to handle material list movement towards down side
  down(i) {
    if (i < this.materialDataSource.data.length) {
      const new_index = i + 1;
      this.materialDataSource.data.splice(new_index, 0, this.materialDataSource.data.splice(i, 1)[0]);
      this.materialDataSource._updateChangeSubscription();
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
