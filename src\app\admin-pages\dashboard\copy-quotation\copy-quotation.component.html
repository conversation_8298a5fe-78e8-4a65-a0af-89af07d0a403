
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
  class="sfl-global-spinner-loader"
  [mode]="mode"
  [color]="color"
  [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<h2 mat-dialog-title>Copy Quotation
  <hr>
</h2>

<form #addQuotationForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-card-content>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <input matInput placeholder="Enter New Quote Number" ng-trim="true" [(ngModel)]="quoteNumber" [pattern]="quotationNumberPattern"
            autofocus name="quoteNumber" #quoteNumberInput="ngModel" required>
        </mat-form-field>
      </div>
    </mat-card-content>
  </mat-dialog-content>
  <hr>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="copyQuotationFromCurrentId()" [disabled]="addQuotationForm.invalid">Copy</button>
    <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</form>
