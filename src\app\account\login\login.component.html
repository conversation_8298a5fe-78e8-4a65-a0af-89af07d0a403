<section class="user">
  <div class="user_options-container">
    <mat-card>
      <mat-card-content>
        <div class="user_options-text">
          <div class="user_options-unregistered">
            <div class="user_unregistered-title">
              <img src="../../../assets/images/logo.png" alt="logo">
            </div>
            <p class="user_unregistered-text">Log in with your credentials to continue.</p>
            <p class="user_unregistered-text open-doc" (click)="openSolidworkDownloadPage()">Click here to download
              <strong>Solidwork config</strong> file.</p>
            <div class="mat-badge-warn" *ngIf="isError">
              <strong>Failed to sign in!</strong> Please check your credentials and try again.
            </div>
          </div>
        </div>
      </mat-card-content>
      <div class="user_options-forms" id="user_options-forms">
        <mat-card>
          <mat-card-content>
            <div class="user_forms-login">
              <h2 class="forms_title">Login</h2>
              <form class="forms_form" #loginForm="ngForm" (ngSubmit)="login()">
                <mat-form-field class="forms_fieldset">
                  <input matInput type="email" name="email" [(ngModel)]="email" #emailInput="ngModel"
                    placeholder="Username" autofocus required>
                </mat-form-field>
                <div *ngIf="emailInput.touched && emailInput.invalid">
                  <small class="mat-text-warn" *ngIf="emailInput?.errors.required">Username is required.</small>
                </div>
                <mat-form-field>
                  <input matInput type="password" placeholder="Password" name="password" [(ngModel)]="password"
                    #passwordInput="ngModel" placeholder="Password" required>
                </mat-form-field>
                <div *ngIf="passwordInput.touched && passwordInput.invalid">
                  <small class="mat-text-warn" *ngIf="passwordInput?.errors.required">Password is required.</small>
                </div>
                <div class="forms_buttons">
                  <button type="button" class="forms_buttons-forgot" [routerLink]="['/forgot-password']">Forgot
                    password?
                  </button>
                  <button mat-raised-button type="submit" color="warn"
                    [disabled]="loginForm.form.invalid">Login</button>
                </div>
              </form>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-card>
  </div>
</section>
