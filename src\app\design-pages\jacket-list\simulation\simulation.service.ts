import {catchError, map} from 'rxjs/operators';
import {AppConfig} from './../../../app.config';
import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {utils} from '../../../shared/helpers/app.helper';


@Injectable({providedIn: 'root'})
export class SimulationService {

  constructor(private http: HttpClient) {
  }

  saveSimulation(review) {
    return this.http.post(AppConfig.SIMULATION_SAVE, review).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  updateSimulation(review, jacketId) {
    return this.http.delete(AppConfig.SIMULATION_UPDATE + jacketId, review).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getSimulationByJacketId(jacketId) {
    return this.http.get(AppConfig.SIMULATION_SAVE + '/jacket/' + jacketId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
