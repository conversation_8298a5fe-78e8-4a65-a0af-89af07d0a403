import { Component, Inject, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { PartNumberComponent } from './part-number.component';

@Component({
    selector: 'sfl-pn-errorlist',
    templateUrl: './partnumber-error.component.html'
})

export class PNErrorComponent implements OnInit, OnDestroy {

    errorList = new Array<string>();
    subscription = new Subscription();

    constructor(
        public dialogRef: MatDialogRef<PartNumberComponent>,
        @Inject(MAT_DIALOG_DATA) data,
    ) {
        this.errorList = data.errorList;
    }

    ngOnInit() { }

    closeDialog(): void {
        this.dialogRef.close();
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
