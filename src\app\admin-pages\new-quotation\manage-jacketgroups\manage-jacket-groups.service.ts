import {catchError, map} from 'rxjs/operators';
import {CcdcTemplateDTO, CcdcTemplateExportDTO, JacketGroup} from '../ccdc-model/ccdc.model';
import {AppConfig} from '../../../app.config';
import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {utils} from '../../../shared/helpers/app.helper';
import {createRequestOption} from 'src/app/shared';

@Injectable()
export class JacketGroupService {
    constructor(private http: HttpClient) { }

    getJacketGroupList(quotationId: number) {
        return this.http.get(AppConfig.GET_JACKETS_GROUP + '/revision/' + quotationId).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    getCCDCMaster(CcdcMaterial, pageableObject) {
      return this.http
        .post(AppConfig.CCDC_MASTER_DATA_PAGEABLE, CcdcMaterial, {
          params: createRequestOption(pageableObject)
        }).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
    }


    importCcdcTemplateForJacketGroup(jacketGroupId:number, ccdcTemplateDTO:CcdcTemplateDTO ) {
      return this.http
        .post(AppConfig.CCDC_TEMPLATE_API+"/import/" + jacketGroupId, ccdcTemplateDTO).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
    }

    exportCcdcTemplateForJacketGroup(ccdcTemplateExportDTO:CcdcTemplateExportDTO) {
      return this.http
      .post(AppConfig.CCDC_TEMPLATE_API + "/export", ccdcTemplateExportDTO).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
    }

    saveJacket(jacket: JacketGroup) {
        if (jacket.id) {
            return this.http.put(AppConfig.GET_JACKETS_GROUP, jacket).pipe(map(utils.extractData),
                catchError(utils.handleError),);
        } else {
            return this.http.post(AppConfig.GET_JACKETS_GROUP, jacket).pipe(map(utils.extractData),
                catchError(utils.handleError),);
        }
    }

    deleteJacketGroup(jacketGroupId) {
        return this.http.delete(AppConfig.GET_JACKETS_GROUP + '/' + jacketGroupId).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    copyJacketGroup(jacketGroupId,name) {
        return this.http.post(AppConfig.GET_JACKETS_GROUP + '/copyFrom/' + jacketGroupId+'/'+name, null).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    getJacketGroupsByRevisionId(revisionId: number) {
        return this.http.get(AppConfig.GET_JACKETS_GROUP + '/revision/' + revisionId).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    getJacketGroupById(jacketGroupId: number) {
        return this.http.get(AppConfig.GET_JACKETS_GROUP + '/' + jacketGroupId).pipe(catchError(utils.handleError));
    }
}
