import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child } from '@angular/core';
import { AccessoryMaster, AccessoryMasterPageable, AccessoryFilter, GenericPageable } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageAccessoryComponent } from './manage-accessory/manage-accessory.component';
import { SweetAlertService, Messages, SnakbarService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-accessory',
  templateUrl: './accessory-master.component.html'
})
export class AccessoryMasterComponent implements OnIni<PERSON>, OnDestroy {
  pageTitle = 'Accessory Master';
  accessory: AccessoryMaster;
  accessoryPageable: GenericPageable<AccessoryMaster>;
  accessoryDataSource = new MatTableDataSource<AccessoryMaster>();
  accessoryColumns = DisplayColumns.Cols.AccessoryMaster;

  dataSource = new MatTableDataSource<AccessoryMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  accessoryFilter: AccessoryFilter = new AccessoryFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldPartNumber = Values.FilterFields.partNumber;
  filterFieldControllerName = Values.FilterFields.controllerName;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getAccessoryMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new accessory
  addAccessory() {
    this.editAccessory(new AccessoryMaster());
  }

  // used to filter accessories listing
  addFilter() {
    this.filter = [
      { key: this.filterFieldPartNumber, value: !this.accessoryFilter.partNumber ? '' : this.accessoryFilter.partNumber },
      { key: this.filterFieldControllerName, value: !this.accessoryFilter.controllerName ? '' : this.accessoryFilter.controllerName }
    ];
    this.getAccessoryMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear accessories listing filter
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber,
        value: fieldToClear === this.filterFieldPartNumber ? (this.accessoryFilter.partNumber = '') : this.accessoryFilter.partNumber
      },
      {
        key: this.filterFieldControllerName,
        value:
          fieldToClear === this.filterFieldControllerName ? (this.accessoryFilter.controllerName = '') : this.accessoryFilter.controllerName
      }
    ];
    this.getAccessoryMasterData(this.initialPageIndex, this.pageSize);
  }

  getAccessoryMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getAccessoriesList(this.filter, pageable).subscribe(
        (res: GenericPageable<AccessoryMaster>) => {
          this.accessoryPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createAccessoryTable(this.accessoryPageable);
          this.showLoader = false;
        },
        error => {
          if (error.applicationStatusCode === 1210) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false;
        }
      )
    );
  }

  createAccessoryTable(serviceRequestList: GenericPageable<AccessoryMaster>) {
    this.accessoryDataSource.data = serviceRequestList.content;
  }

  getAccessoryPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getAccessoryMasterData(this.pageIndex, this.pageSize);
  }

  getAccessorySorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getAccessoryMasterData(this.pageIndex, this.pageSize);
  }

  editAccessory(accessory: AccessoryMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = accessory;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-accessory-master-model';
    const dialogRef = this.matDialog.open(ManageAccessoryComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          accessory.id
            ? 'Accessory' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Accessory' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getAccessoryMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteAccessory(accessoryId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteAccessory(accessoryId).subscribe(
        () => {
          this.snakbarService.success('Accessory' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getAccessoryMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
