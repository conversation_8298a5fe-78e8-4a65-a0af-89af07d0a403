import { Component, OnInit, Input, ViewChild, ElementRef, AfterViewInit, OnChanges, SimpleChanges } from '@angular/core';

@Component({
  selector: 'sfl-animate-counter',
  templateUrl: './animate-counter.component.html',
  styleUrls: ['./animate-counter.component.css']
})
export class AnimateCounterComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() duration: number;
  @Input() digit: number;
  @Input() steps: number;
  @ViewChild('animatedDigit') animatedDigit: ElementRef;

  constructor() {}

  ngOnInit() {}

  // used to begin the counter animation with duration default set to 1000
  animateCount() {
    if (!this.duration) {
      this.duration = 1000;
    }

    if (typeof this.digit === 'number') {
      this.counterFunc(this.digit, this.duration, this.animatedDigit);
    }
  }

  // used to take the counter end value, the animation duration in ms and element ref
  counterFunc(endValue: number, durationMs: number, element: ElementRef) {
    if (!this.steps) {
      this.steps = 12;
    }

    const stepCount = Math.abs(durationMs / this.steps);
    const valueIncrement = (endValue - 0) / stepCount;
    const sinValueIncrement = Math.PI / stepCount;

    let currentValue = 0;
    let currentSinValue = 0;

    function step() {
      currentSinValue += sinValueIncrement;
      currentValue += valueIncrement * Math.sin(currentSinValue) ** 2 * 2;

      element.nativeElement.textContent = Math.abs(Math.floor(currentValue));

      if (currentSinValue < Math.PI) {
        window.requestAnimationFrame(step);
      }
    }

    step();
  }

  ngAfterViewInit() {
    if (this.digit) {
      this.animateCount();
    }
  }

  // if any changes made in to the supplied digit value the animation for the counter will re-triggered
  ngOnChanges(changes: SimpleChanges) {
    if (changes['digit']) {
      this.animateCount();
    }
  }
}
