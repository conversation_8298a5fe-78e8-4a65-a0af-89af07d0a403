<h2 mat-dialog-title>Select Colour To Highlight Row
  <hr>
</h2>

<form #colourSelectForm="ngForm" role="form">
<div class="container-fluid">
  <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
    <mat-select placeholder="Select Colour" name ="colorSelect" #selectedColour>
      <mat-option *ngFor="let color of highlightedColors" [value]="color?.value" [ngStyle]="{'background': color?.value}">
        {{ color?.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>
</div>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" [disabled]="!selectedColour?.value" (click)="selectRow(selectedColour?.value)">Select Colour</button>
    <button mat-raised-button type="submit" (click)="closeDialog(false)">Cancel</button>
  </mat-dialog-actions>
</form>

