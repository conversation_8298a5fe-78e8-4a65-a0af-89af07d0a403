<h2 mat-dialog-title>Find Customer
    <hr>
</h2>
<mat-dialog-content>
    <form>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <input matInput placeholder="Customer Name" (keyup)="applyFilter($event.target.value)">
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <input matInput placeholder="Customer Code" (keyup)="applyFilter($event.target.value)">
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <input matInput placeholder="Eng. Cust Abrev" (keyup)="applyFilter($event.target.value)">
            </mat-form-field>
        </div>
    </form>
    <div class="cust_table">
        <mat-table [dataSource]="dataSource">
            <ng-container matColumnDef="name">
                <mat-header-cell *matHeaderCellDef> Name </mat-header-cell>
                <mat-cell *matCellDef="let element">{{element.name}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="code">
                <mat-header-cell *matHeaderCellDef> Cust. Code </mat-header-cell>
                <mat-cell *matCellDef="let element">{{element?.code}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="abrev">
                <mat-header-cell *matHeaderCellDef> Eng. Abrev </mat-header-cell>
                <mat-cell *matCellDef="let element">{{element?.abrev}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="contact">
                <mat-header-cell *matHeaderCellDef> Contact </mat-header-cell>
                <mat-cell *matCellDef="let element">{{element?.contact}}</mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>
    </div>
</mat-dialog-content>
<hr>
<mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="saveCustomer()">Select</button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>