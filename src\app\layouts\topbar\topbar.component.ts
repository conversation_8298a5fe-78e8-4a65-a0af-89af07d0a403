import {Observable, Subscription} from 'rxjs';
import {Component, EventEmitter, OnDestroy, OnInit, Output} from '@angular/core';
import {Router} from '@angular/router';
import {NgForm} from '@angular/forms';
import * as screenfull from 'screenfull';
import {MatDialog} from '@angular/material';
import {SharedService} from '../../shared';
import {AddQuotationComponent} from '../add-quotation/add-quotation.component';
import {Route} from '../../shared/constants/router.constants';
import {Quotation, QuotationSearchFilter, SalesAssociate, Status} from '../../admin-pages/dashboard/dashboard.model';
import {DashboardService} from '../../admin-pages/dashboard';
import {TopbarService} from './topbar.service';
import {QuotationList} from './topbar.model';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';
import {Values} from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-topbar',
  templateUrl: './topbar.component.html'
})
export class TopbarComponent implements OnInit, OnDestroy {
  @Output() toggleSidenav = new EventEmitter<void>();
  subscription = new Subscription();
  status: Status[];
  salesassociate: SalesAssociate[];
  quotation: Quotation[];
  quotationSearchFilter = new QuotationSearchFilter();
  quote: string;
  quoteList: QuotationList[] = [];
  allquote: QuotationList[] = [];
  quotationQbservable$ = new Observable<QuotationList[]>();
  filter: boolean;
  isDesignAdmin: boolean;
  isAppAdmin: boolean;
  isFullScreen: boolean;
  userRole: string;
  constructor(
    private router: Router,
    private sharedService: SharedService,
    private matDialog: MatDialog,
    private dashboardService: DashboardService,
    private topbarService: TopbarService
  ) {}

  async ngOnInit() {
    this.status = await this.sharedService.getAllStatus();
    this.status = this.status.filter(element => {
      return (element.type === Values.ApplicationEng || element.type === Values.Both) && element.status !== Values.Archived;
    });
    this.userRole = this.sharedService.getRole();
    this.getSaleAssociate();
    this.filter = false;
  }

  searchQuotations(quotationFilterForm: NgForm) {
    this.sharedService.setQuotationList(this.quotationSearchFilter);
    this.filter = !this.filter;
    this.router.navigate([Route.APP_ENGG.dashboard]);
  }

  reset() {
    this.quotationSearchFilter = new QuotationSearchFilter();
    this.sharedService.setQuotationList(this.quotationSearchFilter);
    this.filter = !this.filter;
    this.router.navigate([Route.APP_ENGG.dashboard]);
  }

  goToSalesDashboard() {
    this.router.navigate([Route.SALES.dashboard]);
  }

  getSaleAssociate() {
    this.subscription.add(
      this.dashboardService.getSalesAssociate(false).subscribe((res: Array<SalesAssociate>) => {
        this.salesassociate = res;
      })
    );
  }

  quoteFilter(event) {
    if (this.quote.length >= 3) {
      this.quotationQbservable$ = this.topbarService.getQuotation(event);
    }
  }

  showFilter() {
    this.filter = !this.filter;
  }

  fullScreenToggle(): void {
    if (screenfull.enabled) {
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    }
  }

  switchToDesign() {
    this.router.navigate([Route.DESIGN_ENGG.dashboard]);
  }
  switchToSuperAdmin() {
    this.router.navigate([Route.SUPER_ADMIN.user_list]);
  }

  logout() {
    this.sharedService.logout(null);
  }
  gotDashboard() {
    this.router.navigate([Route.APP_ENGG.dashboard]);
  }

  openAddQuo(): void {
    this.matDialog.open(AddQuotationComponent, {
      width: PopupSize.size.popup_xmd,
      autoFocus: true
    });
  }

  search() {
    this.router.navigate([Route.APP_ENGG.quot_list]);
    this.filter = false;
  }

  gotolist() {
    this.router.navigate([Route.APP_ENGG.quot_list]);
  }

  selectQuotation(quotOption) {
    this.router.navigate([Route.APP_ENGG.ccdc], { queryParams: { quotId: quotOption.id } });
  }
  getQuotNumberFn(quot) {
    return quot ? quot.quotationNumber : quot;
  }

  switchToMasterdataManagement() {
    this.router.navigate([Route.MASTERDATA.management]);
  }

  // used to switch to the quote tracker screen
  switchToQuoteTracker() {
    this.router.navigate([Route.QUOTE_TRACKER.dashboard]);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
