import { Component, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { DepartmentsMaster, GenericPageable, DepartmentsFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { ManageDepartmentsComponent } from './manage-departments/manage-departments.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-departments-masters',
  templateUrl: './departments-masters.component.html'
})
export class DepartmentsMastersComponent implements OnInit, OnDestroy {
  pageTitle = 'Departments Master';
  departments: DepartmentsMaster;
  departmentsPageable: GenericPageable<DepartmentsMaster>;
  departmentsDataSource = new MatTableDataSource<DepartmentsMaster>();
  departmentsColumns = DisplayColumns.Cols.DepartmentsCols;

  dataSource = new MatTableDataSource<DepartmentsMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  departmentsFilter: DepartmentsFilter = new DepartmentsFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldName = Values.FilterFields.name;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getDepartmentsMaster(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new departments
  addDepartments() {
    this.editDepartments(new DepartmentsMaster());
  }

  // used to add filter to departments listing
  async addFilter() {
    this.filter = this.departmentsFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.departmentsFilter.name }];
    this.getDepartmentsMaster(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter for departments listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.departmentsFilter.name = '') : this.departmentsFilter.name
      }
    ];
    this.getDepartmentsMaster(this.initialPageIndex, this.pageSize);
  }

  getDepartmentsMaster(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getDepartmentsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<DepartmentsMaster>) => {
          this.departmentsPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createDepartmentsTable(this.departmentsPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createDepartmentsTable(serviceRequestList: GenericPageable<DepartmentsMaster>) {
    this.departmentsDataSource.data = serviceRequestList.content;
  }

  getDepartmentsPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getDepartmentsMaster(this.pageIndex, this.pageSize);
  }

  getDepartmentsSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getDepartmentsMaster(this.pageIndex, this.pageSize);
  }

  editDepartments(departments: DepartmentsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = departments;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-departments-master-model';
    const dialogRef = this.matDialog.open(ManageDepartmentsComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          departments.id
            ? 'Department' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Department' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getDepartmentsMaster(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteDepartment(departmentsId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteDepartment(departmentsId).subscribe(
        () => {
          this.snakbarService.success('Department' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getDepartmentsMaster(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
