export class Quotation {
  constructor(
    public id?: number,
    public quotationNumber?: string,
    public salesOrderNumber?: number,
    public quotationStatusName?: string,
    public revisionName?: string,
    public salesAssociate?: string,
    public customerDTO?: Customer,
    public design?: number
  ) { }
}

export class SalesAssociate {
  constructor(public id?: number, public firstName?: string, public lastName?: number, public authorities?: string) { }
}

export class QuotationSearchFilter {
  public quotationNumber?: string;
  public salesAssociateName?: string;
  public status?: string;
  public customerName?: string;
  public projectTitle?: string;
  public id?: number;
}

export class StatusCount {
  constructor(
    public rfqInQueueStatus?: number,
    public quoteCompleteStatus?: number,
    public quotingInProcessStatus?: number,
    public patternDesign?: number,
    public levelOneReview?: number,
    public outForApproval?: number
  ) {
    this.rfqInQueueStatus = 0;
    this.quoteCompleteStatus = 0;
    this.quotingInProcessStatus = 0;
    this.patternDesign = 0;
    this.levelOneReview = 0;
    this.outForApproval = 0;
  }
}

export class Status {
  constructor(public id: number, public status: string, public type: string, public orderNumber: number, public defaultHidden: boolean) { }
}

export class Customer {
  constructor(
    public addressLine1?: string,
    public addressLine2?: string,
    public code?: string,
    public contact?: string,
    public email?: string,
    public engCustAbrev?: string,
    public faxNumber?: string,
    public id?: 0,
    public name?: string,
    public phoneNumber?: string
  ) { }
}

export class QuotationPageable {
  constructor(public content?: Quotation[], public totalElements?: number, public totalPages?: number, public number?: number) { }
}
