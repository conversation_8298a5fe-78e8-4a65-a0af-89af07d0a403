import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { SalesOrderSummaryService } from '../../../admin-pages/new-quotation/summary-sales-order.service';
import { Messages, SnakbarService } from '../../../shared';
import { Variable } from '../../../shared/constants/Variable.constants';
import { APIRESULT } from '../jacket-list.model';
import { JacketListService } from '../jacket-list.service';

@Component({
  selector: 'sfl-link-dialog',
  templateUrl: './link-dialog.component.html',
  styleUrls: ['./link-dialog.component.css']
})

export class LinkDialogComponent implements OnInit {
  sqtLink:string;
  cfdLink:string;
  heaterManLink:string;
  soNumber:string;
  country:string;
  quoteId:number;
  constructor(public dialogRef: MatDialogRef<LinkDialogComponent>,
              @Inject(MAT_DIALOG_DATA) data,private jacketListService:JacketListService,
              private snakbarService: SnakbarService,private salesOrderSummaryService: SalesOrderSummaryService) {
    if(data){
      this.quoteId = data.quoteId;
      this.soNumber = data.soNumber;
      this.country = data.country
    }
    this.getSQTLink();
  }

  ngOnInit() {
    if(this.country.toLowerCase()==='usa'){
      this.cfdLink = Variable.CFD_LINK_US+'SO'+this.soNumber;
      this.heaterManLink = Variable.HEATERMAN_LINK_US+this.soNumber;
    }
    else{
      this.cfdLink = Variable.CFD_LINK_VIETNAM+'SO'+this.soNumber;
      this.heaterManLink = Variable.HEATERMAN_LINK_VIETNAM+this.soNumber;
    }
  }

  getSQTLink(){
    this.jacketListService.getCustomerSQTLink(this.quoteId).subscribe(res => {
      if(res){
        this.sqtLink = res.name;
      }
    });
  }

  openFileInExplorer(link) {
      this.salesOrderSummaryService.openFileInExplorer(link).subscribe(
        (res: APIRESULT) => {
          if (res.success) {
          } else {
            this.snakbarService.error(Messages.Title_Block.error_msg);
          }
        },
        error => {
          this.snakbarService.error(Messages.Solid_Work.not_install);
        }
      )
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
}
