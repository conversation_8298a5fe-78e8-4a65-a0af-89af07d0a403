import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {LaborMaster} from '../masterdata-management.model';
import {DisplayColumns} from 'src/app/shared/constants/displayColName.constants';
import {MatDialog, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {Subscription} from 'rxjs';
import {MasterdataManagementService} from '../masterdata-management.service';
import {Messages, SnakbarService, SweetAlertService} from 'src/app/shared';
import {ManageLaborMasterComponent} from './manage-labor-master/manage-labor-master.component';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';

@Component({
    selector: 'sfl-thermostat-types',
    templateUrl: './labor-master.component.html'
})
export class LaborMasterComponent implements OnInit, OnDestroy {
    pageTitle = 'Labor Master';
    laborMaster: LaborMaster[];
    laborMasterDataSource = new MatTableDataSource<LaborMaster>();
    laborMasterColumns = DisplayColumns.Cols.LaborMasterCols;
    dataSource = new MatTableDataSource<LaborMaster>();

    length: number;
    numberOfElements: number;
    showLoader = false;

    subscription = new Subscription();
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatSort) sort: MatSort;

    constructor(
        private readonly masterDataService: MasterdataManagementService,
        private readonly matDialog: MatDialog,
        private readonly sweetAlertService: SweetAlertService,
        private readonly snakbarService: SnakbarService
    ) {
    }

    ngOnInit() {
        this.getLaborMasterData();
    }

    // used to add new thermostat type
    addLaborMaster() {
        this.editLaborMaster(new LaborMaster());
    }

    getLaborMasterData() {
        this.showLoader = true;
        this.subscription.add(
            this.masterDataService.getLaborMaster().subscribe(
                (res: LaborMaster[]) => {
                    this.laborMaster = res;
                    this.laborTable(this.laborMaster);
                    this.showLoader = false;
                },
                error => {
                    this.showLoader = false;
                    this.laborMasterDataSource.data = [];
                }
            )
        );
    }

    laborTable(serviceRequestList: LaborMaster[]) {
        this.laborMasterDataSource.data = serviceRequestList;
    }


    async deleteLaborMaster(id: number) {
        if (await this.sweetAlertService.deleteAlert()) {
            const strId = this.fixedEncodeURIComponent(id);
            this.showLoader = true;
            this.masterDataService.deleteLaborMaster(strId).subscribe(
                () => {
                    this.snakbarService.success('Labor and Burden' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
                    this.getLaborMasterData();
                    this.showLoader = false;
                },
                () => {
                    this.showLoader = false;
                }
            );
        }
    }

    fixedEncodeURIComponent = equation => {
        return encodeURIComponent(equation).replace(/[!'()*]/g, c => {
            return '%' + c.charCodeAt(0).toString(16);
        });
    };

    editLaborMaster(thermostatType: LaborMaster) {
        const matDataConfig = new MatDialogConfig();
        matDataConfig.data = thermostatType;
        matDataConfig.width = PopupSize.size.popup_md;
        matDataConfig.panelClass = 'labor-master-model';
        const dialogRef = this.matDialog.open(ManageLaborMasterComponent, matDataConfig);
        dialogRef.afterClosed().subscribe(res => {
            if (res) {
                this.snakbarService.success(
                    thermostatType.id
                        ? 'Labor and Burden' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
                        : 'Labor and Burden' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
                );
                this.getLaborMasterData();
            }
        });
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
