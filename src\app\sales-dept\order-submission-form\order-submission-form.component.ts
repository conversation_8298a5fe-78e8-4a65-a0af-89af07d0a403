import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { CustomerDTO, Quotation, SalesOrderFormDTO } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesAssociate, SoForm } from 'src/app/quote-tracker/quote-tracker.model';
import { Messages, SnakbarService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { SalesDeptService } from '../sales-dept.service';


@Component({
  selector: 'sfl-order-submission-form',
  templateUrl: './order-submission-form.component.html',
  styleUrls: ['./order-submission-form.component.css']
})
export class OrderSubmissionFormComponent implements OnInit {
  headingTitle = 'Order Submission Form';
  showLoader = false;
  salesassociates: SalesAssociate[];
  quotationNumber: string;
  salesSubmissionForm: FormGroup;
  customer: CustomerDTO;
  subscription = new Subscription();
  soNumber: string;
  folderSubmittedDate: Date = new Date();
  appSubmittedDate: Date = new Date();
  requestDate: string;
  docOrderAmt: number;
  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  constructor(private datePipe: DatePipe, private readonly salesdeptService: SalesDeptService, private readonly titleService: Title, private readonly snakbarService: SnakbarService, private readonly formBuilder: FormBuilder) {
  }

  ngOnInit() {
    this.titleService.setTitle('Sales Order Form - Sales Department');
    this.getSaleAssociate();
    this.createSoForm();
  }

  getSaleAssociate() {
    this.subscription.add(
      this.salesdeptService.getSalesAssociate(false).subscribe(
        (res: Array<SalesAssociate>) => {
          this.salesassociates = res;
        }
      )
    );
  }

  setSubmittedToApp(){
    this.appSubmittedDate = this.folderSubmittedDate;
  }

  checkIfSoNumberExist() {
    if (this.soNumber && this.soNumber.trim().length) {
      this.subscription.add(
        this.salesdeptService.checkSoNumber(this.soNumber).subscribe(
          isExist => {
            if (isExist) {
              this.snakbarService.error(Messages.error.so_number_already_exist);
              this.formGroupDirective.resetForm();
            } else {
              this.getSODetailsFromEpicor();
            }
          }
        )
      );
    }
  }

  private getSODetailsFromEpicor() {
    this.subscription.add(
      this.salesdeptService.getSoDetails(this.soNumber).subscribe(
        (res: SoForm) => {
          if (res) {
            let shipDate = res.requestDate ? res.requestDate.concat('T00:00:00') : '';
            this.requestDate = shipDate;
            this.docOrderAmt = res.docOrderAmt;
            this.salesSubmissionForm.patchValue({ shipDate: shipDate });
            this.salesSubmissionForm.patchValue({ dollarAmount: res.docOrderAmt });
            this.salesSubmissionForm.patchValue({ salesOrderNumber: res.orderNum });
            if (res.quotationNumber != null) {
              this.quotationNumber = res.quotationNumber;
              this.getQuotationNumber();
            }
          }
        }
      )
    );
  }

  getQuotationNumber() {
    if (this.quotationNumber && this.quotationNumber.trim().length) {
      this.subscription.add(
        this.salesdeptService.getSOQuotation(this.quotationNumber).subscribe((res: SalesOrderFormDTO) => {
          if (res) {
            let shipDate = this.datePipe.transform(res.shipDate ? res.shipDate : this.requestDate, Values.dateFormat.formatHyphen);
            shipDate = shipDate ? shipDate.concat('T00:00:00') : '';

            let folderSubmittedDate = this.datePipe.transform(res.folderSubmittedDate, Values.dateFormat.formatHyphen);
            folderSubmittedDate = folderSubmittedDate ? folderSubmittedDate.concat('T00:00:00') : '';

            const dollarAmount = res.dollarAmount ? res.dollarAmount : this.docOrderAmt;

            const salesOrderNumber = res.salesOrderNumber ? res.salesOrderNumber : this.soNumber;

             this.salesSubmissionForm.patchValue({
               ...res,
               salesOrderNumber,
               shipDate,
               folderSubmittedDate,
               dollarAmount
            });
          } else {
            this.subscription.add(
              this.salesdeptService.getQuotation(this.quotationNumber).subscribe((response: Quotation) => {
                if (response) {
                  if (response.salesOrderNumber === null) {
                    response.salesOrderNumber = this.soNumber;
                  }
                  if (response.customerDTO !== null) {
                    this.salesSubmissionForm.patchValue({ customerDTO: response.customerDTO });
                  }
                  if (response.projectName !== null) {
                    this.salesSubmissionForm.patchValue({ projectTitle: response.projectName });
                  }
                }
              }
              )
            );
          }
        })
      );
    }
  }

  clearFormFields() {
    this.salesSubmissionForm.get('customerDTO').patchValue(new CustomerDTO());
    this.salesSubmissionForm.get('quotationId').patchValue(null);
    this.salesSubmissionForm.get('projectTitle').patchValue(null);
    this.salesSubmissionForm.get('folderSubmittedDate').patchValue(null);
    this.salesSubmissionForm.get('salesAssociateId').patchValue(null);
    this.salesSubmissionForm.get('assignedAppEngineerId').patchValue(null);
    this.salesSubmissionForm.get('designComments').patchValue(null);
    this.salesSubmissionForm.get('sqtLink').patchValue(null);
    this.salesSubmissionForm.get('notes').patchValue(null);
    this.folderSubmittedDate = new Date();
    this.appSubmittedDate = this.folderSubmittedDate;
  }

  createSoForm() {
    this.salesSubmissionForm = this.formBuilder.group({
      customerDTO: this.formBuilder.group({
        addressLine1: new FormControl(null),
        addressLine2: new FormControl(null),
        code: new FormControl(null),
        contact: new FormControl(null),
        email: new FormControl(null),
        engCustAbrev: new FormControl(null),
        faxNumber: new FormControl(null),
        id: new FormControl(null),
        name: new FormControl(null),
        phoneNumber: new FormControl(null),
        sqtLink: new FormControl(null),
        notes: new FormControl(null),
      }),
      designComments: new FormControl(null, Validators.required),
      dollarAmount: new FormControl(null, Validators.required),
      folderSubmittedDate: new FormControl(null, Validators.required),
      projectTitle: new FormControl(null, Validators.required),
      quotationId: new FormControl(null),
      quotationNumber: new FormControl(null, Validators.required),
      salesAssociateId: new FormControl(null, Validators.required),
      assignedAppEngineerId: new FormControl(null, Validators.required),
      salesOrderNumber: new FormControl(null, Validators.required),
      sendCopyOfResponse: new FormControl(null, Validators.required),
      shipDate: new FormControl(null, Validators.required),
      customNoOfDesigns: new FormControl(null, Validators.required),
    });
  }

  saveSoFormDetails() {
    if((this.salesSubmissionForm.value.quotationNumber===undefined || this.salesSubmissionForm.value.quotationNumber===null)
    || (this.salesSubmissionForm.value.folderSubmittedDate===undefined || this.salesSubmissionForm.value.folderSubmittedDate===null)
    || (this.salesSubmissionForm.value.salesOrderNumber===undefined || this.salesSubmissionForm.value.salesOrderNumber===null)){
      this.snakbarService.error('Please fill required details');
      return;
    }
    const folderSubmitedDate = this.datePipe.transform(this.salesSubmissionForm.get('folderSubmittedDate').value, Values.dateFormat.formatHyphen);
    const shipDate = this.datePipe.transform(this.salesSubmissionForm.get('shipDate').value, Values.dateFormat.formatHyphen);

    this.salesSubmissionForm.patchValue({ folderSubmittedDate: folderSubmitedDate });
    this.salesSubmissionForm.patchValue({ shipDate: shipDate });
    this.subscription.add(
      this.salesdeptService.saveSOForm(this.salesSubmissionForm.value).subscribe((res) => {
        this.snakbarService.success(Messages.SO_FORM.form_successfull);
        this.formGroupDirective.resetForm();
        this.folderSubmittedDate = new Date();
        this.appSubmittedDate = this.folderSubmittedDate;
      })
    );
  }

}
