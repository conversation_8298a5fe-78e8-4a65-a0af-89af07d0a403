import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { superadmindashboardRoutes } from './super-admin-dashboard.route';
import { LayoutModule } from '@angular/cdk/layout';
import { SuperAdminDashboardComponent } from './super-admin-dashboard.component';

@NgModule({
    imports: [
        RouterModule.forChild(superadmindashboardRoutes),
        SharedModule,
        LayoutModule
    ],
    declarations: [
        SuperAdminDashboardComponent
    ],
    entryComponents: [
    ],
    providers: [
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class SuperAdminDashboardModule { }
