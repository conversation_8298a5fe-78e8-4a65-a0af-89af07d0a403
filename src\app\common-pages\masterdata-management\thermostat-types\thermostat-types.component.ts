import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON>hil<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ThermostatTypesMaster, GenericPageable, ThermostatTypesFilter } from '../masterdata-management.model';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { ManageThermostatTypesComponent } from './manage-thermostat-types/manage-thermostat-types.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-thermostat-types',
  templateUrl: './thermostat-types.component.html'
})
export class ThermostatTypesComponent implements OnInit, OnDestroy {
  pageTitle = 'Thermostat Types Master';
  thermostatTypeMaster: ThermostatTypesMaster;
  thermostatTypePageable: GenericPageable<ThermostatTypesMaster>;
  thermostatTypeDataSource = new MatTableDataSource<ThermostatTypesMaster>();
  thermostatTypeMasterColumns = DisplayColumns.Cols.ThermostatTypesMasterCols;
  thermostatTypeFilter: ThermostatTypesFilter = new ThermostatTypesFilter();
  dataSource = new MatTableDataSource<ThermostatTypesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  showLoader = false;
  filterFieldId = Values.FilterFields.id;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getThermostatTypesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new thermostat type
  addThermostatTypeMaster() {
    this.editThermostatTypes(new ThermostatTypesMaster());
  }

  // used to add filter to thermostat type listing
  async addFilter() {
    this.filter = this.thermostatTypeFilter.id === '' ? [] : [{ key: this.filterFieldId, value: this.thermostatTypeFilter.id }];
    this.getThermostatTypesMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of thermostat type listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldId,
        value: fieldToClear === this.filterFieldId ? (this.thermostatTypeFilter.id = '') : this.thermostatTypeFilter.id
      }
    ];
    this.getThermostatTypesMasterData(this.initialPageIndex, this.pageSize);
  }

  getThermostatTypesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getThermostatTypesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<ThermostatTypesMaster>) => {
          this.thermostatTypePageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createThermostatTypeTable(this.thermostatTypePageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.thermostatTypeDataSource.data = [];
        }
      )
    );
  }

  createThermostatTypeTable(serviceRequestList: GenericPageable<ThermostatTypesMaster>) {
    this.thermostatTypeDataSource.data = serviceRequestList.content;
  }

  getThermostatTypePagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getThermostatTypesMasterData(this.pageIndex, this.pageSize);
  }

  getThermostatTypeSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getThermostatTypesMasterData(this.pageIndex, this.pageSize);
  }

  async deleteThermostatType(thermostatTypeId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      const strThermostatTypeId = this.fixedEncodeURIComponent(thermostatTypeId);
      this.showLoader = true;
      this.masterDataService.deleteThermostatType(strThermostatTypeId).subscribe(
        () => {
          this.snakbarService.success('Thermostat Type' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getThermostatTypesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }
  fixedEncodeURIComponent = equation => {
    return encodeURIComponent(equation).replace(/[!'()*]/g, c => {
      return '%' + c.charCodeAt(0).toString(16);
    });
  };
  editThermostatTypes(thermostatType: ThermostatTypesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = thermostatType;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-thermostat-types-master-model';
    const dialogRef = this.matDialog.open(ManageThermostatTypesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          thermostatType.id
            ? 'Thermostat Types' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Thermostat Types' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getThermostatTypesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
