import { DatePipe } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { IPageInfo } from 'ngx-virtual-scroller';
import { Subscription } from 'rxjs';
import { AccountManager } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { AppTrackerFieldComponent } from 'src/app/design-pages/jacket-list/app-tracker-field/app-tracker-field.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { RoleAuthorisationServiceService } from 'src/app/shared/service/role-authorisation-service.service';
import { Role, SharedService } from '../../shared';
import { Values } from '../../shared/constants/values.constants';
import { Variable } from '../../shared/constants/Variable.constants';
import {
  AppEngineeringQuoteTracker,
  AppQuoteTrackerColumnColorDTO,
  FilterPageable,
  FilterQuoteTracker,
  GenericPageable,
  SalesAssociate,
  Statuses
} from '../quote-tracker.model';
import { QuoteTrackerService } from '../quote-tracker.service';
import {
  AppEnggQuoteTrackerColumnColourComponent
} from './app-engg-quote-tracker-column-colour/app-engg-quote-tracker-column-colour.component';
import {
  AppEnggQuoteTrackerRowColourComponent
} from './app-engg-quote-tracker-row-colour/app-engg-quote-tracker-row-colour.component';


@Component({
  selector: 'sfl-app-engg-quote-tracker',
  templateUrl: './app-engg-quote-tracker.component.html',
  styleUrls: ['./app-engg-quote-tracker.component.scss']
})
export class AppEnggQuoteTrackerComponent implements OnInit, OnDestroy {
  @ViewChild('notesInput') notesInput: any;
  @ViewChild('sqtLinkInput') sqtLinkInput: any;

  headingTitle = 'App Engineering Quote Tracker';
  appEngQuoteTracker: AppEngineeringQuoteTracker;
  subscription = new Subscription();
  appEnggQTSPageable: GenericPageable<AppEngineeringQuoteTracker>;
  filter: FilterQuoteTracker = new FilterQuoteTracker();
  filteredList: AppEngineeringQuoteTracker[] = [];
  showLoader = false;
  authorizedRole = [Role.ADMIN_ROLE, Role.DESIGN_ROLE, Role.ENG_ROLE];
  isAuthorized = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  statuses: Statuses[];
  salesAssociates: SalesAssociate[];
  length: number;
  pageIndex = Variable.activePage;
  pageSize = 50
  totalNoOfPages = 0;
  sortShipDate = Variable.sortByshipDate;
  sortOrder = Variable.defaultSortOrderDescending;
  newFilterDesignList = [];
  sortField = Variable.sortByQuotation_number;
  ascSort = Variable.sortAscending;
  numberOfElements: number;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  accountManager: AccountManager[];
  appJacketLink = Variable.appJacketListLink;
  fromDate = new Date();
  toDate = new Date();
  today = new Date().toLocaleDateString();
  checkBoxYesLabel = Values.CheckboxLabels.YES;
  checkBoxNoLabel = Values.CheckboxLabels.NO;
  checkBoxNullLabel = Values.CheckboxLabels.NULL;
  CustomerQuoteCheckBox = Values.QuoteStatus_Customer_Clarification_Required;
  ExternalQuoteCheckBox = Values.QuoteStatus_External_Quote_Required;
  roleBasedUser: Role;
  salesRole: SalesAssociate[] = [];
  isSales: boolean;
  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly datePipe: DatePipe,
    private readonly sharedService: SharedService,
    private readonly titleService: Title,
    private readonly matDialog: MatDialog,
    private readonly roleAuthorisationServiceService: RoleAuthorisationServiceService
  ) { }

  // used to set from and to date for filter, retrieves quote statuses and then gets the app QTS quotes
  async ngOnInit() {
    this.titleService.setTitle('QTS - App Engineering');
    // setting up the default filter of 30 days from current day
    this.fromDate.setDate(this.toDate.getDate() - 30);
    this.getQuoteStatuses();
    this.checkisAuthorized();
    await this.getSaleAssociate();
    await this.getAllAccountManagers();
    this.initializeTable();
    this.isSales = this.sharedService.getRole() === Role.SALES_ROLE;
  }


  initializeTable() {
    this.filteredList = [];
    this.fetchNextChunk(this.initialPageIndex, this.pageSize).then(items => {
      this.appendItems(items);
    })
  }

  // used to get the quotes statuses
  getQuoteStatuses() {
    this.subscription.add(this.sharedService.getStatuesByTypeOrderByOrderNumber().subscribe((res: Statuses[]) => {
      if (res) {
        this.statuses = res;
      }
    }));
  }

  // used to get sales associates list
  getSaleAssociate() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.quoteTrackerService.getSalesAssociate(true).subscribe(
        (res: Array<SalesAssociate>) => {
          for (const role of res) {
            if (role.authorities == Role.ADMIN_ROLE || role.authorities == Role.ENG_ROLE) {
              this.salesRole.push(role);
            }
          }
          this.salesAssociates = this.salesRole;
          this.showLoader = false;
          resolve();
        },
        () => {
          resolve();
          this.showLoader = false;
        }
      );
    });
  }

  openTrackerField(quoteId, designStatusId) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { quotationId: quoteId, designStatusId: designStatusId, salesOrderNumber: this.filter.soNumber };
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-tracker-fields-model';
    this.matDialog
      .open(AppTrackerFieldComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if(res) {
          this.initializeTable();
        }
      });

  }

  // This function is used to open color selection modal for Row
  openColorSelectionRow(quoteId: number, quoteStatusId: number) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {quoteId, quoteStatusId}
    this.matDialog
      .open(AppEnggQuoteTrackerRowColourComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res) {
          this.initializeTable();
        }
      });
  }

  // This function is used to open color selection modal for Column
  openColorSelectionColumn(quoteId: number) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = quoteId;
    this.matDialog
      .open(AppEnggQuoteTrackerColumnColourComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res) {
          this.initializeTable();
        }
      });
  }

  // This function is used to select color & column
  columnColorSelection(columnName: string, columnColors: AppQuoteTrackerColumnColorDTO[]) {
    const color = columnColors.find(item => item.appEngQuotationColumnName === columnName)
    if (color && color.colorValue) {
      return { 'background': color.colorValue }
    }
    return {};
  }

  rowColorSelection(statusId:number){
    const status = this.statuses.find(data=> data.id === statusId);
    if(status!==null && status.appEngRowColor!==null && status.appEngRowColor!==undefined){
      return {'background': status.appEngRowColor};
    }
    else{
      return {'background': 'white'};
    }
  }

  // used to handle the search/ filter on the QTS quotes
  searchAppQuotes() {
    if (this.filter.dateFilterCategory) {
      this.filter.startDate = this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen);
      this.filter.endDate = this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen);
    }
    this.initializeTable();
  }

  // used to clears the filter object
  clearFilter() {
    this.filter = new FilterQuoteTracker();
    this.initializeTable();
  }

  // used to get the app engg quotes for QTS
  async getAppEnggQuotes(pageIndex: number, pageSize: number) {
    return new Promise<AppEngineeringQuoteTracker[]>((resolve, reject) => {
      this.showLoader = true;
      let newDesignList = [];
      let newDesignTypeList = [];
      let newBothTypeList = [];
      let newAppTypeList = [];
      this.newFilterDesignList = [];
      const pageable: FilterPageable = { page: pageIndex, size: pageSize, sort: this.sortShipDate + ',' + this.sortOrder };
      this.subscription.add(
        this.quoteTrackerService.getAppEngQuoteTracker(pageable, this.filter).subscribe(
          (res: GenericPageable<AppEngineeringQuoteTracker>) => {
            this.appEnggQTSPageable = res;
            if(this.filter.quoteStatusId === null) {
              for(let k = 0; k<this.statuses.length; k++) {
                if(!this.statuses[k].defaultHidden) {
                  newDesignList = res.content.filter(x => x.designStatusId === this.statuses[k].id);
                 if(newDesignList.length > 0) {
                   for(let j=0; j<newDesignList.length; j++) {
                    this.newFilterDesignList.push(...newDesignList[j])
                   }
                 }
                }
              }
              for (let s = 0; s < res.content.length; s++) {
                if (res.content[s].quotationStatusType === Values.ApplicationEng) {
                  newAppTypeList.push(res.content[s]);
                } else if (res.content[s].quotationStatusType === Values.Both) {
                  newBothTypeList.push(res.content[s]);
                } else if (res.content[s].quotationStatusType === Values.DesignEng) {
                  newDesignTypeList.push(res.content[s]);
                }
              }
              this.newFilterDesignList.push(...newAppTypeList);
              this.newFilterDesignList.push(...newBothTypeList);
              this.newFilterDesignList.push(...newDesignTypeList);
            } else {
              this.newFilterDesignList = res.content;
            }
            this.newFilterDesignList = this.newFilterDesignList.map((item) => ({
              ...item,
              isEditingNotes: false,
              isEditingSqtLink: false,
            }));
            this.length = res.totalElements;
            this.pageIndex = res.number;
            this.totalNoOfPages = res.totalPages;
            this.numberOfElements = res.numberOfElements;
            this.appEnggQTSPageable.content = [];
            this.appEnggQTSPageable.content = res.content;
            this.showLoader = false;
            resolve(this.newFilterDesignList);
          },
          () => {
            (this.showLoader = false)
            reject();
          }
        )
      );
    });
  }

  public appendItems(items): void {
    this.filteredList.push(...items);
  }

  setCheckBoxTriStateValues(fieldValue: boolean, field: string) {
    switch (fieldValue) {
      case true: {
        this.checkFieldWhichNeedsToBeUpdated(field, false);
        break;
      }
      case false: {
        this.checkFieldWhichNeedsToBeUpdated(field, null);
        break;
      }
      case null: {
        this.checkFieldWhichNeedsToBeUpdated(field, true);
        break;
      }
    }
  }

  checkFieldWhichNeedsToBeUpdated(field: string, valueToUpdate: boolean) {
    switch (field) {
      case this.CustomerQuoteCheckBox:
        this.filter.customerClarificationRequired = valueToUpdate;
        break;
      case this.ExternalQuoteCheckBox:
        this.filter.extQuoteRequired = valueToUpdate;
        break;
    }
  }


  // used to handle the sorting on the mat table
  getSorting(event) {
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    if (this.ascSort) {
      this.sortOrder = Variable.defaultSortOrderDescending;
    } else {
      this.sortOrder = Variable.defaultSortOrder;
    }
    this.sortField = event.active;
    this.initializeTable();
  }

  downloadAppEnggReportExcel() {
    this.showLoader = true;
    if (this.filter.dateFilterCategory) {
      this.filter.startDate = this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen);
      this.filter.endDate = this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen);
    }
    this.subscription.add(
      this.quoteTrackerService.downloadAppEnggQuoteExcel(this.filter).subscribe(
        success => {
          this.downloadExcel(success, 'App-Engg-Quote-Tracker' + ' - ' + this.datePipe.transform(this.today, Values.dateFormat.excelFormat) + '.xlsx');
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  downloadExcel(response, sheetName: string) {
    const dwldLink = document.createElement('a');
    const url = URL.createObjectURL(response);
    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('target', '_blank');
    dwldLink.setAttribute('download', sheetName);
    dwldLink.style.visibility = 'hidden';
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  getAllAccountManagers() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.quoteTrackerService.getAccountManagers().subscribe(
        (res: AccountManager[]) => {
          this.accountManager = res;
          this.showLoader = false;
          resolve();
        },
        () => {
          resolve();
          this.showLoader = false;
        }
      );
    });
  }

  async fetchMore(event: IPageInfo) {
    if (this.pageIndex >= this.totalNoOfPages) return;
    if ((event.endIndex === -1) || (event.endIndex !== this.filteredList.length - 1)) return;
    this.showLoader = true;
    await this.fetchNextChunk(this.pageIndex + 1, this.pageSize).then(chunk => {
      this.appendItems(chunk);
      this.showLoader = false;
    }, () => this.showLoader = false);
  }

  fetchNextChunk(skip: number, limit: number): Promise<AppEngineeringQuoteTracker[]> {
    return new Promise(async (resolve) => {
      this.pageIndex = skip;
      resolve(await this.getAppEnggQuotes(skip, limit));
    });
  }

  checkisAuthorized() {
    this.isAuthorized = this.roleAuthorisationServiceService.isAuthorised(this.authorizedRole);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  updateSingleColumnValue(item: AppEngineeringQuoteTracker, columnName: string): void {
    const payload = {
      id: item.id,
      [columnName]: item[columnName],
    };
    this.quoteTrackerService.updateColumnValues(payload).subscribe();
  }

  enableEditNotes(item: AppEngineeringQuoteTracker): void {
    item.isEditingNotes = true;
    setTimeout(() => {
      const element = this.notesInput.nativeElement;
      element.focus();
      this.autoGrow(element);
    });
  }

  saveNotes(item: AppEngineeringQuoteTracker): void {
    if (item.notes && item.notes.trim()) {
      this.updateSingleColumnValue(item, 'notes');
    }
    item.isEditingNotes = false;
  }

  cancelNotesEdit(item: AppEngineeringQuoteTracker): void {
    item.isEditingNotes = false;
  }

  enableEditSqtLink(item: AppEngineeringQuoteTracker): void {
    item.isEditingSqtLink = true;
    setTimeout(() => {
      const element = this.sqtLinkInput.nativeElement;
      element.focus();
      this.autoGrow(element);
    });
  }

  saveSqtLink(item: AppEngineeringQuoteTracker): void {
    if (item.sqtLink && item.sqtLink.trim()) {
      this.updateSingleColumnValue(item, 'sqtLink');
    }
    item.isEditingSqtLink = false;
  }

  cancelSqtLinkEdit(item: AppEngineeringQuoteTracker): void {
    item.isEditingSqtLink = false;
  }

  autoGrow(element: any): void {
    element.style.height = 'auto';
    element.style.height = (element.scrollHeight) + 'px';
  }

  formatLinks(text: string): string {
    if (!text) return '';

    // Split the text by line breaks
    const lines = text.split('\n');

    // Process each line
    const formattedLines = lines.map(line => {
      // URL regex pattern
      const urlPattern = /(https?:\/\/[^\s]+)/g;

      // Check if the line contains a URL
      if (urlPattern.test(line)) {
        // Replace URLs with anchor tags
        return line.replace(urlPattern, url =>
          `<a href="${url}" target="_blank" class="link" onclick="event.stopPropagation()">${url}</a>`
        );
      } else {
        // Return the line as is if no URL is found
        return line;
      }
    });

    // Join the lines back with <br> tags for HTML display
    return formattedLines.join('<br>');
  }
}
