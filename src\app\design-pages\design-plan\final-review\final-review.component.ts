import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Mat<PERSON>ialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DMTLogComponent } from 'src/app/shared/component/dml-log/dmt-log.component';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { deepCopyFunction } from '../../../shared/utils/utils';
import { Operation } from '../bom-editor/bom-editor.model';
import { BomEditorService } from '../bom-editor/bom-editor.service';
import { FinalAddLabelComponent } from './final-add-label/final-add-label.component';
import { FinalAddMaterialComponent } from './final-add-material/final-add-material.component';
import { FinalAddOperationComponent } from './final-add-operation/final-add-operation.component';
import { FinalDesignReviewDTO, LabelDTO, MaterialDTO, OperationDTO, Part, Status } from './final-review.model';
import { FinalReviewService } from './final-review.service';

@Component({
  selector: 'sfl-final-review',
  templateUrl: './final-review.component.html',
  styleUrls: ['./final-review.component.css']
})
export class FinalReviewComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  finalReviewDTO: FinalDesignReviewDTO = new FinalDesignReviewDTO();
  finalReviewDTOCloned: FinalDesignReviewDTO = new FinalDesignReviewDTO();
  statuses: Status = new Status();

  private _JacketID: number;
  private _quotationNo: string;
  _salesOrderNumber: string;
  _partNumber: string;
  type = 'Usa';
  oldSequence: number;
  part: Part;
  partNumberMissing = false;

  showLoader = false;
  partInfoLoader = false;
  materialInfoLoader = false;
  operationInfoLoader = false;
  labelsInfoLoader = false;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  operationsDataSourceUSA = new MatTableDataSource<OperationDTO>();
  operationsDataSourceVietnam = new MatTableDataSource<OperationDTO>();
  operationsDataSourceCostaRica = new MatTableDataSource<OperationDTO>();
  operationsColumns = DisplayColumns.Cols.OperationsFinalReviewColumn;

  materialsDataSourceUSA = new MatTableDataSource<MaterialDTO>();
  materialsDataSourceVietnam = new MatTableDataSource<MaterialDTO>();
  materialsDataSourceCostaRica = new MatTableDataSource<MaterialDTO>();
  materialsColumns = DisplayColumns.Cols.MaterialsColumn;

  labelsDataSourceUSA = new MatTableDataSource<LabelDTO>();
  labelsDataSourceVietnam = new MatTableDataSource<LabelDTO>();
  labelsDataSourceCostaRica = new MatTableDataSource<LabelDTO>();
  labelsColumns = DisplayColumns.Cols.LabelsColumn;
  labelCommonFields: LabelDTO;
  syncInProcess = '';
  syncStatus = '';
  noJacketUSA = true;
  noJacketVietnam = false;
  noJacketCostaRica = false;
  lastSyncStatus = '';
  isSyncSuccess = false;
  isEdited: boolean;

  get quotationNo() {
    return this._quotationNo;
  }

  @Input()
  set quotationNo(val) {
    this._quotationNo = val;
  }

  get salesOrderNumber() {
    return this._salesOrderNumber;
  }

  @Input()
  set salesOrderNumber(val) {
    this._salesOrderNumber = val;
  }

  get jacketID() {
    return this._JacketID;
  }

  @Input()
  set jacketID(val) {
    this._JacketID = val;
    this.getPartByJacketId();
  }

  constructor(
    private readonly finalReviewService: FinalReviewService,
    private readonly bomeditorService: BomEditorService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
  ) {
  }

  ngOnInit() {
  }

  private getPartByJacketId() {
    this.syncStatus = '';
    this.showLoader = true;
    this.subscription.add(
      this.finalReviewService.getPartByJacketId(this._JacketID).subscribe(
        async (res: Part) => {
          if (res) {
            this.part = res;
            // may need to call it in async
            if (this.part.jacketPartNumber) {
              this.checkLastSyncStatusByJacketIdAndType();
              this.showLoader = true;
              await this.initializeFinalReview();
              this.partNumberMissing = false;
            } else {
              this.partNumberMissing = true;
              if (await this.sweetAlertService.partNumberNotExistWarning()) {
                this.operationsDataSourceUSA.data = [];
                this.materialsDataSourceUSA.data = [];
                this.labelsDataSourceUSA.data = [];

                this.operationsDataSourceVietnam.data = [];
                this.materialsDataSourceVietnam.data = [];
                this.labelsDataSourceVietnam.data = [];

                this.operationsDataSourceCostaRica.data = [];
                this.materialsDataSourceCostaRica.data = [];
                this.labelsDataSourceCostaRica.data = [];

                this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.jacketId = this.part.jacketId;
                this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.partNum = this.part.jacketPartNumber;
                this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.revisionName = this.part.revisionName;
                this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.type = this.part.type;
                this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.internalCrossReference = this.part.internalCrossRef;
                this.finalReviewDTO.usaFinalDesignReviewDTO.salesOrderNumber = this._salesOrderNumber;

                this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.jacketId = this.part.jacketId;
                this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.partNum = this.part.jacketPartNumber;
                this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.revisionName = this.part.revisionName;
                this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.type = this.part.type;
                this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.internalCrossReference = this.part.internalCrossRef;
                this.finalReviewDTO.vietnamFinalDesignReviewDTO.salesOrderNumber = this._salesOrderNumber;

                this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.jacketId = this.part.jacketId;
                this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.partNum = this.part.jacketPartNumber;
                this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.revisionName = this.part.revisionName;
                this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.type = this.part.type;
                this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.internalCrossReference = this.part.internalCrossRef;
                this.finalReviewDTO.costaRicaFinalDesignReviewDTO.salesOrderNumber = this._salesOrderNumber;
              }
            }
            this.showLoader = false;
          }
        },
        (error) => {
          this.showLoader = false;
          if (error.applicationStatusCode === 1228) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  private initializeFinalReview() {
    return new Promise(resolve => {
      const finalReview = {
        partNum: this.part.jacketPartNumber,
        rev: this.part.revisionName,
        jacketId: this._JacketID,
        quotationNumber: this._quotationNo ? this._quotationNo : 'NA',
        soNumber: this._salesOrderNumber ? this._salesOrderNumber : 'NA'
      };

      this.showLoader = true;
      this.subscription.add(
        this.finalReviewService.getFinalReview(finalReview).subscribe(
          (designReview: FinalDesignReviewDTO) => {
            this.finalReviewDTO = designReview;
            // to set relative data based on made in
            this.onMadeInChanged();
            this.showLoader = false;
            resolve();
          },
          () => {
            this.showLoader = false;
            resolve();
          }
        )
      );
      this.finalReviewDTOCloned = deepCopyFunction(this.finalReviewDTO); // deep copy values here due to mutation problem
    });
  }

  addOperations() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {jacketId: this._JacketID, type: this.type};
    this.matDialog
      .open(FinalAddOperationComponent, matDataConfig)
      .afterClosed()
      .subscribe(async (operation: Operation) => {
        if (operation) {
          if (this.type === 'Usa') {
            if (
              this.operationsDataSourceUSA.data.filter(d => d.sequence === Number(operation.sequence) && d.status !== this.statuses.DELETED)
                .length > 0 &&
              (await this.sweetAlertService.sequenceDuplicationWarning())
            ) {
              // sequence is already exist
            } else {
              if (
                this.operationsDataSourceUSA.data.some(d => d.sequence === Number(operation.sequence) && d.status === this.statuses.DELETED)
              ) {
                this.operationsDataSourceUSA.data.forEach(d => {
                  if (d.sequence === Number(operation.sequence) && d.status === this.statuses.DELETED) {
                    d.status = this.statuses.MODIFIED;
                    this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
                  }
                });
              } else {
                const oper: OperationDTO = {
                  opNumber: operation.opNumber,
                  operation: operation.operationName,
                  prodHours: operation.prodHrs,
                  sequence: Number(operation.sequence),
                  setupHours: operation.setupHrs,
                  status: this.statuses.ADDED
                };
                this.operationsDataSourceUSA.data.push(oper);
                this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
              }
              this.operationsDataSourceUSA._updateChangeSubscription();
            }
          } else if (this.type === 'Costa Rica') {
            if (
              this.operationsDataSourceCostaRica.data.filter(
                d => d.sequence === Number(operation.sequence) && d.status !== this.statuses.DELETED
              ).length > 0 &&
              (await this.sweetAlertService.sequenceDuplicationWarning())
            ) {
              // sequence is already exist
            } else {
              if (
                this.operationsDataSourceCostaRica.data.some(
                  d => d.sequence === Number(operation.sequence) && d.status === this.statuses.DELETED
                )
              ) {
                this.operationsDataSourceCostaRica.data.forEach(d => {
                  if (d.sequence === Number(operation.sequence) && d.status === this.statuses.DELETED) {
                    d.status = this.statuses.MODIFIED;
                    this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
                  }
                });
              } else {
                const oper: OperationDTO = {
                  opNumber: operation.opNumber,
                  operation: operation.operationName,
                  prodHours: operation.prodHrs,
                  sequence: Number(operation.sequence),
                  setupHours: operation.setupHrs,
                  status: this.statuses.ADDED
                };
                this.operationsDataSourceCostaRica.data.push(oper);
                this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
              }
              this.operationsDataSourceCostaRica._updateChangeSubscription();
            }
          } else {
            if (
              this.operationsDataSourceVietnam.data.filter(
                d => d.sequence === Number(operation.sequence) && d.status !== this.statuses.DELETED
              ).length > 0 &&
              (await this.sweetAlertService.sequenceDuplicationWarning())
            ) {
              // sequence is already exist
            } else {
              if (
                this.operationsDataSourceVietnam.data.some(
                  d => d.sequence === Number(operation.sequence) && d.status === this.statuses.DELETED
                )
              ) {
                this.operationsDataSourceVietnam.data.forEach(d => {
                  if (d.sequence === Number(operation.sequence) && d.status === this.statuses.DELETED) {
                    d.status = this.statuses.MODIFIED;
                    this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
                  }
                });
              } else {
                const oper: OperationDTO = {
                  opNumber: operation.opNumber,
                  operation: operation.operationName,
                  prodHours: operation.prodHrs,
                  sequence: Number(operation.sequence),
                  setupHours: operation.setupHrs,
                  status: this.statuses.ADDED
                };
                this.operationsDataSourceVietnam.data.push(oper);
                this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
              }
              this.operationsDataSourceVietnam._updateChangeSubscription();
            }
          }
        }
      });
  }

  addMaterial() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {jacketId: this._JacketID, type: this.type};
    this.matDialog
      .open(FinalAddMaterialComponent, matDataConfig)
      .afterClosed()
      .subscribe((material: MaterialDTO) => {
        if (material) {
          material.status = this.statuses.ADDED;
          if (this.type === 'Usa') {
            if (this.materialsDataSourceUSA.data.length) {
              material.sequence =
                Math.max.apply(
                  Math,
                  this.materialsDataSourceUSA.data.map(function (mtr) {
                    return mtr.sequence;
                  })
                ) + 10;
            } else {
              material.sequence = 10;
            }
            this.materialsDataSourceUSA.data.push(material);
            this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
            this.materialsDataSourceUSA._updateChangeSubscription();
          } else if (this.type === 'Costa Rica') {
            if (this.materialsDataSourceCostaRica.data.length) {
              material.sequence =
                Math.max.apply(
                  Math,
                  this.materialsDataSourceCostaRica.data.map(function (mtr) {
                    return mtr.sequence;
                  })
                ) + 10;
            } else {
              material.sequence = 10;
            }
            this.materialsDataSourceCostaRica.data.push(material);
            this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
            this.materialsDataSourceCostaRica._updateChangeSubscription();
          } else {
            if (this.materialsDataSourceVietnam.data.length) {
              material.sequence =
                Math.max.apply(
                  Math,
                  this.materialsDataSourceVietnam.data.map(function (mtr) {
                    return mtr.sequence;
                  })
                ) + 10;
            } else {
              material.sequence = 10;
            }
            this.materialsDataSourceVietnam.data.push(material);
            this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
            this.materialsDataSourceVietnam._updateChangeSubscription();
          }
        }
      });
  }

  addLabel(value) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {jacketId: this._JacketID, groupingId: value, type: this.type};
    this.matDialog
      .open(FinalAddLabelComponent, matDataConfig)
      .afterClosed()
      .subscribe(async (label: LabelDTO) => {
        if (label) {
          const TempLabel = await this.getLabelCommonFields(label.partNumber);
          TempLabel.status = this.statuses.ADDED;
          if (this.type === 'Usa') {
            if (this.labelsDataSourceUSA.data.length) {
              TempLabel.number =
                Math.max.apply(
                  Math,
                  this.labelsDataSourceUSA.data.map(function (lbl) {
                    return lbl.number;
                  })
                ) + 10;
            } else {
              TempLabel.number = 10;
            }
            this.labelsDataSourceUSA.data.push(TempLabel);
            this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
            this.finalReviewDTO.usaFinalDesignReviewDTO.labels = this.labelsDataSourceUSA.data;
            this.labelsDataSourceUSA._updateChangeSubscription();
          } else if (this.type === 'Costa Rica') {
            if (this.labelsDataSourceCostaRica.data.length) {
              TempLabel.number =
                Math.max.apply(
                  Math,
                  this.labelsDataSourceCostaRica.data.map(function (lbl) {
                    return lbl.number;
                  })
                ) + 10;
            } else {
              TempLabel.number = 10;
            }
            this.labelsDataSourceCostaRica.data.push(TempLabel);
            this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
            this.labelsDataSourceCostaRica._updateChangeSubscription();
          } else {
            if (this.labelsDataSourceVietnam.data.length) {
              TempLabel.number =
                Math.max.apply(
                  Math,
                  this.labelsDataSourceVietnam.data.map(function (lbl) {
                    return lbl.number;
                  })
                ) + 10;
            } else {
              TempLabel.number = 10;
            }
            this.labelsDataSourceVietnam.data.push(TempLabel);
            this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
            this.labelsDataSourceVietnam._updateChangeSubscription();
          }
        }
      });
  }

  onFocusCloneOldValue(sequence: number) {
    this.oldSequence = sequence;
  }

  async updateOperation(element: OperationDTO, field: string, type: string) {
    if (type === 'Usa') {
      if (
        field === 'sequence' &&
        this.operationsDataSourceUSA.data.filter(op => op.sequence === element.sequence && op.status !== this.statuses.DELETED).length > 1
      ) {
        if (await this.sweetAlertService.sequenceDuplicationWarning()) {
          // sequence is already exist warn and abort
          element.sequence = this.oldSequence;
          this.oldSequence = null;
        }
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
        }
      }
    } else if (type === 'Costa Rica') {
      if (
        field === 'sequence' &&
        this.operationsDataSourceCostaRica.data.filter(op => op.sequence === element.sequence && op.status !== this.statuses.DELETED).length >
        1
      ) {
        if (await this.sweetAlertService.sequenceDuplicationWarning()) {
          // sequence is already exist warn and abort
          element.sequence = this.oldSequence;
          this.oldSequence = null;
        }
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
        }
      }
    } else {
      if (
        field === 'sequence' &&
        this.operationsDataSourceVietnam.data.filter(op => op.sequence === element.sequence && op.status !== this.statuses.DELETED).length >
        1
      ) {
        if (await this.sweetAlertService.sequenceDuplicationWarning()) {
          // sequence is already exist warn and abort
          element.sequence = this.oldSequence;
          this.oldSequence = null;
        }
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
        }
      }
    }
  }

  async updateMaterial(element: MaterialDTO, field: string, type: string) {
    if (type === 'Usa') {
      if (
        field === 'sequence' &&
        this.materialsDataSourceUSA.data.filter(op => op.sequence === element.sequence && op.status !== this.statuses.DELETED).length > 1
      ) {
        if (await this.sweetAlertService.sequenceDuplicationWarning()) {
          // sequence is already exist abort
          element.sequence = this.oldSequence;
          this.oldSequence = null;
        }
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
        }
      }
    } else if (type === 'Costa Rica') {
      if (
        field === 'sequence' &&
        this.materialsDataSourceCostaRica.data.filter(op => op.sequence === element.sequence && op.status !== this.statuses.DELETED).length >
        1
      ) {
        if (await this.sweetAlertService.sequenceDuplicationWarning()) {
          // sequence is already exist abort
          element.sequence = this.oldSequence;
          this.oldSequence = null;
        }
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
        }
      }
    } else {
      if (
        field === 'sequence' &&
        this.materialsDataSourceVietnam.data.filter(op => op.sequence === element.sequence && op.status !== this.statuses.DELETED).length >
        1
      ) {
        if (await this.sweetAlertService.sequenceDuplicationWarning()) {
          // sequence is already exist abort
          element.sequence = this.oldSequence;
          this.oldSequence = null;
        }
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
        }
      }
    }
  }

  async updateLabel(element: LabelDTO, field: string) {
    if (this.type === 'Usa') {
      if (
        field === 'number' &&
        this.labelsDataSourceUSA.data.filter(op => op.number === element.number && op.status !== this.statuses.DELETED).length > 1 &&
        (await this.sweetAlertService.sequenceDuplicationWarning())
      ) {
        // sequence is already exist abort
        element.number = this.oldSequence;
        this.oldSequence = null;
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
        }
      }
    } else if (this.type === 'Costa Rica') {
      if (
        field === 'number' &&
        this.labelsDataSourceCostaRica.data.filter(op => op.number === element.number && op.status !== this.statuses.DELETED).length > 1 &&
        (await this.sweetAlertService.sequenceDuplicationWarning())
      ) {
        // sequence is already exist abort
        element.number = this.oldSequence;
        this.oldSequence = null;
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
        }
      }
    } else {
      if (
        field === 'number' &&
        this.labelsDataSourceVietnam.data.filter(op => op.number === element.number && op.status !== this.statuses.DELETED).length > 1 &&
        (await this.sweetAlertService.sequenceDuplicationWarning())
      ) {
        // sequence is already exist abort
        element.number = this.oldSequence;
        this.oldSequence = null;
      } else {
        if (element.status !== this.statuses.ADDED || element.status === this.statuses.DELETED) {
          element.status = this.statuses.MODIFIED;
          this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
        }
      }
    }
  }

  updateJacket() {
    if (this.type === 'Usa') {
      this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.status = this.statuses.MODIFIED;
      this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
    } else if (this.type === 'Costa Rica') {
      this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.status = this.statuses.MODIFIED;
      this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
    } else {
      this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.status = this.statuses.MODIFIED;
      this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
    }
  }

  async deleteOperation(element: OperationDTO, type: string) {
    if (type === 'Usa') {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
        } else {
          this.operationsDataSourceUSA.data.splice(this.operationsDataSourceUSA.data.indexOf(element), 1);
        }
        this.operationsDataSourceUSA._updateChangeSubscription();
      }
    } else if (type === 'Costa Rica') {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
        } else {
          this.operationsDataSourceCostaRica.data.splice(this.operationsDataSourceCostaRica.data.indexOf(element), 1);
        }
        this.operationsDataSourceCostaRica._updateChangeSubscription();
      }
    } else {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
        } else {
          this.operationsDataSourceVietnam.data.splice(this.operationsDataSourceVietnam.data.indexOf(element), 1);
        }
        this.operationsDataSourceVietnam._updateChangeSubscription();
      }
    }
  }

  async deleteMaterial(element: MaterialDTO, type: string) {
    if (type === 'Usa') {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
        } else {
          this.materialsDataSourceUSA.data.splice(this.materialsDataSourceUSA.data.indexOf(element), 1);
        }
        this.materialsDataSourceUSA._updateChangeSubscription();
      }
    } else if (type === 'Costa Rica') {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
        } else {
          this.materialsDataSourceCostaRica.data.splice(this.materialsDataSourceCostaRica.data.indexOf(element), 1);
        }
        this.materialsDataSourceCostaRica._updateChangeSubscription();
      }
    } else {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
        } else {
          this.materialsDataSourceVietnam.data.splice(this.materialsDataSourceVietnam.data.indexOf(element), 1);
        }
        this.materialsDataSourceVietnam._updateChangeSubscription();
      }
    }
  }

  async deleteLabel(element: LabelDTO) {
    if (this.type === 'Usa') {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.usaFinalDesignReviewDTO.sync = true;
        } else {
          this.labelsDataSourceUSA.data.splice(this.labelsDataSourceUSA.data.indexOf(element), 1);
        }
        this.labelsDataSourceUSA._updateChangeSubscription();
      }
    } else if (this.type === 'Costa Rica') {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = true;
        } else {
          this.labelsDataSourceCostaRica.data.splice(this.labelsDataSourceCostaRica.data.indexOf(element), 1);
        }
        this.labelsDataSourceCostaRica._updateChangeSubscription();
      }
    } else {
      if (await this.sweetAlertService.deleteAlert()) {
        if (element.status !== this.statuses.ADDED) {
          element.status = this.statuses.DELETED;
          this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = true;
        } else {
          this.labelsDataSourceVietnam.data.splice(this.labelsDataSourceVietnam.data.indexOf(element), 1);
        }
        this.labelsDataSourceVietnam._updateChangeSubscription();
      }
    }
  }

  onMadeInChanged() {
    // start all the way from start
    if (this.type === 'Usa') {
      if (!this.partNumberMissing) {
        this.operationsDataSourceUSA.data = this.finalReviewDTO.usaFinalDesignReviewDTO.operations;
        this.materialsDataSourceUSA.data = this.finalReviewDTO.usaFinalDesignReviewDTO.materials;
        this.labelsDataSourceUSA.data = this.finalReviewDTO.usaFinalDesignReviewDTO.labels;
      }
    } else if (this.type === 'Vietnam') {
      if (!this.partNumberMissing) {
        this.operationsDataSourceVietnam.data = this.finalReviewDTO.vietnamFinalDesignReviewDTO.operations;
        this.materialsDataSourceVietnam.data = this.finalReviewDTO.vietnamFinalDesignReviewDTO.materials;
        this.labelsDataSourceVietnam.data = this.finalReviewDTO.vietnamFinalDesignReviewDTO.labels;
      }
    } else if (this.type === 'Costa Rica') {
      if (!this.partNumberMissing) {
        this.operationsDataSourceCostaRica.data = this.finalReviewDTO.costaRicaFinalDesignReviewDTO.operations;
        this.materialsDataSourceCostaRica.data = this.finalReviewDTO.costaRicaFinalDesignReviewDTO.materials;
        this.labelsDataSourceCostaRica.data = this.finalReviewDTO.costaRicaFinalDesignReviewDTO.labels;
      }
    }
    if (this.finalReviewDTO.usaFinalDesignReviewDTO.jacket) {
      this.noJacketUSA = false;
      this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.jacketId = this.part.jacketId;
      this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.revisionName = this.part.revisionName;
      this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.type = this.part.type;
      this.finalReviewDTO.usaFinalDesignReviewDTO.jacket.internalCrossReference = this.part.internalCrossRef;
      this.checkLastSyncStatusByJacketIdAndType();
    } else {
      // no jacket available for UAS
      this.noJacketUSA = true;
    }
    this.finalReviewDTO.usaFinalDesignReviewDTO.salesOrderNumber = this._salesOrderNumber;

    if (this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket) {
      this.noJacketVietnam = false;
      this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.jacketId = this.part.jacketId;
      this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.revisionName = this.part.revisionName;
      this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.type = this.part.type;
      this.finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.internalCrossReference = this.part.internalCrossRef;
      this.checkLastSyncStatusByJacketIdAndType();
    } else {
      // no jacket available for UAS
      this.noJacketVietnam = true;
    }
    this.finalReviewDTO.vietnamFinalDesignReviewDTO.salesOrderNumber = this._salesOrderNumber;

    if (this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket) {
      this.noJacketCostaRica = false;
      this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.jacketId = this.part.jacketId;
      this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.revisionName = this.part.revisionName;
      this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.type = this.part.type;
      this.finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.internalCrossReference = this.part.internalCrossRef;
      this.checkLastSyncStatusByJacketIdAndType();
    } else {
      // no jacket available for UAS
      this.noJacketCostaRica = true;
    }
    this.finalReviewDTO.costaRicaFinalDesignReviewDTO.salesOrderNumber = this._salesOrderNumber;
  }


  updatelabelConfig() {
    this.showLoader = true;
    this.subscription.add(
      this.bomeditorService
        .updateAllLabelEntriesEpicor(this.labelsDataSourceUSA.data)
        .subscribe(
          (res: LabelDTO[]) => {
            if (res) {
              this.labelsDataSourceUSA.data = res;
            }
            this.showLoader = false;
            this.isEdited = false;
          },
          (err) => {
            this.showLoader = false;
          }
        )
    );
  }

  // send final review updates to Epicore, also maintaining the status of the Sync
  sendToEpicore() {
    this.showLoader = true;
    this.syncInProcess = 'in progress';
    this.isSyncSuccess = false;
    if (this.noJacketUSA) {
      this.finalReviewDTO.usaFinalDesignReviewDTO.sync = false;
    }
    if (this.noJacketVietnam) {
      this.finalReviewDTO.vietnamFinalDesignReviewDTO.sync = false;
    }
    if (this.noJacketCostaRica) {
      this.finalReviewDTO.costaRicaFinalDesignReviewDTO.sync = false;
    }
    this.subscription.add(
      this.finalReviewService.finalReviewSave(this.finalReviewDTO).subscribe(
        async (syncSuccess: boolean) => {
          if (syncSuccess) {
            this.syncStatus = Messages.FINAL_REVIEW_SCREEN_MESSAGES.Sync_Success;
            this.isSyncSuccess = true;
          } else {
            this.syncStatus = Messages.FINAL_REVIEW_SCREEN_MESSAGES.Sync_Failed;
            this.isSyncSuccess = false;
          }
          await this.initializeFinalReview();
          this.showLoader = false;
          this.syncInProcess = 'completed';
        },
        () => {
          this.showLoader = false;
          this.syncInProcess = 'completed';
        }
      )
    );
  }

  getLabelCommonFields(labelPartNumber: string): Promise<LabelDTO> {
    let labelData: LabelDTO;
    return new Promise((resolve) => {
      this.subscription.add(
        this.finalReviewService.getLabelCommonFields(this._JacketID, labelPartNumber,this.type).subscribe((labelCommonFields: LabelDTO) => {
            if (labelCommonFields) {
              labelData = labelCommonFields;
              resolve(labelData);
            }
          },
          error => {
            if (error.applicationStatusCode === 1213) {
              this.snakbarService.error(error.message);
            }
          }
        )
      );
    });
  }

  openDMTLog() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_lg;
    matDataConfig.data = {jacketId: this._JacketID, epicorType: this.type, finalReviewScreen: true};
    this.matDialog
      .open(DMTLogComponent, matDataConfig)
      .afterClosed()
      .subscribe(() => {
      });
  }

  // on init get the last logs of the sync
  checkLastSyncStatusByJacketIdAndType() {
    this.syncInProcess = null;
    this.subscription.add(
      this.finalReviewService.checkLastSyncStatus(this._JacketID, this.type).subscribe(
        (syncStatus: string) => {
          if (syncStatus) {
            this.syncStatus = syncStatus;
            this.isSyncSuccess = syncStatus.includes(Messages.Bom_Element.sync_failed) ? false : true;
            this.syncInProcess = 'completed';
          }
        },
        () => {
          this.syncInProcess = 'completed';
        }
      )
    );
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
