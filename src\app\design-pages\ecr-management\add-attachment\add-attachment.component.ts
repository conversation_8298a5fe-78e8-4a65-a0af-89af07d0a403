import { Component, OnInit, OnDestroy } from '@angular/core';
import { MatDialogRef, MatDialog, MatTableDataSource } from '@angular/material';
import { Messages } from 'src/app/shared';
import { Document } from '../ecr-management.model';
import { EcrManagementService } from '../ecr-management.service';
import { Variable } from 'src/app/shared/constants/Variable.constants';

@Component({
  selector: 'sfl-add-attachment',
  templateUrl: './add-attachment.component.html'
})
export class AddAttachmentComponent implements OnInit, OnDestroy {

  attachment: string;
  value: string;
  documentDataSource = new MatTableDataSource<Document>();

  isValidFileSize = false;
  filedata: File;
  filename: string;
  document: Document;
  isUploading = false;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(private matDialog: MatDialog, private dialogRef: MatDialogRef<AddAttachmentComponent>, private ecrManagementService: EcrManagementService) { }

  ngOnInit() {
    this.value = Messages.Quotation.upload_message;
  }

  readUrl(event) {
    if (event.target.files && event.target.files[0]) {
      this.isValidFileSize = false;
      this.filedata = event.target.files[0];
      this.filename = this.filedata.name;
      this.value = this.filename;
    } else {
      this.filedata = null;
      this.value = Messages.Quotation.upload_message;
    }
  }

  uploadFile() {
    if (this.filedata.size > 10485760) {
      this.isValidFileSize = true;
      this.filedata = null;
      this.filename = null;
    } else {
      this.isUploading = true;
      this.isValidFileSize = false;
      this.dialogRef.close(this.filedata);
    }
  }

  add() {
    this.dialogRef.close(this.attachment);
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() { }
}
