<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Plug Name</mat-label>
          <input matInput [(ngModel)]="plugFilter.plugName" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldplugName)"
            *ngIf="plugFilter.plugName"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Part Number</mat-label>
          <input matInput [(ngModel)]="plugFilter.partNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldpartNumber)"
            *ngIf="plugFilter.partNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addPlug()">Add New Plug</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="plugMasterDataSource" (matSortChange)="getPlugMasterSorting($event)">
        <ng-container matColumnDef="plugName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Plug Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.plugName }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="plugCost">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Plug Cost </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.plugCost }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.partNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="jacketType">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Jacket Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.jacketType }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clothCe">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cloth CE </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.clothCe | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clothUl">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cloth UL </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.clothUl | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="imageUrl">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Image Url</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.imageUrl }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="jumperPartNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Jumper Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.jumperPartNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxAmps">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="5"> Max Amps </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5"> {{ element?.maxAmps }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxVolts">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="5"> Max Volts </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5"> {{ element?.maxVolts }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editPlug(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deletePlug(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="plugMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: plugMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!plugMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPlugMasterPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
