<!-- So In Design Main Content for Grid -->
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color"
                        [diameter]="spinnerDiameter"></mat-progress-spinner>
</div>
<div id="search-popup" *ngIf="isFindPopupVisible">
  <input #searchInput type="text" id="search-input" (keydown.enter)="nextMatch()" placeholder="Find in page"
         [(ngModel)]="searchText" (input)="onSearch()">
  <button class="button" id="previous-button" (click)="previousMatch()">&#9650;</button>
  <button class="button" id="next-button" (click)="nextMatch()">&#9660;</button>
  <span id="match-count">{{ currentMatch }}/{{ totalMatches }}</span>
  <button class="button" id="close-button" (click)="closePopup()">&#10005;</button>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <!-- Date filter section -->
    <form #quoteCompletedFilterForm="ngForm">
      <div fxLayout="row wrap" class="mb-10 cust_fields form-container">
        <div fxFlex fxLayoutAlign="start center" fxLayoutGap="20px" fxLayout="row wrap">
          <mat-form-field appearance="outline" fxFlex="11">
            <mat-label>SO #</mat-label>
            <input matInput (change)="clearSOFilter()" placeholder="SO #" name="soNumber"
                   [(ngModel)]="filter.soNumber"/>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex="11">
            <mat-label>Project Name</mat-label>
            <input matInput placeholder="Project Name" name="projectName" [(ngModel)]="filter.projectName"/>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex="11">
            <mat-label>Customer</mat-label>
            <input matInput placeholder="Customer Name" name="customerName" [(ngModel)]="filter.customerName"/>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex="11">
            <mat-label>Quote Status</mat-label>
            <mat-select placeholder="Quote Status" name="quoteStatus" [(ngModel)]="filter.quoteStatusIds"
                        (selectionChange)="onSelectionChange($event)" multiple>
              <mat-option [value]="allStatusValue">{{ allStatusLabel }}</mat-option>
              <mat-option *ngFor="let status of statuses | orderBy:'orderNumber'" [value]="status.id">
                {{ status?.status }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex="11">
            <mat-label>Design Location</mat-label>
            <mat-select placeholder="Design Location" name="designLocation" [(ngModel)]="filter.designLocation"
                        multiple>
              <mat-option value="USA">USA</mat-option>
              <mat-option value="Vietnam">Vietnam</mat-option>
              <mat-option value="Costa Rica">Costa Rica</mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex="11">
            <mat-label>Design Engineer</mat-label>
            <mat-select placeholder="Design Engineer" name="designEngineer" [(ngModel)]="filter.designerIds" multiple>
              <mat-option *ngFor="let designengineer of salesassociates" [value]="designengineer?.id">
                {{ designengineer?.firstName }} {{ designengineer?.lastName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex="11">
            <mat-label>Product Type</mat-label>
            <mat-select placeholder="Product Type" name="productType" [(ngModel)]="filter.customProductType" multiple>
              <mat-option>None</mat-option>
              <mat-option *ngFor="let type of productTypes" [value]="type.id">
                {{ type?.id }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <div fxLayout="row wrap" class="mb-10 cust_fields form-container">
            <div fxFlex="100" fxLayoutAlign="end" fxLayoutGap="10px">
              <button mat-raised-button color="warn" class="p-2" (click)="searchDesignQuotes()"
                      [disabled]="quoteCompletedFilterForm.invalid">
                Search
              </button>
              <button mat-raised-button class="p-2" (click)="clearFilter()">Clear</button>
            </div>
          </div>
        </div>
      </div>
    </form>
  </mat-card>
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ headingTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayout="row wrap" fxLayoutGap="15px" fxLayoutAlign="end center">
        <mat-checkbox name="show-all-fields" color="warn" [(ngModel)]="filter.statusReadyForWork"
                      (change)="selectStatus()">
          Ready to Work on
        </mat-checkbox>
        <mat-checkbox name="show-all-fields" color="warn" [(ngModel)]="filter.statusInProgress"
                      (change)="selectStatus()">
          In Progress
        </mat-checkbox>
        <mat-checkbox name="show-all-fields" color="warn" [(ngModel)]="filter.statusOutForApproval"
                      (change)="selectStatus()">
          Out for Approval
        </mat-checkbox>
        <button class="p-2" mat-raised-button color="warn" (click)="downloadDesignEnggReportExcel()">Export To Excel
        </button>
      </div>
    </div>

    <virtual-scroller class="dynamic-virtual-scroll" #scroll [items]="filteredList" (vsEnd)="fetchMore($event)"
                      [enableUnequalChildrenSizes]="true" [bufferAmount]="50">
      <table class="table" id="table-items">
        <thead #header class="sticky-table-header">
        <tr>
          <th class="td_size_action" *ngIf="!isSales">#</th>
          <th [ngClass]="{'sticky-col': !isSales, 'sticky-col-sales': isSales}" (click)="getSorting({ active: 'salesOrderNumber', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">SO Number</span>
              <i *ngIf="sortActive === 'salesOrderNumber' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'salesOrderNumber' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'salesOrderNumber'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'designLocation', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Design Location</span>
              <i *ngIf="sortActive === 'designLocation' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'designLocation' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'designLocation'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'productTypeEnum', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Product Type</span>
              <i *ngIf="sortActive === 'productTypeEnum' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'productTypeEnum' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'productTypeEnum'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'customNoOfDesigns', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Design Count</span>
              <i *ngIf="sortActive === 'customNoOfDesigns' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'customNoOfDesigns' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'customNoOfDesigns'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'customer.name', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Customer</span>
              <i *ngIf="sortActive === 'customer.name' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'customer.name' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'customer.name'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'engReleaseDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Est. Eng. Release Date</span>
              <i *ngIf="sortActive === 'engReleaseDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'engReleaseDate' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'engReleaseDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'shipDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Ship Date</span>
              <i *ngIf="sortActive === 'shipDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'shipDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'shipDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'quotationStatus.status', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Design Status</span>
              <i *ngIf="sortActive === 'quotationStatus.status' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'quotationStatus.status' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'quotationStatus.status'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'assignedDesignerId', direction: sortOrder })">
            <span class="open-doc">Designer</span>
            <i *ngIf="sortActive === 'assignedDesignerId' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
            <i *ngIf="sortActive === 'assignedDesignerId' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
            <i *ngIf="sortActive !== 'assignedDesignerId'" class="material-icons">unfold_more</i>
          </th>
          <th (click)="getSorting({ active: 'designComments', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Comments</span>
              <i *ngIf="sortActive === 'designComments' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'designComments' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'designComments'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'submittedToDesignDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Submitted Date</span>
              <i *ngIf="sortActive === 'submittedToDesignDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'submittedToDesignDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'submittedToDesignDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'approvalStatus.status', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Approval Status</span>
              <i *ngIf="sortActive === 'approvalStatus.status' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'approvalStatus.status' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'approvalStatus.status'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'ofa1Date', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">OFA 1</span>
              <i *ngIf="sortActive === 'ofa1Date' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'ofa1Date' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'ofa1Date'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'soRevCount', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">SO Revision Count</span>
              <i *ngIf="sortActive === 'soRevCount' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'soRevCount' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'soRevCount'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'revisionDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Revision Date</span>
              <i *ngIf="sortActive === 'revisionDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'revisionDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'revisionDate'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'lastOFA', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Last OFA</span>
              <i *ngIf="sortActive === 'lastOFA' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'lastOFA' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'lastOFA'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'approval1Date', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Approval</span>
              <i *ngIf="sortActive === 'approval1Date' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'approval1Date' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'approval1Date'" class="material-icons">unfold_more</i>
            </div>
          </th>

<!--          <th (click)="getSorting({ active: 'releaseDate', direction: sortOrder })">-->
<!--            <div class="header-content">-->
<!--              <span class="open-doc">Release Date</span>-->
<!--              <i *ngIf="sortActive === 'releaseDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>-->
<!--              <i *ngIf="sortActive === 'releaseDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>-->
<!--              <i *ngIf="sortActive !== 'releaseDate'" class="material-icons">unfold_more</i>-->
<!--            </div>-->
<!--          </th>-->

          <th (click)="getSorting({ active: 'salesAssociate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Sales Rep</span>
              <i *ngIf="sortActive === 'salesAssociate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'salesAssociate' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'salesAssociate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'dollarAmount', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Dollar Amount</span>
              <i *ngIf="sortActive === 'dollarAmount' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'dollarAmount' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'dollarAmount'" class="material-icons">unfold_more</i></div>
          </th>
          <th (click)="getSorting({ active: 'noOfCustomRevision', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">No of Revisions</span>
              <i *ngIf="sortActive === 'noOfCustomRevision' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'noOfCustomRevision' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'noOfCustomRevision'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'noOfDaysOFA1', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">OFA Days</span>
              <i *ngIf="sortActive === 'noOfDaysOFA1' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'noOfDaysOFA1' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'noOfDaysOFA1'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'appEngineer', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">App Engineer</span>
              <i *ngIf="sortActive === 'appEngineer' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'appEngineer' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'appEngineer'" class="material-icons">unfold_more</i>
            </div>
          </th>

          <th (click)="getSorting({ active: 'projectTitle', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Project Name</span>
              <i *ngIf="sortActive === 'projectTitle' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'projectTitle' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'projectTitle'" class="material-icons">unfold_more</i>
            </div>
          </th>

<!--          <th (click)="getSorting({ active: 'submittedDesignDate', direction: sortOrder })">-->
<!--            <div class="header-content">-->
<!--              <span class="open-doc">Submitted Design Date</span>-->
<!--              <i *ngIf="sortActive === 'submittedDesignDate' && sortOrder === 'asc'"-->
<!--                 class="material-icons">arrow_upward</i>-->
<!--              <i *ngIf="sortActive === 'submittedDesignDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>-->
<!--              <i *ngIf="sortActive !== 'submittedDesignDate'" class="material-icons">unfold_more</i>-->
<!--            </div>-->
<!--          </th>-->
          <th (click)="getSorting({ active: 'designQueueDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Pattern Design Date</span>
              <i *ngIf="sortActive === 'designQueueDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'designQueueDate' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'designQueueDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'patternDesignDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Pattern Design Date (IP)</span>
              <i *ngIf="sortActive === 'patternDesignDate' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'patternDesignDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'patternDesignDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'level1ReviewDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Level 1 Review Date</span>
              <i *ngIf="sortActive === 'level1ReviewDate' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'level1ReviewDate' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'level1ReviewDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'level1ReviewDateIp', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Level 1 Review (IP)</span>
              <i *ngIf="sortActive === 'level1ReviewDateIp' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'level1ReviewDateIp' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'level1ReviewDateIp'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'readyForOfaDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Ready For Ofa Date</span>
              <i *ngIf="sortActive === 'readyForOfaDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'readyForOfaDate' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'readyForOfaDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'outForApprovalDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Out For Approval Date</span>
              <i *ngIf="sortActive === 'outForApprovalDate' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'outForApprovalDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'outForApprovalDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'simulationDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Simulation Date</span>
              <i *ngIf="sortActive === 'simulationDate' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'simulationDate' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'simulationDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'simulationDateIp', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Simulation Date (IP)</span>
              <i *ngIf="sortActive === 'simulationDateIp' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'simulationDateIp' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'simulationDateIp'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'elementAndBomDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Element And Bom Date</span>
              <i *ngIf="sortActive === 'elementAndBomDate' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'elementAndBomDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'elementAndBomDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'elementAndBomDateIp', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Element and BOM (IP)</span>
              <i *ngIf="sortActive === 'elementAndBomDateIp' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'elementAndBomDateIp' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'elementAndBomDateIp'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'finalReviewDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Final Review Date</span>
              <i *ngIf="sortActive === 'finalReviewDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'finalReviewDate' && sortOrder === 'desc'"
                 class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'finalReviewDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'finalReviewDateIp', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Final Review (IP)</span>
              <i *ngIf="sortActive === 'finalReviewDateIp' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'finalReviewDateIp' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'finalReviewDateIp'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'vietnamConversionDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Vietnam Conversion Date</span>
              <i *ngIf="sortActive === 'vietnamConversionDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'vietnamConversionDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'vietnamConversionDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'readyForReleaseDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Ready For Release Date</span>
              <i *ngIf="sortActive === 'readyForReleaseDate' && sortOrder === 'asc'"
                 class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'readyForReleaseDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'readyForReleaseDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
          <th (click)="getSorting({ active: 'releasedDate', direction: sortOrder })">
            <div class="header-content">
              <span class="open-doc">Released Date</span>
              <i *ngIf="sortActive === 'releasedDate' && sortOrder === 'asc'" class="material-icons">arrow_upward</i>
              <i *ngIf="sortActive === 'releasedDate' && sortOrder === 'desc'" class="material-icons">arrow_downward</i>
              <i *ngIf="sortActive !== 'releasedDate'" class="material-icons">unfold_more</i>
            </div>
          </th>
        </tr>
        </thead>
        <tbody class="text-center" #container>
        <tr class="overflow-text-css" *ngFor="let item of scroll.viewPortItems; let i = index"
            [ngStyle]="rowColorSelection(item?.designStatusId)">
          <td class="td_size_action" *ngIf="!isSales">
            <button mat-icon-button class="menu-icon" [matMenuTriggerFor]="menu">
              <mat-icon>more_horizontal</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <a mat-menu-item [href]="jacketLink + '?quotId=' + item?.id" target="_blank" rel="noopener noreferrer">
                <mat-icon>open_in_new</mat-icon>
                <span>Open in Design</span>
              </a>
              <a mat-menu-item [href]="appQuoteLink + '?quotId=' + item?.id" target="_blank" rel="noopener noreferrer">
                <mat-icon>open_in_new</mat-icon>
                <span>Open in App Engineering Quote</span>
              </a>
              <button mat-menu-item (click)="openTrackerField(item?.id)">
                <mat-icon>update</mat-icon>
                <span>Updated Tracker Fields</span>
              </button>
            </mat-menu>
          </td>
          <td
          [ngClass]="{'sticky-col': !isSales, 'sticky-col-sales': isSales}"
              [ngStyle]="columnColorSelection('soNumber', item?.designEngQuoteTrackerColumnColorList)"
              matTooltip="{{ item?.soNumber }}" matTooltipClass="sfl-formula-tooltip">
            <a [style.pointer-events]="!isSales ?'auto':'none'"
               [ngClass]="{'highlight': item.highlight==='current', 'previous-highlight': item.highlight==='previous'}"
               (click)="openTrackerField(item?.id)"
               class="link">{{ item?.soNumber }}</a>
            <ng-template #other> {{ item?.soNumber }}</ng-template>
          </td>
          <td (click)="!isSales && openColorSelectionColumn('designLocation',item)"
              [ngStyle]="columnColorSelection('designLocation', item?.designEngQuoteTrackerColumnColorList)">{{ item?.designLocation | uppercase }}
          </td>
          <td class="td-size-productType" (click)="!isSales && openColorSelectionColumn('productType',item)"
              [ngStyle]="columnColorSelection('productType', item?.designEngQuoteTrackerColumnColorList)">{{ item?.productType }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('noOfDesigns',item)"
              [ngStyle]="columnColorSelection('noOfDesigns', item?.designEngQuoteTrackerColumnColorList)">{{ item?.noOfDesigns }}
          </td>
          <td class="td-size-customer" (click)="!isSales && openColorSelectionColumn('customerName',item)"
              [ngStyle]="columnColorSelection('customerName', item?.designEngQuoteTrackerColumnColorList)"
              matTooltip="{{ item?.customerName }}"
              matTooltipClass="sfl-formula-tooltip">{{ item?.customerName }}
          </td>
          <td class="td_size_shipDate" (click)="!isSales && openColorSelectionColumn('engReleaseDate',item)"
              [ngStyle]="columnColorSelection('engReleaseDate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.engReleaseDate | date: 'MM-dd-yyyy' }}
          </td>
          <td class="td_size_shipDate" (click)="!isSales && openColorSelectionColumn('shipDate',item)"
              [ngStyle]="columnColorSelection('shipDate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.shipDate | date: 'MM-dd-yyyy' }}
          </td>
          <td class="td-size-status" (click)="!isSales && openColorSelectionColumn('designStatusId',item)"
              [ngStyle]="columnColorSelection('designStatusId', item?.designEngQuoteTrackerColumnColorList)"
              matTooltip="{{ item?.designStatusId | getQuoteStatusName: statuses }}"
              matTooltipClass="sfl-formula-tooltip">
            {{ item?.designStatusId | getQuoteStatusName: statuses }}
          </td>
          <td class="td_size_designer" (click)="!isSales && openColorSelectionColumn('designerId',item)"
              [ngStyle]="columnColorSelection('designerId', item?.designEngQuoteTrackerColumnColorList)">{{
              item?.designerId | getFirstNameLastNameDesign:
                salesassociates:salesassociates
            }}
          </td>
          <td class="td_size_projectTitle" (click)="!isSales && openColorSelectionColumn('designComments',item)"
            [ngStyle]="columnColorSelection('designComments', item?.designEngQuoteTrackerColumnColorList)"
            matTooltip="{{ item?.designComments }}" matTooltipClass="sfl-formula-tooltip"><b>{{ item?.designComments }}</b></td>
          <td (click)="!isSales && openColorSelectionColumn('folderSubmittedDate',item)"
              [ngStyle]="columnColorSelection('folderSubmittedDate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.folderSubmittedDate | date: 'MM-dd-yyyy' }}
          </td>
          <td class="td_size" (click)="!isSales && openColorSelectionColumn('approvalStatusId',item)"
              [ngStyle]="columnColorSelection('approvalStatusId', item?.designEngQuoteTrackerColumnColorList)"
              matTooltip="{{ item?.approvalStatusId | getQuoteStatusName: statuses }}"
              matTooltipClass="sfl-formula-tooltip">
            {{ item?.approvalStatusId | getQuoteStatusName: statuses }}
          </td>
          <td class="td_size_ofa" (click)="!isSales && openColorSelectionColumn('ofa1Date',item)"
              [ngStyle]="columnColorSelection('ofa1Date', item?.designEngQuoteTrackerColumnColorList)">{{ item?.ofa1Date | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('soRevCount',item)"
              [ngStyle]="columnColorSelection('soRevCount', item?.designEngQuoteTrackerColumnColorList)">{{ item?.soRevCount }}
          </td>
          <td class="td_size_ofa" (click)="!isSales && openColorSelectionColumn('revisionDate',item)"
              [ngStyle]="columnColorSelection('revisionDate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.revisionDate | date: 'MM-dd-yyyy' }}
          </td>
          <td class="td_size_ofa" (click)="!isSales && openColorSelectionColumn('lastOFADate',item)"
              [ngStyle]="columnColorSelection('lastOFADate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.lastOFADate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('approval1Date',item)"
              [ngStyle]="columnColorSelection('approval1Date', item?.designEngQuoteTrackerColumnColorList)">{{ item?.approval1Date | date: 'MM-dd-yyyy' }}
          </td>
<!--          <td class="td_size_ofa" (click)="openColorSelectionColumn('releaseDate',item)"-->
<!--              [ngStyle]="columnColorSelection('releaseDate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.releaseDate | date: 'MM-dd-yyyy' }}-->
<!--          </td>-->

          <td class="td_size" (click)="!isSales && openColorSelectionColumn('salesRepId',item)"
              [ngStyle]="columnColorSelection('salesRepId', item?.designEngQuoteTrackerColumnColorList)">{{
              item?.salesRepId | getFirstNameLastName:
                salesassociates:salesassociates
            }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('dollarAmount',item)"
              [ngStyle]="columnColorSelection('dollarAmount', item?.designEngQuoteTrackerColumnColorList)">{{ item?.dollarAmount | currency: 'USD':'symbol':'1.2-2' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('noOfRevisions',item)"
              [ngStyle]="columnColorSelection('noOfRevisions', item?.designEngQuoteTrackerColumnColorList)">{{ item?.noOfRevisions }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('noOfDaysOFA1',item)"
              [ngStyle]="columnColorSelection('noOfDaysOFA1', item?.designEngQuoteTrackerColumnColorList)">{{ item?.noOfDaysOFA1 }}
          </td>
          <td class="td_size_app" (click)="!isSales && openColorSelectionColumn('appEnggAssigned',item)"
              [ngStyle]="columnColorSelection('appEnggAssigned', item?.designEngQuoteTrackerColumnColorList)"
              matTooltip="{{ item?.appEnggAssigned | getFirstNameLastName: salesassociates:salesassociates }}"
              matTooltipClass="sfl-formula-tooltip">
            {{ item?.appEnggAssigned | getFirstNameLastName: salesassociates:salesassociates }}
          </td>
          <td class="td_size_projectTitle" (click)="!isSales && openColorSelectionColumn('projectTitle',item)"
              [ngStyle]="columnColorSelection('projectTitle', item?.designEngQuoteTrackerColumnColorList)"
              matTooltip="{{ item?.projectTitle }}"
              matTooltipClass="sfl-formula-tooltip">{{ item?.projectTitle }}
          </td>
<!--          <td (click)="openColorSelectionColumn('submittedDesignDate', item)"-->
<!--              [ngStyle]="columnColorSelection('submittedDesignDate', item?.designEngQuoteTrackerColumnColorList)"-->
<!--              class="td_size_shipDate">{{ item?.submittedDesignDate | date: 'MM-dd-yyyy' }}-->
<!--          </td>-->
          <td (click)="!isSales && openColorSelectionColumn('designQueueDate', item)"
              [ngStyle]="columnColorSelection('designQueueDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.designQueueDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('patternDesignDate', item)"
              [ngStyle]="columnColorSelection('patternDesignDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.patternDesignDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('level1ReviewDate', item)"
              [ngStyle]="columnColorSelection('level1ReviewDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.level1ReviewDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('level1ReviewDateIp', item)"
              [ngStyle]="columnColorSelection('level1ReviewDateIp', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.level1ReviewDateIp | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('readyForOfaDate', item)"
              [ngStyle]="columnColorSelection('readyForOfaDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.readyForOfaDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('outForApprovalDate', item)"
              [ngStyle]="columnColorSelection('outForApprovalDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.outForApprovalDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('simulationDate', item)"
              [ngStyle]="columnColorSelection('simulationDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.simulationDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('simulationDateIp', item)"
              [ngStyle]="columnColorSelection('simulationDateIp', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.simulationDateIp | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('elementAndBomDate', item)"
              [ngStyle]="columnColorSelection('elementAndBomDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.elementAndBomDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('elementAndBomDateIp', item)"
              [ngStyle]="columnColorSelection('elementAndBomDateIp', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.elementAndBomDateIp | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('finalReviewDate', item)"
              [ngStyle]="columnColorSelection('finalReviewDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.finalReviewDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('finalReviewDateIp', item)"
              [ngStyle]="columnColorSelection('finalReviewDateIp', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.finalReviewDateIp | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('vietnamConversionDate', item)"
              [ngStyle]="columnColorSelection('vietnamConversionDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.vietnamConversionDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('readyForReleaseDate', item)"
              [ngStyle]="columnColorSelection('readyForReleaseDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.readyForReleaseDate | date: 'MM-dd-yyyy' }}
          </td>
          <td (click)="!isSales && openColorSelectionColumn('releasedDate', item)"
              [ngStyle]="columnColorSelection('releasedDate', item?.designEngQuoteTrackerColumnColorList)"
              class="td_size_shipDate">{{ item?.releasedDate | date: 'MM-dd-yyyy' }}
          </td>
        </tr>
        </tbody>
      </table>
    </virtual-scroller>
    <div class="status">
      Showing <span>{{ scroll.viewPortInfo.startIndex + 1 }}</span> -
      <span>{{ scroll.viewPortInfo.endIndex + 1 }}</span> of
      <span>{{ filteredList?.length }}</span>
    </div>
  </mat-card>
</div>
