import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import {
  ClosureMaterialMaster,
  ClosureMaterialMasterPageable,
  ClosureMaterialFilter,
  GenericPageable
} from '../masterdata-management.model';
import { MatTableDataSource, MatSort, MatPaginator, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageClosureMaterialComponent } from './manage-closure-material/manage-closure-material.component';
import { SnakbarService, Messages, SweetAlertService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-closure-material-master',
  templateUrl: './closure-material-master.component.html'
})
export class ClosureMaterialMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Closure Material Master';
  closureMaterialMaster: ClosureMaterialMaster;
  closureMaterialMasterPageable: GenericPageable<ClosureMaterialMaster>;
  closureMaterialMasterDataSource = new MatTableDataSource<ClosureMaterialMaster>();
  closureMaterialMasterMasterColumns = DisplayColumns.Cols.ClosureMaterialMaster;

  dataSource = new MatTableDataSource<ClosureMaterialMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  closureMaterialFilter: ClosureMaterialFilter = new ClosureMaterialFilter();

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;

  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  filterFieldPartNumber1 = Values.FilterFields.partNumber1;
  filterFieldName = Values.FilterFields.name;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) {}

  ngOnInit() {
    this.getClosureMaterialMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new Closure Material
  addClosureMaterial() {
    this.editClosureMaterial(new ClosureMaterialMaster());
  }

  // used to add filter to Closure Material listing
  async addFilter() {
    this.filter = [
      { key: this.filterFieldPartNumber1, value: !this.closureMaterialFilter.partNumber1 ? '' : this.closureMaterialFilter.partNumber1 },
      { key: this.filterFieldName, value: !this.closureMaterialFilter.name ? '' : this.closureMaterialFilter.name }
    ];
    this.getClosureMaterialMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to Closure Material listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber1,
        value:
          fieldToClear === this.filterFieldPartNumber1
            ? (this.closureMaterialFilter.partNumber1 = '')
            : this.closureMaterialFilter.partNumber1
      },
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.closureMaterialFilter.name = '') : this.closureMaterialFilter.name
      }
    ];
    this.getClosureMaterialMasterData(this.initialPageIndex, this.pageSize);
  }

  getClosureMaterialMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getClosureMaterialList(this.filter, pageable).subscribe(
        (res: GenericPageable<ClosureMaterialMaster>) => {
          this.closureMaterialMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createClosureTable(this.closureMaterialMasterPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.closureMaterialMasterDataSource.data = [];
        }
      )
    );
  }

  createClosureTable(serviceRequestList: GenericPageable<ClosureMaterialMaster>) {
    this.closureMaterialMasterDataSource.data = serviceRequestList.content;
  }

  getClosurePagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getClosureMaterialMasterData(this.pageIndex, this.pageSize);
  }

  getClosureMaterialMasterSorting(event) {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getClosureMaterialMasterData(this.pageIndex, this.pageSize);
  }

  async deleteClosureMaterial(materialPropertyId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteClosureMaterial(materialPropertyId).subscribe(
        res => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getClosureMaterialMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }
  editClosureMaterial(closureMaterial: ClosureMaterialMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = closureMaterial;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-closure-material-master-model';
    const dialogRef = this.matDialog.open(ManageClosureMaterialComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          closureMaterial.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getClosureMaterialMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
