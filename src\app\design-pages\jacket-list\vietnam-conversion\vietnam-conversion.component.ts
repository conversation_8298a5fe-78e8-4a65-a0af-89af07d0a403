import { Component } from '@angular/core';
import { MatDialogRef, MatTableDataSource } from '@angular/material';
import { Messages, SnakbarService } from '../../../shared';
import { JacketListComponent } from '..';

@Component({
    selector: 'sfl-vietnam-conversion',
    templateUrl: './vietnam-conversion.component.html'
})

export class VietnamConversionComponent {

    partdisplayedColumns = ['uspn', 'vietnampn', 'rev', 'getdet', 'createusv', 'createviet', 'unglobalize', 'uploadbom', 'uploadlabels'];
    partdataSource = new MatTableDataSource<PartData>(Part_Data);

    oprdisplayedColumns = ['opr', 'des', 'opr2', 'prod', 'setup'];
    oprdataSource = new MatTableDataSource<OperationData>(Operation_Data);

    matdisplayedColumns = ['part', 'des', 'qty', 'action'];
    matdataSource = new MatTableDataSource<MaterialsData>(Materials_Data);

    matdisplayedColumns2 = ['part', 'des', 'qty'];
    matdataSource2 = new MatTableDataSource<MaterialsData>(Materials_Data);

    constructor(
        private snakbarService: SnakbarService,
        public dialogRef: MatDialogRef<JacketListComponent>
    ) { }

    saveCustomer() {
        this.dialogRef.close();
    }

    closeDialog(): void {
        this.dialogRef.close();
    }
}

export interface PartData {
    uspn: string;
    vietnampn: string;
    rev: string;
    getdet: string;
    createusv: string;
    createviet: string;
    unglobalize: string;
    uploadbom: string;
    uploadlabels: string;
}

export interface OperationData {
    opr: string;
    des: string;
    opr2: string;
    prod: string;
    setup: string;
}

export interface MaterialsData {
    part: string;
    des: string;
    qty: string;
}

export interface LableData {
    format: string;
    volts: string;
    watts: string;
    amps: string;
    phase: string;
}

export const Part_Data: PartData[] = [
    { uspn: '7468276', vietnampn: '72682768', rev: '0', getdet: 'X', createusv: '', createviet: '', unglobalize: '', uploadbom: '', uploadlabels: '' },
    { uspn: '7468276', vietnampn: '72682768', rev: '0', getdet: 'X', createusv: '', createviet: '', unglobalize: '', uploadbom: '', uploadlabels: '' },
    { uspn: '7468276', vietnampn: '72682768', rev: '0', getdet: 'X', createusv: '', createviet: '', unglobalize: '', uploadbom: '', uploadlabels: '' },
    { uspn: '7468276', vietnampn: '72682768', rev: '0', getdet: 'X', createusv: '', createviet: '', unglobalize: '', uploadbom: '', uploadlabels: '' },
    { uspn: '7468276', vietnampn: '72682768', rev: '0', getdet: 'X', createusv: '', createviet: '', unglobalize: '', uploadbom: '', uploadlabels: '' },
    { uspn: '7468276', vietnampn: '72682768', rev: '0', getdet: 'X', createusv: '', createviet: '', unglobalize: '', uploadbom: '', uploadlabels: '' }
];

export const Operation_Data: OperationData[] = [
    { opr: '10', des: 'StockRoom', opr2: '1', prod: '0.25', setup: '0.17' },
    { opr: '20', des: 'STD Cloth Line 1', opr2: '2', prod: '4.5', setup: '0' }
];

export const Materials_Data: MaterialsData[] = [
    { part: '5728728', des: '2*1 3/4 Hot Surface', qty: '5' }
];


