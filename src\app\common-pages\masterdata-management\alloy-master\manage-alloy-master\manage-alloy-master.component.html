<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #alloysForm="ngForm" (ngSubmit)="updateAlloy()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Alloy Names" [(ngModel)]="alloys.alloyName" name="alloyName" #alloyNameInput="ngModel" required />
        </mat-form-field>
        <div *ngIf="alloyNameInput.touched && alloyNameInput.invalid">
          <small class="mat-text-warn" *ngIf="alloyNameInput?.errors?.required">Alloy name is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            type="number"
            matInput
            placeholder="OHMS Per Foot"
            [(ngModel)]="alloys.ohmsPerFoot"
            name="ohmsPerFoot"
            #ohmsPerFootInput="ngModel"
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="Max Temp. C" [(ngModel)]="alloys.maxTempC" name="maxTempC" #maxTempCInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="GND Part Number"
            [(ngModel)]="alloys.gndPartNumber"
            name="gndPartNumber"
            #gndPartNumberInput="ngModel"
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="FG Part Number" [(ngModel)]="alloys.fgPartNumber" name="fgPartNumber" #fgPartNumberInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="SAM Part Number"
            [(ngModel)]="alloys.samPartNumber"
            name="samPartNumber"
            #samPartNumberInput="ngModel"
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="TPIGN Part Number"
            [(ngModel)]="alloys.tpignPartNumber"
            name="tpignPartNumber"
            #tpignPartNumberInput="ngModel"
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="TPIFG Part Number"
            [(ngModel)]="alloys.tpifgPartNumber"
            name="tpifgPartNumber"
            #tpifgPartNumberInput="ngModel"
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="TPISAM Part Number"
            [(ngModel)]="alloys.tpisamPartNumber"
            name="tpisamPartNumber"
            #tpisamPartNumberInput="ngModel"
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            type="number"
            matInput
            placeholder="TPI W per In"
            [(ngModel)]="alloys.tpiWperIn"
            name="tpiWperIn"
            #tpiWperInInput="ngModel"
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="MSM" [(ngModel)]="alloys.msm" name="msm" #msmInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="MSB" [(ngModel)]="alloys.msb" name="msb" #msbInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="alloys.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!alloysForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
