<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
  class="sfl-global-spinner-loader"
  [mode]="mode"
  [color]="color"
  [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<div fxLayout="row" fxLayoutAlign="end" fxLayoutGap="20px" class="mb-20">
  <div fxLayout="column" fxFlex.gt-lg="15" fxFlex.gt-md="15">
    <button mat-raised-button type="button" color="warn" [disabled]="dataSource?.data.length === 0" (click)="regenerateEcoCosting()">Re-Generate Cost Report</button>
  </div>
</div>

<!-- Cost report -->
<div class="cust-table">
  <table aria-describedby="costing_table">
    <tr class="mat-header-row">
      <th class="mat-header-cell" scope="col">
        Part Number
      </th>
      <th class="mat-header-cell" scope="col">
        List
      </th>
      <th class="mat-header-cell" scope="col">
        Net
      </th>
      <th class="mat-header-cell" scope="col">
        Material
      </th>
      <th class="mat-header-cell" scope="col">
        Labor
      </th>
      <th class="mat-header-cell" scope="col">
        Burden
      </th>
      <th class="mat-header-cell" scope="col">
        Sub Cnt
      </th>
      <th class="mat-header-cell" scope="col">
        Mtl Burd
      </th>
      <th class="mat-header-cell" scope="col">
        Total
      </th>
      <th class="mat-header-cell" scope="col">
        Margin
      </th>
      <th class="mat-header-cell" scope="col">
        Status
      </th>
    </tr>
    <tbody *ngFor="let costReport of dataSource.data; let i = index" class="text-center">
      <tr id="collaps" (click)="toggleHiddenRow[i] = !toggleHiddenRow[i]" [ngClass]="(costReport.inActive || costReport.onHold)? 'open-doc' : '' ">
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.partNumber}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.listPrice | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.netPrice | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.materialCost | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.laborCost | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.burdenCost | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.subConstCost | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.mtlBurCost | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.totalCost | currency}}
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          {{costReport?.margin }}%
        </td>
        <td class="mat-cell" [ngClass]="((costReport?.inActive || costReport?.onHold) || (!costReport?.valid) ? 'inactive-onhold-costreport' : '')" [matTooltip]="costReport?.onHold == true || costReport?.inActive == true ? 'Some of the materials added in this jacket are onhold or inactive' : null" matTooltipPosition="above" matTooltipClass="cost-report-tooltip">
          &nbsp;
        </td>
      </tr>
      <tr *ngFor="let childCostDetail of costReport?.childCostDetailsDTOS;" class="inner-row" [hidden]="!toggleHiddenRow[i]">
        <td class="mat-cell">
          {{childCostDetail?.partNumber}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.listPrice | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.netPrice | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.materialCost | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.laborCost | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.burdenCost | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.subConstCost | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.mtlBurCost | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.totalCost | currency}}
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.margin }}%
        </td>
        <td class="mat-cell">
          {{ childCostDetail?.status }}
        </td>
      </tr>
    </tbody>
  </table>
  <div *ngIf="dataSource?.data.length === 0 && !showLoader">
    <div class="no-records">No data found</div>
  </div>
</div>
