import { DatePipe } from '@angular/common';
import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SweetAlertService } from 'src/app/shared/service/sweet-alert.service';
import { JacketStatus } from '../jacket-list.model';
import { UndoResponse } from '../pattern/pattern.model';
import { Values } from './../../../shared/constants/values.constants';
import { ChecklistSimulationDTO, Simulation } from './simulation.model';
import { SimulationService } from './simulation.service';

@Component({
  selector: 'sfl-simulation',
  templateUrl: './simulation.component.html'
})
export class SimulationComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  simulation = new Simulation();
  checklistSimulationDTO: ChecklistSimulationDTO = new ChecklistSimulationDTO();
  jacketList = new Array<number>();
  jacketId: number;
  quotationId: number;
  showLoader: boolean;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  undoResponseDTO: UndoResponse = new UndoResponse();
  JacketStatuses: JacketStatus = new JacketStatus();

  constructor(
    public readonly dialogRef: MatDialogRef<SimulationComponent>,
    private readonly sweetAlertService: SweetAlertService,
    private readonly datePipe: DatePipe,
    private readonly simulationService: SimulationService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketList = data.jacketList;
    this.jacketId = data.jacketId;
    this.quotationId = data.quotationId;
  }

  ngOnInit() {
    this.simulation.simulationDate = new Date();
    if (this.jacketId) {
      this.getSimulationByJacketId(this.jacketId);
    }
    this.checklistSimulationDTO.quotationID = this.quotationId;
  }

  getSimulationByJacketId(jacketId) {
    this.subscription.add(
      this.simulationService.getSimulationByJacketId(jacketId).subscribe((res: Simulation) => {
        if (res) {
          this.simulation = res;
        }
      })
    );
  }

  saveSimulation() {
    this.showLoader = true;
    this.simulation.simulationDate = this.datePipe.transform(this.simulation.simulationDate, Values.dateFormat.format);
    if (this.jacketList && this.jacketList.length > 0) {
      this.jacketList.forEach(ele => {
        const simulationConst = Object.assign({}, this.simulation);
        simulationConst.jacketId = ele;
        simulationConst.checked = true;
        this.checklistSimulationDTO.simulationDTOs.push(simulationConst);
      });
    } else {
      if (this.jacketId) {
        this.simulation.jacketId = this.jacketId;
        this.checklistSimulationDTO.simulationDTOs.push(this.simulation);
      }
    }

    if (this.checklistSimulationDTO.simulationDTOs.length > 0) {
      this.subscription.add(
        this.simulationService.saveSimulation(this.checklistSimulationDTO).subscribe(
          res => {
            this.checklistSimulationDTO = new ChecklistSimulationDTO();
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.SAVE, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    } else {
      this.showLoader = false;
    }
  }

  async undoSimulation() {
    if (await this.sweetAlertService.warningWhenUndoSignature()) {
      this.subscription.add(
        this.simulationService.updateSimulation(this.checklistSimulationDTO, this.jacketId).subscribe(
          res => {
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.UNDO, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  closeDialog(updated?: string, jacketId?: number): void {
    this.undoResponseDTO.status = updated;
    this.undoResponseDTO.id = jacketId;
    this.dialogRef.close(this.undoResponseDTO);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
