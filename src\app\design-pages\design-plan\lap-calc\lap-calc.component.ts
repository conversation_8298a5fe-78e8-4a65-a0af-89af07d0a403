import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { Utils, SharedService } from 'src/app/shared';
import { MatDialogRef } from '@angular/material';
import { DesignPlanComponent } from '../design-plan.component';
import { LapCalcService } from './lap-calc.service';
import { LapCalc } from './lap-calc.model';
import {ManageUnitsService} from '../../../admin-pages/new-quotation/manage-units/manage-units.service';
import {Units} from '../../../admin-pages/new-quotation/ccdc-model/ccdc.model';

@Component({
  selector: 'sfl-lap-calc',
  templateUrl: './lap-calc.component.html',
})
export class LapCalcComponent implements OnInit, OnDestroy {

  subscription: Subscription = new Subscription();
  lapCalc: LapCalc;
  lapCalcRes: LapCalc[];
  notApplicable = 'N/A';
  loading: boolean;
  measureUnit: string;

  constructor(
    public dialogRef: MatDialogRef<DesignPlanComponent>,
    private lapCalcService: LapCalcService,
  ) { }

  ngOnInit() {
    this.loading = false;
    this.lapCalc = new LapCalc();
    this.lapCalc.unit = 'IN';
  }

  calculateLapList() {
    if (this.lapCalc.pipeDiameter) {
      this.loading = true;
      this.subscription.add(this.lapCalcService.lapCalc(this.lapCalc).subscribe((res: LapCalc[]) => {
        if (res) {
          this.lapCalcRes = res;
          this.loading = false;
        } else {
          this.loading = false;
        }
      }, () => {
        this.loading = false;
      }));
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
