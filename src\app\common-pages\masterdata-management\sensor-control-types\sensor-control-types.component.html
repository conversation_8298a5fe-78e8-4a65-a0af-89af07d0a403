<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex.gt-lg="30" fxFlex.gt-md="30">
          <mat-label>Search Sensor Control Type</mat-label>
          <input matInput [(ngModel)]="sensorControlTypesFilter.id" (change)="addFilter()" />
        </mat-form-field>
        <button mat-raised-button color="warn" (click)="addSensorControlTypes()">Add New Sensor Control Type</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="sensorControlTypesDataSource"
        (matSortChange)="getSensorControlTypesSorting($event)"
      >
        <ng-container matColumnDef="value">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="95"> Sensor Control Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="95"> {{ element?.value }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editSensorControlTypes(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteSensorControlTypes(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="sensorControlTypesColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: sensorControlTypesColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!sensorControlTypesDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getSensorControlTypesPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
