import { User } from './../user.model';
import { UserService } from './../user.service';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Messages, SharedService, SnakbarService } from '../../../shared';
import { UsersComponent } from '../users.component';
import { NgForm } from '@angular/forms';
import { AccountService } from '../../../account/account.service';

@Component({
  selector: 'sfl-add-user',
  templateUrl: './add-user.component.html'
})
export class AddUserComponent implements OnInit {
  user: User;
  username: string;
  title: string;
  isUserExist = false;

  constructor(
    private readonly snakbarService: SnakbarService,
    public readonly dialogRef: MatDialogRef<UsersComponent>,
    public readonly addDialogRef: MatDialogRef<AddUserComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly userService: UserService,
    private readonly sharedService: SharedService
  ) {
    this.username = data.username;
  }

  ngOnInit() {
    this.username !== '' ? ((this.title = 'Update User'), (this.isUserExist = true)) : (this.title = 'Add User');
    if (this.username !== '') {
      this.getSelectedUser(this.username);
    }
    this.user = new User();
    this.user.authorities = new Array();
  }

  getSelectedUser(email) {
    this.userService.find(email).subscribe(
      response => (this.user = response.body),
      () => this.closeAddDialog(1)
    );
  }

  saveUser() {
    if (this.user.id || this.user.id !== null) {
      this.userService.update(this.user).subscribe(
        response => {
          if (this.sharedService.getUserId() === this.user.id) {
            this.sharedService.setUsersCountry(this.user.country);
          }
          this.closeAddDialog(0);
        },
        err => this.closeAddDialog(err.error.title)
      );
    } else {
      this.userService.create(this.user).subscribe(
        response => this.closeAddDialog(0),
        err => this.closeAddDialog(err.error.title)
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  closeAddDialog(status): void {
    if (status) {
      this.snakbarService.error(status);
    }

    this.addDialogRef.close();
  }

  sendForgotPasswordEmail() {
    this.userService.sendForgotPasswordEmail(this.user.email).subscribe(
      response => {
        this.snakbarService.success(Messages.Login.fogot_Password_success);
      },
      error => {
        if (error.status === 400 && error.error.title === Messages.Login.email_not_exists) {
          this.snakbarService.error(Messages.Login.email_not_registered);
        } else {
          this.snakbarService.error(Messages.error.error_msg);
        }
      }
    );
  }
}
