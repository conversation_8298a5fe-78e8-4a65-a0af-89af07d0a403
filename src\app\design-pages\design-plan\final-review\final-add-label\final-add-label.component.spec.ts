import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FinalAddLabelComponent } from './final-add-label.component';

describe('FinalAddLabelComponent', () => {
  let component: FinalAddLabelComponent;
  let fixture: ComponentFixture<FinalAddLabelComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FinalAddLabelComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FinalAddLabelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
