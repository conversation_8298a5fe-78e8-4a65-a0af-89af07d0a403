<h2 mat-dialog-title>Manage CCDC</h2>
<mat-horizontal-stepper #stepper>
  <mat-step label="Edit CCDC" state="home">
    <div class="sfl-loading" *ngIf="showLoader">
      <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
      </mat-progress-spinner>
    </div>
    <h2 mat-dialog-title>
      Edit CCDC
      <mat-icon
        *ngIf="showReloadButton"
        class="open-doc sfl-pull-right"
        [matTooltip]="outDatedViewErrorMessage"
        color="warn"
        matTooltipClass="sfl-formula-tooltip"
        (click)="reloadPage()"
        id="refresh"
      >
        cached
      </mat-icon>
      <hr />
    </h2>
    <div fxLayout="row" fxLayoutAlign="space-between" fxFlex="100">
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select placeholder="User Name" [(ngModel)]="ccdcMasterDataObs.userId">
          <mat-option *ngFor="let user of users" [value]="user.id" name="users">
            {{ user.id | getFirstNameLastName: users }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Template Name" name="templateName" [(ngModel)]="ccdcMasterDataObs.name" />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Description" name="description" [(ngModel)]="ccdcMasterDataObs.description" />
      </mat-form-field>
    </div>
    <hr />
    <br />
    <mat-dialog-actions fxLayoutAlign="space-between">
      <div fxLayoutAlign="end">
        <button mat-raised-button matStepperNext name="saveandnext">Next</button>
      </div>
    </mat-dialog-actions>
  </mat-step>

  <mat-step label="Application Information">
    <app-manage-ccdc-application
      [applicationInfoDto]="ccdcMasterDataObs?.applicationInfoTemplateDTO"
      [measureUnit]="ccdcMasterDataObs.measurementUnit"
      [tempUnit]="ccdcMasterDataObs.temperatureUnit"
    ></app-manage-ccdc-application>
  </mat-step>

  <mat-step label="Plugging Information">
    <app-manage-ccdc-plugging-information
      [pluggingInfoDto]="ccdcMasterDataObs?.pluggingInformationTemplateDTO"
      [measureUnit]="ccdcMasterDataObs.measurementUnit"
      [tempUnit]="ccdcMasterDataObs.temperatureUnit"
    ></app-manage-ccdc-plugging-information>
  </mat-step>

  <mat-step label="Material Information">
    <app-manage-ccdc-material-information
      [ccdcMasterData]="ccdcMasterDataObs"
      [materialInfoDto]="ccdcMasterDataObs?.materialInfoTemplateDTOS"
    ></app-manage-ccdc-material-information>
  </mat-step>

  <mat-step label="Closure Information">
    <app-manage-closure-information
      [closureTemplatedto]="ccdcMasterDataObs?.closureTemplateDTO"
      [measureUnit]="ccdcMasterDataObs.measurementUnit"
      [tempUnit]="ccdcMasterDataObs.temperatureUnit"
    ></app-manage-closure-information>
  </mat-step>

  <mat-step label="Sensor Information">
    <app-manage-sensor-information
      [ccdcMasterData]="ccdcMasterDataObs"
      [sensorInfoDto]="ccdcMasterDataObs?.sensorsInformationTemplateDTOS"
    ></app-manage-sensor-information>
  </mat-step>

  <mat-step label="Thermostat Information">
    <app-manage-thermostat-information
      [ccdcMasterData]="ccdcMasterDataObs"
      [thermostatInfoDto]="ccdcMasterDataObs?.thermostatInformationTemplateDTOS"
    ></app-manage-thermostat-information>
  </mat-step>

  <mat-step label="Notes">
    <mat-dialog-content>
      <mat-form-field fxFlex="100">
        <textarea matInput placeholder="Notes" name="note" rows="10" [(ngModel)]="ccdcMasterDataObs.notes" #noteInput="ngModel"></textarea>
      </mat-form-field>
    </mat-dialog-content>
    <mat-dialog-actions fxLayoutAlign="space-between">
      <div fxLayoutAlign="end">
        <button mat-raised-button matStepperNext name="saveandnext">Next</button>
      </div>
    </mat-dialog-actions>
  </mat-step>

  <mat-step label="Save All Your Templates">
    <h2 mat-dialog-title>Save All Your Work</h2>
    <div fxLayoutAlign="start">
      <button mat-raised-button color="warn" type="submit" (click)="saveCcdcTemplate()" name="save">Save</button>
    </div>
  </mat-step>
</mat-horizontal-stepper>
