<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>

<form class="forms_form" #currencyOptionsForm="ngForm" (ngSubmit)="updateMaterialProperty()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Name" [(ngModel)]="currencyMaterial.name" name="name" #name="ngModel" required />
        </mat-form-field>
        <div *ngIf="name.touched && name.invalid">
          <small class="mat-text-warn" *ngIf="name?.errors.required">Name is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="name?.errors?.whitespace && !name?.errors?.required">
          Invalid Name.
        </small>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Abbreviation" [(ngModel)]="currencyMaterial.abbreviation" name="abbreviation" #abbreviation="ngModel" required sflNoWhiteSpaces />
        </mat-form-field>
        <div *ngIf="abbreviation.touched && abbreviation.invalid">
          <small class="mat-text-warn" *ngIf="abbreviation?.errors.required">Abberavation is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="abbreviation?.errors?.whitespace && !abbreviation?.errors?.required">
          Invalid Abberavation.
        </small>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Conversion Rate wrt USD" [(ngModel)]="currencyMaterial.conversionRateWrtUSD" name="conversionRateWrtUSD" #conversionRateWrtUSD="ngModel"
            required sflNoWhiteSpaces />
        </mat-form-field>
        <div *ngIf="conversionRateWrtUSD.touched && conversionRateWrtUSD.invalid">
          <small class="mat-text-warn" *ngIf="conversionRateWrtUSD?.errors.required">Conversion Rate is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="conversionRateWrtUSD?.errors?.whitespace && !conversionRateWrtUSD?.errors?.required">
          Invalid Conversion Rate.
        </small>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Symbol" [(ngModel)]="currencyMaterial.symbol" name="symbol" #symbol="ngModel" required sflNoWhiteSpaces />
        </mat-form-field>
        <div *ngIf="symbol.touched && symbol.invalid">
          <small class="mat-text-warn" *ngIf="symbol?.errors.required">Symbol is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="symbol?.errors?.whitespace && !symbol?.errors?.required">
          Invalid Symbol.
        </small>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-checkbox name="obsolete" color="warn" [(ngModel)]="currencyMaterial.obsolete">Is Obsolete</mat-checkbox>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!currencyOptionsForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
