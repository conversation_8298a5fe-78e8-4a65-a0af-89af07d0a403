import {Component, Inject, Input, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {Subscription} from 'rxjs';
import {Values} from 'src/app/shared/constants/values.constants';
import {DesignEngineeringQuoteTracker} from '../../../quote-tracker/quote-tracker.model';
import {DashboardDesignEnggTrackerComponent} from '../dashboard-design-engg-tracker.component';
import {QuoteTrackerService} from '../../../quote-tracker/quote-tracker.service';

@Component({
  selector: 'app-design-engg-quote-tracker-row-colour',
  templateUrl: './dashboard-design-engg-tracker-row-colour.component.html',
  styleUrls: ['./dashboard-design-engg-tracker-row-colour.component.css']
})
export class DashboardDesignEnggTrackerRowColourComponent implements OnInit {
  highlightedColors = Values.highlightColors;
  highlightedRowColors = Values.highlightRowColors;
  rowHighlight: DesignEngineeringQuoteTracker = new DesignEngineeringQuoteTracker();
  subscription: Subscription = new Subscription();
  showLoader = false;
  quoteStatusId: number;
  @Input() designStatusId: number;
  @Input() id: number;
  @Input() primaryColorValue: string;
  quoteId: any;
  private _quotationId: any;
  private _quotationStatusId: any;

  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    public dialogRef: MatDialogRef<DashboardDesignEnggTrackerComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
    this._quotationId = data.quotationId;
    this._quotationStatusId = data.designStatusId;
  }

  ngOnInit() {

  }

  selectRow(colorValue: string) {
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.designRowHighlighting(this._quotationId, colorValue, this._quotationStatusId).subscribe(
        (res: DesignEngineeringQuoteTracker) => {
          if (res) {
            this.closeDialog(true);
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog(flag = false): void {
    this.dialogRef.close(flag);
  }

}
