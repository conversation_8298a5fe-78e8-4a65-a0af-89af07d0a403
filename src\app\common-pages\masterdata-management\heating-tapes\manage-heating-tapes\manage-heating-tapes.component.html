<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #heatingTapesForm="ngForm" (ngSubmit)="updateHeatingTapes()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select
            placeholder="Tape Type"
            [(ngModel)]="heatingTape.tapeType"
            name="tapeType"
            #tapeTypeInput="ngModel"
            required
            (ngModelChange)="changeInTapeType()"
          >
            <mat-option *ngFor="let type of tapeTypes" [value]="type.id">
              {{ type.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="tapeTypeInput.touched && tapeTypeInput.invalid">
          <small class="mat-text-warn" *ngIf="tapeTypeInput?.errors?.required">Tape Type is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="Width" [(ngModel)]="heatingTape.width" name="width" #widthInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Tape Part Number"
            [(ngModel)]="heatingTape.tapePartNumber"
            name="tapePartNumber"
            #tapePartNumberInput="ngModel"
            required
            (change)="validateTapePartNumber()"
          />
        </mat-form-field>
        <div *ngIf="tapePartNumberInput.touched && tapePartNumberInput.invalid">
          <small class="mat-text-warn" *ngIf="tapePartNumberInput?.errors?.required">Tape Part Number is required.</small>
        </div>
      </div>

      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <div>
          <button
            mat-stroked-button
            type="button"
            color="warn"
            matTooltip="Click to create system generated Tape Part Number"
            (click)="generateTapePartNumber()"
          >
            Generate Tape Part Number
          </button>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Alloy Name" [(ngModel)]="heatingTape.alloyName" name="alloyName" #alloyNameInput="ngModel" required>
            <mat-option *ngFor="let alloy of alloys" [value]="alloy.alloyName">
              {{ alloy.alloyName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="alloyNameInput.touched && alloyNameInput.invalid">
          <small class="mat-text-warn" *ngIf="alloyNameInput?.errors?.required">Alloy name is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <div>
          <button mat-stroked-button type="button" color="warn" (click)="manageAlloys()">Manage Alloys</button>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Wire Type" [(ngModel)]="heatingTape.wireType" name="wireType" #wireTypeInput="ngModel">
            <mat-option *ngFor="let type of wireTypes" [value]="type.id">
              {{ type.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="Strands" [(ngModel)]="heatingTape.strands" name="strands" #strandsInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="TPI" [(ngModel)]="heatingTape.tpi" name="tpi" #tpiInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="Picks" [(ngModel)]="heatingTape.picks" name="picks" #picksInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input type="number" matInput placeholder="MSM" [(ngModel)]="heatingTape.warps" name="warps" #warpsInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            type="number"
            matInput
            placeholder="OHMS Per Ft"
            [(ngModel)]="heatingTape.ohmsPerFt"
            name="ohmsPerFt"
            #ohmsPerFtInput="ngModel"
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="heatingTape.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isDualWireTape" color="warn" [(ngModel)]="heatingTape.dualWireTape">Is Dual Wire Tape</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!heatingTapesForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
