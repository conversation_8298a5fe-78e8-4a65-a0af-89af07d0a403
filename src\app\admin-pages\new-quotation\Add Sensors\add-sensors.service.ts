
import {catchError, map} from 'rxjs/operators';
import { SensorInformation, SensorInformationObject } from './../ccdc-model/ccdc.model';
import { utils } from '../../../shared/helpers/app.helper';
import { AppConfig } from '../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class SensorService {
  constructor(private readonly http: HttpClient) {}

  getSensorList(id) {
    return this.http.get(AppConfig.GET_SENSOR_LIST.concat('/jacketGroup/').concat(id)).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  saveSensorInfo(sensorInformations: SensorInformationObject, id) {
    return this.http
      .post(AppConfig.SAVE_SENSOR_INFO.concat('/jacketGroup/').concat(id), sensorInformations).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteSensorInfo(id) {
    return this.http.delete(AppConfig.GET_SENSOR_LIST.concat('/').concat(id)).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getSensorConnectorsList() {
    return this.http.get(AppConfig.SENSOR_CONNECTORS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getSensorTypesList() {
    return this.http.get(AppConfig.SENSOR_TYPES).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getThermostatTypeList() {
    return this.http.get(AppConfig.THERMOSTAT_TYPES).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
}
