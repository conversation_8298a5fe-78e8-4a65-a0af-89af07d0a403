import { environment } from '../environments/environment';

export class AppConfig {
  public static UAA = environment.API_URL.concat('bhuaa/api');
  public static BHX = environment.API_URL.concat('bhBhx/api');
  // public static BHX = 'http://localhost:8094/api';

  public static LOGIN_API = environment.API_URL.concat('auth/login');
  public static USER_API = AppConfig.UAA.concat('/users');
  public static Account_API = AppConfig.UAA.concat('/account');
  public static RESET_PASSWORD_INIT_API = AppConfig.Account_API.concat('/reset-password/init');
  public static RESET_PASSWORD_FINISH_API = AppConfig.Account_API.concat('/reset-password/finish');
  public static QUOTATION = AppConfig.BHX.concat('/quotations');
  public static SEARCH_QUOTATION_API = AppConfig.QUOTATION.concat('/search');
  public static SEARCH_QUOTATION_DESIGN = AppConfig.BHX.concat('/quotations/design/search');
  public static SalesAssociate = AppConfig.UAA.concat('/users/salesAssociates');
  public static COPY_QUOTATIONS = AppConfig.BHX.concat('/quotations/copy/');
  public static DELETE_QUOTATION = AppConfig.QUOTATION;
  public static STATUS_COUNT = AppConfig.BHX.concat('/quotations/status/count');
  public static STATUS_LIST = AppConfig.BHX.concat('/quotation-statuses/type/all');
  public static STATUS_LIST_BY_TYPE = AppConfig.BHX.concat('/quotation-statuses/by-type/');
  public static STATUS_LIST_UPDATE = AppConfig.BHX.concat('/quotationStatuses');
  public static QUOT_LIST = AppConfig.BHX.concat('/quotations/global/search');
  public static QUOT_LIST_DESIGN = AppConfig.BHX.concat('/quotations/design/global/search');
  public static EPICOR_API = environment.API_URL.concat('bhepicore/api');
  public static GET_QUOTATION = AppConfig.EPICOR_API.concat('/quotations');
  public static GET_RFQ_QUOTATION = AppConfig.BHX.concat('/quotations/rfq');
  public static GET_SO_QUOTATION = AppConfig.BHX.concat('/quotations/sales-order-form/');
  public static CHECK_SO_NUMBER = AppConfig.BHX.concat('/quotations/sales-order-form/sales/');
  public static GET_SO_DETAILS = AppConfig.EPICOR_API.concat('/orderHed');
  public static CCDC_MASTER_DATA_PAGEABLE = AppConfig.BHX.concat('/ccdc-templates/filter');
  public static GET_PART_NUMBER_MASTER = AppConfig.BHX.concat('/part-number-master/all');
  public static SAVE_MASTER_PART_NUMBER = AppConfig.BHX.concat('/part-number-master');
  public static CCDC_TEMPLATE_API = AppConfig.BHX.concat('/ccdc-templates');
  public static QUOTATION_EXIXTS = AppConfig.EPICOR_API.concat('/quotations/isExists');
  public static BHX_SAVE_QUOTATION = AppConfig.QUOTATION;
  public static GET_QUOT_BY_ID = AppConfig.QUOTATION;
  public static GET_MATERIALS_LIST = AppConfig.BHX.concat('/materialProperties');
  public static APPLICATION_INFO = AppConfig.BHX.concat('/ccdcApplicationInfos');
  public static GET_MATERIAL_LAYER = AppConfig.BHX.concat('/materials');
  public static GET_MATERIAL_LAYER_IDENTIFIERS = AppConfig.BHX.concat('/material/identifiers');
  public static SAVE_MATERIAL = AppConfig.BHX.concat('/materialInfoList/jacketGroup/');
  public static GET_ALL_MATERIAL_BY_JACKETGROUP_ID = AppConfig.BHX.concat('/materialInfos/jacketGroup');
  public static WORKFLOW_API = AppConfig.BHX.concat('/ccdcWorkflows');
  public static WORKFLOW_BY_JACKETGROUP_API = AppConfig.BHX.concat('/ccdcWorkflows/jacketGroup/');
  public static SAVE_NOTES = AppConfig.BHX.concat('/ccdcNotes');
  public static GET_NOTES_JACKETGROUP_ID = AppConfig.BHX.concat('/ccdcNotes/jacketGroup');
  public static GET_CLOSURE_MATERIAL_LIST = AppConfig.BHX.concat('/closureMaterials');
  public static GET_CURRENCY_MATERIAL_LIST = AppConfig.BHX.concat('/currencies');
  public static SAVE_CLOSURE = AppConfig.BHX.concat('/closures');
  public static GET_THERMOSATS = AppConfig.BHX.concat('/thermostatLists');
  public static SAVE_THERMOSATS_INFO = AppConfig.BHX.concat('/thermostatInformationsList/jacketGroup/');
  public static GET_THERMOSTAT_BY_JGID = AppConfig.BHX.concat('/thermostatInformations/jacketGroup');
  public static SEARCH_THERMOSTATE = AppConfig.BHX.concat('/thermostatLists/search');
  public static PLUGS_API = AppConfig.BHX.concat('/plugs');
  public static GET_PART_CLASS_DISPLAY = AppConfig.BHX.concat('/part-class-displays');
  public static GET_PLUGS_BY_JACKET_TYPE = AppConfig.BHX.concat('/plugs/jacketType/');
  public static PLUGGINGINFORMATION_API = AppConfig.BHX.concat('/pluggingInformations');
  public static PLUGGING_BY_JACKETGROUP_API = AppConfig.BHX.concat('/pluggingInformations/jacketGroup/');
  public static SAVE_SENSOR_INFO = AppConfig.BHX.concat('/sensorsInformationsList');
  public static GET_SENSOR_LIST = AppConfig.BHX.concat('/sensorsInformations');
  public static GET_FEATURES = AppConfig.BHX.concat('/features');
  public static GET_JACKETGROUP_BY_QUOTID = AppConfig.BHX.concat('/jacketGroups/quotation/');
  public static SAVE_JACKET = AppConfig.BHX.concat('/jackets/revision/');
  public static GET_JACKETS_BY_QUOT_ID = AppConfig.BHX.concat('/jackets/quotation/');
  public static SAVE_JACKET_FEATURES = AppConfig.BHX.concat('/jacketFeatures');
  public static GET_ALL_JACKETS_By_REVID = AppConfig.BHX.concat('/jackets/revision/');
  public static GET_JACKETS_GROUP = AppConfig.BHX.concat('/jacketGroups');
  public static SAVE_ALL_JACKETS = AppConfig.BHX.concat('/jackets/saveAll');
  public static DELETE_JACKET = AppConfig.BHX.concat('/jackets/reset-feature/');
  public static DELETE_JACKET_FEATURE = AppConfig.BHX.concat('/jacketFeatures/');
  public static GET_REVISIONS = AppConfig.BHX.concat('/revisions');
  public static COPY_JACKET = AppConfig.BHX.concat('/jackets');
  public static GET_JACKETS_GROUP_BY_REVISON_ID = AppConfig.BHX.concat('/jacketGroups/quotation/');
  public static GET_JACKETS_LIST_BY_REVISON_ID = AppConfig.BHX.concat('/jacket-list/revision/');
  public static GET_CUSTOMER_SQT_LINK = AppConfig.BHX.concat('/customers-sqt-link/');
  public static GET_JACKETS_LIST_BY_REVISON_ID_DROPDOWN = AppConfig.BHX.concat('/jackets/list/');
  public static MOVE_JACKET_FEATURES = AppConfig.BHX.concat('/jacketFeatures/move');

  public static GET_SYNC_STATUS = AppConfig.BHX.concat('/jackets/sync-status');
  public static GET_NEW_HALF_JACKET_LIST = AppConfig.BHX.concat('/process/jacket/');
  public static GET_OTHER_NEW_HALF_JACKET_LIST = AppConfig.BHX.concat('/jacket-list/jacket-group-ids');
  public static GET_ALL_JACKETS_ACCESSORIES = AppConfig.BHX.concat('/jacketAccessories/revision/');
  public static GET_ALL_ACCESSORIES_LIST = AppConfig.BHX.concat('/accessories');
  public static DELETE_MASTER_PART_NUMBER = AppConfig.BHX.concat('/part-number-master');
  public static GET_ALL_ACCESSORY_CONTROLLERS = AppConfig.BHX.concat('/accessoryControllers');
  public static SAVE_ACCESSORIES_LIST = AppConfig.BHX.concat('/revisionAccessories');
  public static UPDATE_REV_ORDER = AppConfig.BHX.concat('/revisionAccessories/updateIndexes');
  public static DELETE_MATERIAL = AppConfig.BHX.concat('/materialInfos/');
  public static DELETE_THERMOSTAT = AppConfig.BHX.concat('/thermostatInformations/');
  public static DOCUMENT_API = AppConfig.BHX.concat('/documents/');
  public static FINALIZE_DETAILS_API = AppConfig.BHX.concat('/finalizeDetails/revision/');
  public static FINALIZE_RESET_DETAILS_API = AppConfig.BHX.concat('/finalizeDetails/reset/revision/');
  public static FINALIZE_RESET_SELECTED_JACKETS_DETAILS_API = AppConfig.BHX.concat('/finalizeDetails/reset-added-values/selected/');
  public static FINALIZE_DELETE_SELECTED_JACKETS = AppConfig.BHX.concat('/finalizeDetails/deleteJacket/selected/');
  public static GEOMETRY_SELECTED_FEATURE_DELETE = AppConfig.BHX.concat('/jacketFeatures/checked');
  public static CHANGE_ENTRY_METHOD = AppConfig.BHX.concat('/changeEntryMethod/');
  public static DELETE_JACKET_FROM_LIST = AppConfig.BHX.concat('/jackets/');
  public static TOGGLE_ON_HOLD = AppConfig.BHX.concat('/jackets');

  public static GET_ELBOWLENGTH = AppConfig.BHX.concat('/elbow/calculateLength/');
  public static GET_JACKET_LENGTH = AppConfig.BHX.concat('/jackets/calculateLength/');
  public static GET_EST_TEMP = AppConfig.BHX.concat('/closures/calculateSurfaceTemp/');
  public static JACKET_DISCOUNT = AppConfig.BHX.concat('/jacketAccessories/applyDiscount/revision/');
  public static SEND_TO_DESING = AppConfig.BHX.concat('/quotations/sendToDesign/');
  public static UPDATE_QTY_FINALIZE = AppConfig.BHX.concat('/finalizeDetails/update');
  public static MASS_UPDATE_JACKET_COSTS = AppConfig.BHX.concat('/finalizeDetails/massUpdate');
  public static DOWNLOAD_QUOTE_EXCEL_API = AppConfig.BHX.concat('/downloadExcel/quotation/');
  public static DOWNLOAD_SO_EXCEL_API = AppConfig.BHX.concat('/downloadSOExcel');
  public static DOWNLOAD_BOM_EXCEL_API = AppConfig.BHX.concat('/bomMaterials/download/jacket/');
  public static DOWNLOAD_ELEMENT_EXCEL_API = AppConfig.BHX.concat('/export-elements-excel/');
  public static DOWNLOAD_SO_ZIP_FILE_API = AppConfig.BHX.concat('/jackets/download-zipFiles');
  public static POWER_CORD_CONNECTORS = AppConfig.BHX.concat('/powerCord/connectors');
  public static GETJACKETBYJACKETID = AppConfig.BHX.concat('/jackets/');
  public static GENERATE_PART_NUMBER_API = AppConfig.BHX.concat('/jackets/partNumber/jacketGroup/');
  public static GET_ALL_JACKET_LIST_BY_QID = AppConfig.BHX.concat('/geometry/quotation/');
  public static APPLY_VIETNAM_DISCOUNT = AppConfig.BHX.concat('/finalizeDetails');
  public static UPDATE_PART_NUMBER_API = AppConfig.BHX.concat('/jackets/partNumber/');
  public static JACKETGROUP_BY_QUOTATION_API = AppConfig.BHX.concat('/jacketGroups/quotation/');
  public static GET_PART_NUMBER_ECR = AppConfig.BHX.concat('/v1/ecr-part-number/list');
  public static GET_ECR = AppConfig.BHX.concat('/v1/ecrs/getForReview');
  public static GET_ECR_PART_BY_ECR_ID = AppConfig.BHX.concat('/v1/ecr-part-number/ecr-id/');
  public static GET_FINAL_REVIEW_ECR = AppConfig.BHX.concat('/v1/ecr/final-review/');
  public static GET_SO_NUMBER_BY_PART_NUMBER = AppConfig.BHX.concat('/jackets/getSONumbers/');
  public static GET_ECR_PART_NUM = AppConfig.BHX.concat('/v1/ecr-part-number/');
  public static UPDATE_PATTERN_DESIGN = AppConfig.BHX.concat('/undo/patternDesigns/');
  public static SEARCH_MFG_PART_NUMBER = AppConfig.EPICOR_API.concat('/partInfo/');
  public static GET_REPEAT_JACKET = AppConfig.BHX.concat('/jackets/mark-repeat');
  public static SAVE_CUSTOMER_PN = AppConfig.BHX.concat('/jackets/save/customerPN');
  public static JACKET_BY_JACKETGROUP_API = AppConfig.BHX.concat('/jackets/jacketGroup/');
  public static UPDATE_BHC_PART_NUMBER_API = AppConfig.BHX.concat('/jackets/partNumber/bhc/');
  public static UPDATE_ROOT_PN_API = AppConfig.BHX.concat('/jackets/jacketGroup/rootPN');
  public static GET_DEFAULTS_GEOMETRY = AppConfig.BHX.concat('/geometryDefaults/getValues');
  public static GEOMETRY_IMAGE_UPLOAD = AppConfig.BHX.concat('/jacketFeatures/');
  public static GEOMETRY_JACKET_IMAGE_UPLOAD = AppConfig.BHX.concat('/jackets/');
  public static LEAD_TIME = AppConfig.BHX.concat('/quotations/leadTime');
  public static ENTRY_METHOD = AppConfig.BHX.concat('/changeEntryMethod/');
  public static GET_ALL_CURRENCIES = AppConfig.BHX.concat('/currencies');
  public static LABEL_CONFIG_MASTER_API = AppConfig.BHX.concat('/labelConfigurationMasters');
  public static LABEL_DETAILED_ENTRY_API = AppConfig.BHX.concat('/labelDetailedEntry');

  public static FINALIZE_DETAILS_API_DISCOUNT_VIETNAM = AppConfig.BHX.concat('/finalizeDetails/vietnamDiscount/single');

  public static RESET_PART_NUMBER_API = AppConfig.BHX.concat('/jackets/resetPartNumber/jacketGroup/');

  // Manage-Units API
  public static SET_UNITS = AppConfig.BHX.concat('/quotations/measureTemp/');

  // Manage-Units API
  public static GET_UNITS_JACKET_GROUP = AppConfig.BHX.concat('/quotations/measureTempByJacketGroupId/');

  // Get-Units API by jacket
  public static GET_UNITS_JACKET = AppConfig.BHX.concat('/quotations/measureTempByJacketId/');

  // Get-Units By Quote
  public static GET_UNITS_QUOTE = AppConfig.BHX.concat('/quotations/measureTempByQuotationId/');

  // Get-Units By Quote
  public static GET_ENTRY_METHOD_QUOTE = AppConfig.BHX.concat('/quotations/getEntryMethodByQuotation/');

  // Design-Eng API
  // Cover Page
  public static GET_DESIGN_QUOTATIONS = AppConfig.BHX.concat('/quotations/design');
  public static GET_COVER_PAGE_MASTER_DATA = AppConfig.BHX.concat('/coverPages/masterData');
  public static SAVE_COVER_PAGE = AppConfig.BHX.concat('/coverPages');

  // Jacket List
  public static GET_JACKETS_BY_QUOTATIONID = AppConfig.BHX.concat('/jackets/quotationId/');
  public static SAVE_PATTERN_DESIGN = AppConfig.BHX.concat('/patternDesigns');
  public static UPDATE_VIETNAM_CONVERSION = AppConfig.BHX.concat('/undo/vietnamConversions/');
  public static UPDATE_CONVERSION_REVIEW = AppConfig.BHX.concat('/undo/conversionReviews/');

  // Cost Report
  public static COST_REPORT = AppConfig.BHX.concat('/jackets/costDetails/revision/');
  public static DOWNLOAD_CR_EXCEL_API = AppConfig.BHX.concat('/jackets/costDetails/download/');

  // Gold Report
  public static COMPLETED_QUOTE = AppConfig.BHX.concat('/soDetails/');
  public static GENERATE_GOLD_REPORT = AppConfig.BHX.concat('/goldReport/');
  public static RE_GENERATE_GOLD_REPORT = AppConfig.BHX.concat('/reset/goldReport/');
  public static DOWNLOAD_GR_EXCEL_API = AppConfig.BHX.concat('/goldReport/downloadExcel/');

  // ECR management
  public static ECR_LOGS = AppConfig.BHX.concat('/v1/ecrs/search'); // used to Get/ filter ECRs
  public static ECR_STATUSES = AppConfig.BHX.concat('/v1/ecr-statuses');
  public static ECR_DETAILS = AppConfig.BHX.concat('/v1/ecrs/');
  public static GET_DEPARTMENTS = AppConfig.BHX.concat('/v1/departments');
  public static GENERATE_ECR = AppConfig.BHX.concat('/v1/ecrs');
  public static DOWNLOAD_ECR_ATTACHMENT = AppConfig.BHX.concat('/v1/ecr-attachments/download/');
  public static DELETE_ECR_ATTACHMENT = AppConfig.BHX.concat('/v1/ecr-attachments/');
  public static ADD_ECR_ATTACHMENT = AppConfig.BHX.concat('/v1/uploadEcrDocs');
  public static ECR_PARTNUMBER = AppConfig.BHX.concat('/v1/ecr-part-numbers/');
  public static ADD_ECR_PARTNUMBER = AppConfig.BHX.concat('/v1/ecr-part-numbers');
  public static PARTNUMBER_EXIST_IN_EPICORE = AppConfig.EPICOR_API.concat('/exists/partNumber/');
  public static SYNC_PARTNUMBER_TO_EPICORE = AppConfig.BHX.concat('/v1/ecr-part-numbers/sync/');
  public static CHECK_PART_NUMBER_EXIST = AppConfig.BHX.concat('/v1/check-ecr-part-numbers');
  public static CHECK_ECR_PART_NUMBER_SYNC_STATUS = AppConfig.BHX.concat('/v1/ecr-part-number/dmtLogs/message/partNumber/');

  //eco line revision master
  public static ECO_LINE_REVISION_MASTER = AppConfig.BHX.concat('/eco-line-revision-masters');
  public static DELETE_ECO_LINE_REVISION_MASTER = AppConfig.BHX.concat('/eco-line-revision-masters/');

  // ECO Plan Management
  public static GET_ECO = AppConfig.BHX.concat('/v1/ecos/ecr/');
  public static GET_PQP_DETAILS = AppConfig.BHX.concat('/v1/eco-pqps/eco/');
  public static GET_PQP_LIST = AppConfig.BHX.concat('/v1/pqpFamilies');
  public static ECO_OPERATIONS = AppConfig.BHX.concat('/v1/ecos');
  public static PQP_OPERATIONS = AppConfig.BHX.concat('/v1/eco-pqps');
  public static GET_PQP_BY_ECO_ID = AppConfig.BHX.concat('/v1/eco-pqps/eco/');
  public static GET_FINAL_REVIEW_BY_ECO_ID = AppConfig.BHX.concat('/v1/eco-final-reviews/eco/');
  public static ECO_FINAL_REVIEW = AppConfig.BHX.concat('/v1/eco-final-reviews');
  public static GET_WORK_FLOW_MASTER_DATA = AppConfig.BHX.concat('/v1/workflows');
  public static GET_WORK_FLOW_BY_ECO_ID = AppConfig.BHX.concat('/v1/eco-workflows/eco/');
  public static ECO_WORKFLOW = AppConfig.BHX.concat('/v1/eco-workflows');
  public static ADD_STAKE_HOLDER = AppConfig.BHX.concat('/v1/eco-stakeholders');
  public static REMOVE_STAKE_HOLDER = AppConfig.BHX.concat('/v1/eco-stakeholders/');
  // ECO design activity
  public static GET_DESIGN_ACTIVITY_MASTER_DATA = AppConfig.BHX.concat('/v1/design-activities');
  public static GET_DESIGN_ACTIVITY_BY_ECO_ID = AppConfig.BHX.concat('/v1/eco-design-activities/eco/');
  public static ECO_DESIGN_ACTIVITY = AppConfig.BHX.concat('/v1/eco-design-activities');
  // ECO Costing
  public static GET_ECO_COSTING = AppConfig.BHX.concat('/v1/eco-costings/ecr/');
  public static REGENERATE_ECO_COSTING = AppConfig.BHX.concat('/v1/eco-costings/refresh/');

  // Level one review
  public static LEVEL_ONE_REVIEW_SAVE = AppConfig.BHX.concat('/levelOneReviews');
  public static LEVEL_ONE_REVIEW_UPDATE = AppConfig.BHX.concat('/undo/levelOneReviews/');

  public static SIMULATION_SAVE = AppConfig.BHX.concat('/simulations');
  public static SIMULATION_UPDATE = AppConfig.BHX.concat('/undo/simulations/');

  // Element BOM
  public static ELEMENT_BOM_SAVE = AppConfig.BHX.concat('/elementBoms');
  public static ELEMENT_BOM_UPDATE = AppConfig.BHX.concat('/undo/elementBoms/');

  // Final Review
  public static FINAL_REVIEW_SAVE = AppConfig.BHX.concat('/finalReviews');
  public static FINAL_REVIEW_UPDATE = AppConfig.BHX.concat('/undo/finalReviews/');

  // Vietnam Conversion
  public static VIETNAM_CONVERSION_SAVE = AppConfig.BHX.concat('/vietnamConversions');

  // Conversion Review
  public static CONVERSION_REVIEW_SAVE = AppConfig.BHX.concat('/conversionReviews');

  // design side quotation status count
  public static DESIGN_STATUS_COUNT = AppConfig.BHX.concat('/quotations/status/design/count');

  // Solidworks crate folder and file and titleblock
  public static CreateFolder = environment.SLD_API_URL.concat('CreateTemplate/PostCreateFolder');
  public static UpdateTitleBlock = environment.SLD_API_URL.concat('TitleBlock/PostTitleBlock');
  public static SAVE_PATH = AppConfig.BHX.concat('/jackets/partNumber/filePath/');
  public static GET_TITLE_BLOCK_BY_JACKETID = AppConfig.BHX.concat('/titleBlocks/jackets/');
  public static UPDATE_TITLE_BLOCK = AppConfig.BHX.concat('/titleBlocks');
  public static RefreshTitleBlock = AppConfig.BHX.concat('/titleBlocks/jackets/reset/');
  public static OPEN_FILE = environment.SLD_API_URL.concat('CreateTemplate/OpenFile');
  public static OPEN_FILE_LOCATION = environment.SLD_API_URL.concat('FolderManager/OpenDirectory');
  public static DRAW_JACKET_DESIGN = environment.SLD_API_URL.concat('SolidWorkSketch/DrawSketch');
  public static RESET_PATH = AppConfig.BHX.concat('/jackets/reset-file-path');
  // SolidWorks Read Assembly to get HTRMAN number, revision and partnumber for Gold Report generation using .net service
  public static READ_SLDWRK_ASSEMBLY_DOCUMENT = environment.SLD_API_URL.concat('GoldReport/PostGetSoNumber/');
  // Wire Selector API
  public static GET_BASIC_INFO = AppConfig.BHX.concat('/wire-selector-basicinfo/');
  public static SEARCH_WIRE = AppConfig.BHX.concat('/calculate-wire-selector');
  public static GETAMPS = AppConfig.BHX.concat('/wire-selector/amps/');

  // Historical Dropdown
  public static GET_DESIGN_TOP_SO = AppConfig.BHX.concat('/audit-counts/dashboard/');

  // master data in plugging information
  public static GET_PLUGIN_MASTER_DATA = AppConfig.BHX.concat('/pluginMasterData');

  // save element selction
  public static SAVE_JACKET_ELEMENT = AppConfig.BHX.concat('/elements');

  // bom-editor
  public static GET_ALL_MMATERIAL_BY_GROUP_ID_AND_PRODUCT_TYPE = AppConfig.BHX.concat('/bhxMaterials/product-type/group/'); // takes {productType}/{id}/{type}
  public static GET_PRODUCT_TYPE_BY_JACKET_ID = AppConfig.BHX.concat('/bhxMaterials/product-type/jacket-id/');
  public static SAVE_OPERATION = AppConfig.BHX.concat('/operations');
  public static SAVE_ALL_OPERATION = AppConfig.BHX.concat('/operations/updateAll');
  public static SAVE_ALL_FACING = AppConfig.BHX.concat('/facing-liner-closures/updateAll');
  public static SAVE_ALL_ELEMENT = AppConfig.BHX.concat('/element-sensors/updateAll');
  public static SAVE_ALL_WIRES = AppConfig.BHX.concat('/wire-pluggings/updateAll');
  public static SAVE_ALL_LABELS = AppConfig.BHX.concat('/labels/updateAll');
  public static SAVE_LABELS = AppConfig.BHX.concat('/labels');
  public static SVAE_ELEMENT_AND_SENSORS = AppConfig.BHX.concat('/element-sensors');
  public static SAVE_FACING_LINER_CLOSURES = AppConfig.BHX.concat('/facing-liner-closures');
  public static SAVE_WIRE_PLUGGINGS = AppConfig.BHX.concat('/wire-pluggings');
  public static GET_ALL_BOM_DATA = AppConfig.BHX.concat('/bomMaterials/jacket/');
  public static RESET_ALL_BOM_DATA = AppConfig.BHX.concat('/bomMaterials/reset/jacket/');
  public static RESET_SECTION = AppConfig.BHX.concat('/bomMaterials/reset/section/jacket/');
  public static DELETE_OPERTION = AppConfig.BHX.concat('/operations/');
  public static DELETE_OPERATION_ECR = AppConfig.BHX.concat('/v1/ecrs/');
  public static DELETE_LABELS = AppConfig.BHX.concat('/labels/');
  public static DELETE_ELEMENT_AND_SENSORS = AppConfig.BHX.concat('/element-sensors');

  public static DELETE_ELEMENT = AppConfig.BHX.concat('/delete/element');

  public static DELETE_SENSORS = AppConfig.BHX.concat('/delete/sensor');

  public static DELETE_FACING_LINER_CLOSURES = AppConfig.BHX.concat('/facing-liner-closures');
  public static DELETE_WIRE_PLUGGINGS = AppConfig.BHX.concat('/wire-pluggings');
  public static SYNC_BOM_TO_EPICOR = AppConfig.BHX.concat('/dmtLogs/');
  public static CHECK_SYNC_BOM_STATUS = AppConfig.BHX.concat('/dmtLogs/status/');
  public static GET_DMT_RESPONSE = AppConfig.BHX.concat('/dmtLogs/response/');
  public static RETRY_SYNC_TO_EPICORE = AppConfig.BHX.concat('/recreate/DMT/');
  public static PART_JACKET_API = AppConfig.BHX.concat('/parts/jacket/');
  public static PART_API = AppConfig.BHX.concat('/parts');
  public static PART_RESET_JACKET_API = AppConfig.BHX.concat('/parts/reset/jacket/');
  public static CHECK_LAST_SYNC_STATUS_BY_JACKET_ID = AppConfig.BHX.concat('/dmtLogs/message/');
  public static CHECK_IF_MULTIPLE_ELEMENT_SENSOR_RECORDS = AppConfig.BHX.concat('/checkMultiple/element-sensors');
  public static CHECK_IF_MULTIPLE_ELEMENT_SENSOR_RECORDS_DELETE = AppConfig.BHX.concat('/checkToDeleteMultiple/element-sensors');

  public static CHECK_IF_MULTIPLE_ELEMENT_RECORDS_DELETE = AppConfig.BHX.concat('/checkToDeleteMultiple/element');
  public static CHECK_IF_MULTIPLE_SENSORS_RECORDS_DELETE = AppConfig.BHX.concat('/checkToDeleteMultiple/sensors');

  public static CHECK_IF_MULTIPLE_FACING_LINER_RECORDS = AppConfig.BHX.concat('/checkMultiple/facing-liner-closures');
  public static CHECK_IF_MULTIPLE_FACING_LINER_RECORDS_DELETE = AppConfig.BHX.concat('/checkToDeleteMultiple/facing-liner-closures');
  public static CHECK_IF_MULTIPLE_WIRE_PLUGGINGS_RECORDS = AppConfig.BHX.concat('/checkMultiple/wire-pluggings');
  public static CHECK_IF_MULTIPLE_WIRE_PLUGGINGS_RECORDS_DELETE = AppConfig.BHX.concat('/checkToDeleteMultiple/wire-pluggings');
  public static CHECK_IF_MULTIPLE_LABELS_RECORDS = AppConfig.BHX.concat('/checkMultiple/labels');

  // new revision
  public static NEW_REVISON_UPDATE_DIR = environment.SLD_API_URL.concat('CreateTemplate/UpdateDirectoryForNewRevision');
  public static UPDATE_REVISION = AppConfig.BHX.concat('/jackets/design/updateRevision');

  // lap clac
  public static LAP_CALC = AppConfig.BHX.concat('/lapCalc');
  public static SET_INITIAL_NAME = AppConfig.UAA.concat('/users/initialName');

  // user management
  public static FILTER_USERS = AppConfig.UAA.concat('/filterUsers');

  //Poll Epicor
  public static POLL_EPICOR = AppConfig.EPICOR_API.concat('/final-design-review/');

  // Plugging Info LeadTypeMaster
  public static LEAD_TYPE_MASTER = AppConfig.BHX.concat('/lead-types');

  // Master data APIs
  public static STATUS_LIST_PAGEABLE = AppConfig.BHX.concat('/quotationStatuses/filter');
  public static GET_ALL_ACCESSORIES_PAGEABLE = AppConfig.BHX.concat('/accessories/filter');
  public static GET_MATERIAL_PAGEABLE = AppConfig.BHX.concat('/materials/filter');
  public static GET_MATERIAL_PROPERTIES_PAGEABLE = AppConfig.BHX.concat('/materialProperties/filter');
  public static GET_CLOSURE_MATERIAL_PAGEABLE = AppConfig.BHX.concat('/closureMaterials/filter');
  public static GET_CURRENCY_MATERIAL_PAGEABLE = AppConfig.BHX.concat('/currencies/filter');
  public static GET_PLUG_PAGEABLE = AppConfig.BHX.concat('/plugs/filter');
  public static GET_THERMOSATS_PAGEABLE = AppConfig.BHX.concat('/thermostatLists/filter');
  public static GET_ACCESSORY_CONTROLLER_PAGEABLE = AppConfig.BHX.concat('/accessoryControllers/filter');
  public static GET_FEATURES_PAGEABLE = AppConfig.BHX.concat('/features/filter');
  public static GET_PQP_FAMILY_PAGEABLE = AppConfig.BHX.concat('/v1/pqpFamilies/filter');
  public static GET_POWER_CORD_CONNECTOR_PAGEABLE = AppConfig.BHX.concat('/power-cord-connectors/filter');
  public static POWER_CORD_CONNECTOR = AppConfig.BHX.concat('/power-cord-connectors');
  public static SLEEVING_TYPES_PAGEABLE = AppConfig.BHX.concat('/sleevingTypes/filter');
  public static SLEEVING_TYPES = AppConfig.BHX.concat('/sleevingTypes');
  public static STRAIN_RELIEFS_PAGEABLE = AppConfig.BHX.concat('/strainReliefs/filter');
  public static STRAIN_RELIEFS = AppConfig.BHX.concat('/strainReliefs');
  public static PLUG_LIGHTS_PAGEABLE = AppConfig.BHX.concat('/plugLights/filter');
  public static PLUG_LIGHTS = AppConfig.BHX.concat('/plugLights');
  public static GOLD_STANDARD_TAPE_WIDTH_PAGEABLE = AppConfig.BHX.concat('/gold-standard-tape-widths/filter');
  public static GOLD_STANDARD_TAPE_WIDTH = AppConfig.BHX.concat('/gold-standard-tape-widths');
  public static BHX_GOLD_WIRE_TAPE_PAGEABLE = AppConfig.BHX.concat('/bhx-gold-wire-tapes/filter');
  public static BHX_GOLD_WIRE_TAPE = AppConfig.BHX.concat('/bhx-gold-wire-tapes');
  public static ALLOYS_PAGEABLE = AppConfig.BHX.concat('/alloys/filter');
  public static ALLOYS = AppConfig.BHX.concat('/alloys');
  public static HEATING_TAPES_PAGEABLE = AppConfig.BHX.concat('/heating-tapes/filter');
  public static HEATING_TAPES = AppConfig.BHX.concat('/heating-tapes');
  public static HEATING_TAPES_PARTNUMBER_VALIDATER = AppConfig.BHX.concat('/heating-tapes/exists/');
  public static HEATING_TAPES_PARTNUMBER_GENERATOR = AppConfig.BHX.concat('/heating-tapes/generate/');
  public static DEPARTMENTS_PAGEABLE = AppConfig.BHX.concat('/v1/departments/filter');
  public static DEPARTMENTS = AppConfig.BHX.concat('/v1/departments');
  public static ECR_STATUSES_PAGEABLE = AppConfig.BHX.concat('/v1/ecr-statuses/filter');
  public static BHX_MATERIAL_PAGEABLE = AppConfig.BHX.concat('/bhxMaterials/filter');
  public static BHX_MATERIAL = AppConfig.BHX.concat('/bhxMaterials');
  public static SENSOR_CONNECTORS_PAGEABLE = AppConfig.BHX.concat('/sensor-connectors/filter');
  public static SENSOR_CONNECTORS = AppConfig.BHX.concat('/sensor-connectors');
  public static SENSOR_TYPES_PAGEABLE = AppConfig.BHX.concat('/sensor-types/filter');
  public static SENSOR_TYPES = AppConfig.BHX.concat('/sensor-types');
  public static POWER_CORD_MATERIALS_PAGEABLE = AppConfig.BHX.concat('/power-cord-materials/filter');
  public static POWER_CORD_MATERIALS = AppConfig.BHX.concat('/power-cord-materials');
  public static POWER_CORD_VOLTAGES_PAGEABLE = AppConfig.BHX.concat('/power-cord-voltages/filter');
  public static POWER_CORD_VOLTAGES = AppConfig.BHX.concat('/power-cord-voltages');
  public static POWER_CORD_AMPS_PAGEABLE = AppConfig.BHX.concat('/power-cord-amps/filter');
  public static POWER_CORD_AMPS = AppConfig.BHX.concat('/power-cord-amps');
  public static POWER_CORD_OPTIONS_PAGEABLE = AppConfig.BHX.concat('/power-cord-options/filter');
  public static POWER_CORD_OPTIONS = AppConfig.BHX.concat('/power-cord-options');
  public static SENSOR_CONTROL_TYPES_PAGEABLE = AppConfig.BHX.concat('/sensor-control-types/filter');
  public static SENSOR_CONTROL_TYPES = AppConfig.BHX.concat('/sensor-control-types');
  public static WARPS_PAGEABLE = AppConfig.BHX.concat('/warps/filter');
  public static WARPS = AppConfig.BHX.concat('/warps');
  public static EST_ENG_REL_DATE_PAGEABLE = AppConfig.BHX.concat('/design-eng-rel-date/filter');
  public static EST_ENG_REL_DATE = AppConfig.BHX.concat('/design-eng-rel-date');
  public static UPDATE_MULTIPLE_JACKETS = AppConfig.BHX.concat('/jackets/updateAll/selectedFilePath');
  public static EXPRESSION_VALIDATOR = AppConfig.BHX.concat('/expression/validate?expression=');
  public static THERMOSTAT_TYPES_PAGEABLE = AppConfig.BHX.concat('/thermostat-types/filter');
  public static LABOR_MASTER = AppConfig.BHX.concat('/labor-masters');
  public static THERMOSTAT_TYPES = AppConfig.BHX.concat('/thermostat-types');
  public static THERMOSTAT_INSTALLATION_METHOD = AppConfig.BHX.concat('/installation-methods');
  public static PRODUCT_TYPES_PAGEABLE = AppConfig.BHX.concat('/product-types/filter');
  public static PRODUCT_TYPES = AppConfig.BHX.concat('/product-types');
  public static PRODUCT_GROUP_PAGEABLE = AppConfig.BHX.concat('/product-groups/filter');
  public static PRODUCT_GROUP = AppConfig.BHX.concat('/product-groups');
  public static LEAD_TYPES_PAGEABLE = AppConfig.BHX.concat('/leadTypes/filter');
  public static LEAD_TYPES = AppConfig.BHX.concat('/lead-types');
  public static MANUFACTURED_IN = AppConfig.BHX.concat('/quotations/send-to-design/configurations');
  public static SOLID_WORKS_BLOCK = AppConfig.BHX.concat('/solid-work-block-masters/');

  // tree add update delete folder api
  public static enumPathURL = environment.SLD_API_URL.concat('Explorer/EnumChildFolders');
  public static addFolderPathURL = environment.SLD_API_URL.concat('Explorer/CreateChildFolder');
  public static deletedFolderPathURL = environment.SLD_API_URL.concat('Explorer/DeleteChildFolder');
  public static renameFolderPathURL = environment.SLD_API_URL.concat('Explorer/RenameChildFolder');
  // copy BOM:: takes {sourceJacketId}, {destinationJacketId} and {type}
  public static COPY_BOM_TO_DIFF_JACKET = AppConfig.BHX.concat('/bomMaterials/duplicate');
  public static HIGHLIGHT_TITLE_BLOCK = AppConfig.BHX.concat('/title-block-highlighters');

  // final review screen APIs
  public static GET_FINAL_REVIEW_DMT_LOGS = AppConfig.BHX.concat('/final-design-review/dmt-response-log/'); // gets the final review DMT logs by jacketId and type
  public static GET_FINAL_REVIEW_LAST_SYNC_STATUS = AppConfig.BHX.concat('/final-design-review-dmt-logs/message/'); // gets the final review last sync status message.
  public static FINAL_DESIGN_REVIEW = AppConfig.EPICOR_API.concat('/final-design-review');
  public static FINAL_DESIGN_REVIEW_POST = AppConfig.BHX.concat('/final-design-review');
  public static GET_ALL_LABELS_COMMON_FIELDS = AppConfig.BHX.concat('/final-design-review/label-common-data/');
  public static GET_ALL_MATERIAL_LIST = AppConfig.BHX.concat('/final-design-review-by-product-type/materials/'); // takes {productType}/{type}

  // BOM Syncing to Epicore for both the side USA & VN
  public static SYNC_BOM_TO_EPICOR_V1 = AppConfig.BHX.concat('/sync-to-epicor'); // sync to epicor for both USA and VN
  public static CHECK_IF_BOM_EXIST = AppConfig.EPICOR_API.concat('/final-design-review/epicor/bom/exists');
  public static GET_DMT_RESPONSE_V1 = AppConfig.BHX.concat('/v1/dmtLogs/response/'); // takes jacketId and Type[USA/ VN]

  // Quotation Tracking Dashboard
  public static SO_IN_DESIGN_COUNT_SUMMARY = AppConfig.BHX.concat('/so-in-design/count/summary');
  public static SO_IN_DESIGN_DETAILED = AppConfig.BHX.concat('/so-in-design/count/detailed');
  public static QUOTES_COMPLETED = AppConfig.BHX.concat('/quote-completed/product-types/count');
  public static QUOTES_STATUS_COUNT_BY_DATE = AppConfig.BHX.concat('/quotation/status/count');
  public static QUOTES_COMPLETED_DETAILED = AppConfig.BHX.concat('/quote-completed/product-types/');
  public static APP_ENGG_QUOTE_TRACKER_DASHBOARD = AppConfig.BHX.concat('/app-quote-tracker/quotation'); // accepts filterDTO, Pageable
  public static DESIGN_ENGG_QUOTE_TRACKER_DASHBOARD = AppConfig.BHX.concat('/design-quote-tracker/quotation'); // accepts filterDTO, Pageable
  public static ACCOUNT_MANAGERS = AppConfig.BHX.concat('/account-managers');
  public static UPDATE_QUOTE_TRACKER_COLUMNS = AppConfig.BHX.concat('/app-quote-tracker/updateColumnValues');
  public static DESIGN_ROW_HIGHLIGHTING = AppConfig.BHX.concat('/design-quote-tracker/row/');
  public static APP_ROW_HIGHLIGHTING = AppConfig.BHX.concat('/app-quote-tracker/row/');
  public static DESIGN_COLUMN_HIGHLIGHTING = AppConfig.BHX.concat('/design-quote-tracker/column/');
  public static APP_COLUMN_HIGHLIGHTING = AppConfig.BHX.concat('/app-quote-tracker/column/');
  public static QUOTE_DEFAULTS = AppConfig.BHX.concat('/quotations/default-values/'); // excepts {QuoteId}
  // design engineering QT editable fields
  public static GET_ALL_DESIGNERS = AppConfig.UAA.concat('/users/designers'); // returns all the available designers users
  public static DESIGN_QUOTE_DEFAULTS = AppConfig.BHX.concat('/quotations/'); // excepts { quotationId }
  public static SAVE_UPDATE_DESIGN_QUOTE = AppConfig.BHX.concat('/quotations/design-workflow'); // excepts { quotationId }

  // Export To Excel
  public static EXPORT_TO_EXCEL = AppConfig.BHX.concat('/app-quote-tracker/quotation/excel');
  public static EXPORT_TO_EXCEL_DESIGN_QUOTE = AppConfig.BHX.concat('/design-quote-tracker/quotation/excel');

  // submitting RFQ Form
  public static RFQ_FORM = AppConfig.BHX.concat('/quotations/rfq');
  public static SO_FORM = AppConfig.BHX.concat('/quotations/sale-order-form');

  // Android API
  public static ANDROID_DATA_IMPORT = AppConfig.BHX.concat('/execute-scheduler');

  public static GET_EPICOR_PARTS = AppConfig.EPICOR_API.concat('/get-epicor-parts');
}
