export class Status {
  constructor(
    public ADDED: string = 'Added',
    public MODIFIED: string = 'Modified',
    public DELETED: string = 'Deleted',
    public UNCHANGED: string = 'Unchanged'
  ) { }
}
enum Statuses {
  ADDED = 'Added',
  MODIFIED = 'Modified',
  DELETED = 'Deleted',
  UNCHANGED = 'Unchanged'
}

export class JacketDTO {
  public jacketId?: number;
  public partNum?: string;
  public revision?: string;
  public description?: string;
  public revisionName?: string;
  public listPrice?: number;
  public netPrice?: string;
  public unitPrice?: string;
  public type?: string;
  public group?: string;
  public className?: string;
  public classID?: string;
  public typeCode?: string;
  public partDescription?: string;
  public onHold?: boolean;
  public inActive?: boolean;
  public internalCrossReference?: string;
  public status?: string = Statuses.UNCHANGED;
}

export class JacketDtoJacketRefernce {
  public jacketId?: number;
  public partNum?: string;
  public revision?: string;
  public description?: string;
  public listPrice?: number;
  public netPrice?: string;
  public type?: string;
  public group?: string;
  public className?: string;
  public classID?: string;
  public typeCode?: string;
  public partDescription?: string;
  public onHold?: boolean;
  public inActive?: boolean;
  public internalCrossReference?: string;
  public status?: string = Statuses.UNCHANGED;
}

export class Label {
  public number?: number;
  public format?: string;
  public volts?: number;
  public watts?: number;
  public amps?: number;
  public phase?: string;
  public size?: string;
  public width?: number;
  public length?: number;
  public csv?: number;
  public csa?: number;
  public mhlv?: number;
  public mhla?: number;
  public tempRange?: string;
  public lowT?: number;
  public hight?: number;
  public modelNumber?: string;
  public open1?: string;
  public open2?: string;
  public open3?: string;
  public partNumber?: string;
  public type?: string;
  public status?: string = Statuses.UNCHANGED;
}

export class OperationDTO {
  public sequence?: number;
  public operation?: string;
  public opNumber?: number;
  public setupHours?: number;
  public prodHours?: number;
  public status?: string = Statuses.UNCHANGED;
}

export class MaterialDTO {
  public sequence?: number;
  public partNumber?: string;
  public description?: string;
  public quantity?: number;
  public relOp?: string;
  public uom?: string;
  public status?: string = Statuses.UNCHANGED;
}

export class LabelDTO {
  public id?: number;
  public number?: number;
  public format?: string;
  public volts?: number;
  public watts?: number;
  public amps?: number;
  public phase?: string;
  public size?: string;
  public width?: number;
  public length?: number;
  public csv?: number;
  public csa?: number;
  public mhlv?: number;
  public mhla?: number;
  public tempRange?: string;
  public lowT?: number;
  public highT?: number;
  public modelNumber?: string;
  public open1?: string;
  public open2?: string;
  public open3?: string;
  public partNumber?: string;
  public type?: string;
  public status?: string = Statuses.UNCHANGED;
}

export class FinalDesignReviewDTOOLD {
  public sync = false;
  public company?: string;
  public type?: string;
  public salesOrderNumber?: string;
  public quotationNumber?: string;
  public jacket: JacketDTO = new JacketDTO();
  public materials: Array<MaterialDTO> = [];
  public operations: Array<OperationDTO> = [];
  public labels: Array<LabelDTO> = [];
}

export class FinalDesignReviewJacketRefernce {
  public sync = false;
  public company?: string;
  public type?: string;
  public salesOrderNumber?: string;
  public quotationNumber?: string;
  public jacket: JacketDtoJacketRefernce = new JacketDtoJacketRefernce();
  public materials: Array<MaterialDTO> = [];
  public operations: Array<OperationDTO> = [];
  public labels: Array<LabelDTO> = [];
}

export class FinalDesignReviewDTO {
  public usaFinalDesignReviewDTO: FinalDesignReviewDTOOLD = new FinalDesignReviewDTOOLD();
  public vietnamFinalDesignReviewDTO: FinalDesignReviewDTOOLD = new FinalDesignReviewDTOOLD();
  public costaRicaFinalDesignReviewDTO: FinalDesignReviewDTOOLD = new FinalDesignReviewDTOOLD();
}

export class Part {
  constructor(
    public id?: number,
    public type?: string,
    public revisionName?: string,
    public className?: string,
    public jacketPartNumber?: string,
    public description?: string,
    public internalCrossRef?: string,
    public listPrice?: number,
    public netPrice?: string,
    public jacketId?: number
  ) { }
}

export class FinalDesignReviewDMTLogsDTO {
  public jacketId?: number;
  public userId?: number;
  public executedOn?: string;
  public status?: string;
  public epicorType?: string;
}
