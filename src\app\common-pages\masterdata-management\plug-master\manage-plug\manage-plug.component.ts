import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { PlugMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogConfig, MatDialog } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { AddAttachmentComponent } from 'src/app/design-pages/ecr-management/add-attachment/add-attachment.component';
import { Values } from 'src/app/shared/constants/values.constants';
import { Messages, SnakbarService } from 'src/app/shared';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'sfl-manage-plug',
  templateUrl: './manage-plug.component.html'
})
export class ManagePlugComponent implements OnI<PERSON>t, OnD<PERSON>roy {
  subscription = new Subscription();
  showLoader = false;
  plug: PlugMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  formData = new FormData();
  imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  jacketTypes: object = Values.JacketTypeConst;
  constructor(
    public dialogRef: MatDialogRef<ManagePlugComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) {
    this.plug = Object.assign({}, data);
    data.clothCe === 'true' || data.clothCe === true ? (this.plug.clothCe = true) : (this.plug.clothCe = false);
    data.clothUl === 'true' || data.clothUl === true ? (this.plug.clothUl = true) : (this.plug.clothUl = false);
  }

  ngOnInit() {
    this.plug = this.plug.id ? Object.assign({}, this.plug) : new PlugMaster();
    this.plug.id ? (this.title = 'Update Plug') : (this.title = 'Add Plug');
    this.plug.imageUrl ? (this.imageUrl = environment.IMAGES_URL + this.plug.imageUrl) : (this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE);
  }

  updatePlug() {
    this.showLoader = true;
    this.formData.append('plugDTO', JSON.stringify(this.plug));
    this.subscription.add(
      this.masterDataService.addPlug(this.formData).subscribe(
        () => {
          this.showLoader = false;
          this.dialogRef.close(true);
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  addAttachment() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-upload-plug-file-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      if (uploadedAttachment && uploadedAttachment.type.match(/image\/*/)) {
        const reader = new FileReader();
        reader.readAsDataURL(uploadedAttachment);
        reader.onload = _event => {
          this.imageUrl = reader.result.toString();
          this.plug.imageUrl = reader.result.toString();
        };
        this.formData.append('file', uploadedAttachment, uploadedAttachment.name);
      } else if (uploadedAttachment) {
        this.snakbarService.error(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Only_Image_Type_Allowed);
      }
    });
  }

  removeAttachment() {
    this.formData.delete('file');
    this.plug.imageUrl = '';
    this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
