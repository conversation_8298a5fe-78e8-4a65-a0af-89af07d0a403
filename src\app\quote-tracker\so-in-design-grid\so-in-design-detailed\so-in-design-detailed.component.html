<!-- So In Design Detailed view Content for Grid -->
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayoutAlign="start center">
      <div>
        <mat-card-title>{{ soInDesignTitle }}</mat-card-title>
      </div>
      <div fxLayoutAlign="end" style="margin-left: auto;" >
        <div class="box-layout">
          <h4>Total Number of Design: {{ totalNoOfDesign }}</h4>
        </div>
        <div class="box-layout">
          <h4>Total Dollar Value: {{ totalDollarValue | currency: 'USD':'symbol':'1.2-2' }}</h4>
        </div>
      </div>
    </div>
    <!-- SO in Design Detailed Table View -->
    <div class="cust_table">
      <mat-table mat-table [dataSource]="soInDesignDetailedDataSource">
        <ng-container matColumnDef="classification">
          <mat-header-cell *matHeaderCellDef fxFlex="35"> Classification </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.classification }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="noOfDesign">
          <mat-header-cell *matHeaderCellDef fxFlex="25"> Number Of Design </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.noOfDesign }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="dollarValue">
          <mat-header-cell *matHeaderCellDef fxFlex="25"> Dollar Value($) </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.dollarValue | currency: 'USD':'symbol':'1.2-2' }} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="soInDesignDetailedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: soInDesignDetailedColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!soInDesignDetailedDataSource?.data?.length && !showLoader">No data found</div>
  </mat-card>
</div>

