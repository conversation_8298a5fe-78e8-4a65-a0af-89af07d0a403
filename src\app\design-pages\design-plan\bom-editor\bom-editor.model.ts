export class OperationsMasterData {
  constructor(
    public id?: number,
    public grouping?: number,
    public operationName?: string,
    public sequence?: number,
    public opNumber?: number,
    public prodHrs?: number,
    public setupHrs?: number,
    public blocked?: boolean,
    public ce?: boolean,
    public closure?: string,
    public connector?: string,
    public customer?: string,
    public description?: string,
    public elementType?: string,
    public greenLight?: string,
    public jacketType?: string,
    public layered?: string,
    public maxDiameter?: number,
    public maxLength?: number,
    public minDiameter?: number,
    public minJumpers?: number,
    public minLength?: number,
    public partNumber?: string,
    public plug?: string,
    public qty?: number,
    public relOpr?: number,
    public sensConn?: string,
    public sensorType?: string,
    public sleeving?: string,
    public thermostat?: boolean,
    public ul?: boolean,
    public uom?: string,
    public wireType?: string,
    public jacketId?: number,
    public type?: string,
    public productType?: string,
    public relOperation?: number,
    public siliconOpr?: number,
    public clothOpr?: number,
    public inseparableOpr?: number,
  ) { }
}

export class ElementSensors {
  constructor(
    public description?: string,
    public id?: number,
    public jacketId?: number,
    public partNumber?: string,
    public qty?: number,
    public relOperation?: number,
    public uom?: string,
    public type?: string,
    public oldPartNumber?: string,
    public oldQty?: number,
    public oldRelOperation?: number,
    public oldRelDescription?: string,
    public oldRelUOM?: string,
    public multiple?: boolean,
    public esIndex?: number,
    public sensorsIndex?: number
  ) { }
}

export class FacingLinerClosure {
  constructor(
    public description?: string,
    public id?: number,
    public jacketId?: number,
    public partNumber?: string,
    public qty?: number,
    public relOperation?: number,
    public uom?: string,
    public type?: string,
    public oldPartNumber?: string,
    public oldQty?: number,
    public oldRelOperation?: number,
    public multiple?: boolean,
    public flcIndex?: number,
    public facingIndex?: number,
    public partIncluded?: boolean
  ) { }
}

export class Label {
  constructor(
    public description?: string,
    public id?: number,
    public jacketId?: number,
    public partNumber?: string,
    public jacketPartNumber?: string,
    public qty?: number,
    public relOperation?: number,
    public uom?: string,
    public type?: string,
    public oldPartNumber?: string,
    public oldQty?: number,
    public oldRelOperation?: number,
    public multiple?: boolean,
    public lblIndex?: number,
    public labelDetailedEntryList = new Array<LabelDetailedEntry>(),
    public labelIndex?: number,
    public partIncluded?: boolean
  ) { }
}

export class LabelDetailedEntry {
  public id?: number;
  public labelPartNumber?: string;
  public partNumber?: number;
  public format?: string;
  public volts?: number;
  public watts?: number;
  public amps?: number;
  public phase?: string;
  public size?: string;
  public width?: number;
  public length?: number;
  public csv?: number;
  public csa?: number;
  public mhlv?: number;
  public mhla?: number;
  public tempRange?: string;
  public lowT?: number;
  public highT?: number;
  public modelNumber?: string;
  public open1?: string;
  public open2?: string;
  public open3?: string;
  public toSync?: boolean;
  public imageUrl?: boolean;
}

export class Operation {
  constructor(
    public id: number,
    public jacketId: number,
    public opNumber: number,
    public operationName: string,
    public prodHrs: number,
    public sequence: number,
    public setupHrs: number,
    public type?: string,
    public operationIndex?: number
  ) { }
}

export class WirePlugging {
  constructor(
    public description?: string,
    public id?: number,
    public jacketId?: number,
    public partNumber?: string,
    public qty?: number,
    public relOperation?: number,
    public uom?: string,
    public type?: string,
    public oldPartNumber?: string,
    public oldQty?: number,
    public oldRelOperation?: number,
    public multiple?: boolean,
    public wpIndex?: number,
    public wireIndex?: number,
    public partIncluded?: boolean
  ) { }
}

export class Part {
  constructor(
    public id?: number,
    public type?: string,
    public revisionName?: string,
    public className?: string,
    public jacketPartNumber?: string,
    public description?: string,
    public internalCrossRef?: string,
    public listPrice?: number,
    public netPrice?: number,
    public jacketId?: number,
    public descriptionUpdatedManually?: boolean,
    public listPriceUpdatedManually?: boolean,
    public netPriceUpdatedManually?: boolean
  ) { }
}

export class BOMData {
  constructor(
    public elementSensorsDTO?: ElementSensors[],
    public facingLinerClosureDTO?: FacingLinerClosure[],
    public labelDTO?: Label[],
    public operationDTO?: Operation[],
    public wirePluggingDTO?: WirePlugging[],
    public elementsOnlyDTO?: ElementOnlyDTO[],
    public sensorsOnlyDTO?: SensorsOnlyDTO[]
  ) { }
}

  export class SensorsOnlyDTO {
    constructor(
    public description?: string,
    public id?: number,
    public jacketId?: number,
    public partNumber?: string,
    public qty?: number,
    public relOperation?: number,
    public uom?: string,
    public type?: string,
    public oldPartNumber?: string,
    public oldQty?: number,
    public oldRelOperation?: number,
    public multiple?: boolean,
    public esIndex?: number,
    public sensorsIndex?: number,
    public partIncluded?: boolean
    ) {}
  }

  export class ElementOnlyDTO {
    constructor(
    public description?: string,
    public id?: number,
    public jacketId?: number,
    public partNumber?: string,
    public qty?: number,
    public relOperation?: number,
    public uom?: string,
    public type?: string,
    public oldPartNumber?: string,
    public oldQty?: number,
    public oldRelOperation?: number,
    public multiple?: boolean,
    public esIndex?: number,
    public elementIndex?: number,
    public partIncluded?: boolean
    ) {}
  }

  export class PartClassDisplayDTO {
    public id: number;
    public type: string;
  }

export class BomEpicor {
  constructor(public status?: string, public jacketId?: number, public epicorType?: string,public fileType?: string) { }
}
export class OldBOMValues {
  constructor(public partNumber?: string, public qty?: number, public relOperation?: number) { }
}

export class ConfirmSyncDto {
  public partNum: string;
  public rev: string;
  public jacketId: number;
  public quotationNumber: string;
  public soNumber: string;
}

export class JacketProductType {
  public jacketId: number;
  public productType: string;
}

export class DuplicateBomDTO {
  public sourceJacketId: number;
  public destinationJacketId: number;
  public type: string;
  public selectedSections: string;
}

export class CommonPartDTO{
  public partNumbers :string[];
  public company :string;
  public partString:string;
}
