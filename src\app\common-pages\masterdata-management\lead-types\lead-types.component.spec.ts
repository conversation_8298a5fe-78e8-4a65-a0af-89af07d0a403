import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { MasterDataModule } from '../masterdata-management.module';
import { LeadTypesComponent } from './lead-types.component';
import { RouterTestingModule } from '@angular/router/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

fdescribe('LeadTypesComponent', () => {
  let component: LeadTypesComponent;
  let fixture: ComponentFixture<LeadTypesComponent>;
  let de: DebugElement;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [MasterDataModule, RouterTestingModule, BrowserAnimationsModule],
      declarations: [LeadTypesComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LeadTypesComponent);
    component = fixture.componentInstance;
    de = fixture.debugElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have a title with `Lead Type`', () => {
    expect(component.pageTitle).toBe('Lead Type');
  });

  it('should have a title in `mat-card-title` tag', () => {
    expect(de.query(By.css('mat-card-title')).nativeElement.innerText).toBe('Lead Type');
  });
});
