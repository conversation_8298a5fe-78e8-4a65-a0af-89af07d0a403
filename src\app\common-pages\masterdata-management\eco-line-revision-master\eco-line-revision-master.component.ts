import { Component, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { EcoLineMaster } from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { ManageEcoLineRevisionMasterComponent } from './manage-eco-line-revision-master/manage-eco-line-revision-master.component';

@Component({
  selector: 'app-eco-line-revision-master',
  templateUrl: './eco-line-revision-master.component.html',
  styleUrls: ['./eco-line-revision-master.component.css']
})
export class EcoLineRevisionMasterComponent implements OnInit {

  pageTitle = "Eco Line Revision Master";
  subscription = new Subscription();
  ecoLineRevisionMasterDataColumns = DisplayColumns.Cols.EcoLineMasterDataColumns;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  ecoLineMasterDTO: EcoLineMaster[];
  dataSource = new MatTableDataSource<EcoLineMaster>();
  showLoader: boolean;
  
  constructor(private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService) { }

  ngOnInit() {
    this.getAllEcoLineRevisionMaster();
  }

  getAllEcoLineRevisionMaster() {
    this.subscription.add(
      this.masterDataService.getEcoLineMaster().subscribe((res: EcoLineMaster[]) => {
        this.ecoLineMasterDTO = res;
        this.dataSource.data = this.ecoLineMasterDTO;
       }
      )
    )
  }

  editEcoLineRevisionMaster(ecoLineRevisionMaster: EcoLineMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = ecoLineRevisionMaster;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-eco-line-material-master-model';
    const dialogRef = this.matDialog.open(ManageEcoLineRevisionMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          ecoLineRevisionMaster.id
            ? this.pageTitle + Messages.ECO_LINE_MASTER_MESSAGES.Updated_Success
            : this.pageTitle + Messages.ECO_LINE_MASTER_MESSAGES.Added_Success
        );
        this.getAllEcoLineRevisionMaster();
      }
    });
  }

  addEcoLineMaterial() {
    this.editEcoLineRevisionMaster(new EcoLineMaster());
  }

  async deleteEcoLineMaterial(id: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteEcoLineMaster(id).subscribe(
        res => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getAllEcoLineRevisionMaster();
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

}
