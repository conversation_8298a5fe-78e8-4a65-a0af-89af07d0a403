<form #colourSelectForm="ngForm" role="form">
  <h2 mat-dialog-title>Select Colour To Highlight Row
    <hr>
  </h2>
<div class="container-fluid">
  <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
    <mat-select placeholder="Select Row Colour" name ="colorSelect" [(ngModel)]="selectedRawColor" [ngModelOptions]="{standalone: true}">
      <mat-option *ngFor="let color of highlightedRowColors" [value]="color?.value" [ngStyle]="{'background': color?.value}">
        {{ color?.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>
</div>
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="end">
      <button mat-raised-button color="warn" type="submit" [disabled]="!selectedRawColor" (click)="selectRow(selectedRawColor)">Select Row Colour</button>
      <button mat-raised-button type="button" (click)="closeDialog(false)">Cancel</button>
    </div>
  </mat-dialog-actions>
</form>
