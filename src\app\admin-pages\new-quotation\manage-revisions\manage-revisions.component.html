<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
  class="sfl-global-spinner-loader"
  [mode]="mode"
  [color]="color"
  [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>Manage Revisions
  <hr>
</h2>
<div class="cust_table">
  <mat-table [dataSource]="dataSource">
    <ng-container matColumnDef="revision">
      <mat-header-cell *matHeaderCellDef fxFlex="15"> Revision </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="15">{{element.revisionName}}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="editby">
      <mat-header-cell *matHeaderCellDef fxFlex="20"> Last Edit By </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="20">{{element.createdByUser}}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="time">
      <mat-header-cell *matHeaderCellDef fxFlex="50"> Time Stamp </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="50">{{element.createdOn | date: 'MM-dd-yyyy, hh:mm a'}}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="active">
      <mat-header-cell *matHeaderCellDef fxFlex="20"> Is Active?</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="20">{{element.activeRevision === true ? 'Active' : ''}}</mat-cell>
    </ng-container>
    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
  </mat-table>
  <mat-paginator [pageSize]="10" [pageSizeOptions]="pageSize" showFirstLastButtons></mat-paginator>
</div>
<hr>
<mat-dialog-actions>
  <button mat-raised-button type="submit" color="warn" (click)="createRevision()" [disabled]="showLoader">New</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
