<div class="pointer less-peding cust_table print-page">
  <div *ngFor="let viewCCDCJacketGroup of viewCCDCJacketGroups; let i = index">
    <!-- customer info -->
    <div id="{{ 'custmerinfo' + i }}" fxFlex="100">
      <mat-card class="print-less-peding print-friendly">
        <mat-card-title fxLayout="row wrap" fxLayoutAlign="space-between" class="print-title">
          <div fxLayout="column wrap">Customer Information</div>
          <div fxLayout="column wrap">Jacket Group : {{ viewCCDCJacketGroup.name }}</div>
        </mat-card-title>
        <hr/>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" class="print-line-space">
          <div fxLayout="column wrap" class="mt-10" fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Quote Number</label><label class="display-box">{{ quotation?.quotationNumber }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Sales Order Number</label><label
              class="display-box">{{ quotation?.salesOrderNumber }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Customer Name</label><label
              class="display-box">{{ quotation?.customerDTO?.name }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Customer Code</label><label
              class="display-box">{{ quotation?.customerDTO?.code }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Eng. Cust Abrev</label><label
              class="display-box">{{ quotation?.customerDTO?.engCustAbrev }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Email Address</label><label
              class="display-box">{{ quotation?.customerDTO?.email }}</label>
            </div>
          </div>
          <div fxLayout="column wrap" class="mt-10" fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Contact</label><label class="display-box">{{ quotation?.customerDTO?.contact }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Phone Number</label><label
              class="display-box">{{ quotation?.customerDTO?.phoneNumber }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Fax Number</label><label
              class="display-box">{{ quotation?.customerDTO?.faxNumber }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Address 1</label><label
              class="display-box">{{ quotation?.customerDTO?.addressLine1 }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Address 2</label><label
              class="display-box">{{ quotation?.customerDTO?.addressLine2 }}</label>
            </div>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- work Info -->
    <div id="{{ 'workflowinfo' + i }}" fxFlex="100">
      <mat-card class="print-less-peding print-friendly">
        <mat-card-title class="print-title">
          <div fxLayout="column wrap"> Workflow Information</div>
        </mat-card-title>
        <hr/>
        <div class="print-line-space" fxLayout="row wrap" fxLayoutAlign="space-between">
          <div fxLayout="column wrap" class="mt-10" fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Approval Level</label><label
              class="display-box">{{ viewCCDCJacketGroup.ccdcWorkflow.approvalLevel }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Product Type</label><label
              class="display-box">{{ viewCCDCJacketGroup.ccdcWorkflow.productType }}&nbsp;</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Entry Date</label><label
              class="display-box">{{ viewCCDCJacketGroup.ccdcWorkflow.entryDate }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Ship Date</label><label
              class="display-box">{{ viewCCDCJacketGroup.ccdcWorkflow.shipDate }}</label>
            </div>
          </div>
          <div fxLayout="column wrap" class="mt-10" fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Approvel Format</label
              ><label class="display-box">{{ viewCCDCJacketGroup.ccdcWorkflow.approvalFormatList | approvalFormats }}&nbsp;</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Markings</label><label
              class="display-box">{{ viewCCDCJacketGroup.ccdcWorkflow.markingList | markings }}&nbsp;</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Project Name</label><label
              class="display-box">{{ viewCCDCJacketGroup.ccdcWorkflow.projectName }}&nbsp;</label>
            </div>
          </div>
        </div>
        <label class="lbl">Notes</label>
        <p class="print-line-space">
          {{ viewCCDCJacketGroup.ccdcWorkflow.notes }}
        </p>
      </mat-card>
    </div>

    <!-- application info -->
    <div id="{{ 'appinfo' + i }}" fxFlex="100">
      <mat-card fxLayout="column" class="print-less-peding print-friendly">

        <mat-card-title class="print-title">
          <div fxLayout="column wrap"> Application Information</div>
        </mat-card-title>

        <hr/>
        <div class="mt-10 print-line-space" fxLayout="row wrap" fxLayoutAlign="space-between">
          <div fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Jacket Type</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.jacketType }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Fit Type</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.fitTypeEnum }}</label
            >
            </div>
            <div class="lbl-view">
              <label class="lbl">Voltage</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.voltage }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Phase</label><label class="display-box">{{ viewCCDCJacketGroup.appInfo.phase }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Operating Temp</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.operatingTemp }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Operating Temp</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.operatingTemp }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Max Exposure Temp</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.maxExposureTemp }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Min Ambient Temp</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.minAmbientTemp }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Heat up From</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.heatupFrom }}</label
            >&nbsp; ({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Heat up To </label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.heatupTo }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">In </label><label class="display-box">{{ viewCCDCJacketGroup.appInfo.heatupIn }}</label
            >&nbsp;(Hours)
            </div>
            <div class="lbl-view">
              <label class="lbl">Pipe Thickness </label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.pipeThickness }}</label
            >&nbsp;({{ measurementUnit ? measurementUnit : '' }})
            </div>
          </div>
          <div fxLayout="column wrap" fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Content Motion</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.contentMotion }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Flowing Rate</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.contentMotionFlowingRate }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Pipe Material</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.materialName }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Specific Heat (BTU/lb)</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.appInfo.materialSpecificHeat ? viewCCDCJacketGroup.appInfo.materialSpecificHeat : viewCCDCJacketGroup.appInfo.otherMaterialHeat
              }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Density (lb/ft3)</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.appInfo.materialDensity ? viewCCDCJacketGroup.appInfo.materialDensity : viewCCDCJacketGroup.appInfo.otherMaterialDensity
              }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Pipe Contents</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.contentName }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Specific Heat (BTU/lb)</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.appInfo.contentSpecificHeat ? viewCCDCJacketGroup.appInfo.contentSpecificHeat : viewCCDCJacketGroup.appInfo.otherContentHeat
              }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Density (lb/ft3)</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.appInfo.contentDensity ? viewCCDCJacketGroup.appInfo.contentDensity : viewCCDCJacketGroup.appInfo.otherContentDensity
              }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Control Type</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.appInfo.controlType != 'Other' ? viewCCDCJacketGroup.appInfo.controlType : viewCCDCJacketGroup.appInfo.otherControlType
              }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Product Type</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.productType | productType }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Watt Density</label><label
              class="display-box">{{ viewCCDCJacketGroup.appInfo.wattDensity }}</label>
            </div>
          </div>
        </div>
        <div class="col" fxFlex="100">
          <label class="lbl">Notes</label>
          <p class="print-line-space">{{ viewCCDCJacketGroup.appInfo.notes }}</p>
        </div>
      </mat-card>
    </div>

    <!-- plugging info -->
    <div id="{{ 'plugginginfo' + i }}" fxFlex="100">
      <mat-card fxLayout="column" class="print-less-peding print-friendly">
        <mat-card-title fxLayout="row wrap" fxLayoutAlign="space-between" class="print-title">
          <div fxLayout="column wrap">Plugging Information</div>
          <div fxLayout="column wrap">Jacket Group : {{ viewCCDCJacketGroup.name }}</div>
        </mat-card-title>
        <hr/>
        <div class="print-line-space" fxLayout="column" fxLayoutAlign="space-between">
          <div class="mt-10 mb-10" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Sleeving Type</label>
              <label class="display-box">{{
                  viewCCDCJacketGroup.pluggingInformation?.sleevingTypeName != 'Other'
                    ? viewCCDCJacketGroup.pluggingInformation?.sleevingTypeName
                    : viewCCDCJacketGroup.pluggingInformation?.otherSleevingType
                }}</label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Strain Relief</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.pluggingInformation?.strainReliefName != 'Other'
                  ? viewCCDCJacketGroup.pluggingInformation?.strainReliefName
                  : viewCCDCJacketGroup.pluggingInformation?.otherStrainRelief
              }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Green Light</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.pluggingInformation?.greenLightName != 'Other'
                  ? viewCCDCJacketGroup.pluggingInformation?.greenLightName
                  : viewCCDCJacketGroup.pluggingInformation?.otherGreenLight
              }}</label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Red Light</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.pluggingInformation?.redLightName != 'Other'
                  ? viewCCDCJacketGroup.pluggingInformation?.redLightName
                  : viewCCDCJacketGroup.pluggingInformation?.otherRedLight
              }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Plug</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.pluggingInformation?.leadPlugDTO?.plugName != 'Other'
                  ? viewCCDCJacketGroup.pluggingInformation?.leadPlugDTO?.plugName
                  : 'Other'
              }}</label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Part number</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.pluggingInformation?.leadPlugDTO?.plugName != 'Other' ?
                  viewCCDCJacketGroup.pluggingInformation?.leadPlugDTO?.partNumber : viewCCDCJacketGroup.pluggingInformation?.otherPlugPartNumber
              }}</label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Connector</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.pluggingInformation?.jumperPlugDTO?.plugName != 'Other'
                  ? viewCCDCJacketGroup.pluggingInformation?.jumperPlugDTO?.plugName
                  : viewCCDCJacketGroup.pluggingInformation?.otherConnector
              }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Lead Length</label
              ><label
              class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.leadPlugDTO?.leadLength }} </label>&nbsp;({{
                measurementUnit ? measurementUnit : ''
              }})
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Jumper Length</label
              ><label
              class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.jumperPlugDTO?.jumperLength }} </label>&nbsp;({{
                measurementUnit ? measurementUnit : ''
              }})
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Jumper Qty</label><label
              class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.jumperPlugDTO?.quantity }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Lead Type</label>
              <label class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.leadTypeDTO?.leadName }} </label>
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Lead Part Number</label>
              <label class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.leadTypeDTO?.partNumber }} </label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Max Temp</label>
              <label
                class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.leadTypeDTO?.maxTemp }} </label>&nbsp;({{ tempUnit ? tempUnit : '' }}
              )
            </div>
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Cost/ft.</label>
              <label
                class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.leadTypeDTO?.costPerFoot }} </label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
              <label class="lbl">Max Volts</label>
              <label class="display-box">{{ viewCCDCJacketGroup.pluggingInformation?.leadTypeDTO?.maxVolts }} </label>
            </div>
          </div>
          <div class="col" fxFlex="100">
            <label class="lbl">Notes</label>
            <p class="print-line-space">{{ viewCCDCJacketGroup.pluggingInformation?.notes }}</p>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Material info  -->
    <div id="{{ 'materialinfo' + i }}" fxFlex="100">
      <mat-card fxLayout="column" class="print-less-peding print-friendly">

        <mat-card-title fxLayout="column wrap" fxLayoutAlign="space-between" class="print-title">Material Information
        </mat-card-title>

        <hr/>
        <div class="table-container mt-10 print-line-space" fxLayoutAlign="space-between">
          <table aria-describedby="Material Information">
            <thead>
            <tr>
              <th scope="col">Layer</th>
              <th scope="col">Material</th>
              <th scope="col">Part Number</th>
              <th scope="col">Max Temp ({{ tempUnit ? tempUnit : '' }})</th>
              <th scope="col">Cost/Ft</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let data of viewCCDCJacketGroup.materialDatas?.materialInfoDTOList">
              <td>{{ data?.layerName }}</td>
              <td>
                {{
                  data.material === 'Other'
                    ? data?.layerName === 'Facing'
                      ? data?.otherFacing
                      : data?.layerName === 'Insulation'
                        ? data?.otherInsulation
                        : data?.otherLiner
                    : data?.material
                }}
              </td>
              <td>
                {{
                  data?.otherFacingPartNumber
                    ? data?.otherFacingPartNumber
                    : data?.otherInsulationPartNumber
                      ? data?.otherInsulationPartNumber
                      : data?.otherLinerPartNumber
                        ? data?.otherLinerPartNumber
                        : data?.partNumber
                }}
              </td>
              <td>{{ tempUnit === '°F' ? data?.maxTempF : data?.maxTemp }}</td>
              <td>
                {{
                  data?.otherFacingCost
                    ? data?.otherFacingCost
                    : data?.otherInsulationCost
                      ? data?.otherInsulationCost
                      : data?.otherLinerCost
                        ? data?.otherLinerCost
                        : data?.costPerSq
                }}
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div fxFlex="100" class="col mt-10">
          <label class="lbl">Notes</label>
          <p class="print-line-space">{{ viewCCDCJacketGroup.materialDatas?.notes }}</p>
        </div>
      </mat-card>
    </div>

    <!-- Closure info -->
    <div id="{{ 'closureinfo' + i }}" fxFlex="100">
      <mat-card fxLayout="column" class="print-less-peding print-friendly">
        <div fxLayout="row wrap">
          <mat-card-title class="print-title">Closure Information</mat-card-title>
        </div>
        <hr/>
        <div fxFlex="100" class="mt-10" fxLayout="row wrap" fxLayoutAlign="space-between">
          <div fxLayout="column wrap" fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Closure</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.closureInfo?.closureMaterialName != 'Other' ? viewCCDCJacketGroup.closureInfo?.closureMaterialName : viewCCDCJacketGroup.closureInfo?.otherClosure
              }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Extended Flap</label
              ><label
              class="display-box">{{ viewCCDCJacketGroup.closureInfo?.extendedFlap == true | convertToYesNo }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Cat 5 Tunnel</label
              ><label
              class="display-box">{{ viewCCDCJacketGroup.closureInfo?.cat5Tunnel == true | convertToYesNo }}</label>
            </div>
          </div>
          <div fxLayout="column wrap" fxFlex.gt-lg="49.5" fxFlex.gt-md="49.5">
            <div class="lbl-view">
              <label class="lbl">Est Surface Temp</label><label
              class="display-box">{{ viewCCDCJacketGroup.closureInfo?.estSurfaceTemp }}</label
            >&nbsp;({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Max Temp</label><label
              class="display-box">{{ viewCCDCJacketGroup.closureMaterial?.maxTemp }}</label
            >&nbsp; ({{ tempUnit ? tempUnit : '' }})
            </div>
            <div class="lbl-view">
              <label class="lbl">Cost per foot</label
              ><label class="display-box">{{
                viewCCDCJacketGroup.closureInfo?.otherClosureCost ? viewCCDCJacketGroup.closureInfo?.otherClosureCost : viewCCDCJacketGroup.closureMaterial?.costPerSq
              }}</label>
            </div>
          </div>
          <div fxFlex="100" class="col mt-10">
            <label class="lbl">Notes</label>
            <p class="print-line-space">{{ viewCCDCJacketGroup.closureInfo?.notes }}</p>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Sensors info -->
    <div id="{{ 'sensorsinfo' + i }}" fxFlex="100">
      <mat-card fxLayout="column" class="print-less-peding print-friendly">

        <mat-card-title class="print-title">Sensor Information</mat-card-title>

        <hr/>
        <div class="table-container mt-10">
          <table aria-describedby="Sensor Information">
            <thead>
            <tr>
              <th scope="col">Type</th>
              <th scope="col">Location</th>
              <th scope="col">Connector</th>
              <th scope="col">Leads&nbsp;({{ measurementUnit ? measurementUnit : '' }})</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let data of viewCCDCJacketGroup.sensorInfoObjects?.sensorsInformationDTOList">
              <td>{{ data?.sensorType ? data?.sensorType?.id : data?.otherSensorType }}</td>
              <td>{{ data?.sensorLocation !== 'Other' ? data?.sensorLocation : data?.otherSensorLocation }}</td>
              <td>{{ data?.sensorConnector ? data?.sensorConnector?.id : data?.otherSensorConnector }}</td>
              <td>{{ data?.sensorLeadLength }}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div fxFlex="100" class="mt-10">
          <label class="lbl">Notes</label>
          <p class="print-line-space">
            {{ viewCCDCJacketGroup.sensorInfoObjects?.notes }}
          </p>
        </div>
      </mat-card>
    </div>

    <!-- Thermostats info -->
    <div id="{{ 'thermostatsinfo' + i }}" fxFlex="100">
      <mat-card fxLayout="column" class="print-less-peding print-friendly">
        <div fxLayout="row wrap">
          <mat-card-title class="print-title">Thermostat Information</mat-card-title>
        </div>
        <hr/>
        <div class="table-container mt-10">
          <table aria-describedby="Thermostat Information">
            <thead>
            <tr>
              <th scope="col">Type</th>
              <th scope="col">Installation Method</th>
              <th scope="col">Open Temp ({{ tempUnit ? tempUnit : '' }})</th>
              <th scope="col">Part Number</th>
              <th scope="col">Cost</th>
              <th scope="col">Manual Reset</th>
              <th scope="col">Open On Rise</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let data of viewCCDCJacketGroup.thermostatInfos?.thermostatInformationDTOList">
              <td>{{ data?.thermostatType?.id ? data?.thermostatType?.id : data?.otherThermostatType?.id }}</td>
              <td>{{ data?.installationMethodDTO?.methodName }}</td>
              <td>{{ tempUnit === '°F' ? data?.openTempF : data?.openTemp }}</td>
              <td>{{ data?.partNumber ? data?.partNumber : data?.otherThermostatPartNumber }}</td>
              <td>{{ data?.cost ? data?.cost : data?.otherThermostatCost }}</td>
              <td>{{ data?.manualReset | convertToYesNo }}</td>
              <td>{{ data?.openOnRise | convertToYesNo }}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div fxFlex="100" class="mt-10">
          <label class="lbl">Notes</label>
          <p class="print-line-space">
            {{ viewCCDCJacketGroup.thermostatInfos?.notes }}
          </p>
        </div>
      </mat-card>
    </div>

    <!-- Notes -->
    <div fxLayout="row wrap" id="{{ 'note' + i }}">
      <mat-card class="print-less-peding print-friendly" fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100"
                fxFlex.gt-xs="100">
        <div fxLayout="row wrap">
          <mat-card-title class="print-title">Note</mat-card-title>
        </div>
        <hr/>
        <br/>
        <div class="print-line-space" fxLayout="row wrap" fxLayoutAlign="space-between">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <p>{{ viewCCDCJacketGroup.notes?.notes }}</p>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
  <br/>
</div>
