<!doctype html>
<html lang="en">
<head>
  <title>Admin Panel - BriskHeat</title>
  <base href="/">
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,700|Material+Icons" rel="stylesheet">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">
  <style type="text/css">
    .loading::before,
    .loading::after {
      position: fixed;
      z-index: 3000;
      top: 0;
      left: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
    .loading::before {
      content: '';
      background-color: #fff;
    }
    .loading::after {
      font-family: "Helvetica Neue", Helvetica, sans-serif;
      content: 'BriskHeat Corporation';
      text-align: center;
      white-space: pre;
      font-weight: normal;
      font-size: 24px;
      letter-spacing: 0.04rem;
      color: #000;
      opacity: 0.8;
      animation: animation 1s alternate infinite;
    }
    @keyframes animation {
      to {
        opacity: 0.2;
      }
    }
  </style>
  <script>
    var global = global || window;
    var Buffer = Buffer || [];
    var process = process || {
      env: { DEBUG: undefined },
      version: []
    };
  </script>
</head>
<body>
  <sfl-app-main>
    <div class="loading"></div>
  </sfl-app-main>
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct" crossorigin="anonymous"></script>
</body>
</html>