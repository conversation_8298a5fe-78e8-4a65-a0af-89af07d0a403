<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Power Cord Material</mat-label>
          <input matInput [(ngModel)]="powerCordMaterialFilter.value" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldValue)"
            *ngIf="powerCordMaterialFilter.value"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addPowerCordMaterials()">Add New Power Cord Material</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="powerCordMaterialDataSource"
        (matSortChange)="getPowerCordMaterialsSorting($event)"
      >
        <ng-container matColumnDef="powerCordMaterialId">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Power Cord Material Id </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.powerCordMaterialId }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="value">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="40"> Power Cord Material </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="40"> {{ element?.value }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="price">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Price </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.price }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editPowerCordMaterials(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deletePowerCordMaterial(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="powerCordMaterialColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: powerCordMaterialColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!powerCordMaterialDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPowerCordMaterialsPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
