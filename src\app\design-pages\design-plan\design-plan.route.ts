import {Routes} from '@angular/router';
import {DesignPlanComponent} from './design-plan.component';
import {TitleBlockEditorComponent} from './title-block-editor/title-block-editor.component';
import {BOMEditorComponent} from './bom-editor/bom-editor.component';
import {ElementComponent} from './element/element.component';
import {PartNumberComponent} from 'src/app/shared/component/Part Number/part-number.component';
import {PNErrorComponent} from 'src/app/shared/component/Part Number/partnumber-error.component';
import {SearchParametersComponent} from './element/search-parameters/search-parameters.component';
import {LapCalcComponent} from './lap-calc/lap-calc.component';
import {AddOperationsComponent} from './bom-editor/add-operations/add-operations.component';
import {AddBOmMaterialsComponent} from './bom-editor/add-materials/add-bom-materials.component';
import {DMTLogComponent} from 'src/app/shared/component/dml-log/dmt-log.component';
import {EditDialogComponent} from './bom-editor/edit-dialog/edit-dialog.component';

export const DesignPlanRoutes: Routes = [
  {
    path: '',
    component: DesignPlanComponent
  },
  {
    path: 'title-block-editor',
    component: TitleBlockEditorComponent
  },
  {
    path: 'bom-editor',
    component: BOMEditorComponent
  },
  {
    path: 'element',
    component: ElementComponent
  },
  {
    path: 'part-number',
    component: PartNumberComponent
  },
  {
    path: 'pn-error',
    component: PNErrorComponent
  },
  {
    path: 'search-parameters',
    component: SearchParametersComponent
  },
  {
    path: 'lap-calc',
    component: LapCalcComponent
  },
  {
    path: 'add-operations',
    component: AddOperationsComponent
  },
  {
    path: 'edit-dialog',
    component: EditDialogComponent
  },
  {
    path: 'add-materials',
    component: AddBOmMaterialsComponent
  },
  {
    path: 'dmt-log',
    component: DMTLogComponent
  }
];
