import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Location } from '@angular/common';

@Component({
    selector: 'sfl-service-unavailable',
    templateUrl: './service-unavailable.component.html'
})

export class ServiceUnavailableComponent implements OnInit {
    constructor(public router: Router, private location: Location) { }

    ngOnInit() {
    }

    goBack() {
        this.location.back();
    }
}
