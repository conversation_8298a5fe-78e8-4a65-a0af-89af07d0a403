import { SelectionModel } from '@angular/cdk/collections';
import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { Mat<PERSON><PERSON>og, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { CurrencyDTO } from 'src/app/admin-pages/finalize/finalize.model';
import { FinalizeService } from 'src/app/admin-pages/finalize/finalize.service';
import { Messages, SharedService, SnakbarService, SweetAlertService } from 'src/app/shared';
import { SolidworksDownloadComponent } from 'src/app/shared/component/solidworks-download/solidworks-download.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Status } from '../../admin-pages/dashboard/dashboard.model';
import { Quotation } from '../../admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from '../../admin-pages/new-quotation/summary-sales-order.service';
import { SalesAssociate } from '../../quote-tracker/quote-tracker.model';
import { QuoteTrackerService } from '../../quote-tracker/quote-tracker.service';
import { PartNumberComponent } from '../../shared/component/Part Number/part-number.component';
import { TitleBlockHighlighterDTO } from '../design-plan/title-block-editor/title-block-editor.model';
import { CostReportComponent } from './cost-report/cost-report.component';
import { CoverPageComponent } from './cover-page/cover-page.component';
import { ElementBomComponent } from './element-bom/element-bom.component';
import { FinalReviewComponent } from './final-review/final-review.component';
import { GoldReportComponent } from './gold-report/gold-report.component';
import {
  APIRESULT,
  ConvReview,
  Document,
  DownloadZipRequestDTO,
  FilePath,
  FilePathDirectory,
  Jacket,
  JacketListInfo,
  JacketProjection,
  JacketRevisionIdDTO,
  JacketStatus,
  RepeatJacketDTO,
  VietnamConv
} from './jacket-list.model';
import { JacketListService } from './jacket-list.service';
import { Level1ReviewComponent } from './level1-review/level1-review.component';
import { LinkDialogComponent } from './link-dialog/link-dialog.component';
import { NewRevisionComponent } from './new-revision/new-revision.component';
import { PatternComponent } from './pattern/pattern.component';
import { SimulationComponent } from './simulation/simulation.component';
import { TrackerFieldsEditorComponent } from './tracker-fields-editor/tracker-fields-editor.component';

@Component({
  selector: 'sfl-jacket-list',
  templateUrl: './jacket-list.component.html',
  styleUrls: ['./jacket-list.scss']
})
export class JacketListComponent implements OnInit, OnDestroy {
  length: number;
  pageSizeOptions = Variable.pageSizeOptions;
  pageSize = Variable.itemsPerPage;
  pageIndex = Variable.activePage;
  initialPageIndex = Variable.activePage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultJacketSortBy;
  totalPages: number;
  numberOfElements: number;
  revisionId: number;
  jacketListInfo: JacketListInfo;
  selectedJacketGroup = 'All';
  selectedJacketListForPattern = Array<number>();
  selectedJacketListForLevelOneReview = Array<number>();
  selectedJacketListForSimulation = Array<number>();
  selectedJacketListForElementBom = Array<number>();
  selectedJacketListForFinalReview = Array<number>();
  selectedJacketsForVietnamConversion = Array<number>();
  selectedJacketsForConversionReview = Array<number>();
  quotationId: number;
  subscription = new Subscription();
  displayedColumns = [
    'action',
    'customerPN',
    'repeat',
    'name',
    'partNumber',
    'revisionName',
    'jacketgroup',
    'reference',
    'path',
    'pattern',
    'level1',
    'simulations',
    'element',
    'final',
    'vietanamconv',
    'syncStatus',
    'vnSync',
    'crSync'
  ];
  dataSourceJacket = new MatTableDataSource<JacketProjection>();
  dataSourceJacketNew: JacketProjection[] = [];
  isNoDataFound = this.dataSourceJacket.connect().pipe(map(data => data.length === 0));
  isAllowGoldReport = false;
  isModalOpen: boolean = false;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  selectionp = new SelectionModel<JacketProjection>(true, []);
  selectionl = new SelectionModel<JacketProjection>(true, []);
  selections = new SelectionModel<JacketProjection>(true, []);
  selectione = new SelectionModel<JacketProjection>(true, []);
  selectionf = new SelectionModel<JacketProjection>(true, []);
  selectionv = new SelectionModel<JacketProjection>(true, []);
  selectionc = new SelectionModel<JacketProjection>(true, []);

  status = [
    {name: 'Submitted', value: 'Submitted'},
    {name: 'Element & BOM', value: 'Element&BOM'},
    {name: 'Pending', value: 'Pending'},
    {name: 'Pattern Design', value: 'PatternDesign'},
    {name: 'Level 1 Review', value: 'Level1Review'},
    {name: 'Level 2 Review', value: 'Level2Review'},
    {name: 'Level 3 Review', value: 'Level3Review'}
  ];
  entryMethod: string;
  currencyId: number;
  documentDataSource = new MatTableDataSource<Document>();
  isUploading = false;
  documentslist = false;
  sizeLG = 100;
  sizeMD = 100;
  sizeSM = 59;
  sizeXS = 100;
  filedata: File;
  filename: string;
  isValidFileSize = false;
  document: Document;
  value: string;
  showLoader = false;
  filePath: FilePath;
  directoryPath: FilePathDirectory;
  solidWorksNotInstalled = false;
  color = Variable.warnRed;
  jacketRepeat = new RepeatJacketDTO;
  saveCustomerPnDTO: Jacket[] = [];
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  currency: CurrencyDTO;
  titleBlockHighlighter: TitleBlockHighlighterDTO = new TitleBlockHighlighterDTO();
  vietnamConvDTO: VietnamConv = new VietnamConv();
  convReview: ConvReview = new ConvReview();
  jacketId: number;
  JacketStatuses: JacketStatus = new JacketStatus();
  customerPartNumber: string;
  designStatusId: number;
  newJacketList: JacketProjection[] = [];
  newJackGroupName: JacketRevisionIdDTO[] = [];
  quotationStatuses: Status[];
  quotation: Quotation;
  salesassociates: SalesAssociate[];

  constructor(
    private readonly matDialog: MatDialog,
    private readonly activatedRoute: ActivatedRoute,
    private readonly sharedService: SharedService,
    private readonly finalizeService: FinalizeService,
    private readonly jacketListService: JacketListService,
    private readonly titleService: Title,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
    private readonly salesOrderSummaryService: SalesOrderSummaryService,
    private readonly quoteTrackerService: QuoteTrackerService,
  ) {

  }

  async ngOnInit() {
    this.titleService.setTitle('Jacket List - Design Eng');
    this.dataSourceJacket.sort = this.sort;
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['quotId']) {
        this.quotationId = params['quotId'];
        this.getQuotDetails();
        this.getJacketGroupByRevisionId(this.quotationId);
        this.getDocumentListyByQuoteId();
      }
    });
    this.value = Messages.Quotation.upload_message;
    this.document = new Document();
    this.filePath = new FilePath();
    this.directoryPath = new FilePathDirectory();
    this.getCurrency();
    this.getSaleAssociate();
  }

  getSaleAssociate() {
    this.showLoader = true;
    return new Promise<void>(resolve => {
      this.quoteTrackerService.getSalesAssociate(true).subscribe(
        (res: Array<SalesAssociate>) => {
          this.salesassociates = res;
          this.showLoader = false;
          resolve();
        },
        () => {
          resolve();
          this.showLoader = false;
        }
      );
    });
  }

  updateDesigner(designerId: number) {
    let obj = {'quotationId': this.quotationId, 'assignedDesignerId': designerId};
    this.jacketListService.saveDesignQuoteTrackerFields(obj, 'designerId').subscribe(
      (res) => {
        this.showLoader = false;
      },
      () => (this.showLoader = false)
    );
  }

  getQuotDetails() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getQUotById(this.quotationId).subscribe(
        async (res: Quotation) => {
          this.quotation = res;
          this.quotationStatuses = await this.sharedService.getAllStatusByType('design', this.quotation.quotationStatusId);
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // update cust clarification required flag/ based on the status selected
  updateQuoteStatus(quoteStatusId: number) {
    this.showLoader = true;
    const statusSelected = this.quotationStatuses.find(status => status.id === quoteStatusId).status;
    if (statusSelected === Values.QuoteStatus_Customer_Clarification_Required) {
      this.quotation.customerClarificationRequired = true;
    }
    if (statusSelected === Values.QuoteStatus_External_Quote_Required) {
      this.quotation.externalQuoteRequired = true;
    }
    this.salesOrderSummaryService.saveOrUpdateQuotation(this.quotation, this.quotation.id).subscribe(() => {
    });
    this.showLoader = false;
  }

  updateRevision(element) {
    const matDataConfig = new MatDialogConfig();
    const jacketIds = [element.id];
    matDataConfig.data = {
      jacketId: element.id,
      partNumber: element.partNumber,
      fixPath: element.partFilePath,
      currentRevision: element.designRevision,
      selectedPartFilePath: element.selectedPartFilePath ? true : false
    };
    matDataConfig.width = PopupSize.size.popup_xmd;
    this.matDialog
      .open(NewRevisionComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res) {
          this.updateTitleBlockHighlightDTO('revision', jacketIds);
        }
        this.getJacketGroupByRevisionId(this.quotationId);
      });
  }

  async deleteJackets(jacketId: JacketProjection) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.subscription.add(this.jacketListService.deleteJacket(jacketId.id).subscribe(res => {
          this.getJacketListByRevisionId(this.revisionId,this.numberOfElements > 1 ? this.pageIndex : this.pageIndex - 1, this.pageSize);
          this.dataSourceJacket._updateChangeSubscription();
          this.snakbarService.success(Messages.JACKET_DELETE.jacket_delete);
      }));
    }
  }

  toggleOnHold(jacket: JacketProjection) {
    jacket.onHold = !jacket.onHold;
    // Here you would typically call a service to update the backend
    // For now, we'll just update the local state
    const message = jacket.onHold ? 'Jacket put on hold' : 'Jacket removed from hold';
    this.snakbarService.success(message);
  }

  onJacketGroupChange(name) {
    if (name !== 'All') {
      const jacketList = this.dataSourceJacketNew.filter(jacket => jacket.jacketGroupName === name);
      this.dataSourceJacket.data = jacketList;
    } else {
      this.getJacketGroupByRevisionId(this.quotationId);
    }
  }

  clearAllSelection() {
    this.selectedJacketListForPattern = new Array<number>();
    this.selectedJacketListForLevelOneReview = new Array<number>();
    this.selectedJacketListForSimulation = new Array<number>();
    this.selectedJacketListForElementBom = new Array<number>();
    this.selectedJacketListForFinalReview = new Array<number>();
    this.selectedJacketsForVietnamConversion = new Array<number>();
    this.selectedJacketsForConversionReview = new Array<number>();
    this.selectionp.clear();
    this.selectionl.clear();
    this.selectione.clear();
    this.selectionf.clear();
    this.selectionv.clear();
    this.selectionc.clear();
  }

  getJacketGroupByRevisionId(revisionId) {
    this.jacketListService.getJacketGroupByRevId(revisionId).subscribe((response: JacketRevisionIdDTO[]) => {
      this.newJackGroupName = response;
      this.revisionId = response[0].revisionId;
      this.getJacketListByRevisionId(response[0].revisionId, this.initialPageIndex, this.pageSize);
    });
  }

  getPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getJacketListByRevisionId(this.revisionId,this.pageIndex, this.pageSize);
  }

  getSorting(event) {
    this.sortField = event.active;
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    this.getJacketListByRevisionId(this.revisionId,this.pageIndex, this.pageSize);
  }

  getJacketListByRevisionId(revisionId, pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = {
      page: pageIndex,
      size: pageSize,
      sort: this.sortField + ',' + this.sortOrder,
      direction: this.sortOrder
    };
    this.subscription.add(
      this.jacketListService.getJacketListByRevId(revisionId, pageable).subscribe(res => {
        if (res) {
            const jacketIds = res.content.map(({ id }) => id);
            if (jacketIds && jacketIds !== null && jacketIds !== undefined && jacketIds.length>0) {
              this.jacketListService.multipleApiCallFunction(revisionId, jacketIds, pageable).subscribe(response => {
                this.newJacketList = response[0].content;
                this.dataSourceJacket.data = response[0].content;
                this.dataSourceJacket.data.forEach(element => {
                  if (element.elementBomId !== null && element.elementBomChecked === true) {
                    this.isAllowGoldReport = true;
                  }
                });
                response[1].forEach(res => {
                  const index = response[0].content.findIndex(x => x.id === res.jacketId);
                  if (index !== -1) {
                    response[0].content[index].jacketId = res.jacketId;
                    response[0].content[index].syncStatus = res.syncStatus;
                    response[0].content[index].vietnamStatus = res.vietnamStatus;
                    response[0].content[index].costaRicaStatus = res.costaRicaStatus;
                  }
                });
                this.dataSourceJacket.data = response[0].content;
              });
            }
            this.length = res.totalElements;
            this.totalPages = res.totalPages;
            this.pageIndex = res.number;
            this.numberOfElements = res.numberOfElements;
            this.clearAllSelection();
            this.showLoader = false;
          }
        },
        error => {
          this.showLoader = false;
          if (error.applicationStatusCode === 1208) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  patternSelectionChanged(event, row) {
    if (event && !row.onHold) { // Prevent selection if row is on hold
      this.selectionp.toggle(row);
      if (event.checked) {
        this.selectedJacketListForPattern.push(row.id);
      } else {
        this.selectedJacketListForPattern.splice(this.selectedJacketListForPattern.indexOf(row.id), 1);
      }
    }
  }

  simulationChanged(event, row) {
    if (event && !row.onHold) { // Prevent selection if row is on hold
      this.selections.toggle(row);
      if (event.checked) {
        this.selectedJacketListForSimulation.push(row.id);
      } else {
        this.selectedJacketListForSimulation.splice(this.selectedJacketListForSimulation.indexOf(row.id), 1);
      }
    }
  }

  reviewSelectionChanged(event, row) {
    if (event && !row.onHold) { // Prevent selection if row is on hold
      this.selectionl.toggle(row);
      if (event.checked) {
        this.selectedJacketListForLevelOneReview.push(row.id);
      } else {
        this.selectedJacketListForLevelOneReview.splice(this.selectedJacketListForLevelOneReview.indexOf(row.id), 1);
      }
    }
  }

  repeatJacket(id, event) {
    this.showLoader = true;
    this.jacketRepeat.jacketId = id;
    this.jacketRepeat.repeat = event.checked;
    this.subscription.add(
      this.jacketListService.markJacketAsRepeat(this.jacketRepeat).subscribe(
        error => {
        this.showLoader = false;
        }
      ));

  }

  elementBomSelectionChanged(event, row) {
    if (event && !row.onHold) { // Prevent selection if row is on hold
      this.selectione.toggle(row);
      if (event.checked) {
        this.selectedJacketListForElementBom.push(row.id);
      } else {
        this.selectedJacketListForElementBom.splice(this.selectedJacketListForElementBom.indexOf(row.id), 1);
      }
    }
  }

  finalReviewSelectionChanged(event, row) {
    if (event && !row.onHold) { // Prevent selection if row is on hold
      this.selectionf.toggle(row);
      if (event.checked) {
        this.selectedJacketListForFinalReview.push(row.id);
      } else {
        this.selectedJacketListForFinalReview.splice(this.selectedJacketListForFinalReview.indexOf(row.id), 1);
      }
    }
  }

  vietnamConversionChanged(event, row) {
    if (event && !row.onHold) { // Prevent selection if row is on hold
      this.selectionv.toggle(row);
      if (event.checked) {
        this.selectedJacketsForVietnamConversion.push(row.id);
      } else {
        this.selectedJacketsForVietnamConversion.splice(this.selectedJacketsForVietnamConversion.indexOf(row.id), 1);
      }
    }
  }

  conversionReviewChanged(event, row) {
    if (event && !row.onHold) { // Prevent selection if row is on hold
      this.selectionc.toggle(row);
      if (event.checked) {
        this.selectedJacketsForConversionReview.push(row.id);
      } else {
        this.selectedJacketsForConversionReview.splice(this.selectedJacketsForConversionReview.indexOf(row.id), 1);
      }
    }
  }

  openPattern(jacketId) {
    const matDataConfig = new MatDialogConfig();
    const jacketIds = this.selectedJacketListForPattern;
    if (jacketId) {
      matDataConfig.data = {jacketId: jacketId, quotationId: this.quotationId};
    } else {
      matDataConfig.data = {
        jacketList: this.selectedJacketListForPattern,
        jacketId: jacketId,
        quotationId: this.quotationId
      };
      this.selectedJacketListForPattern = new Array();
    }
    matDataConfig.width = PopupSize.size.popup_xmd;
    const dialogRef = this.matDialog.open(PatternComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      this.getJacketGroupByRevisionId(this.quotationId);
      this.selectedJacketGroup = 'All';
      this.selectionp.clear();
      this.selectedJacketListForPattern = new Array();
      if (res.status === this.JacketStatuses.SAVE) {
        this.updateTitleBlockHighlightDTO('drawnBy', jacketIds);
      } else if (res.status === this.JacketStatuses.UNDO) {
        const index = this.dataSourceJacket.data.indexOf(res.id);
        this.dataSourceJacket.data[index].patternChecked = false;
      }
    });
  }

  openLinks() {
    const country: string = this.sharedService.getUsersCountry();
    const soNumber: string = this.dataSourceJacket.data[0].salesOrderNumber;
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {'country': country, 'soNumber': soNumber, 'quoteId': this.quotationId};
    matDataConfig.width = PopupSize.size.popup_lg;
    matDataConfig.panelClass = 'sfl-link-dialog';
    const dialogRef = this.matDialog.open(LinkDialogComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(() => {
    });
  }

  costReport() {
    const revisionId = this.newJackGroupName[0].revisionId;
    const soNumber = this.newJacketList[0].salesOrderNumber;
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {revisionId: revisionId, soNumber: soNumber};
    matDataConfig.width = PopupSize.size.popup_xlg;
    matDataConfig.panelClass = 'sfl-cost-report-model';
    const dialogRef = this.matDialog.open(CostReportComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(() => {
    });
  }

  goldReport(soNumber) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {soNumber: soNumber};
    matDataConfig.width = PopupSize.size.popup_xlg;
    matDataConfig.panelClass = 'sfl-gold-report-modal';
    const dialogRef = this.matDialog.open(GoldReportComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(() => {
    });
  }

  openLevelReview(jacketId) {
    const matDataConfig = new MatDialogConfig();
    const jacketIds = this.selectedJacketListForPattern;
    if (jacketId) {
      matDataConfig.data = {jacketId: jacketId, quotationId: this.quotationId};
    } else {
      matDataConfig.data = {
        jacketList: this.selectedJacketListForLevelOneReview,
        jacketId: jacketId,
        quotationId: this.quotationId
      };
      this.selectedJacketListForLevelOneReview = new Array();
    }
    matDataConfig.width = PopupSize.size.popup_xmd;
    const dialogRef = this.matDialog.open(Level1ReviewComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      this.getJacketGroupByRevisionId(this.quotationId);
      this.selectedJacketGroup = 'All';
      this.selectionl.clear();
      this.selectedJacketListForLevelOneReview = new Array();
      if (res.status === this.JacketStatuses.SAVE) {
        this.updateTitleBlockHighlightDTO('drawnBy', jacketIds);
      } else if (res.status === this.JacketStatuses.UNDO) {
        const index = this.dataSourceJacket.data.indexOf(res.id);
        this.dataSourceJacket.data[index].levelOneChecked = null;
      }
    });

  }

  openSimulation(jacketId) {
    const matDataConfig = new MatDialogConfig();
    const jacketIds = this.selectedJacketListForSimulation;
    if (jacketId) {
      matDataConfig.data = {jacketId: jacketId, quotationId: this.quotationId};
    } else {
      matDataConfig.data = {
        jacketList: this.selectedJacketListForSimulation,
        jacketId: jacketId,
        quotationId: this.quotationId
      };
      this.selectedJacketListForSimulation = new Array();
    }
    matDataConfig.width = PopupSize.size.popup_xmd;
    const dialogRef = this.matDialog.open(SimulationComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      this.getJacketGroupByRevisionId(this.quotationId);
      this.selectedJacketGroup = 'All';
      this.selections.clear();
      this.selectedJacketListForSimulation = new Array();
      if (res.status === this.JacketStatuses.SAVE) {
        this.updateTitleBlockHighlightDTO('drawnBy', jacketIds);
      } else if (res.status === this.JacketStatuses.UNDO) {
        const index = this.dataSourceJacket.data.indexOf(res.id);
        this.dataSourceJacket.data[index].simulationChecked = null;
      }
    });

  }

  openElement(jacketId) {
    const matDataConfig = new MatDialogConfig();
    const jacketIds = this.selectedJacketListForElementBom;
    if (jacketId) {
      matDataConfig.data = {jacketId: jacketId, quotationId: this.quotationId};
    } else {
      matDataConfig.data = {
        jacketList: this.selectedJacketListForElementBom,
        jacketId: jacketId,
        quotationId: this.quotationId
      };
      this.selectedJacketListForElementBom = new Array();
    }
    matDataConfig.width = PopupSize.size.popup_xmd;
    const dialogRef = this.matDialog.open(ElementBomComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      this.getJacketGroupByRevisionId(this.quotationId);
      this.selectedJacketGroup = 'All';
      this.selectione.clear();
      this.selectedJacketListForElementBom = new Array();
      if (res.status === this.JacketStatuses.SAVE) {
        this.updateTitleBlockHighlightDTO('tapeBy', jacketIds);
      } else if (res.status === this.JacketStatuses.UNDO) {
        const index = this.dataSourceJacket.data.indexOf(res.id);
        this.dataSourceJacket.data[index].elementBomChecked = null;
      }
    });
  }

  openFinalReview(jacketId) {
    const matDataConfig = new MatDialogConfig();
    const jacketIds = this.selectedJacketListForFinalReview;
    if (jacketId) {
      matDataConfig.data = {jacketId: jacketId, quotationId: this.quotationId};
    } else {
      matDataConfig.data = {
        jacketList: this.selectedJacketListForFinalReview,
        jacketId: jacketId,
        quotationId: this.quotationId
      };
      this.selectedJacketListForFinalReview = new Array();
    }
    matDataConfig.width = PopupSize.size.popup_xmd;
    const dialogRef = this.matDialog.open(FinalReviewComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      this.getJacketGroupByRevisionId(this.quotationId);
      this.selectedJacketGroup = 'All';
      this.selectionf.clear();
      this.selectedJacketListForFinalReview = new Array();
      if (res.status === this.JacketStatuses.SAVE) {
        this.updateTitleBlockHighlightDTO('finalReview', jacketIds);
      } else if (res.status === this.JacketStatuses.UNDO) {
        const index = this.dataSourceJacket.data.indexOf(res.id);
        this.dataSourceJacket.data[index].finalReviewChecked = null;
      }
    });
  }

  async undoVietnamConv(jacketId) {
    if (await this.sweetAlertService.warningWhenUndoSignature()) {
      this.subscription.add(
        this.jacketListService.updateVietnamConv(this.vietnamConvDTO, jacketId).subscribe(
          res => {
            this.getJacketGroupByRevisionId(this.quotationId);
            this.showLoader = false;
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  async undoConvReview(jacketId) {
    if (await this.sweetAlertService.warningWhenUndoSignature()) {
      this.subscription.add(
        this.jacketListService.updateConvReview(this.convReview, jacketId).subscribe(
          res => {
            this.getJacketGroupByRevisionId(this.quotationId);
            this.showLoader = false;
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  CoverPage() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: this.quotationId};
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog.open(CoverPageComponent, matDataConfig);
  }

  PartNumber() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: this.quotationId};
    matDataConfig.width = PopupSize.size.popup_xxlg;
    this.matDialog
      .open(PartNumberComponent, matDataConfig)
      .afterClosed()
      .subscribe(result => {
        this.getJacketGroupByRevisionId(this.quotationId);
      });
  }

  isAllSelectedp() {
    const numSelected = this.selectionp.selected.length;
    const numSelectableRows = this.dataSourceJacket.data.filter(row => !row.onHold).length;
    return numSelected === numSelectableRows && numSelectableRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterTogglep() {
    this.selectedJacketListForPattern = new Array();
    if (this.isAllSelectedp()) {
      // Only clear selections for rows that are not on hold
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold && this.selectionp.isSelected(row)) {
          this.selectionp.deselect(row);
        }
      }
    } else {
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold) { // Skip rows that are on hold
          this.selectionp.select(row);
          if (row.patternChecked == null) {
            this.selectedJacketListForPattern.push(row.id);
          }
        }
      }
    }
  }

  isAllSelectedl() {
    const numSelected = this.selectionl.selected.length;
    const numSelectableRows = this.dataSourceJacket.data.filter(row => !row.onHold).length;
    return numSelected === numSelectableRows && numSelectableRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterTogglel() {
    this.selectedJacketListForLevelOneReview = new Array();
    if (this.isAllSelectedl()) {
      // Only clear selections for rows that are not on hold
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold && this.selectionl.isSelected(row)) {
          this.selectionl.deselect(row);
        }
      }
    } else {
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold) { // Skip rows that are on hold
          this.selectionl.select(row);
          if (row.levelOneId == null) {
            this.selectedJacketListForLevelOneReview.push(row.id);
          }
        }
      }
    }
  }

  isAllSelecteds() {
    const numSelected = this.selections.selected.length;
    const numSelectableRows = this.dataSourceJacket.data.filter(row => !row.onHold).length;
    return numSelected === numSelectableRows && numSelectableRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggles() {
    this.selectedJacketListForSimulation = new Array();
    if (this.isAllSelecteds()) {
      // Only clear selections for rows that are not on hold
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold && this.selections.isSelected(row)) {
          this.selections.deselect(row);
        }
      }
    } else {
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold) { // Skip rows that are on hold
          this.selections.select(row);
          if (row.simulationId == null) {
            this.selectedJacketListForSimulation.push(row.id);
          }
        }
      }
    }
  }

  isAllSelectede() {
    const numSelected = this.selectione.selected.length;
    const numSelectableRows = this.dataSourceJacket.data.filter(row => !row.onHold).length;
    return numSelected === numSelectableRows && numSelectableRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterTogglee() {
    this.selectedJacketListForElementBom = new Array();
    if (this.isAllSelectede()) {
      // Only clear selections for rows that are not on hold
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold && this.selectione.isSelected(row)) {
          this.selectione.deselect(row);
        }
      }
    } else {
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold) { // Skip rows that are on hold
          this.selectione.select(row);
          if (row.elementBomId == null) {
            this.selectedJacketListForElementBom.push(row.id);
          }
        }
      }
    }
  }

  isAllSelectedf() {
    const numSelected = this.selectionf.selected.length;
    const numSelectableRows = this.dataSourceJacket.data.filter(row => !row.onHold).length;
    return numSelected === numSelectableRows && numSelectableRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterTogglef() {
    this.selectedJacketListForFinalReview = new Array();
    if (this.isAllSelectedf()) {
      // Only clear selections for rows that are not on hold
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold && this.selectionf.isSelected(row)) {
          this.selectionf.deselect(row);
        }
      }
    } else {
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold) { // Skip rows that are on hold
          this.selectionf.select(row);
          if (row.finalReviewId == null) {
            this.selectedJacketListForFinalReview.push(row.id);
          }
        }
      }
    }
  }

  isAllSelectedv() {
    const numSelected = this.selectionv.selected.length;
    const numSelectableRows = this.dataSourceJacket.data.filter(row => !row.onHold).length;
    return numSelected === numSelectableRows && numSelectableRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterTogglev() {
    this.selectedJacketsForVietnamConversion = new Array();
    if (this.isAllSelectedv()) {
      // Only clear selections for rows that are not on hold
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold && this.selectionv.isSelected(row)) {
          this.selectionv.deselect(row);
        }
      }
    } else {
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold) { // Skip rows that are on hold
          this.selectionv.select(row);
          if (row.vietnamConversionId == null) {
            this.selectedJacketsForVietnamConversion.push(row.id);
          }
        }
      }
    }
  }

  saveVietnameConversion() {
    const vietnamObject = new Array();
    if (this.selectedJacketsForVietnamConversion.length > 0) {
      for (let element of this.selectedJacketsForVietnamConversion) {
        vietnamObject.push({jacketId: element});
      }
      ;
      this.subscription.add(
        this.jacketListService.saveVietnamConversion(vietnamObject, this.quotationId).subscribe(res => {
          this.getJacketGroupByRevisionId(this.quotationId);
          this.selectedJacketsForVietnamConversion = new Array();
          this.selectedJacketGroup = 'All';
          this.selectionv.clear();
        })
      );
    }
  }

  saveConversionReview() {
    const conversionObject = new Array();
    if (this.selectedJacketsForConversionReview.length > 0) {
      for (let element of this.selectedJacketsForConversionReview) {
        conversionObject.push({jacketId: element});
      }
      ;
      this.subscription.add(
        this.jacketListService.saveConversionReview(conversionObject).subscribe(res => {
          this.getJacketGroupByRevisionId(this.quotationId);
          this.selectedJacketsForConversionReview = new Array();
          this.selectedJacketGroup = 'All';
          this.selectionc.clear();
        })
      );
    }
  }

  isAllSelectedc() {
    const numSelected = this.selectionc.selected.length;
    const numSelectableRows = this.dataSourceJacket.data.filter(row => !row.onHold).length;
    return numSelected === numSelectableRows && numSelectableRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterTogglec() {
    this.selectedJacketsForConversionReview = new Array();
    if (this.isAllSelectedc()) {
      // Only clear selections for rows that are not on hold
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold && this.selectionc.isSelected(row)) {
          this.selectionc.deselect(row);
        }
      }
    } else {
      for (let row of this.dataSourceJacket.data) {
        if (!row.onHold) { // Skip rows that are on hold
          this.selectionc.select(row);
          if (row.conversionReviewId == null) {
            this.selectedJacketsForConversionReview.push(row.id);
          }
        }
      }
    }
  }

  saveCustomerPN() {
    this.showLoader = true;
    this.subscription.add(
      this.jacketListService.saveCustomerPartNumber(this.dataSourceJacket.data).subscribe((res) => {
        if (res) {
          this.snakbarService.success(Messages.Customer_PN_Success.part_number_save);
          this.showLoader = false;
        }
        () => {
          this.showLoader = false;
        };
      })
    );
  }

  downloadSOExcel() {
    this.subscription.add(
      this.finalizeService.downloadSOExcel(this.newJackGroupName[0].revisionId, this.currencyId).subscribe(success => {
        this.downloadExcel(success, 'SOSheet-' + this.newJacketList[0].salesOrderNumber + '.xlsx');
      })
    );
  }


  getCurrency() {
    this.subscription.add(
      this.finalizeService.getAllCurrency().subscribe((res: CurrencyDTO) => {
        if (res) {
          this.currency = res;
          this.currencyId = this.currency[0].id;
        }
      })
    );
  }

  downloadZipFile(jacket: JacketProjection) {
    const downloadZipRequestDTO = {
      quotationId: Number(this.quotationId),
      jacketId: jacket.id,
    };
    this.downloadZipFiles(downloadZipRequestDTO);
  }

  retrieveAllZipFiles() {
    const downloadZipRequestDTO = {
      quotationId: Number(this.quotationId),
      jacketId: null,
    };
    this.downloadZipFiles(downloadZipRequestDTO);
  }

  downloadZipFiles(downloadZipRequestDTO: DownloadZipRequestDTO) {
    this.subscription.add(
      this.jacketListService.downloadZipFiles(downloadZipRequestDTO).subscribe((res) => {
        if (res.zips && res.zips.length > 0) {
          res.zips.forEach((zip) => {
            if (zip.zipFileBase64) {
              const blob = this.base64ToBlob(zip.zipFileBase64, 'application/zip');
              this.downloadFile(blob, zip.fileName);
            } else {
              this.snakbarService.error(zip.errorMessage, 10000);
            }
          });
        }

        if (res.errors && res.errors.length > 0) {
          const message = res.errors.join('\n').replace(/\r\n/g, '\n');
          this.snakbarService.error(message, 10000);
        }
      }),
    );
  }

  base64ToBlob(base64: string, contentType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i) => byteCharacters.charCodeAt(i));
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
  }

  downloadFile(blob: Blob, fileName: string) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }

  downloadExcel(response, sheetName: string) {
    const dwldLink = document.createElement('a');
    const url = URL.createObjectURL(response);
    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('target', '_blank');
    dwldLink.setAttribute('download', sheetName);
    dwldLink.style.visibility = 'hidden';
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  getDocumentListyByQuoteId() {
    this.jacketListService.getDocumentListyByQuoteId(this.quotationId).subscribe((res: Document[]) => {
      this.documentDataSource.data = res;
      this.isUploading = false;
    });
  }

  openDoclist() {
    this.documentslist = !this.documentslist;
    if (!this.documentslist) {
      this.sizeLG = 100;
      this.sizeMD = 100;
      this.sizeSM = 59;
      this.sizeXS = 100;
    } else {
      this.sizeLG = 79;
      this.sizeMD = 75;
      this.sizeSM = 60;
      this.sizeXS = 100;
    }
  }

  readUrl(event) {
    if (event.target.files && event.target.files[0]) {
      this.isValidFileSize = false;
      this.filedata = event.target.files[0];
      this.filename = this.filedata.name;
      this.value = this.filename;
      this.document.quotationId = this.quotationId;
    } else {
      this.filedata = null;
      this.value = Messages.Quotation.upload_message;
    }
  }

  uploadFile() {
    if (this.filedata.size > 10485760) {
      this.isValidFileSize = true;
      this.filedata = null;
      this.filename = null;
    } else {
      this.isUploading = true;
      this.isValidFileSize = false;
      const formData = new FormData();
      formData.append('file', this.filedata);
      formData.append('document', JSON.stringify(this.document));
      this.jacketListService.uploadDocument(formData).subscribe(() => {
        this.document = new Document();
        this.filedata = null;
        this.filename = '';
        this.value = Messages.Quotation.upload_message;
        this.getDocumentListyByQuoteId();
      });
    }
  }

  openDoc(fileId) {
    this.subscription.add(
      this.jacketListService.getDocument(fileId).subscribe(success => {
          const url = URL.createObjectURL(success);
          window.open(url, '_target');
        },
        error => {
          if (error.applicationStatusCode === 1240) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  deleteFile(fileId) {
    this.subscription.add(
      this.jacketListService.deleteDocument(fileId).subscribe(() => {
        this.documentDataSource.data.splice(this.documentDataSource.data.map(x => x.id).indexOf(fileId), 1);
      })
    );
  }

  async confirmDelete(fileId) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.deleteFile(fileId);
    }
  }

  openFile(element) {
    this.showLoader = true;
    this.filePath.path = element.partFilePath;
    this.filePath.rev = element.designRevision;
    this.filePath.partNumber = element.partNumber;
    const selectedPartFilePath = element.selectedPartFilePath ? true : false;
    this.subscription.add(
      this.jacketListService.openFileInSLD(this.filePath, selectedPartFilePath).subscribe(
        (res: APIRESULT) => {
          if (res.success) {
            this.showLoader = false;
          } else {
            this.snakbarService.error(Messages.Title_Block.error_msg);
            this.showLoader = false;
          }
        },
        error => {
          this.showLoader = false;
          this.solidWorksNotInstalled = true;
          this.snakbarService.error(Messages.Solid_Work.not_install);
        }
      )
    );
  }

  fileLocationTransform(value: string) {
    let userLocation = this.sharedService.getUsersCountry();
    if (userLocation && value) {
      if (userLocation.toLowerCase() === 'usa') {
        if (value.includes('************')) {
          value = value.replace('************\\PUBLIC\\ENGINEERING', '**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering');
        }
      } else {
        if (value.includes('**********')) {
          value = value.replace('**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering', '************\\PUBLIC\\ENGINEERING');
        }
      }
      return value;
    } else {
      return value;
    }
  }

  openFileInExplorer(element) {
    this.showLoader = true;
    this.directoryPath.path = this.fileLocationTransform(element.partFilePath);
    this.subscription.add(
      this.jacketListService.openFileInExplorer(this.directoryPath).subscribe(
        (res: APIRESULT) => {
          if (res.success) {
            this.showLoader = false;
          } else {
            this.showLoader = false;
            this.snakbarService.error(Messages.Title_Block.error_msg);
          }
        },
        error => {
          this.solidWorksNotInstalled = true;
          this.snakbarService.error(Messages.Solid_Work.not_install);
          this.showLoader = false;
        }
      )
    );
  }

  openSolidworkDownloadPage() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog.open(SolidworksDownloadComponent, matDataConfig);
  }

  // used to update the TitleBlockHighlightDTO fields. Accepts field name and updates that field(s) in the DTO
  updateTitleBlockHighlightDTO(field: string, jacketId: number[]): void {
    this.showLoader = true;
    this.titleBlockHighlighter.jacketIds = jacketId;
    switch (field) {
      case 'revision':
        this.titleBlockHighlighter.highlightedJacketList.revision = true;
        break;
      case 'drawnBy':
        this.titleBlockHighlighter.highlightedJacketList.drawnBy = true;
        this.titleBlockHighlighter.highlightedJacketList.drawnDate = true;
        break;
      case 'tapeBy':
        this.titleBlockHighlighter.highlightedJacketList.tapeBy = true;
        this.titleBlockHighlighter.highlightedJacketList.tapeDate = true;
        this.titleBlockHighlighter.highlightedJacketList.bomBy = true;
        this.titleBlockHighlighter.highlightedJacketList.bomDate = true;
        break;
      case 'finalReview':
        this.titleBlockHighlighter.highlightedJacketList.engineeringApprovalBy = true;
        this.titleBlockHighlighter.highlightedJacketList.engAppDate = true;
        break;
    }
    // call the updateTitleBlockHighlightDTO API to update title block fields
    this.subscription.add(
      this.sharedService.highlightTitleBlockFields(this.titleBlockHighlighter).subscribe(
        () => {
          this.titleBlockHighlighter = new TitleBlockHighlighterDTO();
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      )
    );
  }

  // used to open up the tracker fields editor
  trackerFields(): void {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {
      quotationId: this.quotationId,
      salesOrderNumber: this.newJacketList[0].salesOrderNumber,
      designStatusId: this.designStatusId
    };
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-tracker-fields-model';
    const dialogRef = this.matDialog.open(TrackerFieldsEditorComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((res) => {
      this.quotation.assignedDesignerId = res.designerId;
      this.quotation.quotationStatusId = res.designStatusId;
      this.updateQuoteStatus(res.designStatusId);
    });
  }

  exportElementsToExcel() {
    this.jacketListService.downloadElementExcel(this.newJackGroupName[0].revisionId).subscribe(success => {
      let fileName = 'ElementSheet-' + (this.newJackGroupName[0].revisionId !== null && this.newJackGroupName[0].revisionId !== undefined ? this.newJackGroupName[0].revisionId : this.newJackGroupName[0].revisionId) + '.xlsx';
      this.downloadExcel(success, fileName);
    });
  }

  refreshJacketList() {
    this.getJacketGroupByRevisionId(this.quotationId);
  }

  // Sync methods for header clicks
  syncToUS() {
    const selectedJackets = this.dataSourceJacket.data.filter(jacket =>
      this.selections.isSelected(jacket) && !jacket.onHold
    );

    if (selectedJackets.length === 0) {
      this.snakbarService.error('No valid jackets selected for sync (on-hold jackets cannot be synced)');
      return;
    }

    const jacketIds = selectedJackets.map(jacket => jacket.id);
    this.subscription.add(
      this.jacketListService.syncJackets(jacketIds, 'USA').subscribe(
        () => {
          this.snakbarService.success(`${selectedJackets.length} jackets sync to US initiated`);
          this.refreshJacketList();
        },
        () => {
          this.snakbarService.error('Failed to sync jackets to US');
        }
      )
    );
  }

  syncToVN() {
    const selectedJackets = this.dataSourceJacket.data.filter(jacket =>
      this.selectionv.isSelected(jacket) && !jacket.onHold
    );

    if (selectedJackets.length === 0) {
      this.snakbarService.error('No valid jackets selected for sync (on-hold jackets cannot be synced)');
      return;
    }

    const jacketIds = selectedJackets.map(jacket => jacket.id);
    this.subscription.add(
      this.jacketListService.syncJackets(jacketIds, 'VN').subscribe(
        () => {
          this.snakbarService.success(`${selectedJackets.length} jackets sync to Vietnam initiated`);
          this.refreshJacketList();
        },
        () => {
          this.snakbarService.error('Failed to sync jackets to Vietnam');
        }
      )
    );
  }

  syncToCR() {
    const selectedJackets = this.dataSourceJacket.data.filter(jacket =>
      this.selectionc.isSelected(jacket) && !jacket.onHold
    );

    if (selectedJackets.length === 0) {
      this.snakbarService.error('No valid jackets selected for sync (on-hold jackets cannot be synced)');
      return;
    }

    const jacketIds = selectedJackets.map(jacket => jacket.id);
    this.subscription.add(
      this.jacketListService.syncJackets(jacketIds, 'CR').subscribe(
        () => {
          this.snakbarService.success(`${selectedJackets.length} jackets sync to Costa Rica initiated`);
          this.refreshJacketList();
        },
        () => {
          this.snakbarService.error('Failed to sync jackets to Costa Rica');
        }
      )
    );
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
