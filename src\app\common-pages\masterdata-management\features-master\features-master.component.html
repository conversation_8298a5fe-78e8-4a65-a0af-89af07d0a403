<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex.gt-lg="30" fxFlex.gt-md="30">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="featuresFilter.name" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldName)"
            *ngIf="featuresFilter.name"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="featuresMasterDataSource"
        (matSortChange)="getFeaturesMasterSorting($event)"
      >
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="90"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="90"> {{ element?.name }} </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="featuresMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: featuresMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!featuresMasterDataSource.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getFeaturesMasterPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
