<h2 mat-dialog-title>
  Links
  <hr>
</h2>
<mat-dialog-content>
  <table class="link-table">
    <tr>
      <th class="link-label">Name</th>
      <th class="link-data">Link</th>
    </tr>
    <tr>
      <td class="link-label">SQT Link:</td>
      <td class="link-data">
        <a class="link open-doc"
           (click)="openFileInExplorer(sqtLink)"
           target="_blank"
           matTooltip="{{ sqtLink | fileLocationTransform }}">
          {{ sqtLink }}
        </a>
      </td>
    </tr>
    <tr>
      <td class="link-label">CFD Link:</td>
      <td class="link-data">
        <a class="link open-doc"
           (click)="openFileInExplorer(cfdLink)"
           target="_blank"
           matTooltip="{{ cfdLink | fileLocationTransform }}">
          {{ cfdLink }}
        </a>
      </td>
    </tr>
    <tr>
      <td class="link-label">Heater Man Link:</td>
      <td class="link-data">
        <a class="link open-doc"
           (click)="openFileInExplorer(heaterManLink)"
           target="_blank"
           matTooltip="{{ heaterManLink | fileLocationTransform }}">
          {{ heaterManLink }}
        </a>
      </td>
    </tr>
  </table>
</mat-dialog-content>
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
  </div>
</mat-dialog-actions>
