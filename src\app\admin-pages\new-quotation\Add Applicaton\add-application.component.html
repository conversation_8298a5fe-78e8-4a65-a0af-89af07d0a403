<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Application Information
  <mat-icon *ngIf="showReloadButton" class="open-doc sfl-pull-right" [matTooltip]="outDatedViewErrorMessage" color="warn" matTooltipClass="sfl-formula-tooltip"
    (click)="reloadPage()" id="refresh">
    cached
  </mat-icon>
  <hr />
</h2>
<mat-dialog-content>
  <form #appInfoForm="ngForm" role="form">
    <div fxLayout="column" fxLayoutAlign="space-between">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-select placeholder="Element Type" name="jacketType" [(ngModel)]="appInfo.jacketType" #jacketTypeSelect="ngModel" (ngModelChange)="checkIfPluggingInfoNeedsUpdate()">
            <mat-option *ngFor="let jacket of jacketTypes | orderBy:'value'" [value]="jacket.id">
              {{ jacket.value }}
            </mat-option>
            <mat-option>None</mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Voltage" name="voltage" [(ngModel)]="appInfo.voltage" #voltageInput="ngModel" sflIsNumber />
          <div matSuffix matTooltip="Voltage">V</div>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-select placeholder="Select Phase" name="phase" [(ngModel)]="appInfo.phase" #phaseSelect="ngModel">
            <mat-option *ngFor="let phase of phaseTypes | orderBy:'value'" [value]="phase.id">
              {{ phase.value }}
            </mat-option>
            <mat-option>None</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Operating Temp ({{ tempUnit ? tempUnit : '' }})" name="operatingTemp" [(ngModel)]="appInfo.operatingTemp" #operatingTempInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Max Exposure Temp ({{ tempUnit ? tempUnit : '' }})" name="maxExposureTemp" [(ngModel)]="appInfo.maxExposureTemp"
            #maxExposureTempInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Min Ambient Temp ({{ tempUnit ? tempUnit : '' }})" name="minExposureTemp" [(ngModel)]="appInfo.minAmbientTemp"
            #minExposureTempInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="31" fxFlex.gt-md="30" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Heat up From°" name="heatupFrom" [(ngModel)]="appInfo.heatupFrom" #heatupFromInput="ngModel" />
        </mat-form-field>
        <mat-icon>settings_ethernet</mat-icon>
        <mat-form-field fxFlex.gt-lg="31" fxFlex.gt-md="30" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="To°" name="heatupTo" [(ngModel)]="appInfo.heatupTo" #heatupToInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="31" fxFlex.gt-md="30" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="In" name="heatupIn" [(ngModel)]="appInfo.heatupIn" #heatupInInput="ngModel" />
          <div matSuffix>Hours</div>
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Pipe Thickness ({{ measureUnit ? measureUnit : '' }})" name="pipeThickness" [(ngModel)]="appInfo.pipeThickness"
            #pipeThicknessInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-select placeholder="Content Motion" name="contentMotion" [(ngModel)]="appInfo.contentMotion" #contentMotionSelect="ngModel">
            <mat-option *ngFor="let motion of contentMotions | orderBy:'value'" [value]="motion?.id">
              {{ motion?.value }}
            </mat-option>
            <mat-option>None</mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Flowing at rate of" name="contentMotionFlowingRate" [(ngModel)]="appInfo.contentMotionFlowingRate"
            #contentMotionFlowingRateInput="ngModel" />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input type="text" placeholder="Pipe Material" aria-label="Pipe Material" matInput [(ngModel)]="appInfo.materialName" [matAutocomplete]="autoPipeMaterial"
          [formControl]="pipeMaterialControl" />
        </mat-form-field>
        <mat-autocomplete #autoPipeMaterial="matAutocomplete" [displayWith]="displayPipeMaterial" (optionSelected)="onSelectionChangesPipeMaterial($event.option.value)">
          <mat-option *ngFor="let data of pipeMaterialObservable$ | async; let i = index" [value]="data.name">
            {{ data?.name }}
          </mat-option>
          <mat-option value="none">None</mat-option>
        </mat-autocomplete>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherMaterial">
          <input matInput placeholder="Other Specific Heat (BTU/lb {{
              tempUnit ? tempUnit : ''
            }})" name="materialHeat" [(ngModel)]="appInfo.otherMaterialHeat" #materialHeatInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherMaterial">
          <input matInput placeholder="Other Density (lb/ft3)" name="materialDensity" [(ngModel)]="appInfo.otherMaterialDensity" #materialDensityInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherMaterial">
          <input matInput placeholder="Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})" name="materialHeat" [(ngModel)]="appInfo.materialHeat" #materialHeatInput="ngModel"
            readonly />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherMaterial">
          <input matInput placeholder="Density (lb/ft3)" name="materialDensity" [(ngModel)]="appInfo.materialDensity" #materialDensityInput="ngModel" readonly />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input type="text" placeholder="Pipe Contents" aria-label="Pipe Contents" matInput [(ngModel)]="appInfo.contentName" [matAutocomplete]="autoPipeContent"
          [formControl]="pipeContentControl" />
          <mat-autocomplete #autoPipeContent="matAutocomplete" [displayWith]="displayPipeContents" (optionSelected)="onSelectionChangesPipeContent($event.option.value)">
          <mat-option *ngFor="let data of pipeContentObservable$ | async; let i = index" [value]="data">
            {{ data?.name }}
          </mat-option>
          <mat-option value="none">None</mat-option>
        </mat-autocomplete>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherContent">
          <input matInput placeholder="Other Specific Heat (BTU/lb {{
              tempUnit ? tempUnit : ''
            }})" name="contentHeat" [(ngModel)]="appInfo.otherContentHeat" #contentHeatInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="isOtherContent">
          <input matInput placeholder="Other Density (lb/ft3)" name="density" [(ngModel)]="appInfo.otherContentDensity" #densityInput="ngModel" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherContent">
          <input matInput placeholder="Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})" name="contentHeat" [(ngModel)]="appInfo.contentHeat" #contentHeatInput="ngModel"
            readonly />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100" *ngIf="!isOtherContent">
          <input matInput placeholder="Density (lb/ft3)" name="density" [(ngModel)]="appInfo.contentDensity" #densityInput="ngModel" readonly />
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input type="text" placeholder="Control Type" aria-label="Control Type" matInput [(ngModel)]="appInfo.controlType" [matAutocomplete]="auto"
            [formControl]="materialControl" />
        </mat-form-field>
        <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn" (optionSelected)="onSelectionChanges($event.option.value)">
          <mat-option *ngFor="let data of accessoryObservable$ | async; let i = index" [value]="data.name">
            {{ data?.name }}
          </mat-option>
          <mat-option value="Other"> Other </mat-option>
        </mat-autocomplete>
        <mat-form-field *ngIf="isOther" fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Other Control Type" name="othercontroltype" [(ngModel)]="appInfo.otherControlType" #otherInput="ngModel" type="text" />
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Watt Density" name="wattdesnsity" [(ngModel)]="appInfo.wattDensity" #wattDesnistyInput="ngModel" type="text" />
        </mat-form-field>
      </div>

      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-select placeholder="Fit Type" name="fitType" (selectionChange)="onFitTypeChange($event.value)" [(ngModel)]="appInfo.fitTypeEnum">
            <mat-option *ngFor="let fitType of fitTypes | orderBy:'value'" [value]="fitType.id">
              {{ fitType?.value }}
            </mat-option>
            <mat-option>None</mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Duty Cycle Min" name="dutycyclemin" [(ngModel)]="appInfo.minDutyCycle" #mindutyCycleInput="ngModel" type="text" sflDecimalLimit/>
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <input matInput placeholder="Duty Cycle Max" name="dutycyclemax" [(ngModel)]="appInfo.maxDutyCycle" #maxdutyCycleInput="ngModel" type="text" sflDecimalLimit/>
        </mat-form-field>
      </div>
      <div fxFlex="100">
        <mat-form-field>
          <textarea matInput placeholder="Notes" rows="5" name="notes" [(ngModel)]="appInfo.notes" #notesInput="ngModel"></textarea>
        </mat-form-field>
      </div>
    </div>
  </form>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button color="warn" type="submit" (click)="saveApplicationInfo('save')" name="save">
      Save
    </button>
    <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">
      Cancel
    </button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="submit" (click)="saveApplicationInfo('saveandnext')" name="saveandnext">
      Save And Next
    </button>
  </div>
</mat-dialog-actions>
