export class DesignQuotationList {
  constructor(
    public id?: number,
    public quotationNumber?: string,
    public salesOrderNumber?: number,
    public quotationStatusName?: string,
    public revisionName?: string,
    public salesAssociate?: string,
    public customerDTO?: Customer,
    public design?: number,
    public projectTitle?: string,
    public shipDate?: string,
    public dollarAmount?: number
  ) {}
}

export class SalesAssociate {
  constructor(public id?: number, public firstName?: string, public lastName?: number, public country?: string) {}
}

export class Customer {
  constructor(
    public addressLine1?: string,
    public addressLine2?: string,
    public code?: string,
    public contact?: string,
    public email?: string,
    public engCustAbrev?: string,
    public faxNumber?: string,
    public id?: 0,
    public name?: string,
    public phoneNumber?: string
  ) {}
}

export class QuotationPageable {
  constructor(public content?: DesignQuotationList[], public totalElements?: number, public totalPages?: number, public number?: number) {}
}
