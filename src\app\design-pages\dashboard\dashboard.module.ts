import {CUSTOM_ELEMENTS_SCHEMA, NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {SharedModule} from '../../shared/shared.module';
import {DashboardComponent} from '.';
import {dashboardRoutes} from './dashboard.route';
import {LayoutModule} from '@angular/cdk/layout';
import {VirtualScrollerModule} from 'ngx-virtual-scroller';
import {
  DashboardDesignEnggTrackerComponent
} from '../dashboard-design-engg-tracker/dashboard-design-engg-tracker.component';
import {
  DashboardDesignEnggTrackerRowColourComponent
} from '../dashboard-design-engg-tracker/dashboard-design-engg-tracker-row-colour/dashboard-design-engg-tracker-row-colour.component';
import {
  DashboardDesignEnggTrackerColumnColorComponent
} from '../dashboard-design-engg-tracker/dashboard-design-engg-tracker-column-color/dashboard-design-engg-tracker-column-color.component';

@NgModule({
  imports: [
    RouterModule.forChild(dashboardRoutes),
    SharedModule,
    LayoutModule,
    VirtualScrollerModule
  ],
  declarations: [
    DashboardComponent,
    DashboardDesignEnggTrackerComponent,
    DashboardDesignEnggTrackerRowColourComponent, DashboardDesignEnggTrackerColumnColorComponent
  ],
  entryComponents: [
    DashboardDesignEnggTrackerRowColourComponent, DashboardDesignEnggTrackerColumnColorComponent
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class DashboardModule {
}
