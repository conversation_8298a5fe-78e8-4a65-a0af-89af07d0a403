<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
  class="sfl-global-spinner-loader"
  [mode]="mode"
  [color]="color"
  [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<sfl-pre-loader *ngIf="!isDataAvailable"></sfl-pre-loader>
<div *ngIf="isDataAvailable">

  <!-- Work Action Required section -->
  <div class="pb-5" *ngFor="let workAction of ecoWorkFlowData['Notifications Required']; let i = index;">
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div  fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
      <strong class="pb-15" *ngIf="i == 0">{{workAction.title}}</strong>
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="workAction.status" name="criticalPart" (ngModelChange)="updateEcoWorkFlow(workAction,i)"> <p class="text-break"> {{workAction.description}} </p>  </mat-checkbox>
    </div>
      <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
        <strong class="pb-15" *ngIf="i == 0">Completed By</strong>
        <mat-form-field appearance="outline">
          <mat-select placeholder="{{workAction.description}} Completed By" [(ngModel)]="workAction.completedBy"  name="peerCompBy" [disabled]="!workAction.status" (ngModelChange)="updateWorkFlow(workAction, i)">
            <mat-option *ngFor="let peerCompBy of users" [value]="peerCompBy?.id">
              {{peerCompBy.firstName}} {{peerCompBy.lastName}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
        <strong class="pb-15" *ngIf="i == 0">Date</strong>
        <mat-form-field appearance="outline">
          <mat-label>Date</mat-label>
          <input matInput [matDatepicker]="peerReviewDateM" [max]="maxDate" placeholder="Date" name="peerReviewDateM" [(ngModel)]="workAction.completedDate" #peerReviewDateM="ngModel" autocomplete="off" [disabled]="!workAction.status" readonly (ngModelChange)="updateWorkFlow(workAction, i)">
          <mat-datepicker-toggle matSuffix [for]="peerReviewDateM"></mat-datepicker-toggle>
          <mat-datepicker #peerReviewDateM></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
      <div *ngIf="workAction.description === 'Planning Department notified if open jobs or inventory are affected'">
        <div fxLayoutAlign="space-between" class="pb-5" class="sub-action" *ngFor="let newWorkActionPlan of workAction?.childWorkFlows; let x = index;">
          <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
          <mat-checkbox color="warn" class="critical-check" [(ngModel)]="newWorkActionPlan.status" name="criticalPart" (ngModelChange)="updateEcoWorkFlowChild(newWorkActionPlan, x)"> {{newWorkActionPlan?.description}} </mat-checkbox>
        </div>
            <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
              <mat-form-field appearance="outline">
                <mat-select placeholder="{{newWorkActionPlan.description}} Completed By" [(ngModel)]="newWorkActionPlan.completedBy"  name="peerCompBy" [disabled]="!newWorkActionPlan.status" (ngModelChange)="updateWorkFlow(newWorkActionPlan, x)">
                  <mat-option *ngFor="let peerCompBy of users" [value]="peerCompBy?.id">
                    {{peerCompBy.firstName}} {{peerCompBy.lastName}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
              <mat-form-field appearance="outline">
                <mat-label>Date</mat-label>

                <input matInput [matDatepicker]="peerReviewDateM" [max]="maxDate" placeholder="Date" name="peerReviewDateM" [(ngModel)]="newWorkActionPlan.completedDate" #peerReviewDateM="ngModel" autocomplete="off" [disabled]="!newWorkActionPlan.status" readonly (ngModelChange)="updateWorkFlowChild(newWorkActionPlan, x)">
                <mat-datepicker-toggle matSuffix [for]="peerReviewDateM"></mat-datepicker-toggle>
                <mat-datepicker #peerReviewDateM></mat-datepicker>
              </mat-form-field>
            </div>

        </div>
      </div>
      <div  *ngIf="workAction.description === 'New Parts'">
        <div  fxLayoutAlign="space-between" class="pb-5" class="sub-action" *ngFor="let newWorkAction of workAction?.childWorkFlows; let y = index;">
          <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
          <mat-checkbox color="warn" class="critical-check" [(ngModel)]="newWorkAction.status" name="criticalPart" (ngModelChange)="updateEcoWorkFlowChild(newWorkAction, y)"> <p class="text-break-child">{{newWorkAction?.description}}</p> </mat-checkbox>
        </div>
            <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
              <strong class="pb-15" *ngIf="i == 0">Completed By</strong>
              <mat-form-field appearance="outline">
                <mat-select placeholder="{{newWorkAction.description}} Completed By" [(ngModel)]="newWorkAction.completedBy" name="peerCompBy" [disabled]="!newWorkAction.status" (ngModelChange)="updateWorkFlowChild(newWorkAction, y)">
                  <mat-option *ngFor="let peerCompBy of users" [value]="peerCompBy?.id">
                    {{peerCompBy.firstName}} {{peerCompBy.lastName}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
            <strong class="pb-15" *ngIf="i == 0">Date</strong>
            <mat-form-field appearance="outline">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="peerReviewDateM" [max]="maxDate" placeholder="Date" name="peerReviewDateM" [(ngModel)]="newWorkAction.completedDate" #peerReviewDateM="ngModel" autocomplete="off" [disabled]="!newWorkAction.status" readonly (ngModelChange)="updateWorkFlowChild(newWorkAction, y)">
              <mat-datepicker-toggle matSuffix [for]="peerReviewDateM"></mat-datepicker-toggle>
              <mat-datepicker #peerReviewDateM></mat-datepicker>
            </mat-form-field>
          </div>

        </div>
      </div>

  </div>

  <!-- <div class="pb-5" *ngFor="let workAction of ecoWorkFlowData['Closure']; let i = index;">
    <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
      <strong class="pb-15" *ngIf="i == 0">{{workAction.title}}</strong>
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="workAction.status" name="criticalPart" (ngModelChange)="updateEcoWorkFlow(workAction)"> {{workAction.description}} </mat-checkbox>
    </div>
  </div> -->

  <!-- Notification Required section -->
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5" *ngFor="let notification of ecoWorkFlowData['Closure']; let i = index;">
    <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
      <strong class="pb-15" *ngIf="i == 0">{{notification.title}}</strong>
      <mat-checkbox color="warn" class="critical-check" [(ngModel)]="notification.status" name="criticalPart" (ngModelChange)="updateEcoWorkFlow(notification, i)"> {{notification.description}} </mat-checkbox>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
      <strong class="pb-15" *ngIf="i == 0">Completed By</strong>
      <mat-form-field appearance="outline">
        <mat-select placeholder="{{notification.description}} Completed By" [(ngModel)]="notification.completedBy" name="peerCompBy" [disabled]="!notification.status" (ngModelChange)="updateWorkFlow(notification, i)">
          <mat-option *ngFor="let peerCompBy of users" [value]="peerCompBy?.id">
            {{peerCompBy.firstName}} {{peerCompBy.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div fxLayout="column" fxFlex.gt-lg="33" fxFlex.gt-md="33">
      <strong class="pb-15" *ngIf="i == 0">Date</strong>
      <mat-form-field appearance="outline">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="peerReviewDateM" [max]="maxDate" placeholder="Date" name="peerReviewDateM" [(ngModel)]="notification.completedDate" #peerReviewDateM="ngModel" autocomplete="off" [disabled]="!notification.status" readonly (ngModelChange)="updateWorkFlowChild(notification, i)">
        <mat-datepicker-toggle matSuffix [for]="peerReviewDateM"></mat-datepicker-toggle>
        <mat-datepicker #peerReviewDateM></mat-datepicker>
      </mat-form-field>
    </div>
  </div>

  <!-- Stackholders section -->
  <div fxLayout="row wrap" fxLayoutAlign="space-between" class="pb-5">
    <div fxLayout="column" fxFlex.gt-lg="40" fxFlex.gt-md="40">
      <strong class="pb-15">Final Stakeholder ECN Email Notice</strong>
      <div class="mb-10 cust_fields sfl-mat-list mat-list-selected-pn" fxFlex="grow">
        <div class="mb-10 cust_fields sfl-mat-list mat-list-selected-pn">
          <div fxLayout="row wrap" class="filename" *ngFor="let stake of stakeHolders;let i = index">
            <h4 fxFlex>{{stake?.userName}}</h4>
            <a (click)="confirmDelete(stake.stakeHolderId)">
              <div class="open-doc" fxLayoutAlign="end">
                <mat-icon>delete</mat-icon>
              </div>
            </a>
            <hr>
          </div>
        </div>
      </div>
      <mat-form-field appearance="outline" class="mb-10">
        <mat-select placeholder="Select Stakeholder" [(ngModel)]="dropDownStakeHolder" name="stakeHolder" (selectionChange)="onStakeHolderChange(dropDownStakeHolder)">
          <mat-option *ngFor="let stake of users" [value]="stake.id">
            {{stake.firstName}} {{stake.lastName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
</div>
