<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #powerCordVoltageForm="ngForm" (ngSubmit)="updatePowerCordVoltage()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Power Cord Voltage"
            [(ngModel)]="powerCordVoltage.value"
            name="value"
            #valueInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="valueInput.touched && valueInput.invalid">
          <small class="mat-text-warn" *ngIf="valueInput?.errors.required">Power Cord Voltage is required.</small>
          <small class="mat-text-warn" *ngIf="valueInput?.errors?.whitespace && !valueInput?.errors?.required">
            Invalid Power Cord Voltage.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="powerCordVoltage.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!powerCordVoltageForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
