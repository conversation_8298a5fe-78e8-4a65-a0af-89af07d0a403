<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form #userForm="ngForm" role="form">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="First name" name="firstName" [(ngModel)]="user.firstName" #firstNameInput="ngModel" required />
        <mat-error>This field is required</mat-error>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Last name" name="lastName" [(ngModel)]="user.lastName" #lastNameInput="ngModel" required />
        <mat-error>This field is required</mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field>
        <input matInput placeholder="Email" name="email" type="email" [(ngModel)]="user.email" #emailInput="ngModel" required email />
        <mat-error *ngIf="emailInput.touched && emailInput.invalid">
          <mat-error class="mat-text-warn" *ngIf="emailInput?.errors.required">Email is required.</mat-error>
          <mat-error class="mat-text-warn" *ngIf="emailInput?.errors.email && !emailInput?.errors.required">Email is not valid.</mat-error>
        </mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-select placeholder="Select user role" name="role" [(ngModel)]="user.authorities[0]" #roleInput="ngModel" required>
          <mat-option value="ROLE_ADMIN">Super Admin</mat-option>
          <mat-option value="ROLE_APP_ENG">App Engineering</mat-option>
          <mat-option value="ROLE_DES_ENG">Design Engineering</mat-option>
          <mat-option value="ROLE_SALES">Sales</mat-option>
        </mat-select>
        <mat-error>This field is required</mat-error>
      </mat-form-field>

      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <input matInput placeholder="Username" name="username" [(ngModel)]="user.login" #usernameInput="ngModel" required />
        <mat-error>This field is required</mat-error>
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Select User's Country" name="userCountry" [(ngModel)]="user.country" #countryInput="ngModel" required>
            <mat-option value="Usa">USA</mat-option>
            <mat-option value="Vietnam">Vietnam</mat-option>
          </mat-select>
          <mat-error>Country is required</mat-error>
        </mat-form-field>
      </div>
      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
        <input matInput placeholder="Initial" name="initialName" [(ngModel)]="user.initialName" />
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
        <input type="password" matInput [disabled]="user.androidUser===false" placeholder="Mobile App Password" name="mobilePassword" [(ngModel)]="user.mobilePassword" />
      </mat-form-field>
      <div fxFlex.gt-lg="24" class="field" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
        <mat-slide-toggle [value]="user.activated" name="activated" color="warn" [(ngModel)]="user.activated">Active</mat-slide-toggle>
        <mat-slide-toggle [value]="user.androidUser" name="androidUser" color="warn" [(ngModel)]="user.androidUser">Mobile User</mat-slide-toggle>
      </div>
    </div>
    <br />
  </mat-dialog-content>
  <hr />
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button color="warn" type="submit" (click)="saveUser()" [disabled]="userForm.invalid">Save</button>
      <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end" *ngIf="isUserExist">
      <button mat-raised-button color="warn" type="submit" (click)="sendForgotPasswordEmail()">Reset Password</button>
    </div>
  </mat-dialog-actions>
</form>
