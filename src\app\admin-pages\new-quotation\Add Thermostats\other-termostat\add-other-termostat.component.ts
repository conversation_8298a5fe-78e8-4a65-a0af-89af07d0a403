import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MatTableDataSource, MatDialog, MAT_DIALOG_DATA, MatDialogConfig, MatPaginator } from '@angular/material';
import { Subscription } from 'rxjs';
import { AddThermostatsComponent } from '../add-thermostats.component';
import { ThermostatList, ThermostatType } from '../../ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from '../../summary-sales-order.service';

@Component({
  selector: 'sfl-add-other-termostat',
  templateUrl: './add-other-termostat.component.html'
})
export class AddOthrerThermostatsComponent implements OnInit, OnDestroy {
  otherThermostat: ThermostatList;
  subscription: Subscription = new Subscription();
  thermostatType: ThermostatType[];

  constructor(
    public readonly dialogRef: MatDialogRef<AddThermostatsComponent>,
    private readonly matDialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly salesOrderSummaryService: SalesOrderSummaryService
  ) {}

  ngOnInit() {
    this.otherThermostat = new ThermostatList();
    this.getThermostatTypes();
  }

  getThermostatTypes() {
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatTypeList().subscribe((res: ThermostatType[]) => {
        this.thermostatType = res;
      })
    );
  }

  addOtherThermostat() {
    this.dialogRef.close(this.otherThermostat);
  }

  closeDialog() {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
