
import {catchError, map} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AppConfig } from 'src/app/app.config';
import { utils } from '../../../shared/helpers/app.helper';


@Injectable({
  providedIn: 'root'
})
export class CostReportService {
  constructor(private http: HttpClient) { }

  getCostReport(revisionId) {
    return this.http.get(AppConfig.COST_REPORT + revisionId).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }
  downloadCRExcel(soNumber, costReportObj) {
    return this.http.post(AppConfig.DOWNLOAD_CR_EXCEL_API + soNumber, costReportObj, { 'responseType': 'blob' }).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }
}
