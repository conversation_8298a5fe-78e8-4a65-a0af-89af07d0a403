<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #materialForm="ngForm" (ngSubmit)="updateMaterial()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Material"
            [(ngModel)]="material.material"
            name="material"
            #materialInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="materialInput.touched && materialInput.invalid">
          <small class="mat-text-warn" *ngIf="materialInput?.errors?.required">Material is required.</small>
          <small class="mat-text-warn" *ngIf="materialInput?.errors?.whitespace && !materialInput?.errors?.required">
            Invalid Material.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number"
            [(ngModel)]="material.partNumber"
            name="partnumber"
            #partnumberInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partnumberInput.touched && partnumberInput.invalid">
          <small class="mat-text-warn" *ngIf="partnumberInput?.errors?.required">Part number is required.</small>
          <small class="mat-text-warn" *ngIf="partnumberInput?.errors?.whitespace && !partnumberInput?.errors?.required">
            Invalid Part number.
          </small>
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Inventory"
            [(ngModel)]="material.inventory"
            name="inventory"
            #inventoryInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="inventoryInput.touched && inventoryInput.invalid">
          <small class="mat-text-warn" *ngIf="inventoryInput?.errors?.required">Inventory is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Cost / Sq. Ft."
            [(ngModel)]="material.costPerSq"
            name="costPerSq"
            #costPerSqInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="costPerSqInput.touched && costPerSqInput.invalid">
          <small class="mat-text-warn" *ngIf="costPerSqInput?.errors?.required">Cost Per Sq is required.</small>
        </div>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Max Temperature"
            [(ngModel)]="material.maxTemp"
            name="maxTemp"
            #maxTempInput="ngModel"
            required
            sflIsDecimal
            (change)="convertTeperature('celcius')"
          />
        </mat-form-field>
        <div *ngIf="maxTempInput.touched && maxTempInput.invalid">
          <small class="mat-text-warn" *ngIf="maxTempInput?.errors?.required">Max temperature is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Temperature Fahrenheit"
            [(ngModel)]="material.maxTempF"
            name="tempF"
            #tempFInput="ngModel"
            required
            sflIsDecimal
            (change)="convertTeperature('fahrenheit')"
          />
        </mat-form-field>
        <div *ngIf="tempFInput.touched && tempFInput.invalid">
          <small class="mat-text-warn" *ngIf="tempFInput?.errors?.required">Fahrenheit temperature is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Identifier"
            [(ngModel)]="material.identifier"
            name="identifier"
            required
            #identifierFInput="ngModel"
          />
        </mat-form-field>
        <div *ngIf="identifierFInput.touched && identifierFInput.invalid">
          <small class="mat-text-warn" *ngIf="identifierFInput?.errors?.required">Identifier is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="material.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <span>Material Image</span>
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <img class="jumper-product-img" src="{{ imageUrl }}" alt="Material Image" />
        </div>
      </div>
      <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <div class="mb-10" fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" (click)="addAttachment()">Add Image</button>
        </div>
        <div fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" [disabled]="!material?.imageUrl" (click)="removeAttachment()">
            Remove Image
          </button>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!materialForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
