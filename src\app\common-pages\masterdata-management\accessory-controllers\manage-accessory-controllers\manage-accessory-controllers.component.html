<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #accessoryControllerForm="ngForm" (ngSubmit)="updateAccessoryController()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Accessory Controller Name"
            [(ngModel)]="accessoryController.name"
            name="accessoryControllerName"
            #accessoryControllerNameInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="accessoryControllerNameInput.touched && accessoryControllerNameInput.invalid">
          <small class="mat-text-warn" *ngIf="accessoryControllerNameInput?.errors?.required">Accessory controller name is required.</small>
          <small
            class="mat-text-warn"
            *ngIf="accessoryControllerNameInput?.errors?.whitespace && !accessoryControllerNameInput?.errors?.required"
            >Invalid accessory controller name.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="accessoryController.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!accessoryControllerForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
