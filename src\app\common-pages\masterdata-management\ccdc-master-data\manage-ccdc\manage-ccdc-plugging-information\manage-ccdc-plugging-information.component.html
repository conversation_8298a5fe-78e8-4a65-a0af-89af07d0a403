<div fxLayout="column" fxLayoutAlign="space-between">

  <div class="sfl-loading" *ngIf="showLoader">
    <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
    </mat-progress-spinner>
  </div>
  <h2 mat-dialog-title>
    Plugging Information
    <mat-icon
      *ngIf="showReloadButton"
      class="open-doc sfl-pull-right"
      [matTooltip]="outDatedViewErrorMessage"
      color="warn"
      matTooltipClass="sfl-formula-tooltip"
      (click)="reloadPage()"
      id="refresh"
    >
      cached
    </mat-icon>
    <hr />
  </h2>
  <form #pluggingForm="ngForm">


    <mat-dialog-content>
      <div fxLayout="column" fxLayoutAlign="space-between">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Sleeving Type"
              name="sleevingType"
              (selectionChange)="onSleevingTypeChanged($event)"
              [(ngModel)]="pluggingInfoDto.sleevingTypeId"
            >
              <mat-option *ngFor="let sleevType of sleevingTypes" [value]="sleevType.id">{{ sleevType.name }}</mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Strain Relief"
              name="strainRelief"
              (selectionChange)="onStrainReliefChanged($event)"
              [(ngModel)]="pluggingInfoDto.strainReliefId"
            >
              <mat-option *ngFor="let strainRelief of strainReliefs" [value]="strainRelief.id">{{ strainRelief.name }}</mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Green Light"
              name="greenLight"
              (selectionChange)="onGreenLightChanged($event)"
              [(ngModel)]="pluggingInfoDto.greenLightId"
            >
              <mat-option *ngFor="let greenLight of greenLights" [value]="greenLight.id">{{ greenLight.name }}</mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Red Light"
              name="redLight"
              (selectionChange)="onRedLightChanged($event)"
              [(ngModel)]="pluggingInfoDto.redLightId"
            >
              <mat-option *ngFor="let redLight of redLights" [value]="redLight.id">{{ redLight.name }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherSleevingType">
            <input matInput placeholder="Other Sleeving Type" name="otherSleevingType" [(ngModel)]="pluggingInfoDto.otherSleevingType" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherSleevingType">
            <input
              matInput
              placeholder="Other Sleeving Type Part Nnumber"
              name="otherSleevingTypePartNumber"
              [(ngModel)]="pluggingInfoDto.otherSleevingTypePartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherSleevingType">
            <input
              matInput
              placeholder="Other Sleeving Type Cost"
              name="otherSleevingTypeCost"
              [(ngModel)]="pluggingInfoDto.otherSleevingTypeCost"
            />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherStrainRelief">
            <input matInput placeholder="Other Strain Relief" name="otherStrainRelief" [(ngModel)]="pluggingInfoDto.otherStrainRelief" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherStrainRelief">
            <input
              matInput
              placeholder="Other Strain Relief Part Number"
              name="otherStrainReliefPartNumber"
              [(ngModel)]="pluggingInfoDto.otherStrainReliefPartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherStrainRelief">
            <input
              matInput
              placeholder="Other Strain Relief Cost"
              name="otherStrainReliefCost"
              [(ngModel)]="pluggingInfoDto.otherStrainReliefCost"
            />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherGreenLight">
            <input matInput placeholder="Other Green Light " name="otherGreenLight" [(ngModel)]="pluggingInfoDto.otherGreenLight" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherGreenLight">
            <input
              matInput
              placeholder="Other Green Light Part Number "
              name="otherGreenLightPartNumber"
              [(ngModel)]="pluggingInfoDto.otherGreenLightPartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherGreenLight">
            <input
              matInput
              placeholder="Other Green Light Cost "
              name="otherGreenLightCost"
              [(ngModel)]="pluggingInfoDto.otherGreenLightCost"
            />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherRedLight">
            <input matInput placeholder="Other Red Light" name="otherRedLight" [(ngModel)]="pluggingInfoDto.otherRedLight" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherRedLight">
            <input
              matInput
              placeholder="Other Red Light Part Number"
              name="otherRedLightPartNumber"
              [(ngModel)]="pluggingInfoDto.otherRedLightPartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherRedLight">
            <input matInput placeholder="Other Red Light Cost" name="otherRedLightCost" [(ngModel)]="pluggingInfoDto.otherRedLightCost" />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="column wrap" fxFlex="49">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="69" fxFlex.gt-md="69" fxFlex.gt-sm="69" fxFlex.gt-xs="100">
                <mat-select
                  placeholder="Plug"
                  name="leadPlugName"
                  (selectionChange)="onLeadPlugChanged($event.value)"
                  [(ngModel)]="leadPlug.id"
                >
                  <mat-option *ngFor="let ledPlug of plugs" [value]="ledPlug.id">{{ ledPlug?.plugName }}</mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Lead Length ({{ measureUnit ? measureUnit : '' }})"
                  name="leadLength"
                  [(ngModel)]="pluggingInfoDto.leadPlugDTO.leadLength"
                />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="isOtherLeadPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Plug" name="otherLeadPlug" [(ngModel)]="pluggingInfoDto.otherPlug" />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Other Plug Part Number"
                  name="otherLeadPlugPartNumber"
                  [(ngModel)]="pluggingInfoDto.otherPlugPartNumber"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Plug Cost" name="otherLeadPlugCost" [(ngModel)]="pluggingInfoDto.otherPlugCost" />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherLeadPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Part Number" name="leadPartNumber" [(ngModel)]="leadPlug.partNumber" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Cost" name="leadPlugCost" [(ngModel)]="leadPlug.plugCost" readonly />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherLeadPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Amps" name="leadMaxAmps" [(ngModel)]="leadPlug.maxAmps" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Volts" name="leadMaxVolts" [(ngModel)]="leadPlug.maxVolts" readonly />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="69" fxFlex.gt-md="69" fxFlex.gt-sm="69" fxFlex.gt-xs="100">
                <mat-select
                  placeholder="Lead Type"
                  name="leadType"
                  (selectionChange)="onLeadTypeChanged($event.value)"
                  [(ngModel)]="leadType.id"
                >
                  <mat-option *ngFor="let leadType of leadTypesList" [value]="leadType?.id">{{ leadType?.leadName }}</mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Part Number" name="partnumber" [(ngModel)]="leadType.partNumber" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Temp (°C)" name="maxtemp" [(ngModel)]="leadType.maxTemp" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Cost/ft."
                  name="costft"
                  [ngModel]="leadType.costPerFoot | currency"
                  (ngModelChange)="leadType.costPerFoot = $event"
                  readonly
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Volts" name="maxvolts" [(ngModel)]="leadType.maxVolts" readonly />
              </mat-form-field>
            </div>
          </div>
        </div>
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <div>
              <label>Lead</label>
            </div>
            <img src="{{ leadPlugImageUrl }}" class="jumper-product-img" alt="No Image" />
          </div>
        </div>
      </div>
      <h2 mat-dialog-title>
        Jumper Information
        <hr />
      </h2>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayout="column wrap" fxFlex="49">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <mat-form-field>
              <mat-select
                placeholder="Connector"
                name="jumperPlugName"
                (selectionChange)="onJumperPlugChanged($event.value)"
                [(ngModel)]="jumperPlug.id"
              >
                <mat-option *ngFor="let jumPlug of plugs" [value]="jumPlug.id">{{ jumPlug?.plugName }}</mat-option>
              </mat-select>
            </mat-form-field>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="69" fxFlex.gt-md="69" fxFlex.gt-sm="69" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Jumper Length ({{ measureUnit ? measureUnit : '' }})"
                  name="jumperLength"
                  [(ngModel)]="pluggingInfoDto.jumperPlugDTO.jumperLength"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Qty" name="quantity" [(ngModel)]="pluggingInfoDto.jumperPlugDTO.quantity" />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="isOtherJumperPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Connector" name="otherConnector" [(ngModel)]="pluggingInfoDto.otherConnector" />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Other Connector Part Number"
                  name="otherConnectorPartNumber"
                  [(ngModel)]="pluggingInfoDto.otherConnectorPartNumber"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Other Connector Cost"
                  name="otherConnectorCost"
                  [(ngModel)]="pluggingInfoDto.otherConnectorCost"
                />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherJumperPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Part Number" name="jumperPartNumber" [(ngModel)]="jumperPlug.jumperPartNumber" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Cost" name="jumperPlugCost" [(ngModel)]="jumperPlug.plugCost" readonly />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherJumperPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Amps" name="jumperMaxAmps" [(ngModel)]="jumperPlug.maxAmps" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Volts" name="jumperMaxVolts" [(ngModel)]="jumperPlug.maxVolts" readonly />
              </mat-form-field>
            </div>
          </div>
        </div>
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <div>
              <label>Jumper</label>
            </div>
            <img src="{{ jumperConnectorImageUrl }}" class="jumper-product-img" alt="No Image" />
          </div>
        </div>
      </div>
      <div fxFlex="100">
        <mat-form-field>
          <textarea matInput placeholder="Notes" name="notes" [(ngModel)]="pluggingInfoDto.notes" rows="5"></textarea>
        </mat-form-field>
      </div>
    </div>
    </mat-dialog-content>
    <hr />
    <mat-dialog-actions fxLayoutAlign="space-between">
      <div fxLayoutAlign="end">
        <button mat-raised-button matStepperNext name="saveandnext">Next</button>
      </div>
    </mat-dialog-actions>
  </form>
  </div>
