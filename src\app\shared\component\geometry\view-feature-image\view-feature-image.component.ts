import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { GeometryComponent } from '../geometry.component';

@Component({
  selector: 'app-view-feature-image',
  templateUrl: './view-feature-image.component.html',
  styleUrls: ['./view-feature-image.component.css']
})
export class ViewFeatureImageComponent implements OnInit {

  imageUrl: string;

  constructor(
    public  readonly dialogRef: MatDialogRef<GeometryComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
    this.imageUrl = data.imageUrl;
   }

  ngOnInit() {
  }

  closeDialog(): void {
    this.dialogRef.close();
}

}
