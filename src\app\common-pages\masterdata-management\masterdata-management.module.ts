import {NgModule} from '@angular/core';
import {MatStepperModule} from '@angular/material';
import {RouterModule} from '@angular/router';
import {AccessoriesService} from 'src/app/admin-pages/accessories/accessories.service';
import {SensorService} from 'src/app/admin-pages/new-quotation/Add Sensors/add-sensors.service';
import {SharedModule} from 'src/app/shared/shared.module';
import {AccessoryControllersComponent} from './accessory-controllers/accessory-controllers.component';
import {
  ManageAccessoryControllersComponent
} from './accessory-controllers/manage-accessory-controllers/manage-accessory-controllers.component';
import {AccessoryMasterComponent} from './accessory/accessory-master.component';
import {ManageAccessoryComponent} from './accessory/manage-accessory/manage-accessory.component';
import {AlloyMasterComponent} from './alloy-master/alloy-master.component';
import {ManageAlloyMasterComponent} from './alloy-master/manage-alloy-master/manage-alloy-master.component';
import {AppEnggMasterDataComponent} from './app-engg-master-data/app-engg-master-data.component';
import {BhxGoldWireTapesComponent} from './bhx-gold-wire-tapes/bhx-gold-wire-tapes.component';
import {
  ManageBhxGoldWireTapeComponent
} from './bhx-gold-wire-tapes/manage-bhx-gold-wire-tape/manage-bhx-gold-wire-tape.component';
import {BhxMaterialMasterComponent} from './bhx-material-master/bhx-material-master.component';
import {
  ConfirmGroupingChangeComponent
} from './bhx-material-master/confirm-grouping-change/confirm-grouping-change.component';
import {
  FilterBhxMaterialComponent
} from './bhx-material-master/filter-bhx-material/filter-bhx-material/filter-bhx-material.component';
import {ManageBhxMaterialComponent} from './bhx-material-master/manage-bhx-material/manage-bhx-material.component';
import {CcdcMasterDataComponent} from './ccdc-master-data/ccdc-master-data.component';
import {
  ManageCcdcApplicationComponent
} from './ccdc-master-data/manage-ccdc/manage-ccdc-application/manage-ccdc-application/manage-ccdc-application.component';
import {
  ManageCcdcMaterialInformationComponent
} from './ccdc-master-data/manage-ccdc/manage-ccdc-material-information/manage-ccdc-material-information.component';
import {
  ManageCcdcPluggingInformationComponent
} from './ccdc-master-data/manage-ccdc/manage-ccdc-plugging-information/manage-ccdc-plugging-information.component';
import {ManageCcdcComponent} from './ccdc-master-data/manage-ccdc/manage-ccdc.component';
import {
  ManageClosureInformationComponent
} from './ccdc-master-data/manage-ccdc/manage-closure-information/manage-closure-information.component';
import {
  ManageSensorInformationComponent
} from './ccdc-master-data/manage-ccdc/manage-sensor-information/manage-sensor-information.component';
import {
  ManageThermostatInformationComponent
} from './ccdc-master-data/manage-ccdc/manage-thermostat-information/manage-thermostat-information.component';
import {ClosureMaterialMasterComponent} from './closure-material-master/closure-material-master.component';
import {
  ManageClosureMaterialComponent
} from './closure-material-master/manage-closure-material/manage-closure-material.component';
import {CurrencyMasterComponent} from './currency-master/currency-master.component';
import {ManageCurrencyMasterComponent} from './currency-master/manage-currency-master/manage-currency-master.component';
import {DepartmentsMastersComponent} from './departments-masters/departments-masters.component';
import {ManageDepartmentsComponent} from './departments-masters/manage-departments/manage-departments.component';
import {DesignEnggMasterDataComponent} from './design-engg-master-data/design-engg-master-data.component';
import {EcrStatusesComponent} from './ecr-statuses/ecr-statuses.component';
import {ManageEcrStatusesComponent} from './ecr-statuses/manage-ecr-statuses/manage-ecr-statuses.component';
import {EquationFormulaGeneratorComponent} from './equation-formula-generator/equation-formula-generator.component';
import {FeaturesMasterComponent} from './features-master/features-master.component';
import {GoldStandardTapeWidthComponent} from './gold-standard-tape-width/gold-standard-tape-width.component';
import {
  ManageGoldStandardTapeWidthComponent
} from './gold-standard-tape-width/manage-gold-standard-tape-width/manage-gold-standard-tape-width.component';
import {HeatingTapesComponent} from './heating-tapes/heating-tapes.component';
import {ManageHeatingTapesComponent} from './heating-tapes/manage-heating-tapes/manage-heating-tapes.component';
import {LabelMasterComponent} from './label-master/label-master.component';
import {
  LabelConfigurationGeneratorComponent
} from './label-master/manage-label-master/label-configuration-generator/label-configuration-generator.component';
import {ManageLabelMasterComponent} from './label-master/manage-label-master/manage-label-master.component';
import {LeadTypesComponent} from './lead-types/lead-types.component';
import {ManageLeadTypesComponent} from './lead-types/manage-lead-types/manage-lead-types.component';
import {MasterDataManagementComponent} from './master-data-management.component';
import {MasterdataManagementRoutes} from './masterdata-management.route';
import {ManageMaterialComponent} from './material-master/manage-material/manage-material.component';
import {MaterialMasterComponent} from './material-master/material-master.component';
import {
  ManageMaterialPropertyComponent
} from './material-properties/manage-material-property/manage-material-property.component';
import {MaterialPropertiesComponent} from './material-properties/material-properties.component';
import {ManagePlugLightsComponent} from './plug-lights/manage-plug-lights/manage-plug-lights.component';
import {PlugLightsComponent} from './plug-lights/plug-lights.component';
import {ManagePlugComponent} from './plug-master/manage-plug/manage-plug.component';
import {PlugMasterComponent} from './plug-master/plug-master.component';
import {ManagePowerCordAmpsComponent} from './power-cord-amps/manage-power-cord-amps/manage-power-cord-amps.component';
import {PowerCordAmpsComponent} from './power-cord-amps/power-cord-amps.component';
import {
  ManagePowerCordConnectorComponent
} from './power-cord-connectors/manage-power-cord-connector/manage-power-cord-connector.component';
import {PowerCordConnectorsComponent} from './power-cord-connectors/power-cord-connectors.component';
import {
  ManagePowerCordMaterialComponent
} from './power-cord-material/manage-power-cord-material/manage-power-cord-material.component';
import {PowerCordMaterialComponent} from './power-cord-material/power-cord-material.component';
import {
  ManagePowerCordOptionsComponent
} from './power-cord-options/manage-power-cord-options/manage-power-cord-options.component';
import {PowerCordOptionsComponent} from './power-cord-options/power-cord-options.component';
import {
  ManagePowerCordVoltagesComponent
} from './power-cord-voltages/manage-power-cord-voltages/manage-power-cord-voltages.component';
import {PowerCordVoltagesComponent} from './power-cord-voltages/power-cord-voltages.component';
import {ManagePqpFamilyComponent} from './pqp-family/manage-pqp-family/manage-pqp-family.component';
import {PqpFamilyComponent} from './pqp-family/pqp-family.component';
import {ProductTypeComponent} from './product-type/product-type.component';
import {ManageQuotStatusComponent} from './quotation-status/manage-quot-status/manage-quot-status.component';
import {QuotationStatusComponent} from './quotation-status/quotation-status.component';
import {
  ManageSensorConnectorComponent
} from './sensor-connector/manage-sensor-connector/manage-sensor-connector.component';
import {SensorConnectorComponent} from './sensor-connector/sensor-connector.component';
import {
  ManageSensorControlTypesComponent
} from './sensor-control-types/manage-sensor-control-types/manage-sensor-control-types.component';
import {SensorControlTypesComponent} from './sensor-control-types/sensor-control-types.component';
import {ManageSensorTypesComponent} from './sensor-types/manage-sensor-types/manage-sensor-types.component';
import {SensorTypesComponent} from './sensor-types/sensor-types.component';
import {ManageSleevingTypesComponent} from './sleeving-types/manage-sleeving-types/manage-sleeving-types.component';
import {SleevingTypesComponent} from './sleeving-types/sleeving-types.component';
import {ManageStrainReliefsComponent} from './strain-reliefs/manage-strain-reliefs/manage-strain-reliefs.component';
import {StrainReliefsComponent} from './strain-reliefs/strain-reliefs.component';
import {ManageThermostatListComponent} from './thermostat-list/manage-thermostat-list/manage-thermostat-list.component';
import {ThermostatListComponent} from './thermostat-list/thermostat-list.component';
import {
  ManageThermostatTypesComponent
} from './thermostat-types/manage-thermostat-types/manage-thermostat-types.component';
import {ThermostatTypesComponent} from './thermostat-types/thermostat-types.component';
import {ManageWarpsMasterComponent} from './warps-master/manage-warps-master/manage-warps-master.component';
import {WarpsMasterComponent} from './warps-master/warps-master.component';
import {SolidworksMasterComponent} from './solidworks-master/solidworks-master.component';
import {
  ManageSolidworksMasterComponent
} from './solidworks-master/manage-solidworks-master/manage-solidworks-master.component';
import {
  ManageSwBlockSensorInfoComponent
} from './solidworks-master/manage-solidworks-master/manage-sw-block-sensor-info/manage-sw-block-sensor-info.component';
import {
  ManageSwBlockThermostatInfoComponent
} from './solidworks-master/manage-solidworks-master/manage-sw-block-thermostat-info/manage-sw-block-thermostat-info.component';
import {PartNumberMasterDataComponent} from './part-number-master-data/part-number-master-data.component';
import {
  ManagePartNumberMasterComponent
} from './part-number-master-data/manage-part-number-master/manage-part-number-master.component';
import {EcoLineRevisionMasterComponent} from './eco-line-revision-master/eco-line-revision-master.component';
import {
  ManageEcoLineRevisionMasterComponent
} from './eco-line-revision-master/manage-eco-line-revision-master/manage-eco-line-revision-master.component';
import {
  DuplicateBhxMaterialMasterComponent
} from './bhx-material-master/manage-bhx-material/duplicate-bhx-material-master/duplicate-bhx-material-master.component';
import {LaborMasterComponent} from './labor-master/labor-master.component';
import {ManageLaborMasterComponent} from './labor-master/manage-labor-master/manage-labor-master.component';
import {
  EstimatedEngReleaseMasterComponent
} from './estimated-eng-release-master/estimated-eng-release-master.component';
import {
  ManageEstimatedEngReleaseMasterComponent
} from './estimated-eng-release-master/manage-estimated-eng-release-master/manage-estimated-eng-release-master.component';

@NgModule({
  imports: [RouterModule.forChild(MasterdataManagementRoutes), SharedModule, MatStepperModule],
  entryComponents: [
    ManageQuotStatusComponent,
    ManageAccessoryComponent,
    ManageMaterialComponent,
    ManageMaterialPropertyComponent,
    ManageClosureMaterialComponent,
    ManageEcoLineRevisionMasterComponent,
    ManagePlugComponent,
    ManageThermostatListComponent,
    ManageAccessoryControllersComponent,
    ManagePqpFamilyComponent,
    ManageSolidworksMasterComponent,
    ManagePowerCordConnectorComponent,
    ManageSleevingTypesComponent,
    ManageStrainReliefsComponent,
    ManagePlugLightsComponent,
    ManageGoldStandardTapeWidthComponent,
    ManageBhxGoldWireTapeComponent,
    ManageAlloyMasterComponent,
    ManageHeatingTapesComponent,
    ManageDepartmentsComponent,
    ManageEcrStatusesComponent,
    ManageBhxMaterialComponent,
    DuplicateBhxMaterialMasterComponent,
    ConfirmGroupingChangeComponent,
    ManageSensorConnectorComponent,
    ManagePowerCordMaterialComponent,
    ManagePowerCordVoltagesComponent,
    ManagePowerCordAmpsComponent,
    ManagePowerCordOptionsComponent,
    ManageSensorTypesComponent,
    ManageSensorControlTypesComponent,
    ManageWarpsMasterComponent,
    EquationFormulaGeneratorComponent,
    ManageThermostatTypesComponent,
    ManageLeadTypesComponent,
    ManageCurrencyMasterComponent,
    FilterBhxMaterialComponent,
    ManageCcdcApplicationComponent,
    ManageCcdcComponent,
    ManageLabelMasterComponent,
    LabelConfigurationGeneratorComponent,
    ManagePartNumberMasterComponent,
    LaborMasterComponent,
    ManageLaborMasterComponent,
    EstimatedEngReleaseMasterComponent,
    ManageEstimatedEngReleaseMasterComponent
  ],
  declarations: [
    MasterDataManagementComponent,
    AppEnggMasterDataComponent,
    DesignEnggMasterDataComponent,
    QuotationStatusComponent,
    AccessoryMasterComponent,
    MaterialMasterComponent,
    PlugMasterComponent,
    ClosureMaterialMasterComponent,
    MaterialPropertiesComponent,
    ThermostatListComponent,
    FeaturesMasterComponent,
    AccessoryControllersComponent,
    ProductTypeComponent,
    ManageQuotStatusComponent,
    ManageAccessoryComponent,
    ManageMaterialComponent,
    ManageMaterialPropertyComponent,
    ManageClosureMaterialComponent,
    ManageEcoLineRevisionMasterComponent,
    ManagePlugComponent,
    ManageThermostatListComponent,
    ManageAccessoryControllersComponent,
    PqpFamilyComponent,
    ManagePqpFamilyComponent,
    PowerCordConnectorsComponent,
    ManagePowerCordConnectorComponent,
    SleevingTypesComponent,
    ManageSleevingTypesComponent,
    StrainReliefsComponent,
    ManageStrainReliefsComponent,
    PlugLightsComponent,
    ManagePlugLightsComponent,
    GoldStandardTapeWidthComponent,
    ManageGoldStandardTapeWidthComponent,
    BhxGoldWireTapesComponent,
    ManageBhxGoldWireTapeComponent,
    AlloyMasterComponent,
    ManageAlloyMasterComponent,
    HeatingTapesComponent,
    ManageHeatingTapesComponent,
    DepartmentsMastersComponent,
    ManageDepartmentsComponent,
    EcrStatusesComponent,
    ManageEcrStatusesComponent,
    BhxMaterialMasterComponent,
    ManageBhxMaterialComponent,
    DuplicateBhxMaterialMasterComponent,
    ConfirmGroupingChangeComponent,
    SensorConnectorComponent,
    ManageSensorConnectorComponent,
    PowerCordMaterialComponent,
    ManagePowerCordMaterialComponent,
    PowerCordVoltagesComponent,
    ManagePowerCordVoltagesComponent,
    PowerCordAmpsComponent,
    ManagePowerCordAmpsComponent,
    PowerCordOptionsComponent,
    ManagePowerCordOptionsComponent,
    SensorTypesComponent,
    ManageSensorTypesComponent,
    SensorControlTypesComponent,
    ManageSensorControlTypesComponent,
    WarpsMasterComponent,
    ManageWarpsMasterComponent,
    EquationFormulaGeneratorComponent,
    ThermostatTypesComponent,
    ManageThermostatTypesComponent,
    LeadTypesComponent,
    ManageLeadTypesComponent,
    CurrencyMasterComponent,
    ManageCurrencyMasterComponent,
    FilterBhxMaterialComponent,
    CcdcMasterDataComponent,
    ManageCcdcComponent,
    ManageCcdcApplicationComponent,
    ManageCcdcPluggingInformationComponent,
    ManageCcdcMaterialInformationComponent,
    ManageClosureInformationComponent,
    ManageSensorInformationComponent,
    ManageThermostatInformationComponent,
    LabelMasterComponent,
    ManageLabelMasterComponent,
    LabelConfigurationGeneratorComponent,
    SolidworksMasterComponent,
    ManageSolidworksMasterComponent,
    ManageSwBlockSensorInfoComponent,
    ManageSwBlockThermostatInfoComponent,
    PartNumberMasterDataComponent,
    ManagePartNumberMasterComponent,
    EcoLineRevisionMasterComponent,
    ManageEcoLineRevisionMasterComponent,
    DuplicateBhxMaterialMasterComponent,
    LaborMasterComponent,
    ManageLaborMasterComponent,
    EstimatedEngReleaseMasterComponent,
    ManageEstimatedEngReleaseMasterComponent
  ],
  providers: [AccessoriesService, SensorService]
})
export class MasterDataModule {
}
