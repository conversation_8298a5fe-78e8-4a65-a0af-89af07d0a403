import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { MatDialogRef } from '@angular/material';
import { EcrPartnumberDto, EcrPartNumberExistDTO } from '../ecr-management.model';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Subscription } from 'rxjs';
import { EcrManagementService } from '../ecr-management.service';

@Component({
  selector: 'sfl-add-partnumber',
  templateUrl: './add-partnumber.component.html'
})
export class AddPartnumberComponent implements OnInit, OnDestroy {
  partnumberDto = new EcrPartnumberDto();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  subscription = new Subscription();
  ecrPartNumberDTO : EcrPartNumberExistDTO;

  constructor(private readonly sweetAlertService: SweetAlertService, private ecrManagementService: EcrManagementService, private dialogRef: MatDialogRef<AddPartnumberComponent>, private snakbarService: SnakbarService) {}

  ngOnInit() {}

  add() {
    this.partnumberDto.partNumber = this.partnumberDto.partNumber.trim();
    if (this.partnumberDto.partNumber && this.partnumberDto.partNumber.match(Values.ECR_PartNumber_Regex)) {
      this.dialogRef.close(this.partnumberDto);
    } else {
      this.snakbarService.error(Messages.error.part_error_msg);
    }
  }

  checkExistsingPartNumber() {
    this.subscription.add(this.ecrManagementService.checkPartNumberExists(this.partnumberDto).subscribe((res: EcrPartNumberExistDTO) => {
      this.add();
      if (res.found === true) {
        this.sweetAlertService.checkPartNumberExist(res.message);
      } else {
       this.sweetAlertService.checkPartNumberDontExist(res.message);
      }
    }));
  }

  closeDialog(): void {
    this.partnumberDto = new EcrPartnumberDto();
    this.dialogRef.close(this.partnumberDto);
  }

  ngOnDestroy() {}
}
