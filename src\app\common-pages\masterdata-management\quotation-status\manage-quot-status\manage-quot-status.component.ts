import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { QuotationStatusMaster } from '../../masterdata-management.model';
import { Values } from 'src/app/shared/constants/values.constants';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Variable } from 'src/app/shared/constants/Variable.constants';

@Component({
  selector: 'sfl-manage-quot-status',
  templateUrl: './manage-quot-status.component.html'
})
export class ManageQuotStatusComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  quotStatus: QuotationStatusMaster;
  _data: any;
  quotTypes = Values.QuotStatusTypes;
  highlightedColors = Values.highlightColors;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public dialogRef: MatDialogRef<ManageQuotStatusComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this._data = data;
  }

  ngOnInit() {
    this.quotStatus = this._data.isEdit ? Object.assign({}, this._data.quotStatus) : new QuotationStatusMaster();
    this._data.isEdit ? (this.title = 'Update Quotation Status') : (this.title = 'Add Quotation Status');
  }

  updateQuotStatus() {
    this.showLoader = true;
    if (this._data.isEdit) {
      this.subscription.add(
        this.masterDataService.updateQuotationStatus(this.quotStatus).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addQuotationStatus(this.quotStatus).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
