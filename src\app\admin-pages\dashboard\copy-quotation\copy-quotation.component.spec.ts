import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CopyQuotationComponent } from './copy-quotation.component';

describe('CopyQuotationComponent', () => {
  let component: CopyQuotationComponent;
  let fixture: ComponentFixture<CopyQuotationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CopyQuotationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CopyQuotationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
