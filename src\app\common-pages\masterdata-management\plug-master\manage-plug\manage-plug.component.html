<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #plugMasterForm="ngForm" (ngSubmit)="updatePlug()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Name"
            [(ngModel)]="plug.plugName"
            name="plugName"
            #plugNameInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="plugNameInput.touched && plugNameInput.invalid">
          <small class="mat-text-warn" *ngIf="plugNameInput?.errors?.required">Plug name is required.</small>
          <small class="mat-text-warn" *ngIf="plugNameInput?.errors?.whitespace && !plugNameInput?.errors?.required">
            Invalid plug name.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number"
            [(ngModel)]="plug.partNumber"
            name="partNumber"
            #partNumberInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partNumberInput.touched && partNumberInput.invalid">
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors?.required">Plug part number is required.</small>
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors?.whitespace && !partNumberInput?.errors?.required">
            Invalid plug part number.
          </small>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Plug Cost" [(ngModel)]="plug.plugCost" name="plugCost" #plugCostInput="ngModel" sflIsDecimal />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="20" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothCE" color="warn" [(ngModel)]="plug.clothCe">Cloth CE</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="21" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothUL" color="warn" [(ngModel)]="plug.clothUl">Cloth UL</mat-checkbox>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Jacket Type" name="jacketType" [(ngModel)]="plug.jacketType" #jacketTypeSelect="ngModel">
            <mat-option *ngFor="let jacket of jacketTypes" [value]="jacket.id">
              {{ jacket?.value }}
            </mat-option>
            <mat-option>None</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Jumper Part Number"
            [(ngModel)]="plug.jumperPartNumber"
            name="jumperPN"
            #jumperPNInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="jumperPNInput.touched && jumperPNInput.invalid">
          <small class="mat-text-warn" *ngIf="jumperPNInput?.errors?.required">Jumper part number is required.</small>
          <small class="mat-text-warn" *ngIf="jumperPNInput?.errors?.whitespace && !jumperPNInput?.errors?.required">
            Invalid jumper part number.
          </small>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Max Amps" [(ngModel)]="plug.maxAmps" name="maxAmps" #maxAmpsInput="ngModel" required sflIsDecimal />
        </mat-form-field>
        <div *ngIf="maxAmpsInput.touched && maxAmpsInput.invalid">
          <small class="mat-text-warn" *ngIf="maxAmpsInput?.errors?.required">Max Amps is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Max Volts"
            [(ngModel)]="plug.maxVolts"
            name="maxVolts"
            #maxVoltsInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="maxVoltsInput.touched && maxVoltsInput.invalid">
          <small class="mat-text-warn" *ngIf="maxVoltsInput?.errors?.required">Jumper part number is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="plug.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <span>Plug Image</span>
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <img class="jumper-product-img" src="{{ imageUrl }}" alt="Plug Image" />
        </div>
      </div>
      <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <div class="mb-10" fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" (click)="addAttachment()">Add Image</button>
        </div>
        <div fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" [disabled]="!plug?.imageUrl" (click)="removeAttachment()">
            Remove Image
          </button>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!plugMasterForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
