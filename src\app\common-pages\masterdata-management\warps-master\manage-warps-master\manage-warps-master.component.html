<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #plugMasterForm="ngForm" (ngSubmit)="updateWarp()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Tape Width"
            [(ngModel)]="warp.tapeWidth"
            name="tapeWidth"
            #tapeWidthInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="tapeWidthInput.touched && tapeWidthInput.invalid">
          <small class="mat-text-warn" *ngIf="tapeWidthInput?.errors?.required">Tape Width is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Warp" [(ngModel)]="warp.warp" name="warp" #warpInput="ngModel" required sflIsDecimal />
        </mat-form-field>
        <div *ngIf="warpInput.touched && warpInput.invalid">
          <small class="mat-text-warn" *ngIf="warpInput?.errors?.required">Warp is required.</small>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="warp.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!plugMasterForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
