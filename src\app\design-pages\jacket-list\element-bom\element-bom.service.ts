
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';



@Injectable({ providedIn: 'root' })
export class ElementBomService {

    constructor(private http: HttpClient) { }

    saveElementBOM(review) {
        return this.http.post(AppConfig.ELEMENT_BOM_SAVE, review).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

    updateElementBOM(review, jacketId) {
      return this.http.delete(AppConfig.ELEMENT_BOM_UPDATE + jacketId, review).pipe(
          map(utils.extractData),
          catchError(utils.handleError),);
  }

    getElementBomByJacketId(jacketId) {
        return this.http.get(AppConfig.ELEMENT_BOM_SAVE + '/jacket/' + jacketId).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }
}
