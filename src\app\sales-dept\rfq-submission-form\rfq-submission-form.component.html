<div class="container" fxLayout="column" xmlns="http://www.w3.org/1999/html">
  <div fxLayout="row wrap" class="mb-10 cust_fields">
    <div fxFlex fxLayoutAlign="start center">
      <mat-card-title>{{ headingTitle }}</mat-card-title>
    </div>
  </div>
  <form class="forms_form" [formGroup]="RFQSubmissionForm" (ngSubmit)="saveRFQForm()">
    <div fxLayout="column">
      <div fxLayout="row wrap">
        <div fxLayout="column" fxFlex="40">
          <div class="labels">
            <h4>Quotation Number</h4>
          </div>
        </div>
        <div fxLayout="column" fxFlex="60">
          <mat-form-field>
            <mat-label>Quotation Number</mat-label>
            <input matInput formControlName="quotationNumber" (change)="getQuotationNumber()"
                   placeholder="Quotation Number" [(ngModel)]="quotationNumber" sflNoWhiteSpaces required/>
          </mat-form-field>
          <mat-error *ngIf="isControlHasError('quotationNumber', 'required')"> Quote number is required!</mat-error>
        </div>
      </div>

      <div fxLayout="row wrap" formGroupName="customerDTO">
        <div fxLayout="column" fxFlex="40">
          <div class="labels">
            <h4>Customer Name</h4>
          </div>
        </div>
        <div fxLayout="column" fxFlex="60">
          <mat-form-field>
            <mat-label>Customer Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter Customer Name" sflNoWhiteSpaces required/>
          </mat-form-field>
          <mat-error *ngIf="isControlHasError('name', 'required')"> Customer name is required!</mat-error>
        </div>
      </div>
      <div fxLayout="row wrap">
        <div fxLayout="column" fxFlex="40">
          <div class="labels">
            <h4>Project Title</h4>
          </div>
        </div>
        <div fxLayout="column" fxFlex="60">
          <mat-form-field>
            <mat-label>Project Title</mat-label>
            <input matInput formControlName="projectTitle" placeholder="Project Title" sflNoWhiteSpaces/>
          </mat-form-field>
          <mat-error *ngIf="isControlHasError('projectTitle', 'required')"> Project title is required!</mat-error>
        </div>
      </div>

      <div fxLayout="row wrap">
        <div fxLayout="column" fxFlex="40">
          <div class="labels">
            <h4>Account Manager</h4>
          </div>
        </div>
        <div fxLayout="column" fxFlex="60">
          <mat-form-field>
            <mat-label>Select Account Manager</mat-label>
            <mat-select formControlName="accountMgrid" placeholder="Account Mgr." required>
              <mat-option *ngFor="let option of accountMgr" [value]="option.id">
                {{ option?.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-error *ngIf="isControlHasError('accountMgrid', 'required')"> Account manager is required!</mat-error>
        </div>
      </div>
      <div fxLayout="row wrap">
        <div fxLayout="column" fxFlex="40">
          <div class="labels">
            <h4>Date Submitted to App</h4>
          </div>
        </div>
        <div fxLayout="column" fxFlex="60">
          <mat-form-field>
            <mat-label>Date Submitted to App</mat-label>
            <input
              matInput
              [(ngModel)]="appSubmittedDate"
              formControlName="dateSubmittedDateApp"
              [matDatepicker]="dateSubmittedDateApp"
              placeholder="Date Submitted to App"
            />
            <mat-datepicker-toggle matSuffix [for]="dateSubmittedDateApp"></mat-datepicker-toggle>
            <mat-datepicker #dateSubmittedDateApp></mat-datepicker>
          </mat-form-field>
          <mat-error *ngIf="isControlHasError('dateSubmittedDateApp', 'required')"> Project title is required!</mat-error>
        </div>
      </div>
      <div fxLayout="row wrap" formGroupName="customerDTO">
        <div fxLayout="column" fxFlex="40">
          <div class="labels">
            <h4>SQT Link</h4>
          </div>
        </div>
        <div fxLayout="column" fxFlex="60">
          <mat-form-field>
            <textarea matInput formControlName="sqtLink"
                      placeholder="SQT Link"
                      name="sqtLink"></textarea>
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="row wrap" formGroupName="customerDTO">
        <div fxLayout="column" fxFlex="40">
          <div class="labels">
            <h4>Notes</h4>
          </div>
        </div>
        <div fxLayout="column" fxFlex="60">
          <mat-form-field>
            <textarea matInput formControlName="notes"
                      placeholder="Notes"
                      name="notes"></textarea>
          </mat-form-field>
        </div>
      </div>

<!--      <div fxLayout="row wrap">-->
<!--        <div fxLayout="column" fxFlex="40">-->
<!--          <div class="lable_project_title">-->
<!--            <h4>File Upload</h4>-->
<!--          </div>-->
<!--        </div>-->
<!--        <div class="form-group form_file">-->
<!--          <input type="file" (change)="readUrl($event)"/>-->
<!--          <p class="font-weight-bold">{{ value }}</p>-->
<!--        </div>-->
<!--      </div>-->

      <div fxLayout="row wrap">
        <div fxLayout="column" fxFlex="40">
          <div class="mb-20" appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
            <mat-checkbox formControlName="sendCopyOfResponse" color="warn" [disabled]="true">Send Me a Copy Of My
              Responses
            </mat-checkbox>
          </div>
        </div>
      </div>

      <div fxLayout="row" fxFlex fxLayoutAlign="space-between" class="mt-10">
        <div fxLayoutAlign="start">
          <button mat-raised-button color="warn" type="submit">Submit</button>
        </div>
        <div fxLayoutAlign="end">
          <button mat-raised-button type="reset">Cancel</button>
        </div>
      </div>
    </div>
  </form>

</div>
