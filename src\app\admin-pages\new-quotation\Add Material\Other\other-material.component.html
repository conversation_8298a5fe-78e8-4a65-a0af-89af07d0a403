<form #otherMaterialForm="ngForm">
    <h2 mat-dialog-title>Other Material Information
        <hr>
    </h2>
    <mat-dialog-content class="cust_table">

        <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="materialLayer.layerName === 'Facing'">
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Facing" [(ngModel)]="materialLayer.otherFacing" name="otherFacing" required>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Facing Part Number" [(ngModel)]="materialLayer.otherFacingPartNumber"
                    name="otherFacingPartNumber">
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Facing Cost" [(ngModel)]="materialLayer.otherFacingCost"
                    name="otherFacingCost">
            </mat-form-field>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="materialLayer.layerName === 'Insulation'">
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Insulation" [(ngModel)]="materialLayer.otherInsulation"
                    name="otherInsulation" required>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Insulation Part Number"
                    [(ngModel)]="materialLayer.otherInsulationPartNumber" name="otherInsulationPartNumber">
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Insulation Cost" [(ngModel)]="materialLayer.otherInsulationCost"
                    name="otherInsulationCost">
            </mat-form-field>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="materialLayer.layerName === 'Liner'">
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Liner" [(ngModel)]="materialLayer.otherLiner" name="otherLiner" required>
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Liner Part Number" [(ngModel)]="materialLayer.otherLinerPartNumber"
                    name="otherLinerPartNumber">
            </mat-form-field>
            <mat-form-field fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Liner Cost" [(ngModel)]="materialLayer.otherLinerCost"
                    name="otherLinerCost">
            </mat-form-field>
        </div>

    </mat-dialog-content>
    <hr>
    <mat-dialog-actions>
        <button mat-raised-button color="warn" type="submit" [disabled]="otherMaterialForm.form.invalid" (click)="addOtherMaterial()" name="add">Add</button>
        <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
    </mat-dialog-actions>
</form>