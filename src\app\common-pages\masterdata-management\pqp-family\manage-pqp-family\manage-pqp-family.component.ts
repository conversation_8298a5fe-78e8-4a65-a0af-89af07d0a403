import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { PQPFamilyMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-pqp-family',
  templateUrl: './manage-pqp-family.component.html'
})
export class ManagePqpFamilyComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  pqpFamily: PQPFamilyMaster;
  _data: PQPFamilyMaster;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public dialogRef: MatDialogRef<ManagePqpFamilyComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this._data = data;
  }

  ngOnInit() {
    this.pqpFamily = this._data.id ? Object.assign({}, this._data) : new PQPFamilyMaster();
    this._data.id ? (this.title = 'Update PQP Family') : (this.title = 'Add PQP Family');
  }

  updatePQPFamily() {
    this.showLoader = true;
    if (this._data.id) {
      this.subscription.add(
        this.masterDataService.updatePQPFamily(this.pqpFamily).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addPQPFamily(this.pqpFamily).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
