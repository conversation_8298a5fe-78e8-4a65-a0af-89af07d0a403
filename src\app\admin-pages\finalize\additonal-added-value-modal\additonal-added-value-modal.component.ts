import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { JacketAccessoriesList } from '../../accessories/accessories.model';

@Component({
  selector: 'sfl-additonal-added-value-modal',
  templateUrl: './additonal-added-value-modal.component.html',
})
export class AdditonalAddedValueModalComponent {
  jacket: JacketAccessoriesList;

  constructor(
    public dialogRef: MatDialogRef<AdditonalAddedValueModalComponent>,
    @Inject(MAT_DIALOG_DATA) data) {
    this.jacket = data;
  }

  closeDialog() {
    this.dialogRef.close();
  }

}
