<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-select placeholder="User Name" [(ngModel)]="importFilter.userId.value" name="name">
            <mat-option *ngFor="let user of users" [value]="user?.id"> {{ user?.firstName }} {{ user?.lastName }} </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Template Name</mat-label>
          <input matInput placeholder="Search Template Name" [(ngModel)]="importFilter.name.value" />
        </mat-form-field>

        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Description</mat-label>
          <input matInput placeholder="Search Description" [(ngModel)]="importFilter.description.value" />
        </mat-form-field>
      </div>
      &nbsp;&nbsp; <button mat-raised-button type="submit" color="warn" class="w-auto text-wrap add-btn my-1" (click)="addFilter()">Search</button>&nbsp;&nbsp;
      <button mat-raised-button type="button" color="warn" class="w-auto text-wrap add-btn my-1" (click)="resetFilter()">Reset</button>
    </div>

    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="ccdcMaterialMasterDataSource"
        (matSortChange)="getCcdcMaterialMasterSorting($event)"
      >
        <ng-container matColumnDef="user">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="22"> User </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="22"> {{ element?.userId | getFirstNameLastName: users }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="templateName">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="32"> Template Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="32"> {{ element?.name }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="35"> Description </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="35"> {{ element?.description }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editCCDC(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteCcdcTemplateData(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="ccdcMaterialMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: ccdcMaterialMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!ccdcMaterialMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getCcdcPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
