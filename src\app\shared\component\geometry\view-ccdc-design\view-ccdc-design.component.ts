import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef, MatTableDataSource} from '@angular/material';
import {Subscription} from 'rxjs';
import {AccessoriesComponent} from 'src/app/admin-pages/accessories/accessories.component';
import {AddApplicationService} from 'src/app/admin-pages/new-quotation/Add Applicaton/add-application.service';
import {AddClosureService} from 'src/app/admin-pages/new-quotation/Add Closure/add-closure.service';
import {SensorService} from 'src/app/admin-pages/new-quotation/Add Sensors/add-sensors.service';
import {
  AccountManager,
  ApplicationInfo,
  CcdcWorkflow,
  ClosureInformation,
  ClosureMaterial,
  MaterialInfoReq,
  MaterialLayers,
  PluggingInformation,
  SensorInformation,
  SensorInformationObject,
  ThermostatInfo,
  ThermostatList,
  Units
} from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import {SalesOrderSummaryService} from 'src/app/admin-pages/new-quotation/summary-sales-order.service';
import {SalesAssociate} from 'src/app/quote-tracker/quote-tracker.model';
import {SharedService} from 'src/app/shared';
import {Values} from 'src/app/shared/constants/values.constants';
import {ViewCCDCModalData} from '../geometry.model';
import {ManageUnitsService} from '../../../../admin-pages/new-quotation/manage-units/manage-units.service';

@Component({
  selector: 'app-view-ccdc-design',
  templateUrl: './view-ccdc-design.component.html',
  styleUrls: ['./view-ccdc-design.component.css']
})
export class ViewCcdcDesignComponent implements OnInit {
  subscription = new Subscription();
  workflow: CcdcWorkflow = new CcdcWorkflow();
  jacketGroupId: number;
  jacketGroupIds = [];
  quotationId: number;
  closureInfo: ClosureInformation;
  closureMaterial: ClosureMaterial;
  materials: ClosureMaterial[];
  showLoader = false;
  appInfo: ApplicationInfo;
  contentMotions = Values.ContentMotionsConst;
  salesassociates: SalesAssociate[];
  jacketTypes = Values.JacketTypeConst;
  phaseTypes = Values.PhaseTypeConst;
  appInfos: ApplicationInfo;
  measureUnit: string;
  tempUnit: string;
  pluggingInformation: PluggingInformation;
  materialData: MaterialInfoReq;
  accountManagers: AccountManager[];
  materialDataSource = new MatTableDataSource<MaterialLayers>();
  materialdisplayedColumns = ['layerName', 'material', 'partNumber', 'maxTemp', 'costPerSq'];
  sensorInfoObject: SensorInformationObject;
  SensorsdisplayedColumns = ['sensorType', 'sensorLocation', 'sensorConnector', 'sensorLeadLength', 'sensorTempType'];
  sensorsDataSource = new MatTableDataSource<SensorInformation>();
  thermostatInfo: ThermostatInfo;
  thermostatdisplayedColumns = [
    'thermostatType',
    'installationMethod',
    'openTemp',
    'partNumber',
    'cost',
    'manualReset',
    'openOnRise',
    'usaStock',
    'vietnamStock'
  ];
  thermostatdataSource = new MatTableDataSource<ThermostatList>();

  constructor(
    private readonly salesOrderSummaryService: SalesOrderSummaryService,
    private readonly addApplicationService: AddApplicationService,
    private readonly sharedService: SharedService,
    private readonly addClosureService: AddClosureService,
    private readonly sensorService: SensorService,
    private manageUnitService: ManageUnitsService,
    public readonly dialogRef: MatDialogRef<AccessoriesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ViewCCDCModalData
  ) {
    this.jacketGroupId = data.jacketGroupId;
  }

  jacket = [
    {id: 1, name: 'Default', measurementUnit: 'CM', tempUnit: '°C'},
    {id: 2, name: 'Ground', measurementUnit: 'MM', tempUnit: '°F'},
    {id: 3, name: 'Non-Ground', measurementUnit: 'IN', tempUnit: '°C'}
  ];

  ngOnInit() {
    this.getWorkFlowByJacketGroupId(this.jacketGroupId);
    this.getApplicationInfo(this.jacketGroupId);
    this.getPluggingInformationByJacketGroupId(this.jacketGroupId);
    this.getMaterialByJacketGroupId(this.jacketGroupId);
    this.getClosureMaterialList();
    this.getSensorsListByJacketGroupId(this.jacketGroupId);
    this.getThermostatByJacketGroupId(this.jacketGroupId);
    this.getMeasurementUnit();
  }

  // gets the application info details
  getApplicationInfo(jacketGroupId: number) {
    this.subscription.add(
      this.addApplicationService.getApplicationInfoByJacketGroup(jacketGroupId).subscribe((res: ApplicationInfo) => {
        if (res) {
          this.appInfo = res;
          if (this.appInfo.contentMotion) {
            this.appInfo.contentMotion = this.contentMotions.find(e => e.id === this.appInfo.contentMotion).value;
          }

          if (this.appInfo.jacketType) {
            this.appInfo.jacketType = this.jacketTypes.find(e => e.id === this.appInfo.jacketType).value;
          }
          if (this.appInfo.phase) {
            this.appInfo.phase = this.phaseTypes.find(e => e.id === this.appInfo.phase).value;
          }
          this.appInfos = res;
        }
      })
    );
  }

  getWorkFlowByJacketGroupId(jacketGroupId: number) {
    this.subscription.add(
      this.salesOrderSummaryService.getWorkFlowByJacketGroupId(jacketGroupId).subscribe((res: CcdcWorkflow) => {
        this.workflow = res;
      })
    );
  }

  getPluggingInformationByJacketGroupId(jacketGroupId: number) {
    this.subscription.add(
      this.salesOrderSummaryService.getPluggingInformationByJacketGroupId(jacketGroupId).subscribe((res: PluggingInformation) => {
        this.pluggingInformation = res;
      })
    );
  }

  getMaterialByJacketGroupId(jacketGroupId: number) {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getAllMaterial(jacketGroupId).subscribe(
        (res: MaterialInfoReq) => {
          if (res.materialInfoDTOList.length > 0 || res.notes) {
            this.materialData = res;
            this.materialDataSource.data = this.materialData.materialInfoDTOList;
          } else {
            this.materialData = null;
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  getClosureInfo(jacketGroupId: number) {
    this.subscription.add(
      this.addClosureService.getClosureInfoByJacketGroup(jacketGroupId).subscribe((res: ClosureInformation) => {
        if (res) {
          this.closureInfo = res;
          if (this.materials.length > 0) {
            if (this.closureInfo.closureMaterialId) {
              this.closureMaterial = this.materials.find(x => x.id === this.closureInfo.closureMaterialId);
            }
          }
        }
      })
    );
  }

  getClosureMaterialList() {
    this.subscription.add(
      this.addClosureService.getClosureMaterialList().subscribe((res: ClosureMaterial[]) => {
        this.materials = res;
        this.getClosureInfo(this.jacketGroupId);
      })
    );
  }

  getSensorsListByJacketGroupId(jacketGroupId: number) {
    this.subscription.add(
      this.sensorService.getSensorList(jacketGroupId).subscribe((res: SensorInformationObject) => {
        if (res.sensorsInformationDTOList.length > 0 || res.notes) {
          this.sensorInfoObject = res;
          this.sensorsDataSource.data = this.sensorInfoObject.sensorsInformationDTOList;
        } else {
          this.sensorInfoObject = null;
        }
      })
    );
  }

  getThermostatByJacketGroupId(jacketGroupId: number) {
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatByJacketGroupId(jacketGroupId).subscribe((res: ThermostatInfo) => {
        if (res.thermostatInformationDTOList.length > 0 || res.notes) {
          this.thermostatInfo = res;
          this.thermostatdataSource = new MatTableDataSource<ThermostatList>(this.thermostatInfo.thermostatInformationDTOList);
        } else {
          this.thermostatInfo = null;
        }
      })
    );
  }

  getAccountManagerMaster() {
    this.showLoader = true;
    return new Promise(resolve => {
      this.salesOrderSummaryService.getAccountManagers().subscribe(
        (res: AccountManager[]) => {
          this.accountManagers = res;
          this.showLoader = false;
          resolve();
        },
        () => {
          this.showLoader = false;
        }
      );
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.measureUnit = res.measurementUnit;
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }
}
