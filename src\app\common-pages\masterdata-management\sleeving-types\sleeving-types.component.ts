import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { GenericPageable, SleevingTypesAndStrainReliefsMaster, SleevingTypesAndStrainReliefsFilter } from '../masterdata-management.model';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SnakbarService, SweetAlertService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageSleevingTypesComponent } from './manage-sleeving-types/manage-sleeving-types.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-sleeving-types',
  templateUrl: './sleeving-types.component.html'
})
export class SleevingTypesComponent implements OnInit, OnDestroy {
  pageTitle = 'Sleeving Types Master';
  sleevingTypes: SleevingTypesAndStrainReliefsMaster;
  sleevingTypesPageable: GenericPageable<SleevingTypesAndStrainReliefsMaster>;
  sleevingTypesDataSource = new MatTableDataSource<SleevingTypesAndStrainReliefsMaster>();
  sleevingTypesColumns = DisplayColumns.Cols.SleevingTypesAndStrainReliefsCols;

  dataSource = new MatTableDataSource<SleevingTypesAndStrainReliefsMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  sleevingTypesFilter: SleevingTypesAndStrainReliefsFilter = new SleevingTypesAndStrainReliefsFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldPartNumber = Values.FilterFields.partNumber;
  filterFieldName = Values.FilterFields.name;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getSleevingTypesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new sleeving type
  addSleevingType() {
    this.editSleevingTypes(new SleevingTypesAndStrainReliefsMaster());
  }

  // used to add filter to sleeving type listing
  async addFilter() {
    this.filter = [
      { key: this.filterFieldPartNumber, value: !this.sleevingTypesFilter.partNumber ? '' : this.sleevingTypesFilter.partNumber },
      { key: this.filterFieldName, value: !this.sleevingTypesFilter.name ? '' : this.sleevingTypesFilter.name }
    ];
    this.getSleevingTypesMasterData(this.initialPageIndex, this.pageSize);
  }

  // used to clear filter of sleeving type listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber,
        value:
          fieldToClear === this.filterFieldPartNumber ? (this.sleevingTypesFilter.partNumber = '') : this.sleevingTypesFilter.partNumber
      },
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.sleevingTypesFilter.name = '') : this.sleevingTypesFilter.name
      }
    ];
    this.getSleevingTypesMasterData(this.initialPageIndex, this.pageSize);
  }

  getSleevingTypesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getSleevingTypesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<SleevingTypesAndStrainReliefsMaster>) => {
          this.sleevingTypesPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createSleevingTypesTable(this.sleevingTypesPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createSleevingTypesTable(serviceRequestList: GenericPageable<SleevingTypesAndStrainReliefsMaster>) {
    this.sleevingTypesDataSource.data = serviceRequestList.content;
  }

  getSleevingTypesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getSleevingTypesMasterData(this.pageIndex, this.pageSize);
  }

  getSleevingTypesSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getSleevingTypesMasterData(this.pageIndex, this.pageSize);
  }

  editSleevingTypes(sleevingType: SleevingTypesAndStrainReliefsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = sleevingType;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-sleeving-Type-master-model';
    const dialogRef = this.matDialog.open(ManageSleevingTypesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          sleevingType.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getSleevingTypesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteSleevingTypes(sleevingTypeId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteSleevingType(sleevingTypeId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getSleevingTypesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
