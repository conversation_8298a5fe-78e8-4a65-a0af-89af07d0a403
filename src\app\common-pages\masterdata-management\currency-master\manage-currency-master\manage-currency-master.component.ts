import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { CurrencyMaster } from '../../masterdata-management.model';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-app-manage-currency-master',
  templateUrl: './manage-currency-master.component.html'
})
export class ManageCurrencyMasterComponent implements OnInit {
  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  _data: any;
  currencyMaterial: CurrencyMaster;
  formData = new FormData();
  constructor(
    public dialogRef: MatDialogRef<ManageCurrencyMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private masterDataService: MasterdataManagementService
  ) {
    this.currencyMaterial = data;
  }

  ngOnInit() {
    this.currencyMaterial = this.currencyMaterial.id
      ? Object.assign({}, this.currencyMaterial)
      : new CurrencyMaster();
    this.currencyMaterial.id
      ? (this.title = 'Update Currency Material')
      : (this.title = 'Add Currency Material');
  }

  updateMaterialProperty() {
    this.showLoader = true;
    if (this.currencyMaterial.id) {
      this.subscription.add(
        this.masterDataService
          .editCurrencyMaterial(this.currencyMaterial)
          .subscribe(
            () => {
              this.showLoader = false;
              this.dialogRef.close(true);
            },
            (error) => {
              this.showLoader = false;
            }
          )
      );
    } else {
      this.subscription.add(
        this.masterDataService
          .addCurrencyMaterial(this.currencyMaterial)
          .subscribe(
            (res) => {
              this.showLoader = false;
              this.dialogRef.close(true);
            },
            (error) => {
              this.showLoader = false;
            }
          )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }
}
