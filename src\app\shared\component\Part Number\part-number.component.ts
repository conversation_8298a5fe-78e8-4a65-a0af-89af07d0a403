import { SelectionModel } from '@angular/cdk/collections';
import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogConfig, MatDialogRef, MatTableDataSource } from '@angular/material';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { SnakbarService } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { JacketListComponent } from '../../../design-pages/jacket-list/jacket-list.component';
import {
  APIRESULT,
  CcdcParts,
  FilePath,
  FolderPathRes,
  Jacket,
  JacketGroup,
  JacketPartNumberInfo,
  SelectedJacketFilePathDTO
} from '../../../design-pages/jacket-list/jacket-list.model';
import { JacketListService } from '../../../design-pages/jacket-list/jacket-list.service';
import { Messages } from '../../constants/messages.constants';
import { Values } from '../../constants/values.constants';
import { Variable } from '../../constants/Variable.constants';
import { SharedService } from '../../service/shared.service';
import { SweetAlertService } from '../../service/sweet-alert.service';
import { SolidworksDownloadComponent } from '../solidworks-download/solidworks-download.component';
import {
  FilePathAlreadyExistModalComponent
} from './file-path-already-exist-modal/file-path-already-exist-modal.component';
import { GenerateRootPNModalComponent } from './generate-root-pn-modal/generate-root-pn-modal.component';
import { RootPNModel } from './generate-root-pn-modal/rootPNModel';
import {
  NonStandardPnConfirmationModalComponent
} from './non-standard-pn-confirmation-modal/non-standard-pn-confirmation-modal.component';
import { OpenFilePathEditModalComponent } from './open-file-path-edit-modal/open-file-path-edit-modal.component';
import { PNErrorComponent } from './partnumber-error.component';

@Component({
  selector: 'sfl-part-number',
  templateUrl: './part-number.component.html'
})
export class PartNumberComponent implements OnInit, OnDestroy {
  quotationId: number;
  jacketPartInfo: JacketPartNumberInfo;
  jacketGroups: JacketGroup[];
  subscription = new Subscription();
  jgId: number;
  displayedColumns = ['select', 'nonStandardPartNumber', 'jacketName', 'briskHeatPN', 'reference', 'path', 'bhc', 'gcode', 'mcode'];
  dataSource = new MatTableDataSource<Jacket>();
  selection = new SelectionModel<Jacket>(true, []);
  isNoDataFound = this.dataSource.connect().pipe(map(data => data.length === 0));
  jacketList: Jacket[] = [];
  oldJacket = new Jacket(); // jacket values will be copied onFocus , and in error it would be restored with old values
  isPartError = false;
  errorList = Array<string>();
  showLoader = false;
  filePath: FilePath;
  solidWorksNotInstalled = false;
  apiRespnseError = false;
  notAllPartNumber = true;
  madeIn = 'USA';
  serviceErrorMessage: string;
  rootPathUL: string;
  rootPathNonUL: string;
  rootPathSilicone: string;
  designFileLocation: string;

  // rootDirectoryUSA = '\\\\***********\\Shared\\Public\\Engineering\\GLOBAL\\VFS\\CLOTH\\';
  rootDirectoryUSAUL = '\\\\**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES\\CLOTH\\UL\\';
  rootDirectoryUSANONUL = '\\\\**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES\\CLOTH\\NON-UL\\';

  // rootDirectoryVIETNAM = '\\\\***********\\BHC-PUBLIC\\ENGINEERING\\GLOBAL\\VFS\\CLOTH\\';
  rootDirectoryVIETNAMUL = '\\\\10.145.14.21\\PUBLIC\\ENGINEERING\\DESIGN ENGINEERING\\01 DESIGN FILES\\CLOTH\\UL\\';
  rootDirectoryVIETNAMNONUL = '\\\\10.145.14.21\\PUBLIC\\ENGINEERING\\DESIGN ENGINEERING\\01 DESIGN FILES\\CLOTH\\NON-UL\\';

  rootDirectorySiliconeUSA = '\\\\**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES\\SILICONE\\';
  rootDirectorySiliconeVietnam = '\\\\10.145.14.21\\PUBLIC\\ENGINEERING\\DESIGN ENGINEERING\\01 DESIGN FILES\\SILICONE\\';

  rootDirectoryInseperableUSA = '\\\\**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\01 DESIGN FILES\\CLOTH\\INSEPERABLE\\';
  rootDirectoryInseperableVietnam = '\\\\10.145.14.21\\PUBLIC\\ENGINEERING\\DESIGN ENGINEERING\\01 DESIGN FILES\\CLOTH\\INSEPERABLE\\';

  // Pre defined design files namely Part 1 and Part 2 files for USA and Vietnam
  designFileVietnam = '\\\\10.145.14.21\\PUBLIC\\ENGINEERING\\Pre Defined Design Files\\';
  designFileUSA = '\\\\**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering\\Design Engineering\\02 KNOWLEDGE DATABASE\\Drawing Templates\\Border Templates\\';
  phaseTypes = Values.PhaseTypeConst;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  showDash = false;
  pathsFoundInDriveAndNotInBHX = '';
  showLoaders: boolean;
  jackets: Jacket = new Jacket();

  constructor(
    private matDialog: MatDialog,
    private jacketListService: JacketListService,
    private snakbarService: SnakbarService,
    public dialogRef: MatDialogRef<JacketListComponent>,
    private router: Router,
    private readonly sharedService: SharedService,
    private readonly sweetAlertService: SweetAlertService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.quotationId = data.quotationId;
  }

  ngOnInit() {
    this.filePath = new FilePath();
    this.jacketPartInfo = new JacketPartNumberInfo();
    this.jacketPartInfo.ccdcParts = new CcdcParts();
    this.jacketGroups = new Array<JacketGroup>();
    this.getJacketGroupByQuotationId();
  }

  selectMadeIn(value) {
    this.madeIn = value;
  }

  getJacketGroupByQuotationId() {
    this.subscription.add(
      this.jacketListService.getJacketGroupByQuotationId(this.quotationId).subscribe((res: JacketGroup[]) => {
        if (res.length > 0) {
          this.jacketGroups = res;
          this.onJacketGroupChange(this.jacketGroups[0].id);
        }
      })
    );
  }

  onJacketGroupChange(value) {
    this.showLoader = true;
    this.notAllPartNumber = false;
    this.isPartError = false;
    this.jgId = value;
    this.subscription.add(
      this.jacketListService.getJacketsByJacketGroupId(value).subscribe(
        (res: JacketPartNumberInfo) => {
          if (res) {
            this.jacketPartInfo = res;
            this.dataSource.data = this.jacketPartInfo.jacketList;
            if (this.jacketPartInfo.ccdcParts.phase) {
              this.jacketPartInfo.ccdcParts.phase = this.phaseTypes.find(e => e.id === this.jacketPartInfo.ccdcParts.phase).value;
            }
            this.showHideDashSeperator();
            this.checkAllPartNumberPresent(this.jacketPartInfo.jacketList);
            this.selectAllDefault();
            this.showLoader = false;
            if (!this.jacketPartInfo.rootPN && this.jacketPartInfo.jacketList.length) {
              this.showModalToGenerateRootPN();
            }
          } else {
            this.showLoader = false;
          }
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  showModalToGenerateRootPN() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-generate-rootpn-model';
    const dialogRef = this.matDialog.open(GenerateRootPNModalComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      const rootPNModel: RootPNModel = res;
      if (res.rootPN && res.openModel) {
        if (res.openModel === 'createRootPN') {
          this.jacketPartInfo.rootPN = res.rootPN;
          this.updatePartNumbersFromRootPN(false);
        } else if (res.openModel === 'generateNonStandardPN') {
          this.jacketPartInfo.rootPN = res.rootPN;
          this.updatePartNumbersFromRootPN(true);
        } else {
          return;
        }
      }
    });
  }

  checkAllPartNumberPresent(jacketList) {
    for (let i = 0; i < jacketList.length; i++) {
      if (this.jacketPartInfo.jacketList[i].partNumber === null) {
        this.notAllPartNumber = true;
        break;
      }
    }
  }

  selectAllDefault() {
    this.selection.clear();
    this.dataSource.data.forEach(item => {
      this.selection.select(item);
    });
  }

  generatePartNumber() {
    this.showLoader = true;
    this.notAllPartNumber = false;
    this.jacketList = this.selection.selected;
    this.isPartError = false;
    const jacketIdList = [];
    this.jacketList.forEach(jacket => {
      jacketIdList.push(jacket.id);
    });
    if (jacketIdList.length > 0) {
      const jacketDesignObj = {
        jacketIds: jacketIdList,
        controlType: this.jacketPartInfo.ccdcParts.controlType ? this.jacketPartInfo.ccdcParts.controlType : '',
        rootPN: this.jacketPartInfo.rootPN,
        ul: this.jacketPartInfo.ccdcParts.ul,
        repeat: this.oldJacket.repeat
      };
      this.subscription.add(
        this.jacketListService.generatePartNumber(this.jgId, false, jacketDesignObj).subscribe(
          (res: JacketPartNumberInfo) => {
            if (res) {
              this.jacketPartInfo.rootPN = res.rootPN;
              this.jacketPartInfo.jacketList = res.jacketList;
              this.jacketPartInfo.errorList = res.errorList;
              this.dataSource.data = this.jacketPartInfo.jacketList;
              if (this.jacketPartInfo.errorList.length > 0) {
                this.isPartError = true;
              }
              this.showHideDashSeperator();
              this.checkAllPartNumberPresent(this.jacketPartInfo.jacketList);
              this.showLoader = false;
            } else {
              this.showLoader = false;
            }
          },
          error => {
            this.showLoader = false;
            if (error.applicationStatusCode === 3001) {
              this.snakbarService.error(error.message);
            }
          }
        )
      );
      this.selection.clear();
    }
  }

  resetPartNumber() {
    this.showLoaders = true;
    this.jacketList = this.selection.selected;
    const jacketIdLists = [];
    this.jacketList.forEach(jacket => {
      jacketIdLists.push(jacket.id);
    });
    if (jacketIdLists.length > 0) {
      const jacketDesignObj = {
        jacketIds: jacketIdLists,
        controlType: this.jacketPartInfo.ccdcParts.controlType ? this.jacketPartInfo.ccdcParts.controlType : '',
        rootPN: this.jacketPartInfo.rootPN,
        ul: this.jacketPartInfo.ccdcParts.ul,
        repeat: this.oldJacket.repeat
      };
      this.subscription.add(
        this.jacketListService.resetPartNumber(this.jgId, jacketDesignObj).subscribe(
          (res: JacketPartNumberInfo) => {
            if (res) {
              this.jacketPartInfo.rootPN = res.rootPN;
              this.jacketPartInfo.jacketList = res.jacketList;
              this.getJacketGroupByQuotationId();
              this.snakbarService.success(Messages.JacketList.part_number_reset);
              this.showLoaders = false;
            } else {
              this.showLoader = false;
            }
          },
          error => {
            this.showLoaders = false;
            if (error.applicationStatusCode === 3001) {
              this.snakbarService.error(error.message);
            }
          }
        )
      );
      this.selection.clear();
    }
  }

  async resetRoot() {
    if (await this.sweetAlertService.rootPartNumberResetWarning()) {
      this.showLoader = true;
      this.notAllPartNumber = false;
      this.jacketList = this.selection.selected;
      this.isPartError = false;
      const jacketIdList = this.jacketList.map(jacket => jacket.id);
      if (jacketIdList.length > 0) {
        const jacketDesignObj = {
          jacketIds: jacketIdList,
          controlType: this.jacketPartInfo.ccdcParts.controlType ? this.jacketPartInfo.ccdcParts.controlType : '',
          rootPN: this.jacketPartInfo.rootPN,
          ul: this.jacketPartInfo.ccdcParts.ul,
          repeat: this.oldJacket.repeat
        };
        this.subscription.add(
          this.jacketListService.generatePartNumber(this.jgId, true, jacketDesignObj).subscribe(
            (res: JacketPartNumberInfo) => {
              if (res) {
                this.jacketPartInfo.rootPN = res.rootPN;
                this.jacketPartInfo.jacketList = res.jacketList;
                this.jacketPartInfo.errorList = res.errorList;
                this.dataSource.data = this.jacketPartInfo.jacketList;
                if (this.jacketPartInfo.errorList.length > 0) {
                  this.isPartError = true;
                }
                this.showHideDashSeperator();
                this.checkAllPartNumberPresent(this.jacketPartInfo.jacketList);
                this.showLoader = false;
              } else {
                this.showLoader = false;
              }
            },
            error => {
              this.showLoader = false;
            }
          )
        );
        this.selection.clear();
      }
    }
  }

  copyJacketOldValues(jacket: Jacket) {
    this.oldJacket = Object.assign({}, jacket);
  }

  updatePartNumber(jacket: Jacket, nonStandardPN: boolean) {
    this.showLoader = true;
    let isStandardPartNumber = false;
    if ((jacket.tempPartNumber !== undefined && jacket.tempPartNumber !== null && jacket.tempPartNumber) || (jacket.tempSequenceNumber && jacket.tempSequenceNumber !== undefined && jacket.tempSequenceNumber !== null)) {
      if (!nonStandardPN) {
        if (this.jacketPartInfo.ccdcParts.ul) {
          if (jacket.tempPartNumber.match(Values.PartNumber_Regex) || jacket.tempSequenceNumber.match(Values.UL_Sequence_Regex)) {
            isStandardPartNumber = true;
            jacket.nonStandardPartNumber = false;
          }
        } else {
          // Non UL
          if (jacket.tempPartNumber.match(Values.PartNumber_Regex) || jacket.tempSequenceNumber.match(Values.NON_UL_Sequence_Regex)) {
            isStandardPartNumber = true;
            jacket.nonStandardPartNumber = false;
          }
        }
      }
      // if the partNumber is standard or it's already non-standard PN# then update without showing pop-up
      if (isStandardPartNumber || jacket.nonStandardPartNumber) {
        this.updateJacketPartNumber(jacket);
      } else {
        this.showNonStandardPNConfirmationModal(jacket);
        this.showLoader = false;
        this.selection.clear();
      }
    } else {
      jacket.tempPartNumber = jacket.partNumber;
      jacket.tempSequenceNumber = '';
      this.updateJacketPartNumber(jacket);
      this.showLoader = false;
    }
  }

  async checkNonStarndardPN(isGenerated: boolean) {
    let tempPart: Jacket = this.selection.selected.find(jacket => jacket.tempPartNumber === null || jacket.tempPartNumber === undefined);
    if (tempPart && (tempPart.rootPartNumber === undefined || tempPart.rootPartNumber === null)) {
      this.showModalToGenerateRootPN();
      this.snakbarService.error('Please generate root part number first');
      return;
    } else {
      this.generateNonStandardPN(isGenerated);
    }
  }

  async generateNonStandardPN(isGenerated: boolean) {
    if (isGenerated) {
      if (await this.sweetAlertService.confirmNonStandardPartNumber() === false) {
        return;
      } else {
        for (const data of this.selection.selected) {
          this.jackets = this.jacketPartInfo.jacketList.find(jackets => jackets.id === data.id);
          if (isGenerated) {
            this.jackets.nonStandardPartNumber = true;
          } else {
            this.jackets.nonStandardPartNumber = false;
          }
          if (this.jackets.partNumber !== null && !this.jackets.tempPartNumber.includes('-')) {
            this.jackets.tempPartNumber = this.jackets.tempPartNumber.concat('-');
          }
          this.updatePartNumber(this.jackets, true);
        }
      }
    }
  }

  private updateJacketPartNumber(jacket: Jacket) {
    jacket.ul = this.jacketPartInfo.ccdcParts.ul;
    jacket.tempControlType = this.jacketPartInfo.ccdcParts.controlType ? this.jacketPartInfo.ccdcParts.controlType : '';
    jacket.rootPN = this.jacketPartInfo.rootPN;
    this.subscription.add(
      this.jacketListService.updatePartNumber(jacket, this.quotationId).subscribe(
        (res: Jacket) => {
          if (res) {
            const index = this.jacketPartInfo.jacketList.indexOf(jacket);
            this.jacketPartInfo.jacketList[index] = res;
            this.dataSource.data = this.jacketPartInfo.jacketList;
            this.jacketList = new Array();
            this.selection.clear();
            this.showHideDashSeperator();
          }
          this.showLoader = false;
        },
        error => {
          this.onJacketGroupChange(this.jgId);
          if (error.applicationStatusCode === 1222 || error.applicationStatusCode === 1243 || error.applicationStatusCode === 1244) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false;
        }
      )
    );
  }

  showNonStandardPNConfirmationModal(jacket: Jacket) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-non-standard-pn-modal';
    const dialogRef = this.matDialog.open(NonStandardPnConfirmationModalComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        jacket.nonStandardPartNumber = true;
        this.updateJacketPartNumber(jacket);
      }
    });
  }


  regenerateNonStandardPN() {
    this.showLoaders = true;
    this.jacketList = this.selection.selected;
    const jacketIdLists = [];
    this.jacketList.forEach(jacket => {
      jacketIdLists.push(jacket.id);
    });
    if (jacketIdLists.length > 0) {
      const jacketDesignObj = {
        jacketIds: jacketIdLists,
        controlType: this.jacketPartInfo.ccdcParts.controlType ? this.jacketPartInfo.ccdcParts.controlType : '',
        rootPN: this.jacketPartInfo.rootPN,
        ul: this.jacketPartInfo.ccdcParts.ul,
        repeat: this.oldJacket.repeat
      };
      this.subscription.add(
        this.jacketListService.resetPartNumber(this.jgId, jacketDesignObj).subscribe(
          (res: JacketPartNumberInfo) => {
            if (res) {
              this.jacketPartInfo.rootPN = res.rootPN;
              this.jacketPartInfo.jacketList = res.jacketList;
              this.getJacketGroupByQuotationId();
              this.showLoaders = false;
            } else {
              this.showLoader = false;
            }
          },
          error => {
            this.showLoaders = false;
          }
        )
      );
      this.selection.clear();
    }
  }

  showNonStandardPNConfirmation() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-non-standard-pn-modal';
    const dialogRef = this.matDialog.open(NonStandardPnConfirmationModalComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.regenerateNonStandardPN();
      }
    });
  }


  openFilePathEditModal(jackets: Jacket[]) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xxlg;
    if (jackets.length === 1) {
      matDataConfig.data = {partFilePath: jackets[0].selectedPartFilePath, manufacturedIn: this.madeIn};
    } else {
      matDataConfig.data = {manufacturedIn: this.madeIn};
    }
    matDataConfig.panelClass = 'sfl-open-file-path-edit-modal';
    this.matDialog
      .open(OpenFilePathEditModalComponent, matDataConfig)
      .afterClosed()
      .subscribe(
        (filePath: string) => {
          this.showLoader = true;
          if (filePath) {
            const pathArray = filePath.split('\\').filter(char => char !== '');
            const pathUpToLastFolder = pathArray.slice(0, pathArray.length - 1);
            const newPath =
              '\\\\' +
              pathUpToLastFolder
                .join('\\')
                .concat('\\')
                .concat(pathArray[pathArray.length - 1]);
            this.updateJacketsWithSelectedPath(jackets, newPath);
            this.showLoader = false;
          } else {
            this.showLoader = false;
          }
        },
        error => {
          this.showLoader = false;
          this.onJacketGroupChange(this.jgId);
        }
      );
  }

  openFilePathEditModalWithSingleElement(jacket: Jacket) {
    const jacketArray = new Array<Jacket>();
    jacketArray.push(jacket);
    this.openFilePathEditModal(jacketArray);
  }

  private updateJacketsWithSelectedPath(jackets: Array<Jacket>, filePath: string) {
    const selectedJacketsWithCommonFilePath = new SelectedJacketFilePathDTO(filePath, jackets);
    this.subscription.add(
      this.jacketListService.updateMultipleJackets(selectedJacketsWithCommonFilePath).subscribe(
        (updatedJackets: Jacket[]) => {
          this.onJacketGroupChange(this.jgId);
        },
        err => {
          this.onJacketGroupChange(this.jgId);
          this.showLoader = false;
        }
      )
    );
  }

  updateBHCPartNumber(jacket: Jacket) {
    this.showLoader = true;
    jacket.ul = this.jacketPartInfo.ccdcParts.ul;
    jacket.tempControlType = this.jacketPartInfo.ccdcParts.controlType ? this.jacketPartInfo.ccdcParts.controlType : '';
    this.subscription.add(
      this.jacketListService.updateBHCPartNumber(jacket).subscribe(
        (res: Jacket) => {
          if (res) {
            this.jacketPartInfo.jacketList[this.jacketPartInfo.jacketList.indexOf(jacket)] = res;
            this.dataSource.data = this.jacketPartInfo.jacketList;
            this.jacketList = new Array();
            this.selectAllDefault();
            this.showHideDashSeperator();
            this.showLoader = false;
          } else {
            this.showLoader = false;
          }
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  updatePartNumbersFromRootPN(updateNonStandardPart: boolean) {
    this.showLoader = true;
    const rootPNObj = {
      ul: this.jacketPartInfo.ccdcParts.ul,
      rootPN: this.jacketPartInfo.rootPN,
      jacketGroupId: this.jgId,
      controlType: this.jacketPartInfo.ccdcParts.controlType,
      repeat: this.oldJacket.repeat
    };
    this.subscription.add(
      this.jacketListService.updatePartNumbersFromRootPN(rootPNObj).subscribe(
        (res: Jacket[]) => {
          if (res.length > 0) {
            this.jacketPartInfo.jacketList = res;
            this.dataSource.data = this.jacketPartInfo.jacketList;
            this.jacketList = new Array();
            this.showHideDashSeperator();
            if (!updateNonStandardPart) {
              this.selection.clear();
            } else {
              this.generateNonStandardPN(true);
            }
            this.showLoader = false;
          }
        },
        error => {
          this.showLoader = false;
          if (error.applicationStatusCode === 3001) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  onSelectedJacket(event, row) {
    if (event) {
      this.selection.toggle(row);
    }
  }

  extractPartNumber(partNumber: string) {
    if (partNumber && partNumber.includes('~')) {
      const indexOfTild = partNumber.indexOf('~');
      const tempPartNumber = partNumber.substring(0, indexOfTild);
      const tempSequenceNumber = partNumber.substring(indexOfTild + 1);
      partNumber = tempPartNumber + tempSequenceNumber;
    }
    return partNumber;
  }

  extractSequenceNumber(partNumber: string) {
    if (partNumber && partNumber.includes('~')) {
      const indexOfTild = partNumber.indexOf('~');
      const tempSequenceNumber = partNumber.substring(indexOfTild + 1);
      return tempSequenceNumber;
    } else if (partNumber && partNumber.includes('-')) {
      const indexOfTild = partNumber.indexOf('-');
      const tempSequenceNumber = partNumber.substring(indexOfTild + 1);
      return tempSequenceNumber;
    }
  }

  mapJacketType(): number {
    if (this.jacketPartInfo.ccdcParts.jacketType === 'GROUNDED') {
      return 1;
    } else if (this.jacketPartInfo.ccdcParts.jacketType === 'NONGROUNDED') {
      return 2;
    } else {
      return 3;
    }
  }

  extractPath(partNumber: string, path: string, tempPartNumber: string) {
    if (this.jacketPartInfo.ccdcParts.productType === Values.valueByProductTypeINSEPARABLE || this.jacketPartInfo.ccdcParts.productType === Values.valueByProductTypeInseparable) {
      if (partNumber && (partNumber.includes('-') || partNumber.includes('~')) && partNumber.includes(this.jacketPartInfo.customerAbbrev)) {
        let firstPath: string = this.jacketPartInfo.customerAbbrev;
        let secondPath: string = partNumber.replace(this.jacketPartInfo.customerAbbrev, '');
        if (secondPath.startsWith('-')) {
          secondPath = secondPath.substring(1, secondPath.length);
        }
        return path + firstPath + '\\' + secondPath;
      }
    } else {
      if (partNumber && (partNumber.includes('-') || partNumber.includes('~')) && partNumber.includes(this.jacketPartInfo.customerAbbrev) && this.jacketPartInfo.ccdcParts.productType === Values.productTypeSilicone) {
        let firstPath: string = this.jacketPartInfo.customerAbbrev.charAt(0);
        let secondPath: string = this.jacketPartInfo.customerAbbrev;
        let thirdPath: string = partNumber.replace(this.jacketPartInfo.customerAbbrev, '').replace('-', '').replace('~', '');
        return path + firstPath + '\\' + secondPath + '\\' + thirdPath;
      } else {
        return path;
      }
    }
    return path;
  }

  // used to set shared drive paths based on the made in selected
  setCountrySpecificPath() {
    if (this.sharedService.getUsersCountry() === 'Vietnam') {
      this.rootPathUL = this.rootDirectoryVIETNAMUL;
      this.rootPathNonUL = this.rootDirectoryVIETNAMNONUL;
      this.designFileLocation = this.designFileVietnam;
    } else {
      // if user's country is null? by default considering USA
      this.rootPathUL = this.rootDirectoryUSAUL;
      this.rootPathNonUL = this.rootDirectoryUSANONUL;
      this.designFileLocation = this.designFileUSA;
    }
  }

  // Updates the root paths for Silicone product type
  setSiliconePath() {
    if (this.sharedService.getUsersCountry() === 'Vietnam') {
      this.rootPathUL = this.rootDirectorySiliconeVietnam;
      this.rootPathNonUL = this.rootDirectorySiliconeVietnam;
      this.designFileLocation = this.designFileVietnam;
    } else {
      // if user's country is null? by default considering USA
      this.rootPathUL = this.rootDirectorySiliconeUSA;
      this.rootPathNonUL = this.rootDirectorySiliconeUSA;
      this.designFileLocation = this.designFileUSA;
    }
  }

  // Updates the root paths for Inseperable product type
  setInseprablePath() {
    if (this.sharedService.getUsersCountry() === 'Vietnam') {
      this.rootPathUL = this.rootDirectoryInseperableVietnam;
      this.rootPathNonUL = this.rootDirectoryInseperableVietnam;
      this.designFileLocation = this.designFileUSA;
    } else {
      this.rootPathUL = this.rootDirectoryInseperableUSA;
      this.rootPathNonUL = this.rootDirectoryInseperableUSA;
      this.designFileLocation = this.designFileUSA;
    }
  }

  setInseprableSelectedPath(partNumber: string, path: string, selectedPartFilePath: string) {
    if ((this.jacketPartInfo.ccdcParts.productType === Values.valueByProductTypeINSEPARABLE || this.jacketPartInfo.ccdcParts.productType === Values.valueByProductTypeInseparable) && selectedPartFilePath == null) {
      if (partNumber && (partNumber.includes('-') || partNumber.includes('~')) && partNumber.includes(this.jacketPartInfo.customerAbbrev)) {
        let firstPath: string = this.jacketPartInfo.customerAbbrev;
        let secondPath: string = partNumber.replace(this.jacketPartInfo.customerAbbrev, '');
        if (secondPath.startsWith('-')) {
          secondPath = secondPath.substring(1, secondPath.length);
        }
        return path + firstPath + '\\' + secondPath;
      } else {
        return null;
      }
    } else {
      return selectedPartFilePath;
    }
  }

  // used to handle the partfile generation using the Solidworks API service
  generateFile() {
    this.showLoader = true;
    const jacketData = [];
    if (this.jacketPartInfo.ccdcParts.productType === Values.productTypeSilicone) {
      this.setSiliconePath();
    } else if (this.jacketPartInfo.ccdcParts.productType === Values.valueByProductTypeINSEPARABLE || this.jacketPartInfo.ccdcParts.productType === Values.valueByProductTypeInseparable) {
      this.setInseprablePath();
    } else {
      this.setCountrySpecificPath();
    }
    for (let i = 0; i < this.selection.selected.length; i++) {
      const obj = {
        workingDirNonUL: this.rootPathNonUL,
        partNumber: this.extractPartNumber(this.selection.selected[i].partNumber),
        workingDirUL: this.extractPath(this.selection.selected[i].partNumber, this.rootPathUL, this.selection.selected[i].tempPartNumber),
        revision: this.selection.selected[i].designRevision,
        unit: this.jacketPartInfo.measurementUnit,
        designFileLocation: this.designFileLocation,
        isUl: this.jacketPartInfo.ccdcParts.ul,
        selectedPath: this.setInseprableSelectedPath(this.selection.selected[i].partNumber, this.rootPathUL, this.selection.selected[i].selectedPartFilePath),
        productType: this.jacketPartInfo.ccdcParts.productType
      };
      jacketData.push(obj);
    }
    this.subscription.add(
      this.jacketListService.createFolders(jacketData).subscribe(
        (res: FolderPathRes) => {
          if (res.success) {
            this.updatePath(res.data);
            this.snakbarService.success(res.message);
            this.showLoader = false;
          } else {
            this.snakbarService.error(Messages.Title_Block.error_msg);
            this.showLoader = false;
          }
          this.apiRespnseError = false;
        },
        error => {
          this.serviceErrorMessage = error;
          this.showLoader = false;
          this.apiRespnseError = true;
        })
    );
  }

  updatePath(pathData) {
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService.updatePath(pathData, this.jgId).subscribe((res: string) => {
          if (res) {
            this.pathsFoundInDriveAndNotInBHX = res;
          }
          this.onJacketGroupChange(this.jgId);
          resolve();
        })
      );
    });
  }

  saveCustomer() {
    this.dialogRef.close();
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  applyFilter(filterValue: string) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  ErrorList() {
    const matDataConfig = new MatDialogConfig();
    this.errorList = new Array();
    matDataConfig.data = {errorList: this.jacketPartInfo.errorList};
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog.open(PNErrorComponent, matDataConfig);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ? this.selection.clear() : this.dataSource.data.forEach(row => this.selection.select(row));
  }

  openFile(element) {
    this.showLoader = true;
    this.filePath.path = element.partFilePath;
    this.filePath.rev = element.designRevision;
    this.filePath.partNumber = element.partNumber;
    const selectedPartFilePath = element.selectedPartFilePath ? true : false;
    this.subscription.add(
      this.jacketListService.openFileInSLD(this.filePath, selectedPartFilePath).subscribe(
        (res: APIRESULT) => {
          if (res.success) {
            this.showLoader = false;
          } else {
            this.snakbarService.error(Messages.Title_Block.error_msg);
            this.showLoader = false;
          }
        },
        error => {
          this.solidWorksNotInstalled = true;
          this.snakbarService.error(Messages.Solid_Work.not_install);
          this.showLoader = false;
        }
      )
    );
  }

  openSolidworkDownloadPage() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog.open(SolidworksDownloadComponent, matDataConfig);
  }

  showHideDashSeperator() {
    if (this.jacketPartInfo.ccdcParts.ul && this.jacketPartInfo.jacketList.length > 0) {
      this.showDash = true;
      this.jacketPartInfo.jacketList.forEach(ulJacket => {
        ulJacket.fieldWidth = this.setPartSeqTextFieldWidth(ulJacket);
      });
    } else if (!this.jacketPartInfo.ccdcParts.ul && this.jacketPartInfo.jacketList.length > 0) {
      this.jacketPartInfo.jacketList.forEach(nonUlJacket => {
        if (nonUlJacket.partNumber) {
          nonUlJacket.partNumber !== '' && nonUlJacket.partNumber.includes('-') ? (this.showDash = true) : (this.showDash = false);
        }
        nonUlJacket.fieldWidth = this.setPartSeqTextFieldWidth(nonUlJacket);
      });
    }
  }

  setPartSeqTextFieldWidth(jacket: Jacket): string {
    jacket.fieldWidth = '100px';
    if (jacket.tempPartNumber) {
      jacket.fieldWidth = (jacket.tempPartNumber.length + 1) * 8.5 + 'px';
    }
    if (jacket.nonStandardPartNumber && jacket.partNumber) {
      jacket.fieldWidth = (jacket.partNumber.length + 1) * 8.5 + 'px';
    }
    return jacket.fieldWidth;
  }

  async resetPath(jackets: Jacket[]) {
    if (await this.sweetAlertService.confirmResetFilePathWarning()) {
      this.subscription.add(this.jacketListService.resetJacketsPath(jackets).subscribe(() => {
        this.onJacketGroupChange(this.jgId);
      }));
    }
  }

  openPathGenerationResponse() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = this.pathsFoundInDriveAndNotInBHX;
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog.open(FilePathAlreadyExistModalComponent, matDataConfig);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
