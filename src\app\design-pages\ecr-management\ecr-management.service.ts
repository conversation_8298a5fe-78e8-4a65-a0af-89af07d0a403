import {catchError, map} from 'rxjs/operators';
import {Injectable} from '@angular/core';
import {utils} from 'src/app/shared/helpers';
import {HttpClient} from '@angular/common/http';
import {AppConfig} from 'src/app/app.config';
import {EcrDto, EcrPartnumberDto, EcrSearchFilter} from './ecr-management.model';
import {createRequestOption} from 'src/app/shared';

@Injectable({
  providedIn: 'root'
})
export class EcrManagementService {

  constructor(private http: HttpClient) { }

  getEcrLog(ecrFilterDTO: EcrSearchFilter, pageableObject) {
    return this.http.post(AppConfig.ECR_LOGS, ecrFilterDTO, {
      params: createRequestOption(pageableObject)
    }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getEcrById(ecrId: number) {
    return this.http.get(AppConfig.ECR_DETAILS + ecrId).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }

  updateEcr(ecrDto: EcrDto) {
    return this.http.put(AppConfig.ECR_DETAILS, ecrDto).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }
  removeListItemByIndex(listToUpdate, selectedItem) {
    listToUpdate.splice(listToUpdate.indexOf(selectedItem), 1);
    return listToUpdate;
  }
  deleteAttachment(attachmentId) {
    return this.http.delete(AppConfig.DELETE_ECR_ATTACHMENT + attachmentId).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }

  deletePartNumber(partNumberId) {
    return this.http.delete(AppConfig.ECR_PARTNUMBER + partNumberId).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }
  getPartNumber(partNumberId) {
    return this.http.get(AppConfig.ECR_PARTNUMBER + partNumberId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
  addAttachment(formData) {
    return this.http.post(AppConfig.ADD_ECR_ATTACHMENT, formData).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }
  addPartNumber(partNumberDto: EcrPartnumberDto) {
    return this.http.post(AppConfig.ADD_ECR_PARTNUMBER, partNumberDto).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }

  getUsers() {
    return this.http.get(AppConfig.USER_API).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getDepartments() {
    return this.http.get(AppConfig.GET_DEPARTMENTS).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  checkPartNumberExists(ecrPartNumberDTO: EcrPartnumberDto) {
    return this.http.post(AppConfig.CHECK_PART_NUMBER_EXIST, ecrPartNumberDTO).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  generateEcr(formData: FormData) {
    return this.http.post(AppConfig.GENERATE_ECR, formData).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }

  getStatuses() {
    return this.http.get(AppConfig.ECR_STATUSES).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getECRPartSyncStatus(partNumber: number) {
    return this.http.get(AppConfig.CHECK_ECR_PART_NUMBER_SYNC_STATUS+partNumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getDocument(fileId) {
    return this.http.get(AppConfig.DOWNLOAD_ECR_ATTACHMENT + fileId, { 'responseType': 'blob' }).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }

  isPartnumberExistInEpicore(partnumber) {
    return this.http.get(AppConfig.PARTNUMBER_EXIST_IN_EPICORE + partnumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteECR(id: number) {
    return this.http
      .delete(AppConfig.DELETE_OPERATION_ECR + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  syncPartNumberToEpicore(syncPartDTO: EcrPartnumberDto,type:string) {
    return this.http.post(AppConfig.SYNC_PARTNUMBER_TO_EPICORE+type, syncPartDTO).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getSalesAssociate(isGlobalSearch: boolean) {
    return this.http.get(AppConfig.SalesAssociate + '/' + isGlobalSearch).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
