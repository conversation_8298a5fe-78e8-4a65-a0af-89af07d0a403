import { Component, OnDestroy, OnInit } from '@angular/core';
import { MatDialogRef, MatTableDataSource } from '@angular/material';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { EcrPartNumberDTO } from '../../design-pages/ecr-management/ecr-management.model';
import { Variable } from '../../shared/constants/Variable.constants';
import { TopbarComponent } from '../topbar/topbar.component';
import { SweetAlertService } from './../../shared/service/sweet-alert.service';
import { SearchEcrModel } from './search-ecr-model';
import { SearchEcrService } from './search-ecr-service';

@Component({
  selector: 'sfl-search-ecr',
  templateUrl: './search-ecr-component.html'
})
export class SearchEcrComponent implements OnInit, OnDestroy {
  partNumber: string;
  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  soNumbers = new MatTableDataSource<SearchEcrModel>();
  ecrPartNumbers = new MatTableDataSource<EcrPartNumberDTO>();
  searchSOColumns = ['quotenumber', 'sonumber', 'revision'];
  ecrPartNumColumns = ['ecrnumber', 'status', 'ecrdate'];

  constructor(
    public readonly dialogRef: MatDialogRef<TopbarComponent>,
    private readonly router: Router,
    private readonly sweetAlertService: SweetAlertService,
    private readonly searchEcrService: SearchEcrService
  ) {
  }

  ngOnInit() {
  }

  searchEcr() {
    return new Promise(resolve => {

      this.showLoader = true;
      this.searchEcrService.getSONumbersByPartNumber(this.partNumber.trim()).subscribe(
        (searchEcrModels: SearchEcrModel[]) => {
          this.soNumbers.data = searchEcrModels;
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
      this.searchEcrService.getEcrsByPartNumber(this.partNumber.trim()).subscribe(
        (ecrPartNumberDTOS: EcrPartNumberDTO[]) => {
          this.ecrPartNumbers.data = ecrPartNumberDTOS;
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
      resolve();
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
