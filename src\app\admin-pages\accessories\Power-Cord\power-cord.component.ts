import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Values } from './../../../shared/constants/values.constants';
import { Subscription } from 'rxjs';
import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { element } from '@angular/core/src/render3/instructions';
import { AccessoriesService } from '../accessories.service';
import {
  PowerCordMaterialsMaster,
  PowerCordVoltagesMaster,
  PowerCordAmpsMaster,
  PowerCordOptionsMaster
} from 'src/app/common-pages/masterdata-management/masterdata-management.model';

@Component({
  selector: 'sfl-power-cord',
  templateUrl: './power-cord.component.html'
})
export class PowerCordComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  materialList: PowerCordMaterialsMaster[];
  voltageList: PowerCordVoltagesMaster[];
  ampsList: PowerCordAmpsMaster[];
  optionsList: PowerCordOptionsMaster[];
  connectorList = [];
  plugList = [];
  sexList = [
    { id: 'M', value: 'M' },
    { id: 'F', value: 'F' },
    { id: 'A', value: 'A' }
  ];
  ulList = [
    { id: 'Y', value: 'Y' },
    { id: 'N', value: 'N' },
    { id: 'A', value: 'A' }
  ];
  partNumber = 'P_____-__-_';
  partNumberArray: string[];
  powerCordLength: number;
  listPrice: number;
  selectedId: number;

  constructor(
    public dialogRef: MatDialogRef<PowerCordComponent>,
    private accessoriesService: AccessoriesService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.selectedId = data.id;
  }

  ngOnInit() {
    this.partNumberArray = new Array(11);
    this.partNumberArray[0] = 'P';
    this.getPowerCordConnectors();
    this.getPowerCordMaterials();
    this.getPowerCordVoltage();
    this.getPowerCordAmps();
    this.getPowerCordOptions();
  }

  getPowerCordConnectors() {
    this.subscription.add(
      this.accessoriesService.getPowerCordConnectors().subscribe((res: any) => {
        this.connectorList = res;
        this.plugList = res;
      })
    );
  }
  getPowerCordMaterials() {
    this.subscription.add(
      this.accessoriesService.getPowerCordMaterialsList().subscribe((powerCordMaterial: PowerCordMaterialsMaster[]) => {
        this.materialList = powerCordMaterial;
      })
    );
  }
  getPowerCordVoltage() {
    this.subscription.add(
      this.accessoriesService.getPowerCordVoltage().subscribe((powerCordVoltage: PowerCordVoltagesMaster[]) => {
        this.voltageList = powerCordVoltage;
      })
    );
  }
  getPowerCordAmps() {
    this.subscription.add(
      this.accessoriesService.getPowerCordAmps().subscribe((powerCordAMPS: PowerCordAmpsMaster[]) => {
        this.ampsList = powerCordAMPS;
      })
    );
  }
  getPowerCordOptions() {
    this.subscription.add(
      this.accessoriesService.getPowerCordOptions().subscribe((powerCordOptions: PowerCordOptionsMaster[]) => {
        this.optionsList = powerCordOptions;
      })
    );
  }

  onOptionChange(value, type) {
    if (type === 'material') {
      this.partNumberArray[1] = value;
    }

    if (type === 'voltage') {
      this.partNumberArray[2] = value;
    }

    if (type === 'amps') {
      this.partNumberArray[3] = value;
    }

    if (type === 'options') {
      this.partNumberArray[10] = value;
    }

    if (type === 'connector') {
      this.partNumberArray[7] = value;
    }

    if (type === 'plug') {
      this.partNumberArray[8] = value;
    }

    if (type === 'length') {
      if (this.powerCordLength > 9) {
        this.partNumberArray[4] = this.powerCordLength.toString().split('')[0];
        this.partNumberArray[5] = this.powerCordLength.toString().split('')[1];
      } else if (this.powerCordLength) {
        this.partNumberArray[4] = '0';
        this.partNumberArray[5] = this.powerCordLength.toString();
      } else {
        this.partNumberArray[4] = undefined;
        this.partNumberArray[5] = undefined;
      }
    }

    for (let index = 0; index < 10; index++) {
      if (this.partNumberArray[index] === undefined) {
        this.partNumberArray[index] = '_';
      }
      if (index === 6) {
        this.partNumberArray[index] = '-';
      }
      if (index === 9) {
        this.partNumberArray[index] = '-';
      }
    }
    this.partNumber = this.partNumberArray.toString().replace(/[,]+/g, '');
  }

  calculateListPrice() {
    // For right now, can you default the power cord price to $66.00 in accessories. We may do something more later. BH-1181
    /*let jacketConnectorPrice = 0;
        let plugConnectorPrice = 0;
        let material = 0;
        let cordLength = 0;

        if (this.partNumberArray[7]) {
            jacketConnectorPrice = this.connectorList.find(e => {
                return e.value === this.partNumberArray[7];
            }).price;
        }
        if (this.partNumberArray[8]) {
            plugConnectorPrice = this.connectorList.find(e => {
                return e.value === this.partNumberArray[8];
            }).price;
        }

        if (this.partNumberArray[1]) {
            material = this.materialList.find(e => {
                return e.id === this.partNumberArray[1];
            }).price;
        }

        if (this.powerCordLength) {
            cordLength = this.powerCordLength;
        }*/

    this.listPrice = 66; // Conveyed by the client, may change in future
  }

  closeDialog(): void {
    this.calculateListPrice();
    this.dialogRef.close({
      partNumber: this.partNumber,
      listPrice: Math.round(this.listPrice),
      id: this.selectedId
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
