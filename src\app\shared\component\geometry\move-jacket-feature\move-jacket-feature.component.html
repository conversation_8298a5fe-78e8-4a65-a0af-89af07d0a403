<form class="forms_form" #moveJacketForm="ngForm">
  <mat-dialog-content>
    <h2 mat-dialog-title>Select The Jacket</h2>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="99" fxFlex.gt-md="99" fxFlex.gt-sm="99" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Select Jacket" (selectionChange)="selectJacket($event.value)" name="jacketName"
            required>
            <mat-option value="New Jacket">
              New Jacket
            </mat-option>
            <mat-option *ngFor="let jacket of newJacketList" [value]="jacket.id">
              {{ jacket.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxLayoutAlign="end">
        <button mat-raised-button color="warn" type="submit" [disabled]="!buttonDisabled" (click)="moveFeaturesToJacket()">Move Features</button>
        <div class="close-button" fxLayoutAlign="start">
          <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
        </div>
      </div>
    </div>
  </mat-dialog-content>
</form>
