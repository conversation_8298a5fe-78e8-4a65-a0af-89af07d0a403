import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { MatDialog, MatDialogConfig, MatPaginator, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { AddOthrerThermostatsComponent } from 'src/app/admin-pages/new-quotation/Add Thermostats/other-termostat/add-other-termostat.component';
import { ThermostatInstallationMethod, ThermostatList, ThermostatType, ThermostatTypesMaster } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from 'src/app/admin-pages/new-quotation/summary-sales-order.service';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { CcdcTemplateDTO, ThermostatInfo, ThermostatInformationTemplateDTO } from '../manage-ccdc.model';

@Component({
  selector: 'app-manage-thermostat-information',
  templateUrl: './manage-thermostat-information.component.html',
  styleUrls: ['./manage-thermostat-information.component.css']
})
export class ManageThermostatInformationComponent implements OnInit {

  @Input("ccdcMasterData")
  ccdcMasterData: CcdcTemplateDTO;
  @Input("thermostatInfoDto")
  thermostatInfoDto: Array<ThermostatInformationTemplateDTO>;
  thermostatList: ThermostatInformationTemplateDTO[];
  selectedThermostat = [];
  thermostatInfo: ThermostatInfo;
  searchThermostate: ThermostatList;
  thermostatType: ThermostatTypesMaster[];
  thermostatInstallationMethod: ThermostatInstallationMethod[];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  thermostatsdisplayedColumns = DisplayColumns.Cols.thermostatsdisplayedColumns;
  thermostatdataSource = new MatTableDataSource<ThermostatInformationTemplateDTO>();
  selecetdthermostatsdisplayedColumns = DisplayColumns.Cols.thermostatsdisplayedColumns;
  selecetdthermostatdataSource = new MatTableDataSource<ThermostatInformationTemplateDTO>();
  isShowThermostat: boolean;
  isAdded: boolean;
  subscription: Subscription = new Subscription();
  pageSize = PopupSize.size.pageSize;
  others: ThermostatInformationTemplateDTO[];
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  defaultSelected = true;


  constructor(
    private snakbarService: SnakbarService,
    private salesOrderSummaryService: SalesOrderSummaryService,
    private matDialog: MatDialog,
    private sweetAlertService: SweetAlertService,
  ) {

  }

  ngOnInit() {
    this.searchThermostate = new ThermostatList();
    this.isShowThermostat = false;
    this.isAdded = false;
    this.thermostatInfo = new ThermostatInfo();
    this.thermostatInfo.thermostatInformationDTOList = [];
    this.selectedThermostat = new Array<ThermostatInfo>();
    this.getThermostats();
    this.getThermostatDataSource();
    this.getThermostatTypes();
    this.getInstallationMethod();
  }

  getThermostats() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatsList().subscribe(
        (res: ThermostatInformationTemplateDTO[]) => {
          this.thermostatList = res;
          this.thermostatdataSource = new MatTableDataSource<ThermostatInformationTemplateDTO>(this.thermostatList);
          this.thermostatdataSource.paginator = this.paginator;
          this.showLoader = false;
        },
        error => {
          if (error.applicationStatusCode === 1235) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false;
        }
      )
    );
  }

  // used to handle adding new thermostats to the list
  addThermostat(element) {
    if (element.thermostatType && element.installationMethodDTO) {
      this.addElementToThermostats(element);
    } else {
      this.snakbarService.error(Messages.Quotation.select_thermostat);
    }
  }

  private addElementToThermostats(element: any) {
    this.thermostatInfoDto.push(element);
    this.selecetdthermostatdataSource.data = this.thermostatInfoDto;
  }

  // used to reset the thermostat adding form
  reset(thermostateForm: NgForm) {
    this.getThermostats();
    thermostateForm.reset();
  }

  // used to handle the filter of the thermostat list
  searchThermosateList(thermostateForm: NgForm) {
    this.showLoader = true;
    this.searchThermostate.tempUnit = this.ccdcMasterData.temperatureUnit;
    this.searchThermostate.openOnRise = this.defaultSelected;
    this.subscription.add(
      this.salesOrderSummaryService.searchThermostate(this.searchThermostate).subscribe(
        (res: ThermostatInformationTemplateDTO[]) => {
          if (res.length > 0) {
            this.thermostatList = res;
            this.thermostatdataSource = new MatTableDataSource<ThermostatInformationTemplateDTO>(this.thermostatList);
            this.thermostatdataSource.paginator = this.paginator;
          } else {
            this.snakbarService.error(Messages.error_msg.nodata);
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to remove the thermostat from the list
  async remove(element) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.selecetdthermostatdataSource.data.splice(this.selecetdthermostatdataSource.data.indexOf(element), 1);
      this.selecetdthermostatdataSource._updateChangeSubscription();
    }
  }



  // used to get the list of thermostat
  getThermostatDataSource() {
    this.selecetdthermostatdataSource.data = this.thermostatInfoDto;
  }

  // used to handle adding of other thermostat using a modal popup
  openOtherThermostat() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {};
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog
      .open(AddOthrerThermostatsComponent, matDataConfig)
      .afterClosed()
      .subscribe((result: ThermostatInformationTemplateDTO) => {
        if (result) {
          result.thermostatType = null;
          this.addElementToThermostats(result);
        }
      });
  }
  // used to get the thermostat types
  getThermostatTypes() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getThermostatTypeList().subscribe(
        (res: ThermostatType[]) => {
          this.thermostatType = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to get the list of installtion methods
  getInstallationMethod() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getInstallationMethod().subscribe(
        (installationMethod: ThermostatInstallationMethod[]) => {
          this.thermostatInstallationMethod = installationMethod;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
