export class LapCalc {
  constructor(public pipeDiameter?: number, public laps?: number, public tapeWidth?: number, public betweenLaps?: string) {
  }
}

export class ElementType {
  constructor(public id?: string, public value?: string) {
  }
}

export class WireSelector {
  constructor(
    public id?: number,
    public goldStandard?: boolean,
    public basicInfoDTO?: BasicInfo,
    public elementInfoDTO?: ElementInfo,
    public searchParameterDTO?: SearchParameter,
    public targetingResult?: TargetingResult,
    public tapeInformation?: TapeInformation,
    public reliability?: Reliability,
    public wireInformation?: WireInformation,
    public tajima?: boolean,
    public minOhms?: number,
    public maxOhms?: number,
    public tgtOhms?: number,
    public inVietnam: boolean = false,
    public inCostaRica: boolean = false,
    public inEpicor = true
  ) {
  }
}

export class BasicInfo {
  constructor(
    public amps?: number,
    public operatingTemp?: number,
    public phase?: string,
    public units?: string,
    public volts?: number,
    public watts?: number,
    public jacketType?: string,
    public productType?: string,
  ) {
    this.units = 'IN';
  }
}

export class ElementInfo {
  constructor(
    public elementType?: string,
    public noOfTapes?: number,
    public dualWireTape?: boolean,
    public tapeWidth?: number,
    public totalLength?: number,
    public wireType?: string,
    public standard?: string
  ) {
    this.noOfTapes = 0;
    this.totalLength = 0;
    this.tapeWidth = 0;
    this.standard = 'std';
  }
}

export class SearchParameter {
  constructor(
    public increment?: number,
    public picksEnd?: number,
    public picksStart?: number,
    public strandsEnd?: number,
    public strandsStart?: number,
    public tpiEnd?: number,
    public tpiPicksEnd?: number,
    public tpiPicksStart?: number,
    public tpiStart?: number
  ) {
    this.increment = 1;
    this.strandsEnd = 24;
    this.strandsStart = 5;
    this.tpiEnd = 90;
    this.tpiStart = 12;
    this.tpiPicksEnd = 4;
    this.tpiPicksStart = 4;
  }
}

export class WireSelectorResult {
  constructor(
    public elementChoiceDTO?: ElementChoice,
    public reliabilityDTO?: Reliability,
    public targetingResultDTO?: TargetingResult,
    public wireInformationDTO?: WireInformation,
    public tapeInformation?: TapeInformation
  ) {
  }
}

export class ElementChoice {
  constructor(public alloyName?: string, public searchParameterName?: string, public searchParameterValue?: number) {
  }
}

export class Reliability {
  constructor(public elementFactor?: number, public elementGrade?: number, public wperInSquareTape?: number, public wperInWire?: number) {
  }
}

export class TargetingResult {
  constructor(public errorPercentage?: number, public targetingWattage?: number) {
  }
}

export class WireInformation {
  constructor(
    public alloyName?: string,
    public inEpiCore?: boolean,
    public length?: number,
    public ohmsPerFoot?: number,
    public partNumber?: string,
    public vietnamStock?: number,
    public costaRicaStock?: number,
    public inActive?: boolean,
    public onHold?: boolean
  ) {
  }
}

export class TapeInformation {
  constructor(public ohmsPerFeet?: number, public partNumber: string = '', public picks?: number, public vietnamStock?: number) {
  }
}

export class AMPS {
  constructor(public amps?: number) {
  }
}

export class Element {
  constructor(
    public id?: number,
    public alloyName?: string,
    public amps?: number,
    public elementFactor?: number,
    public elementGrade?: number,
    public elementType?: string,
    public syncToEpicore: boolean = false,
    public errorPercentage?: number,
    public inEpiCore?: boolean,
    public increment?: number,
    public length?: number,
    public noOfTapes?: number,
    public ohmsPerFeet?: number,
    public ohmsPerFoot?: number,
    public operatingTemp?: number,
    public partNumber?: string,
    public phase?: string,
    public picks?: number,
    public picksEnd?: number,
    public picksStart?: number,
    public strandsEnd?: number,
    public strandsStart?: number,
    public tapeWidth?: number,
    public targetingWattage?: number,
    public totalLength?: number,
    public tpiEnd?: number,
    public tpiPicksEnd?: number,
    public tpiPicksStart?: number,
    public tpiStart?: number,
    public units?: string,
    public volts?: number,
    public watts?: number,
    public wireType?: string,
    public wperInSquareTape?: number,
    public wperInWire?: number,
    public tajima?: boolean,
    public goldStandard?: boolean,
    public jacketId?: number,
    public selectedElement?: number,
    public wirePartNumber?: string,
    public tapePartNumber?: string,
    public oldTapePartNumber?: string,
    public searchParameterValue?: number,
    public searchParameterName?: string,
    public syncStatus?: string,
    public minOhms?: number,
    public maxOhms?: number,
    public tgtOhms?: number,
    public productType?: string,
    public inVietnam: boolean = false,
    public inCostaRica: boolean = false,
    public elementNumber?: number,
    public standard?: string
  ) {
  }
}
