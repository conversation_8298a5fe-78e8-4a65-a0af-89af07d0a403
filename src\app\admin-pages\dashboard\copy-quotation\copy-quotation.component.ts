import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { CopyQuotationData, CustomerDTO, Quotation } from '../../new-quotation/ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from '../../new-quotation/summary-sales-order.service';
import { DashboardComponent } from '../dashboard.component';
import { DashboardService } from '../dashboard.service';

@Component({
  selector: 'app-copy-quotation',
  templateUrl: './copy-quotation.component.html',
  styleUrls: ['./copy-quotation.component.css']
})
export class CopyQuotationComponent implements OnInit {
  subscription: Subscription = new Subscription();
  quotationDTO: Quotation = new Quotation();
  copyQuotationId: number;
  quoteNumber: number;
  showLoader: boolean;
  quotationNumberPattern = Variable.quotePattern;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public readonly dialogRef: MatDialogRef<DashboardComponent>,
    private readonly dashboardService: DashboardService,
    private readonly snakBarService: SnakbarService,
    @Inject(MAT_DIALOG_DATA) public data: CopyQuotationData,
    private readonly salesOrderSummaryService: SalesOrderSummaryService
  ) {
    this.copyQuotationId = data.quotationId;
  }

  ngOnInit() {
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  findCustomerDetail(quoteNumber): Promise<CustomerDTO> {
    return new Promise((resolve, reject) => {
      if (quoteNumber && quoteNumber.length === 5) {
        this.subscription.add(
          this.salesOrderSummaryService.getQuotation(quoteNumber).subscribe((response: Quotation) => {
            if (response && response.customerDTO != null) {
              resolve(response.customerDTO); // Return the customer details once available
            } else {
              resolve(new CustomerDTO()); // Return a default CustomerDTO if no customer is found
            }
          }, (error) => {
            resolve(new CustomerDTO()); // Handle error case
          })
        );
      } else {
        resolve(new CustomerDTO()); // Return default CustomerDTO if the quote number is invalid
      }
    });
  }

  async copyQuotationFromCurrentId() {
    this.showLoader = true;
    this.quotationDTO.quotationNumber = this.quoteNumber;

    try {
      // Wait for customer details before proceeding
      this.quotationDTO.customerDTO = await this.findCustomerDetail(this.quoteNumber);
      this.subscription.add(
        this.dashboardService.copyQuotations(this.quotationDTO, this.copyQuotationId).subscribe((res) => {
          if (res) {
            this.closeDialog();
            this.showLoader = false;
            this.snakBarService.success(Messages.Quotation.copy_quotation_success);
          }
        }, () => {
          this.showLoader = false;
        })
      );
    } catch (error) {
      this.showLoader = false;
      console.error(error); // Handle any errors here
    }
  }

}
