<h2 mat-dialog-title>Select Colour To Highlight Row
  <hr>
</h2>

<form #colourSelectForm="ngForm" [formGroup]="trackerFieldsForm" role="form">
  <div>
    <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <mat-select placeholder="Select Colour" name="Select Color" #selectedColor>
        <mat-option *ngFor="let color of highlightedColors" [value]="color?.value"
                    [ngStyle]="{'background': color?.value}">
          {{ color?.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div [ngSwitch]="value">
    <div *ngSwitchCase="'designStatusId'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-select placeholder="Quotation Status" name="quoteStatus" #quoteStatus formControlName="quotationStatusId">
          <mat-option *ngFor="let status of statuses" [value]="status.id">
            {{ status?.status }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'designerId'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-select placeholder="Assigned Designer" name="assignedDesigner" #assignedDesigner
                    formControlName="assignedDesignerId">

          <mat-option *ngFor="let designer of designers" [value]="designer?.id">
            {{ designer?.firstName }} {{ designer?.lastName }}
          </mat-option>
          <mat-option>None</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'designLocation'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-select placeholder="Design Location" name="designLocation" #designLocation color="warn"
                    formControlName="designLocation">
          <mat-option *ngFor="let country of countries" [value]="country">
            {{ country | uppercase }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'designComments'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <textarea matInput placeholder="Design Comments" #designComments name="designComments"
                  formControlName="designComments"></textarea>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'dollarAmount'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <input matInput placeholder="Dollar Amount" #dollarAmount name="dollarAmount" formControlName="dollarAmount">
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'noOfDesigns'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <textarea matInput placeholder="No. Of Designs" #customNoOfDesigns name="noOfDesign" sflIsNumber
                  formControlName="customNoOfDesigns"></textarea>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'approval1Date'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Approval 1 Date</mat-label>
        <input
          matInput
          (click)="approval1DatePicker.open()"
          [matDatepicker]="approval1DatePicker"
          placeholder="Approval Date"
          #approval1Date
          formControlName="approval1Date"
          name="approval1DateField"
          autocomplete="off"
        />
        <mat-datepicker-toggle matSuffix [for]="approval1DatePicker"></mat-datepicker-toggle>
        <mat-datepicker #approval1DatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'ofa1Date'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>OFA 1 Date</mat-label>
        <input
          matInput
          (click)="ofa1DatePicker.open()"
          [matDatepicker]="ofa1DatePicker"
          placeholder="OFA 1 Date"
          name="ofa1DateField"
          autocomplete="off"
          #ofa1DateField
          formControlName="ofa1Date"
        />
        <mat-datepicker-toggle matSuffix [for]="ofa1DatePicker"></mat-datepicker-toggle>
        <mat-datepicker #ofa1DatePicker></mat-datepicker>
      </mat-form-field>
    </div>
  </div>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="selectColumn(selectedColor?.value)">Submit
    </button>
    <button mat-raised-button type="button" (click)="closeDialog(false)">Cancel</button>
  </mat-dialog-actions>
</form>
