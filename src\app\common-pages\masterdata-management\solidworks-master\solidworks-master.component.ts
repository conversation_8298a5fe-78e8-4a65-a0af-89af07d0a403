import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { SweetAlertService } from 'src/app/shared';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Messages } from 'src/app/shared/constants/messages.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SnakbarService } from 'src/app/shared/service/snakbar.service';
import { GenericPageable } from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { ManageSolidworksMasterComponent } from './manage-solidworks-master/manage-solidworks-master.component';
import { SolidWorksBlockMasterDTO } from './solidworks-master.model';

@Component({
  selector: 'app-solidworks-master',
  templateUrl: './solidworks-master.component.html',
  styleUrls: ['./solidworks-master.component.css']
})
export class SolidworksMasterComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  length: number;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  pageSizeOptions = Variable.pageSizeOptions;
  numberOfElements: number;
  sortField = Variable.defaultSortById;

  ascSort = Variable.sortAscending;
  solidWorksBlockMasterPageable: GenericPageable<SolidWorksBlockMasterDTO>;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  filter = [];

  pageTitle = 'SolidWorks Block Master Configuration';
  solidworksMasterDataSource = new MatTableDataSource<SolidWorksBlockMasterDTO>();
  solidworksMasterColumn = DisplayColumns.Cols.SolidWorksMasterDataColumn;
  constructor(
    private readonly matDialog: MatDialog,
    private readonly masterDataService: MasterdataManagementService,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) { }

  ngOnInit() {
    this.getSWBlockMastersData(this.initialPageIndex, this.initialPageSize);
  }


  getSWBlockMastersPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getSWBlockMastersData(this.pageIndex, this.pageSize);
  }


  getSWBlockMastersData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getSolidWorksMasterData(this.filter, pageable).subscribe(
        (res: GenericPageable<SolidWorksBlockMasterDTO>) => {
          this.solidWorksBlockMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createSolidWorksBlockMasterTable(this.solidWorksBlockMasterPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }


  createSolidWorksBlockMasterTable(serviceRequestList: GenericPageable<SolidWorksBlockMasterDTO>) {
    this.solidworksMasterDataSource.data = serviceRequestList.content;
  }

  addSolidWorksMaster() {
    this.editSolidWorksMaster(new SolidWorksBlockMasterDTO());
  }

  editSolidWorksMaster(solidworksMaster: SolidWorksBlockMasterDTO) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = solidworksMaster;
    matDataConfig.width = PopupSize.size.popup_xxlg;
    matDataConfig.panelClass = 'sfl-bhx-material-model';
    const dialogRef = this.matDialog.open(ManageSolidworksMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          solidworksMaster.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getSWBlockMastersData(this.pageIndex, this.pageSize);
      }
    })
  }

  async deleteSolidWorksBlockMaster(blockId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.subscription.add(this.masterDataService.deleteSolidWorksMaster(blockId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getSWBlockMastersData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      ));
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
