import { DatePipe } from '@angular/common';
import { Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { IPageInfo, VirtualScrollerComponent } from 'ngx-virtual-scroller';
import { Subscription } from 'rxjs';
import { Status } from 'src/app/admin-pages/dashboard/dashboard.model';
import { QuotationStatusMaster } from 'src/app/common-pages/masterdata-management/masterdata-management.model';
import { MasterdataManagementService } from 'src/app/common-pages/masterdata-management/masterdata-management.service';
import {
  TrackerFieldsEditorComponent
} from 'src/app/design-pages/jacket-list/tracker-fields-editor/tracker-fields-editor.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { GetQuoteStatusName } from 'src/app/shared/pipes/common.pipes';
import { RoleAuthorisationServiceService } from 'src/app/shared/service/role-authorisation-service.service';
import { Statuses } from '../../design-pages/ecr-management/ecr-management.model';
import { Role, SharedService, SnakbarService } from '../../shared';
import { Variable } from '../../shared/constants/Variable.constants';
import {
  DesignEngineeringQuoteTracker,
  DesignEngQuoteTrackerColumnColor,
  FilterQuoteTracker,
  GenericPageable,
  SalesAssociate
} from '../quote-tracker.model';
import { QuoteTrackerService } from '../quote-tracker.service';
import {
  DesignEnggQuoteTrackerColumnColorComponent
} from './design-engg-quote-tracker-column-color/design-engg-quote-tracker-column-color.component';
import {
  DesignEnggQuoteTrackerRowColourComponent
} from './design-engg-quote-tracker-row-colour/design-engg-quote-tracker-row-colour.component';

@Component({
  selector: 'sfl-design-engg-quote-tracker',
  templateUrl: './design-engg-quote-tracker.component.html',
  styleUrls: ['./design-engg-quote-tracker.component.css'],
  providers: [
    GetQuoteStatusName
  ],
})
export class DesignEnggQuoteTrackerComponent implements OnInit, OnDestroy {
  headingTitle = 'Design Engineering SO Tracker';
  selectedRowIndex: number = -1;
  subscription = new Subscription();
  designEnggQTSPageable: GenericPageable<DesignEngineeringQuoteTracker>;
  quotationStatusPageable: GenericPageable<QuotationStatusMaster>;
  filter: FilterQuoteTracker = new FilterQuoteTracker();
  filteredList: DesignEngineeringQuoteTracker[] = [];
  showLoader = false;
  authorizedRole = [Role.ADMIN_ROLE, Role.DESIGN_ROLE, Role.ENG_ROLE];
  salesRole = Role.SALES_ROLE;
  isAuthorized = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  statuses: Statuses[];
  newStatus: Status[];
  salesassociates: SalesAssociate[];
  length: number;
  pageIndex = Variable.activePage;
  jacketLink = Variable.designEngJacketListLink;
  appQuoteLink = Variable.appEngQuoteLink;
  pageSize = 200;
  sortAsc = Variable.defaultSortOrder;
  sortOrder = Variable.defaultSortOrder;
  sortField = Variable.sortByQuotation_number;
  sortFieldStatus = Variable.sortByQuotationStatus;
  sortShipDate = Variable.sortByshipDate;
  sortByEngReleaseDate = Variable.sortByEngReleaseDate;
  ascSort = Variable.sortAscending;
  numberOfElements: number;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.itemsPerPage;
  soNumber: string;
  fromDate = new Date();
  toDate = new Date();
  today = new Date().toLocaleDateString();
  totalNoOfPages: number = 0;
  quotTypes = Values.QuotStatusTypes;
  newFilterDesignList = [];
  ignoreDefaultHidden: boolean = false;
  isFilter: any;
  quoteId: number;
  primaryColorValue: string;
  defaultRowColorValue: string;
  primaryColor: string;
  defaultRowColor: string;
  defaultColor: boolean;
  productTypes = Values.TRACKER_FIELDS_PRODUCT_TYPES;
  isSales: boolean;
  sortActive:string='';
  allStatusValue = 'all';
  allStatusLabel = 'Select All';
  searchText="";
  isFindPopupVisible:boolean = false;
  currentMatch = 0;
  totalMatches = 0;
  matchedItems: DesignEngineeringQuoteTracker[] = [];
  @ViewChild('scroll', { }) virtualScroller!: VirtualScrollerComponent;
  @ViewChild('searchInput') searchInput!: ElementRef<HTMLInputElement>;

  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly datePipe: DatePipe,
    private readonly sharedService: SharedService,
    private pipeName: GetQuoteStatusName,
    private readonly titleService: Title,
    private readonly matDialog: MatDialog,
    private readonly masterDataService: MasterdataManagementService,
    private readonly roleAuthorisationServiceService: RoleAuthorisationServiceService,
    private snakbarService: SnakbarService,
  ) {
  }

  // used to set from and to date for filter, retrieves quote statuses and then gets the app QTS quotes
  async ngOnInit() {
    this.titleService.setTitle('QTS - Design Engineering');
    // setting up the default filter of 30 days from current day
    this.fromDate.setDate(this.toDate.getDate() - 30);
    this.checkIsAuthorized();
    this.getQuoteStatuses();
    await this.getSaleAssociate();
    this.loadFilters();
    this.isSales = this.sharedService.getRole() === Role.SALES_ROLE;
  }

  // Listen for Ctrl + F key press
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.ctrlKey && event.key === 'f') {
      event.preventDefault(); // Prevent the default browser find
      this.toggleFindPopup(); // Toggle the find popup
    }
  }
  // Toggle the visibility of the find popup
  toggleFindPopup() {
    this.isFindPopupVisible = !this.isFindPopupVisible;
    if (this.isFindPopupVisible) {
      // Wait until the next tick to focus the input after it's rendered
      setTimeout(() => {
        if (this.searchInput) {
          this.searchInput.nativeElement.focus();
        }
      }, 0);
      this.searchText = ''; // Clear search term when opening
    }
  }

  onFilterChange() {
    this.sharedService.setFilterItem(this.filter);
  }

  loadFilters() {
    const savedFilters = this.sharedService.getFilterItem();
    if (savedFilters) {
      this.filter = savedFilters;
    }
  }

  initializeTable(clearFilter: boolean) {
    this.filteredList = [];
    this.fetchNextChunk(this.initialPageIndex, this.pageSize,clearFilter).then(items => {
      this.appendItems(items);
    });
  }

  // used to get the quotes statuses
  getQuoteStatuses() {
    this.subscription.add(this.sharedService.getStatuesByTypeOrderByOrderNumber().subscribe((res: Statuses[]) => {
      if (res) {
        res.sort((a, b) => a.type < b.type ? 1 : -1);
        this.statuses = res;
        this.selectStatusByDefault();
      }
    }));
  }

  getMasterDataListing(pageIndex, pageSize) {
    const pageable = {page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField};
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getAllStatusesFromApi(this.filter, pageable).subscribe(
        (res: GenericPageable<QuotationStatusMaster>) => {
          this.quotationStatusPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;

          this.showLoader = false;
        },
        error => {
          this.showLoader = false;

        }
      )
    );
  }

  // used to get sales associates list
  getSaleAssociate() {
    this.showLoader = true;
    return new Promise<void>(resolve => {
      this.quoteTrackerService.getSalesAssociate(true).subscribe(
        (res: Array<SalesAssociate>) => {
          this.salesassociates = res;
          this.showLoader = false;
          resolve();
        },
        () => {
          resolve();
          this.showLoader = false;
        }
      );
    });
  }

  // sets the filters and gets the design QTS quotes
  searchDesignQuotes(clearFilter = false) {
    if (this.filter.dateFilterCategory) {
      this.filter.startDate = this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen);
      this.filter.endDate = this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen);
    }
    this.onFilterChange();
    this.initializeTable(clearFilter);
  }

  openColorSelectionRow(item:any) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {quotationId: item.id, designStatusId: item.designStatusId,row: item.designEngQuoteTrackerRowColor};
    this.matDialog
      .open(DesignEnggQuoteTrackerRowColourComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res) {
          if (res && res !== null && res !== undefined) {
            this.replaceItemById(item.id, res);
          }
        }
      });
  }

  openTrackerField(quoteId) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: quoteId, salesOrderNumber: this.filter.soNumber};
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-tracker-fields-model';
    this.matDialog
      .open(TrackerFieldsEditorComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res !== null && res !== undefined) {
          this.replaceItemById(quoteId, res);
        }
      });
  }

  replaceItemById(id: number, newItem: DesignEngineeringQuoteTracker) {
    const index = this.filteredList.findIndex(item => item.id === id);
    if (index !== -1) {
      this.filteredList.splice(index, 1, newItem);
    }
  }

  openColorSelectionColumn(value, data) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {quoteId: data.id, value: value,data:data};
    this.matDialog
      .open(DesignEnggQuoteTrackerColumnColorComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        if (res && res !== null && res !== undefined) {
          this.replaceItemById(data.id, res);
        }
      });
  }

  rowColorSelection(statusId:number){
    const status = this.statuses.find(data=> data.id === statusId);
    if(status!==null && status.rowColor!==null && status.rowColor!==undefined){
      return {'background': status.rowColor};
    }
    else{
      return {'background': 'white'};
    }
  }

  columnColorSelection(columnName: string, columnColors: DesignEngQuoteTrackerColumnColor[]) {
    const color = columnColors.find(item => item.designEngQuotationColumnName === columnName);
    if (color && color.colorValue) {
      return {'background': color.colorValue};
    }
    return {};
  }

  onSelectionChange(event: any) {
    const selectedValues = event.value;

    if (selectedValues.includes(this.allStatusValue)) {
      if (this.filter.quoteStatusIds.length-1 === this.statuses.length) {
        // If all are selected, deselect all
        this.filter.quoteStatusIds = [];
      } else {
        // Select all statuses
        this.filter.quoteStatusIds = this.statuses.map(status => status.id);
      }
    } else {
      // If the selection does not include "Select All"
      if (selectedValues.length === this.statuses.length) {
        // Add "Select All" to selected values if all statuses are selected
        this.filter.quoteStatusIds = [this.allStatusValue, ...selectedValues];
      } else {
        this.filter.quoteStatusIds = selectedValues;
      }
    }
  }

  selectStatusByDefault(clearFilter = false){
    this.filter.quoteStatusIds = [];
    this.filter.statusReadyForWork=true;
    this.filter.statusInProgress=true;
    this.selectStatus(clearFilter);
  }

  selectStatus(clearFilter = false){
    this.filter.quoteStatusIds = [];
    if(this.filter.statusReadyForWork===true){
      this.filter.quoteStatusIds.push(...this.statuses.filter(data=> Variable.STATUS_ARRAY_FOR_READY_TO_WORK.map(data=> data.toLowerCase()).includes(data.status.toLowerCase())).map(data=> data.id));
    }
    if(this.filter.statusInProgress===true){
      this.filter.quoteStatusIds.push(...this.statuses.filter(data=> Variable.STATUS_ARRAY_FOR_IN_PROGRESS.map(data=> data.toLowerCase()).includes(data.status.toLowerCase())).map(data=> data.id));
    }
    if(this.filter.statusOutForApproval===true){
      this.filter.quoteStatusIds.push(...this.statuses.filter(data=> Variable.STATUS_ARRAY_FOR_OFA.map(data=> data.toLowerCase()).includes(data.status.toLowerCase())).map(data=> data.id));
    }
    this.searchDesignQuotes(clearFilter);
  }

  closePopup(){
    this.isFindPopupVisible = false;
    this.matchedItems = [];
    this.currentMatch=0;
    this.totalMatches=0;
    this.filteredList.forEach(data=>{
      data.highlight = null;
    })
  }

  onSearch(){
    if (this.searchText.trim() === '') {
      this.filteredList.forEach(data=>{
        data.highlight = null;
      })
      return;
    }

    const searchTerm = this.searchText.toLowerCase();
    this.matchedItems = [];
    this.totalMatches=0;
    this.currentMatch = 0;
    this.filteredList.forEach(item=>{
      if(item.soNumber.toLowerCase().includes(searchTerm)){
        item.highlight = 'previous';
        this.matchedItems.push(item);
      }
      else{
        item.highlight = null;
      }
    })
    this.totalMatches = this.matchedItems.length;
  }

  previousMatch() {
    if (this.currentMatch > 1) {
      this.currentMatch--;
      this.highlightCurrentMatch();
    } else if (this.totalMatches > 0) {
      this.currentMatch = this.totalMatches;
      this.highlightCurrentMatch();
    }
  }

  nextMatch() {
    if (this.currentMatch < this.totalMatches) {
      this.currentMatch++;
      this.highlightCurrentMatch();
    } else if (this.totalMatches > 0) {
      this.currentMatch = 1;
      this.highlightCurrentMatch();
    }
  }

  highlightCurrentMatch(){
    if (this.currentMatch > 0) {
      this.matchedItems.forEach(data=>{
        data.highlight = 'previous';
      })
      const currentItem = this.matchedItems[this.currentMatch - 1];
      const index : number = this.filteredList.indexOf(currentItem)-5;
      currentItem.highlight = 'current';
      this.virtualScroller.scrollToIndex(index);
    }
  }

  // used to get the app engg quotes for QTS
  getDesignEnggQuotes(pageIndex: number, pageSize: number,clearFilter:boolean) {
    return new Promise<DesignEngineeringQuoteTracker[]>((resolve, reject) => {
      this.showLoader = true;
      let newDesignList = [];
      let newDesignTypeList = [];
      let newBothTypeList = [];
      let newAppTypeList = [];
      this.newFilterDesignList = [];
      if(clearFilter){
        this.sortByEngReleaseDate = Variable.sortByEngReleaseDate;
        this.sortOrder=Variable.defaultSortOrder;
      }
      this.sortActive=this.sortByEngReleaseDate;
      const pageable = {
        page: pageIndex, size: pageSize, sort: this.sortByEngReleaseDate + ',' + this.sortOrder
      };
      this.subscription.add(
        this.quoteTrackerService.getDesignEngQuoteTracker(pageable, this.filter).subscribe(
          (res: GenericPageable<DesignEngineeringQuoteTracker>) => {
            if (this.filter.quoteStatusIds === null) {
              for (let k = 0; k < this.statuses.length; k++) {
                if (!this.statuses[k].defaultHidden) {
                  newDesignList = res.content.filter(x => x.designStatusId === this.statuses[k].id);
                  if (newDesignList.length > 0) {
                    for (let j = 0; j < newDesignList.length; j++) {
                      this.newFilterDesignList.push(...newDesignList[j]);
                    }
                  }
                }
              }
              for (let s = 0; s < res.content.length; s++) {
                if (res.content[s].quotationStatusType === Values.DesignEng) {
                  if (res.content[s].defaultRowColor === true || res.content[s].defaultRowColor === null) {
                    res.content[s].displayColorValue = res.content[s].defaultRowColorValue;
                  } else {
                    res.content[s].displayColorValue = res.content[s].primaryColorValue;
                  }
                  newDesignTypeList.push(res.content[s]);
                } else if (res.content[s].quotationStatusType === Values.Both) {
                  if (res.content[s].defaultRowColor === true || res.content[s].defaultRowColor === null) {
                    res.content[s].displayColorValue = res.content[s].defaultRowColorValue;
                  } else {
                    res.content[s].displayColorValue = res.content[s].primaryColorValue;
                  }
                  newBothTypeList.push(res.content[s]);
                } else if (res.content[s].quotationStatusType === Values.ApplicationEng) {
                  if (res.content[s].defaultRowColor === true || res.content[s].defaultRowColor === null) {
                    res.content[s].displayColorValue = res.content[s].defaultRowColorValue;
                  } else {
                    res.content[s].displayColorValue = res.content[s].primaryColorValue;
                  }
                  newAppTypeList.push(res.content[s]);
                }
              }
              this.newFilterDesignList.push(...newDesignTypeList);
              this.newFilterDesignList.push(...newBothTypeList);
              this.newFilterDesignList.push(...newAppTypeList);
            } else {
              this.newFilterDesignList = res.content;
            }
            this.length = res.totalElements;
            this.designEnggQTSPageable = res;
            this.totalNoOfPages = res.totalPages;
            this.pageIndex = res.number;
            this.numberOfElements = res.numberOfElements;
            this.showLoader = false;
            this.designEnggQTSPageable.content = [];
            this.designEnggQTSPageable.content = res.content;
            resolve(this.newFilterDesignList);
          },
          () => {
            (this.showLoader = false);
            reject();
          }
        )
      );
    });
  }

  clearSOFilter() {
    let soNumber = this.filter.soNumber;
    this.filter = new FilterQuoteTracker();
    this.filter.soNumber = soNumber;
  }

  // used to clear the selected/ applied filter
  clearFilter() {
    this.filter = new FilterQuoteTracker();
    this.sharedService.removeFilterItem();
    this.selectStatusByDefault(true);
  }

  public appendItems(items): void {
    this.filteredList.push(...items);
  }

  // used to handle the sorting on the mat table
  getSorting(event) {
    this.sortActive = event.active;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    if (this.ascSort) {
      this.sortOrder = Variable.defaultSortOrderDescending;
    } else {
      this.sortOrder = Variable.defaultSortOrder;
    }
    this.sortByEngReleaseDate = event.active;
    this.initializeTable(false);
  }

  downloadDesignEnggReportExcel() {
    this.showLoader = true;
    if (this.filter.dateFilterCategory) {
      this.filter.startDate = this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen);
      this.filter.endDate = this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen);
    }
    this.subscription.add(
      this.quoteTrackerService.downloadDesignQuoteTrackerExcel(this.filter).subscribe(
        success => {
          this.downloadExcel(success, 'Design-Engg-Quote-Tracker' + ' - ' + this.parseDate(this.today) + '.xlsx');
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // Custom function to parse the date string
  parseDate(input: string): string {
    const parts = input.split('/');
    return `${parts[2]}-${parts[1]}-${parts[0]}`; // Convert to YYYY-MM-DD
  }

  downloadExcel(response, sheetName: string) {
    const dwldLink = document.createElement('a');
    const url = URL.createObjectURL(response);
    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('target', '_blank');
    dwldLink.setAttribute('download', sheetName);
    dwldLink.style.visibility = 'hidden';
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }


  async fetchMore(event: IPageInfo) {
    if (this.pageIndex >= this.totalNoOfPages) {
      return;
    }
    if ((event.endIndex === -1) || (event.endIndex !== this.filteredList.length - 1)) {
      return;
    }
    this.showLoader = true;
    await this.fetchNextChunk(this.pageIndex + 1, this.pageSize,false).then(chunk => {
      this.appendItems(chunk);
      this.showLoader = false;
    }, () => this.showLoader = false);
  }

  fetchNextChunk(skip: number, limit: number,clearFilter:boolean): Promise<DesignEngineeringQuoteTracker[]> {
    return new Promise(async (resolve) => {
      this.pageIndex = skip;
      resolve(await this.getDesignEnggQuotes(skip, limit,clearFilter));
    });
  }

  checkIsAuthorized() {
    this.isAuthorized = this.roleAuthorisationServiceService.isAuthorised(this.authorizedRole);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
