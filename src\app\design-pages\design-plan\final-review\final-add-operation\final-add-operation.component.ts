import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { OperationsMasterData, Operation, JacketProductType } from '../../bom-editor/bom-editor.model';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { BomEditorService } from '../../bom-editor/bom-editor.service';

@Component({
  selector: 'sfl-final-add-operation',
  templateUrl: './final-add-operation.component.html'
})
export class FinalAddOperationComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();

  operationList: OperationsMasterData[];
  operationDetails: OperationsMasterData;
  saveOperation: OperationsMasterData;
  jacketId: number;
  type: string;
  productType: string;
  selection: string;

  constructor(
    public dialogRef: MatDialogRef<FinalAddOperationComponent>,
    private bomeditorService: BomEditorService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketId = data.jacketId;
    this.type = data.type;
  }

  ngOnInit() {
    this.operationDetails = new OperationsMasterData();
    this.saveOperation = new OperationsMasterData();
    this.getProductType();
  }

  // used to get the product type of a jacket by jacket id
  getProductType() {
    this.subscription.add(
      this.bomeditorService.getProductTypeByJacketId(this.jacketId).subscribe((typeObject: JacketProductType) => {
        this.productType = typeObject.productType;
        this.getAllOperations();
      })
    );
  }

  getAllOperations() {
    this.subscription.add(
      this.bomeditorService.getAllMaterialByGroupId(this.productType, 5, this.type).subscribe((res: OperationsMasterData[]) => {
        if (res) {
          this.operationList = res;
        }
      })
    );
  }

  onOperationSelect(i) {
    this.operationDetails = this.operationList[i];
  }

  saveOperaton() {
    this.operationDetails.jacketId = this.jacketId;
    this.operationDetails.id = null;
    this.operationDetails.type = this.type;
    this.dialogRef.close(this.operationDetails);
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
