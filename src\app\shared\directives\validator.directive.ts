import {
  AfterContentInit,
  Attribute,
  Directive,
  ElementRef,
  forwardRef,
  HostListener,
  Input,
  OnChanges,
  OnDestroy
} from '@angular/core';
import { AbstractControl, FormControl, NG_VALIDATORS, NgModel, Validator, ValidatorFn } from '@angular/forms';
import { SharedService } from '../service/shared.service';

@Directive({
  selector: '[sflIsNumber]'
})
export class IsNumberDirective {
  private el: HTMLInputElement;
  constructor(private elementRef: ElementRef) {
    this.el = this.elementRef.nativeElement;
  }

  @HostListener('keydown', ['$event']) onKeyUp(event) {
    const e = <KeyboardEvent>event;

    if (
      [46, 8, 9, 27, 13].indexOf(e.keyCode) !== -1 ||
      // Allow: Ctrl+A
      (e.keyCode === 65 && (e.ctrlKey || e.metaKey)) ||
      // Allow: Ctrl+C
      (e.keyCode === 67 && (e.ctrlKey || e.metaKey)) ||
      // Allow: Ctrl+V
      (e.keyCode === 86 && (e.ctrlKey || e.metaKey)) ||
      // Allow: Ctrl+X
      (e.keyCode === 88 && (e.ctrlKey || e.metaKey)) ||
      // Allow: home, end, left, right
      (e.keyCode >= 35 && e.keyCode <= 39)
    ) {
      // let it happen, don't do anything
      return;
    }

    // Ensure that it is a number
    if ((e.shiftKey || e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 96 || e.keyCode > 105) &&  // Disallow non-numeric numpad keys
      ![107, 109, 106, 111, 53, 189].includes(e.keyCode)) {
      e.preventDefault();
    }
  }
}

@Directive({
  selector: '[sflIsDecimal]'
})
export class IsDecimalDirective {
  private el: HTMLInputElement;
  constructor(private elementRef: ElementRef, private ngModel: NgModel) {
    this.el = this.elementRef.nativeElement;
  }

  @HostListener('keydown', ['$event']) onkeydown(event) {
    const e = <KeyboardEvent>event;
    const value = (<HTMLInputElement>event.target).value;
    const DEFAULT_DECIMAL_VALUE = 0.0;

    if (
      [46, 8, 9, 27, 13].indexOf(e.keyCode) !== -1 ||
      // Allow: Ctrl+A
      (e.keyCode === 65 && (e.ctrlKey || e.metaKey)) ||
      // Allow: Ctrl+C
      (e.keyCode === 67 && (e.ctrlKey || e.metaKey)) ||
      // Allow: Ctrl+V
      (e.keyCode === 86 && (e.ctrlKey || e.metaKey)) ||
      // Allow: Ctrl+X
      (e.keyCode === 88 && (e.ctrlKey || e.metaKey)) ||
      // Allow: home, end, left, right
      (e.keyCode >= 35 && e.keyCode <= 39)
    ) {
      // let it happen, don't do anything
      return;
    }

    if (
      (e.shiftKey || e.keyCode < 48 || e.keyCode > 57) && // Disallow non-numeric keys
      (e.keyCode < 96 || e.keyCode > 105) && // Disallow non-numeric numpad keys
      ![107, 109, 106, 111, 53, 189].includes(e.keyCode) // Allow + (107), - (109), * (106), / (111), % (53), - (189)
    ) {
      if (e.keyCode === 110 || e.keyCode === 190) { // Allow decimal
        if (value.toString().includes('.')) {
          e.preventDefault();
        }
        if (value.length === 0 && e.key === '.') {
          this.ngModel.update.emit(DEFAULT_DECIMAL_VALUE);
        }
      } else {
        e.preventDefault();
      }
    }

  }
}

@Directive({
  selector: '[sflDecimalLimit]'
})
export class DecimalLimitDirective {
  constructor(private el: ElementRef) {}

  @HostListener('input', ['$event']) onInputChange(event: any) {
    let inputValue = this.el.nativeElement.value;

    // Allow only numbers and single dot
    inputValue = inputValue.replace(/[^0-9.]/g, '');

    // Split integer and decimal parts
    const parts = inputValue.split('.');
    let intPart = parts[0] || '';
    let decPart = parts[1] || '';

    // Limit digits
    if (intPart.length > 15) {
      intPart = intPart.substring(0, 15);
    }

    if (decPart.length > 5) {
      decPart = decPart.substring(0, 5);
    }

    // Final value
    let finalValue = intPart;
    if (inputValue.includes('.') && (decPart.length > 0 || inputValue.endsWith('.'))) {
      finalValue += '.' + decPart;
    }

    // Update input box
    this.el.nativeElement.value = finalValue;
  }

  @HostListener('keypress', ['$event']) onKeyPress(event: KeyboardEvent) {
    const allowedKeys = ['0','1','2','3','4','5','6','7','8','9','.'];
    const inputChar = event.key;

    // Only allow one dot
    if (inputChar === '.' && this.el.nativeElement.value.includes('.')) {
      event.preventDefault();
    }

    if (!allowedKeys.includes(inputChar)) {
      event.preventDefault();
    }
  }
}


@Directive({
  selector: '[sflEmailValidate][ngModel]',
  providers: [{ provide: NG_VALIDATORS, useExisting: EmailValidator, multi: true }]
})
export class EmailValidator implements Validator {
  validator: ValidatorFn;
  constructor() {
    this.validator = validateEmailFactory();
  }
  validate(c: FormControl) {
    return this.validator(c);
  }
}

// validation function
function validateEmailFactory(): ValidatorFn {
  return (c: AbstractControl) => {
    if (c.value) {
      // tslint:disable-next-line:max-line-length
      if (
        c.value.match(
          /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        )
      ) {
        return null;
      } else {
        return {
          appEmailValidate: true
        };
      }
    }
  };
}

@Directive({
  selector: '[sflPhoneValidate][ngModel]',
  providers: [{ provide: NG_VALIDATORS, useExisting: PhoneValidator, multi: true }]
})
export class PhoneValidator implements Validator {
  validator: ValidatorFn;
  constructor() {
    this.validator = validatePhoneFactory();
  }
  validate(c: FormControl) {
    return this.validator(c);
  }
}

// validation function
function validatePhoneFactory(): ValidatorFn {
  return (c: AbstractControl) => {
    if (c.value) {
      if (c.value.match(/^[89]\d{9}$/) || c.value.match(/^[6]\d{9}$/)) {
        return null;
      } else {
        return {
          appPhoneValidate: true
        };
      }
    }
  };
}

@Directive({
  selector: '[sflPasswordValidate][ngModel]',
  providers: [{ provide: NG_VALIDATORS, useExisting: PasswordValidator, multi: true }]
})
export class PasswordValidator implements Validator {
  validator: ValidatorFn;
  constructor() {
    this.validator = validatePasswordFactory();
  }
  validate(c: FormControl) {
    return this.validator(c);
  }
}

// validation function
function validatePasswordFactory(): ValidatorFn {
  return (c: AbstractControl) => {
    if (c.value) {
      if (c.value.match(/^(?=\S*[a-z])(?=\S*[A-Z])(?=\S*\d)(?=\S*[^\w\s])\S{8,}$/)) {
        return null;
      } else {
        return {
          appPasswordValidate: true
        };
      }
    }
  };
}

@Directive({
  selector: '[sflEqualvalidate][formControlName],[formControl],[ngModel]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => EqualValidator),
      multi: true
    }
  ]
})
export class EqualValidator implements Validator {
  constructor(@Attribute('appEqualvalidate') public appEqualvalidate: string) {}

  validate(abControl: AbstractControl): { [key: string]: any } {
    // Get self value.
    const val = abControl.value;
    // Get control value.
    const cValue = abControl.root.get(this.appEqualvalidate);
    // value not equal
    if (cValue && val !== cValue.value) {
      return {
        appEqualvalidate: false
      };
    }

    return null;
  }
}

@Directive({
  selector: '[autoFocus], [sflAutoFocus]'
})
export class AutofocusDirective implements AfterContentInit, OnChanges, OnDestroy {
  @Input() public sflAutoFocus;
  @Input() public feature;

  public constructor(private el: ElementRef, private sharedService: SharedService) {}

  public ngAfterContentInit() {
    if (!this.sflAutoFocus && this.feature !== this.sharedService.currentFeatureForAutoFocus) {
      this.el.nativeElement.focus();
      this.sharedService.currentFeatureForAutoFocus = this.feature;
    }
  }

  ngOnChanges() {}

  ngOnDestroy() {}
}

@Directive({
  selector: '[sflNoWhiteSpaces]',
  providers: [{ provide: NG_VALIDATORS, useExisting: NoWhitespaceDirective, multi: true }]
})
export class NoWhitespaceDirective implements Validator {
  private valFn = NoWhitespaceValidator();
  validate(control: AbstractControl): { [key: string]: any } {
    return this.valFn(control);
  }
}
export function NoWhitespaceValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } => {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { whitespace: 'Only white spaces are not allowed' };
  };
}

@Directive({
  selector: '[sflToUpperCase]'
})
export class ConvertToUppercaseDirective {
  constructor(public elemRef: ElementRef) {}

  @HostListener('input', ['$event']) onInputChange($event) {
    this.elemRef.nativeElement.value = this.elemRef.nativeElement.value.toUpperCase();
  }
}
