export class GenericPageable<T> {
  constructor(
    public content?: T[],
    public totalElements?: number,
    public totalPages?: number,
    public number?: number,
    public numberOfElements?: number
  ) { }
}

export class FilterPageable {
  public page?: number;
  public size?: number;
  public sort?: string;
}

export class FilterDateRange {
  public startDate?: string;
  public endDate?: string;
}

export class SoInDesign {
  constructor(public id?: number, public soInDesign?: string, public noOfDesign?: number, public dollarValue?: number) { }
}

export const SoInDesignPageable = new GenericPageable<SoInDesign>();

export class SoInDesignDetailed {
  constructor(public classification?: string, public noOfDesign?: number, public dollarValue?: number) { }
}

export class QuoteCompleted {
  constructor(public id?: number, public classification?: string, public noOfDesign?: number, public dollarValue?: number) { }
}
export class QuoteCompletedDetailed {
  constructor(
    public salesOrderNumber?: string,
    public quotationNumber?: string,
    public noOfDesigns?: number,
    public dollarValue?: number
  ) { }
}

export const QuoteCompletedDetailedPageable = new GenericPageable<QuoteCompletedDetailed>();

export class QuoteStatusCount {
  constructor(
    public rfqInQueueStatus?: number,
    public quoteCompleteStatus?: number,
    public quotingInProcessStatus?: number,
    public patternDesign?: number,
    public levelOneReview?: number,
    public outForApproval?: number,
    public soInDesign?: number
  ) {
    this.rfqInQueueStatus = 0;
    this.quoteCompleteStatus = 0;
    this.quotingInProcessStatus = 0;
    this.patternDesign = 0;
    this.levelOneReview = 0;
    this.outForApproval = 0;
    this.soInDesign = 0;
  }
}

export class AppRowColorDialog {
  public quoteId?: number;
  public quoteStatusId?: number;
}

export class AppEngineeringQuoteTracker {
  public customerName?: string;
  public projectTitle?: string;
  public quotationNumber?: string;
  public dateSubmittedToApp?: string;
  public appStartedDate?: string;
  public appCompletedDate?: string;
  public productType?: string;
  public extQuoteRequired?: boolean;
  public assignedAppEngineerId?: number;
  public accountManagerId?: number;
  public noOfDesigns?: number;
  public customerClarificationRequired?: boolean;
  public currentStatusComment?: string;
  public expedite: boolean = null;
  public designStatusId?: number;
  public quotationStatusType?: string;
  public oemRequiringSimulation: boolean = null;
  public appQuoteTrackerRowColor: AppQuoteTrackerRowColor = new AppQuoteTrackerRowColor();
  public appQuoteTrackerColumnColorList = new Array<AppQuoteTrackerColumnColorDTO>();
  public id?: number;
  public notes?: string;
  public isEditingNotes?: boolean;
  public sqtLink?: string;
  public isEditingSqtLink?: boolean;
}

export class AppQuoteTrackerColumnColorDTO {
  public colorValue?: string;
  public id?: number;
  public appEngQuotationColumnName?: string;
}

export class AppQuoteTrackerRowColor {
  public colorValue?: string;
  public id?: number;
}

export class DesignEngineeringQuoteTracker {
  public id?: number;
  public projectTitle?: string;
  public soNumber?: string;
  public customerName?: string;
  public designLocation?: string;
  public shipDate?: string;
  public folderSubmittedDate?: string;
  public releaseDate?: string;
  public designStatusId?: number;
  public approvalStatusId?: number;
  public ofa1Date?: string;
  public approval1Date?: string;
  public ofa2Date?: string;
  public approval2Date?: string;
  public salesRepId?: number;
  public productType?: string;
  public designerId?: number;
  public noOfDesigns?: number;
  public dollarAmount?: number;
  public designComments?: string;
  public noOfRevisions?: number;
  public noOfDaysOFA1?: number;
  public noOfDaysOFA2?: number;
  public totalDaysOFA?: number;
  public soRevCount?: number;
  public revisionDate?: string;
  public lastOFADate?: string;
  public appEnggAssigned?: number;
  public quotationStatusType?: string;
  public defaultRowColor: boolean;
  public displayColorValue: string;
  public primaryColorValue: string;
  public defaultRowColorValue: string;
  public designEngQuoteTrackerRowColor: DesignEngQuoteTrackerRowColor = new DesignEngQuoteTrackerRowColor();
  public designEngQuoteTrackerColumnColorList = new Array<DesignEngQuoteTrackerColumnColor>();
  public highlight:string;
}

export class DesignEngineeringRowQuoteTracker {
  public id?: number;
  public projectTitle?: string;
  public soNumber?: string;
  public customerName?: string;
  public designLocation?: string;
  public shipDate?: string;
  public folderSubmittedDate?: string;
  public designStatusId?: number;
  public ofa1Date?: string;
  public approval1Date?: string;
  public ofa2Date?: string;
  public approval2Date?: string;
  public salesRepId?: number;
  public productType?: string;
  public designerId?: number;
  public noOfDesigns?: number;
  public dollarAmount?: number;
  public designComments?: string;
  public noOfRevisions?: number;
  public noOfDaysOFA1?: number;
  public noOfDaysOFA2?: number;
  public totalDaysOFA?: number;
  public appEnggAssigned?: number;
  public quotationStatusType?: string;
  public defaultRowColor: boolean;
  public primaryColorValue: string;
  public defaultRowColorValue: string;
  public designEngQuoteTrackerRowColor: DesignEngQuoteTrackerRowColor = new DesignEngQuoteTrackerRowColor();
  public designEngQuoteTrackerColumnColorList = new Array<DesignEngQuoteTrackerColumnColor>()
}


export class DesignEngQuoteTrackerRowColor {
  public colorValue?: string;
  public id?: number;
}

export class DesignEngQuoteTrackerColumnColor {
  public colorValue?: string;
  public id?: number;
  public designEngQuotationColumnName?: string;
}

export class FilterQuoteTracker {
  constructor(
    public quoteNumber?: string,
    public soNumber?: string,
    public appEngAssignedId?: number,
    public designEngAssignedId?: number[],
    public dateFilterCategory?: string,
    public quoteStatusId?: number,
    public quoteStatusIds?: number[],
    public customProductType?: string[],
    public salesAssociatesId?: number[],
    public startDate?: string,
    public endDate?: string,
    public extQuoteRequired: boolean = null,
    public designerCountry?: string,
    public customerClarificationRequired: boolean = null,
    public projectName?: string,
    public designDashboardTracker?: boolean,
    public customerName?:string,
    public designLocation?:string[],
    public designerIds?:number[],
    public statusReadyForWork?:boolean,
    public statusInProgress?:boolean,
    public statusOutForApproval?:boolean
  ) {
    this.soNumber = null;
    this.quoteNumber = null;
    this.appEngAssignedId = null;
    this.dateFilterCategory = null;
    this.endDate = null;
    this.startDate = null;
    this.quoteStatusId = null;
    this.salesAssociatesId = null;
    this.extQuoteRequired = null;
    this.designerCountry = null;
    this.customerClarificationRequired = null;
    this.projectName = null;
    this.designEngAssignedId = null;
    this.designDashboardTracker = false;
    this.customerName=null;
    this.designLocation=null;
    this.designerIds=null;
    this.statusReadyForWork = false;
    this.statusInProgress = false;
    this.statusOutForApproval = false;
    this.customProductType = null;
  }
}

export class FilterDesignQuoteTracker {
  constructor(
    public dateFilterCategory?: string,
    public quoteStatusId?: number,
    public salesAssociatesId?: number[],
    public startDate?: string,
    public endDate?: string
  ) {
    this.dateFilterCategory = null;
    this.startDate = null;
    this.endDate = null;
    this.quoteStatusId = null;
    this.salesAssociatesId = null;
  }
}

export class Statuses {
  public id: number;
  public status: string;
  public type: string;
  public orderNumber: number;
  public defaultHidden: boolean;
  public rowColor?: string;
  public appEngRowColor?: string;
}

export class SalesAssociate {
  public id?: number;
  public firstName?: string;
  public lastName?: number;
  public authorities?: string;
}

export class ExportToExcel {
  public appEngAssignedId?: number;
  public dateFilterCategory?: string;
  public quoteStatusId?: number;
  public salesAssociatesId?: number;
  public startDate?: string;
  public endDate?: string;
}


export class SoForm {
  public orderNum?: string;
  public requestDate?: string;
  public docOrderAmt?: number;
  public quotationNumber?: string;
}
