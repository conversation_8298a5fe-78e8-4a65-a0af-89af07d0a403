<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #SolidWorksBlockMasterForm="ngForm" (ngSubmit)="updateSolidWorksBlockMaster()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="45" fxFlex.gt-md="45" fxFlex.gt-sm="45" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="USA File Path" name="usaFilePath" [(ngModel)]="solidWorksBlockMaster.usaFilePath" required />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="45" fxFlex.gt-md="45" fxFlex.gt-sm="45" fxFlex.gt-xs="220">
        <mat-form-field>
          <input
            matInput
            placeholder="Vietnam File Path"
            name="vietnamFilePath"
            [(ngModel)]="solidWorksBlockMaster.vietnamFilePath"
            required
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <mat-select placeholder="Element Type" [(ngModel)]="selectedJacketTypes" name="elementType" #elementTypeInput="ngModel" multiple>
            <mat-option *ngFor="let jacketType of jacketTypes" [value]="jacketType?.id">
              {{ jacketType?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <mat-select placeholder="Phase" name="phase" [(ngModel)]="selectedPhase" #phaseSelect="ngModel" multiple>
            <mat-option *ngFor="let phase of phaseTypes" [value]="phase?.id">
              {{ phase?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <mat-select placeholder="Plug" [(ngModel)]="selectedPlugs" name="plug" #plugInput="ngModel" multiple>
            <mat-option *ngFor="let plug of plugs" [value]="plug?.plugName">
              {{ plug?.plugName }} {{ plug?.jacketType ? '- ' + plug?.jacketType : '' }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <mat-select placeholder="Connector" [(ngModel)]="selectedConnectors" name="connector" #connectorInput="ngModel" multiple>
            <mat-option *ngFor="let plug of plugs" [value]="plug?.plugName">
              {{ plug?.plugName }} {{ plug?.jacketType ? '- ' + plug?.jacketType : '' }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Min Jumper Qty" name="minJumperQty" [(ngModel)]="solidWorksBlockMaster.minJumperQty" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Max Jumper Qty" name="maxJumperQty" [(ngModel)]="solidWorksBlockMaster.maxJumperQty" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Min Jumper Length" name="minJumperLength" [(ngModel)]="solidWorksBlockMaster.minJumperLength" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Max Jumper Length" name="maxJumperLength" [(ngModel)]="solidWorksBlockMaster.maxJumperLength" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Min Lead Length" name="minLeadLength" [(ngModel)]="solidWorksBlockMaster.minLeadLength" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Max Lead Length" name="maxLeadLength" [(ngModel)]="solidWorksBlockMaster.maxLeadLength" />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <mat-select placeholder="Control Type" [(ngModel)]="selectedControlTypes" name="controlType" #controlTypeInput="ngModel" multiple>
            <mat-option *ngFor="let controlType of accessoryControllers" [value]="controlType?.name">
              {{ controlType?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <mat-select placeholder="Product Type" [(ngModel)]="selectedProductTypes" name="productType" #productTypeInput="ngModel" multiple>
            <mat-option *ngFor="let prodType of productTypes" [value]="prodType?.id">
              {{ prodType?.value }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <mat-select
            placeholder="Approval Level"
            [(ngModel)]="selectedApprovalLevels"
            name="approvalLevel"
            #approvalLevelInput="ngModel"
            multiple
          >
            <mat-option *ngFor="let approvalLevel of approvalLevels" [value]="approvalLevel?.value">
              {{ approvalLevel?.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox
          name="greenLight"
          color="warn"
          [indeterminate]="solidWorksBlockMaster?.greenLight === null"
          [ngModel]="solidWorksBlockMaster?.greenLight === true"
          (ngModelChange)="setCheckBoxTriStateValues(solidWorksBlockMaster?.greenLight, GreenLightCheckBoxTitle)"
        >
          Green Light -
          {{
            solidWorksBlockMaster?.greenLight === true
              ? checkBoxYesLabel
              : solidWorksBlockMaster?.greenLight === false
              ? checkBoxNoneLabel
              : checkBoxNullLabel
          }}
        </mat-checkbox>
      </div>
      <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox
          name="redLight"
          color="warn"
          [indeterminate]="solidWorksBlockMaster?.redLight === null"
          [ngModel]="solidWorksBlockMaster?.redLight === true"
          (ngModelChange)="setCheckBoxTriStateValues(solidWorksBlockMaster?.redLight, RedLightCheckBoxTitle)"
        >
          Red Light -
          {{
            solidWorksBlockMaster?.redLight === true
              ? checkBoxYesLabel
              : solidWorksBlockMaster?.redLight === false
              ? checkBoxNoneLabel
              : checkBoxNullLabel
          }}
        </mat-checkbox>
      </div>
    </div>

    <mat-card sfl-bhx-material-card label-grp>
      <div fxLayout="row wrap" fxLayoutAlign="start" fxLayoutAlign="space-between">
        <mat-card-subtitle>Sensor Information</mat-card-subtitle>
        <mat-checkbox
          name="anySensor"
          [indeterminate]="solidWorksBlockMaster?.anySensor === null"
          [ngModel]="solidWorksBlockMaster?.anySensor === true"
          color="warn"
          (ngModelChange)="setCheckBoxTriStateValues(solidWorksBlockMaster?.anySensor, AnySensorCheckBoxTitle)"
        >
          Sensor - {{ solidWorksBlockMaster?.anySensor === true ? 'Any' : solidWorksBlockMaster?.anySensor === false ? 'None' : 'Null' }}
        </mat-checkbox>
      </div>
      <sfl-manage-sw-block-sensor-info
        [swBlockSensorsInfo]="solidWorksBlockMaster.solidWorksBlockSensorInformations"
        [anySensor]="solidWorksBlockMaster.anySensor"
      ></sfl-manage-sw-block-sensor-info>
    </mat-card>

    <mat-card sfl-bhx-material-card label-grp>
      <div fxLayout="row wrap" fxLayoutAlign="start" fxLayoutAlign="space-between">
        <mat-card-subtitle>Themostat Information</mat-card-subtitle>
        <mat-checkbox
          name="anyThermostat"
          [indeterminate]="solidWorksBlockMaster?.anyThermostat === null"
          [ngModel]="solidWorksBlockMaster?.anyThermostat === true"
          color="warn"
          (ngModelChange)="setCheckBoxTriStateValues(solidWorksBlockMaster?.anyThermostat, AnyThermostatCheckBoxTitle)"
        >
          Thermostat -
          {{ solidWorksBlockMaster?.anyThermostat === true ? 'Any' : solidWorksBlockMaster?.anyThermostat === false ? 'None' : 'Null' }}
        </mat-checkbox>
      </div>
      <sfl-manage-sw-block-thermostat-info
        [swBlockThermostatInfo]="solidWorksBlockMaster.solidWorksBlockThermostatInformations"
        [anyThermostat]="solidWorksBlockMaster.anyThermostat"
      ></sfl-manage-sw-block-thermostat-info>
    </mat-card>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog(false)">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="SolidWorksBlockMasterForm.invalid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
