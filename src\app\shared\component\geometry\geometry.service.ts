
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { AppConfig } from '../../../app.config';
import { utils } from '../../../shared/helpers/app.helper';
import { SharedService } from '../../service/shared.service';
import { createRequestOption } from '../../utils/utils';
import { AddJackets, AddNewJackets, Features } from './geometry.model';
@Injectable()
export class GemometryService {
  contentTypeAppJson = { 'Content-Type': 'application/json' };
  constructor(private http: HttpClient, private route: Router, private sharedService: SharedService) { }

  getFeatures() {
    return this.http
      .get(AppConfig.GET_FEATURES).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getJacketGroupByRevId(revisionId) {
    return this.http
      .get(AppConfig.GET_JACKETS_GROUP_BY_REVISON_ID + revisionId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  // deleteSelectedFeatures(jacketFeatureIdList){
  //   return this.http.delete(AppConfig.GEOMETRY_SELECTED_FEATURE_DELETE, jacketFeatureIdList).pipe(
  //     map(utils.extractData),
  //     catchError(utils.handleError),);
  // }

  deleteSelectedFeatures(jacketFeatureIdList): Observable<any> {
    return this.http.request<any>('delete', AppConfig.GEOMETRY_SELECTED_FEATURE_DELETE, { body: jacketFeatureIdList });
  }

  saveJacket(jackets: AddNewJackets, quotId: number) {
    return this.http
      .post(AppConfig.SAVE_JACKET + quotId, jackets).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getJacketsByRevId(revisionId) {
    return this.http
      .get(AppConfig.GET_ALL_JACKETS_By_REVID + revisionId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
  getJacketsByRevIdPageable(revisionId: number, jacketgroupId, pageable) {
    return this.http
      .get(AppConfig.GET_ALL_JACKETS_By_REVID + revisionId + this.route.createUrlTree(['jacketgroup/' + jacketgroupId]), {
        params: createRequestOption(pageable)
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  saveJacketFeatures(features: Features) {
    return this.http
      .post(AppConfig.SAVE_JACKET_FEATURES, features).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  updateJacketFeatures(features: Features) {
    return this.http
      .put(AppConfig.SAVE_JACKET_FEATURES, features).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  saveAllJackets(jacketObject) {
    return this.http
      .post(AppConfig.SAVE_ALL_JACKETS, jacketObject).pipe(
      map(utils.extractData),
      catchError(utils.drag_handle),);
  }

  deleteGeoJacket(id: number) {
    return this.http
      .delete(AppConfig.DELETE_JACKET_FROM_LIST + id, {}).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteGeoJacketFeatures(id) {
    return this.http
      .delete(AppConfig.DELETE_JACKET_FEATURE + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteJacketFeature(jacket, jacketFeatureId: number, centerLine) {
    const options = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      }),
      body: jacket
    };
    return this.http
      .delete(AppConfig.DELETE_JACKET_FEATURE + jacketFeatureId + '/' + centerLine, options).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  copyJacket(jacket: AddJackets) {
    return this.http
      .put(AppConfig.COPY_JACKET, jacket).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  changeEntryMethod(methodName: string, jacketList) {
    return this.http
      .put(AppConfig.CHANGE_ENTRY_METHOD + methodName, jacketList).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  elbowLengthCalculator(jacketGroupId, elbowDto) {
    return this.http
      .put(AppConfig.GET_ELBOWLENGTH + jacketGroupId, elbowDto).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  jacketLengthCalculator(jacketList, entryMethod) {
    return this.http
      .put(AppConfig.GET_JACKET_LENGTH + entryMethod, jacketList).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getJacketByJacketId(jacketID) {
    return this.http
      .get(AppConfig.GETJACKETBYJACKETID + jacketID).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getEntryMethodByQuotation(quotationId: number) {
    return this.http.get(AppConfig.GET_ENTRY_METHOD_QUOTE + quotationId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getDefaultsGeometry(defaultGeometry) {
    return this.http
      .post(AppConfig.GET_DEFAULTS_GEOMETRY, defaultGeometry).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  uploadImageGeometry(file: File, jacketFeatureId) {
    const formData: FormData = new FormData();
    formData.append('file', file);
    return this.http
    .post(AppConfig.GEOMETRY_IMAGE_UPLOAD + jacketFeatureId + '/image', formData, {responseType: 'text'}).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }

  uploadJacketImageGeometry(file: File, id) {
    const formData: FormData = new FormData();
    formData.append('file', file);
    return this.http
    .post(AppConfig.GEOMETRY_JACKET_IMAGE_UPLOAD + id + '/image', formData, {responseType: 'text'}).pipe(
    map(utils.extractData),
    catchError(utils.handleError),);
  }


  getJacketsByPageable(revisionId, pageable) {
    return this.http
      .get(AppConfig.GET_ALL_JACKETS_By_REVID + revisionId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  drawJacketDesign(jacket: AddNewJackets, matchingSWBlocks: string[], isJacketModel: boolean) {
    return this.http
      .post(AppConfig.DRAW_JACKET_DESIGN, {
        jacketWithFeatures: jacket,
        ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations(),
        matchingSWBlocks: matchingSWBlocks,
        isJacketModel: isJacketModel,
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getMatchingSWBlockPartFilesLocationByJacketIdAndUserCountry(jacketId: number, loggedInUserCountry: string) {
    return this.http
      .get(AppConfig.SOLID_WORKS_BLOCK + 'part-generation/file-paths/jacket/' + jacketId + '/user/' + loggedInUserCountry).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
