import { BidiModule } from '@angular/cdk/bidi';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule, DatePipe } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_CHIPS_DEFAULT_OPTIONS,
  MAT_DIALOG_DEFAULT_OPTIONS,
  MatAutocompleteModule,
  MatBadgeModule,
  MatButtonModule,
  MatButtonToggleModule,
  MatCardModule,
  MatCheckboxModule,
  MatChipsModule,
  MatDatepickerModule,
  MatDialogModule,
  MatExpansionModule,
  MatFormFieldModule,
  MatIconModule,
  MatInputModule,
  MatListModule,
  MatMenuModule,
  MatNativeDateModule,
  MatPaginatorModule,
  MatProgressBarModule,
  MatProgressSpinnerModule,
  MatRadioModule,
  MatSelectModule,
  MatSidenavModule,
  MatSliderModule,
  MatSlideToggleModule,
  MatSnackBarModule,
  MatSortModule,
  MatStepperModule,
  MatTableModule,
  MatTabsModule,
  MatToolbarModule,
  MatTooltipModule,
  MatTreeModule
} from '@angular/material';
import { LoadingBarRouterModule } from '@ngx-loading-bar/router';
import { DragulaModule, DragulaService } from 'ng2-dragula';
import { CustomFormsModule } from 'ng2-validation';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { PERFECT_SCROLLBAR_CONFIG, PerfectScrollbarConfigInterface, PerfectScrollbarModule } from 'ngx-perfect-scrollbar';
import { SnakbarService, SweetAlertService } from '.';
import { TitleBlockEditorComponent } from '../design-pages/design-plan/title-block-editor/title-block-editor.component';
import { AddAttachmentComponent } from '../design-pages/ecr-management/add-attachment/add-attachment.component';
import { AppTrackerFieldComponent } from '../design-pages/jacket-list/app-tracker-field/app-tracker-field.component';
import {
  TrackerFieldsEditorComponent
} from '../design-pages/jacket-list/tracker-fields-editor/tracker-fields-editor.component';
import {
  DesignEnggQuoteTrackerRowColourComponent
} from '../quote-tracker/design-engg-quote-tracker/design-engg-quote-tracker-row-colour/design-engg-quote-tracker-row-colour.component';
import { DMTLogComponent } from './component/dml-log/dmt-log.component';
import { ErrorMessagesComponent } from './component/error-messages.component';
import { GeometryComponent } from './component/geometry/geometry.component';
import { GemometryService } from './component/geometry/geometry.service';
import { MoveJacketFeatureComponent } from './component/geometry/move-jacket-feature/move-jacket-feature.component';
import { ViewCcdcDesignComponent } from './component/geometry/view-ccdc-design/view-ccdc-design.component';
import { ViewFeatureImageComponent } from './component/geometry/view-feature-image/view-feature-image.component';
import { ViewJacketImageComponent } from './component/geometry/view-jacket-image/view-jacket-image.component';
import { LoaderComponent } from './component/loader-component/loader.component';
import {
  FilePathAlreadyExistModalComponent
} from './component/Part Number/file-path-already-exist-modal/file-path-already-exist-modal.component';
import {
  GenerateRootPNModalComponent
} from './component/Part Number/generate-root-pn-modal/generate-root-pn-modal.component';
import {
  NonStandardPnConfirmationModalComponent
} from './component/Part Number/non-standard-pn-confirmation-modal/non-standard-pn-confirmation-modal.component';
import {
  NewPartFilePathComponent
} from './component/Part Number/open-file-path-edit-modal/new-part-file-path/new-part-file-path.component';
import {
  OpenFilePathEditModalComponent
} from './component/Part Number/open-file-path-edit-modal/open-file-path-edit-modal.component';
import { PartNumberComponent } from './component/Part Number/part-number.component';
import { PNErrorComponent } from './component/Part Number/partnumber-error.component';
import { PreLoaderComponent } from './component/pre-loader/pre-loader.component';
import { SolidworksDownloadComponent } from './component/solidworks-download/solidworks-download.component';
import { AddNodeComponent, NewNodeDialog } from './component/tree/add-node/add-node.component';
import { DeleteNodeComponent } from './component/tree/delete-node/delete-node.component';
import { EditNodeComponent, EditNodeDialog } from './component/tree/edit-node/edit-node.component';
import {
  AutofocusDirective,
  ConvertToUppercaseDirective,
  DecimalLimitDirective,
  EmailValidator,
  EqualValidator,
  IsDecimalDirective,
  IsNumberDirective,
  NoWhitespaceDirective,
  PasswordValidator,
  PhoneValidator
} from './directives/validator.directive';
import { AppInterceptor } from './interceptors/app-intercepter';
import {
  ApprovalFormatPipe,
  ApprovalLevelPipe,
  ArrayToCSVPipe,
  ConvertToYesNoPipe,
  GetAccountManagerName,
  GetFirstNameDesignQuoteTracker,
  GetFullUserName,
  GetQuoteStatusName,
  MarkingsPipe,
  MaterialPipe,
  ProductTypePipe,
  SensorsPipe,
  SortCcdcDropdown,
  ThermostatTypePipe
} from './pipes/common.pipes';
import { ComparePipe } from './pipes/comparison.pipe';
import { AuthGuardService, NonAuthGuardService } from './service/auth-admin-guard.service';

const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
  suppressScrollX: true,
  wheelSpeed: 2,
  wheelPropagation: true,
  minScrollbarLength: 20
};

@NgModule({
  imports: [
    MatSortModule,
    MatTableModule,
    HttpClientModule,
    CustomFormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSidenavModule,
    MatCardModule,
    MatMenuModule,
    MatCheckboxModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    MatTabsModule,
    MatListModule,
    MatTooltipModule,
    MatChipsModule,
    MatButtonToggleModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatProgressBarModule,
    MatPaginatorModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSliderModule,
    FlexLayoutModule,
    LoadingBarRouterModule,
    BidiModule,
    MatStepperModule,
    PerfectScrollbarModule,
    MatFormFieldModule,
    MatBadgeModule,
    MatDialogModule,
    ScrollingModule,
    MatSnackBarModule,
    MatTreeModule,
    MatAutocompleteModule,
    MatRadioModule,
    MatExpansionModule,
    DragulaModule,
    FormsModule,
    InfiniteScrollModule,
    MatProgressSpinnerModule
  ],
  declarations: [
    DesignEnggQuoteTrackerRowColourComponent,
    EqualValidator,
    AutofocusDirective,
    IsNumberDirective,
    IsDecimalDirective,
    EmailValidator,
    PhoneValidator,
    PasswordValidator,
    ApprovalFormatPipe,
    MarkingsPipe,
    ApprovalLevelPipe,
    ProductTypePipe,
    ThermostatTypePipe,
    SensorsPipe,
    MaterialPipe,
    ComparePipe,
    ErrorMessagesComponent,
    GeometryComponent,
    DMTLogComponent,
    LoaderComponent,
    PartNumberComponent,
    MoveJacketFeatureComponent,
    PNErrorComponent,
    SolidworksDownloadComponent,
    GenerateRootPNModalComponent,
    ArrayToCSVPipe,
    PreLoaderComponent,
    NoWhitespaceDirective,
    AddAttachmentComponent,
    NonStandardPnConfirmationModalComponent,
    OpenFilePathEditModalComponent,
    NewPartFilePathComponent,
    AddNodeComponent,
    NewNodeDialog,
    EditNodeComponent,
    EditNodeDialog,
    DeleteNodeComponent,
    ConvertToUppercaseDirective,
    ConvertToYesNoPipe,
    GetFullUserName,
    GetFirstNameDesignQuoteTracker,
    GetQuoteStatusName,
    GetAccountManagerName,
    SortCcdcDropdown,
    FilePathAlreadyExistModalComponent,
    ViewCcdcDesignComponent,
    TrackerFieldsEditorComponent,
    NewPartFilePathComponent,
    AppTrackerFieldComponent,
    MoveJacketFeatureComponent,
    ViewFeatureImageComponent,
    ViewJacketImageComponent,
    TitleBlockEditorComponent,
    DecimalLimitDirective
  ],
  providers: [
    {
      provide: PERFECT_SCROLLBAR_CONFIG,
      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG
    },
    AuthGuardService,
    NonAuthGuardService,
    DatePipe,
    SnakbarService,
    { provide: MAT_DIALOG_DEFAULT_OPTIONS, useValue: { hasBackdrop: false } },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AppInterceptor,
      multi: true
    },
    {
      provide: MAT_CHIPS_DEFAULT_OPTIONS,
      useValue: {}
    },
    SweetAlertService,
    ApprovalFormatPipe,
    MarkingsPipe,
    ApprovalLevelPipe,
    ThermostatTypePipe,
    SensorsPipe,
    MaterialPipe,
    ComparePipe,
    DragulaService,
    GemometryService,
    ArrayToCSVPipe,
    ConvertToYesNoPipe,
    GetFullUserName,
    GetFirstNameDesignQuoteTracker,
    GetQuoteStatusName,
    GetAccountManagerName,
    SortCcdcDropdown
  ],
  exports: [
    MatTableModule,
    MatSortModule,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSidenavModule,
    MatCardModule,
    MatMenuModule,
    ScrollingModule,
    MatCheckboxModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    MatTooltipModule,
    MatChipsModule,
    MatButtonToggleModule,
    MatTabsModule,
    MatListModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatProgressBarModule,
    MatExpansionModule,
    MatPaginatorModule,
    MatRadioModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSliderModule,
    FlexLayoutModule,
    LoadingBarRouterModule,
    BidiModule,
    PerfectScrollbarModule,
    MatStepperModule,
    MatFormFieldModule,
    MatBadgeModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTreeModule,
    MatAutocompleteModule,
    EqualValidator,
    AutofocusDirective,
    DatePipe,
    ApprovalFormatPipe,
    MarkingsPipe,
    ApprovalLevelPipe,
    ProductTypePipe,
    ThermostatTypePipe,
    SensorsPipe,
    MaterialPipe,
    ErrorMessagesComponent,
    EmailValidator,
    ComparePipe,
    DesignEnggQuoteTrackerRowColourComponent,
    GeometryComponent,
    DMTLogComponent,
    LoaderComponent,
    PartNumberComponent,
    MoveJacketFeatureComponent,
    PNErrorComponent,
    SolidworksDownloadComponent,
    IsNumberDirective,
    IsDecimalDirective,
    MatProgressSpinnerModule,
    ArrayToCSVPipe,
    PreLoaderComponent,
    NoWhitespaceDirective,
    ConvertToUppercaseDirective,
    ConvertToYesNoPipe,
    GetFullUserName,
    GetFirstNameDesignQuoteTracker,
    GetQuoteStatusName,
    GetAccountManagerName,
    SortCcdcDropdown,
    TitleBlockEditorComponent,
    DecimalLimitDirective
  ],
  entryComponents: [
    GenerateRootPNModalComponent,
    DesignEnggQuoteTrackerRowColourComponent,
    AddAttachmentComponent,
    NonStandardPnConfirmationModalComponent,
    OpenFilePathEditModalComponent,
    NewPartFilePathComponent,
    NewNodeDialog,
    EditNodeDialog,
    FilePathAlreadyExistModalComponent,
    ViewCcdcDesignComponent,
    TrackerFieldsEditorComponent,
    AppTrackerFieldComponent,
    MoveJacketFeatureComponent,
    ViewFeatureImageComponent,
    ViewJacketImageComponent

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SharedModule { }
