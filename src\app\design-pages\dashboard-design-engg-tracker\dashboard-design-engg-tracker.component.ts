import { DatePipe } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { IPageInfo } from 'ngx-virtual-scroller';
import { Subscription } from 'rxjs';
import { Status } from 'src/app/admin-pages/dashboard/dashboard.model';
import { QuotationStatusMaster } from 'src/app/common-pages/masterdata-management/masterdata-management.model';
import { MasterdataManagementService } from 'src/app/common-pages/masterdata-management/masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { GetQuoteStatusName } from 'src/app/shared/pipes/common.pipes';
import { RoleAuthorisationServiceService } from 'src/app/shared/service/role-authorisation-service.service';
import {
  DesignEngineeringQuoteTracker,
  DesignEngQuoteTrackerColumnColor,
  FilterQuoteTracker,
  GenericPageable,
  SalesAssociate,
  Statuses
} from '../../quote-tracker/quote-tracker.model';
import { QuoteTrackerService } from '../../quote-tracker/quote-tracker.service';
import { Role, SharedService, SnakbarService } from '../../shared';
import { Variable } from '../../shared/constants/Variable.constants';
import { DownloadZipRequestDTO } from '../jacket-list/jacket-list.model';
import { JacketListService } from '../jacket-list/jacket-list.service';
import { TrackerFieldsEditorComponent } from '../jacket-list/tracker-fields-editor/tracker-fields-editor.component';
import {
  DashboardDesignEnggTrackerColumnColorComponent
} from './dashboard-design-engg-tracker-column-color/dashboard-design-engg-tracker-column-color.component';

@Component({
  selector: 'sfl-dashboard-design-engg-tracker',
  templateUrl: './dashboard-design-engg-tracker.component.html',
  styleUrls: ['./dashboard-design-engg-tracker.component.css'],
  providers: [
    GetQuoteStatusName
  ],
})
export class DashboardDesignEnggTrackerComponent implements OnInit, OnDestroy {
  headingTitle = 'Design Engineering SO Tracker';
  selectedRowIndex: number = -1;
  subscription = new Subscription();
  designEnggQTSPageable: GenericPageable<DesignEngineeringQuoteTracker>;
  quotationStatusPageable: GenericPageable<QuotationStatusMaster>;
  filter: FilterQuoteTracker = new FilterQuoteTracker();
  filteredList: DesignEngineeringQuoteTracker[] = [];
  showLoader = false;
  authorizedRole = [Role.ADMIN_ROLE, Role.DESIGN_ROLE, Role.ENG_ROLE];
  salesRole = Role.SALES_ROLE;
  isAuthorized = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  statuses: Statuses[];
  newStatus: Status[];
  salesassociates: SalesAssociate[];
  length: number;
  pageIndex = Variable.activePage;
  jacketLink = Variable.jacketListLink;
  pageSize = 25;
  sortAsc = Variable.defaultSortOrder;
  sortOrder = Variable.defaultSortOrderDescendingDesignQuote;
  sortField = Variable.sortByQuotation_number;
  sortFieldStatus = Variable.sortByQuotationStatus;
  sortShipDate = Variable.sortByshipDate;
  ascSort = Variable.sortAscending;
  numberOfElements: number;
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.itemsPerPage;
  soNumber: string;
  fromDate = new Date();
  toDate = new Date();
  today = new Date().toLocaleDateString();
  totalNoOfPages: number = 0;
  quotTypes = Values.QuotStatusTypes;
  newFilterDesignList = [];
  ignoreDefaultHidden: boolean = false;
  isFilter: any;
  quoteId: number;
  primaryColorValue: string;
  defaultRowColorValue: string;
  primaryColor: string;
  defaultRowColor: string;
  defaultColor: boolean;
  productTypes = Values.TRACKER_FIELDS_PRODUCT_TYPES;
  isSales: boolean;
  userId: number = null;
  designerId : number;

  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly datePipe: DatePipe,
    private readonly sharedService: SharedService,
    private pipeName: GetQuoteStatusName,
    private readonly titleService: Title,
    private readonly matDialog: MatDialog,
    private readonly masterDataService: MasterdataManagementService,
    private readonly roleAuthorisationServiceService: RoleAuthorisationServiceService,
    private readonly jacketListService: JacketListService,
    private readonly snakbarService: SnakbarService,
  ) {
  }

  // used to set from and to date for filter, retrieves quote statuses and then gets the app QTS quotes
  async ngOnInit() {
    this.userId = this.sharedService.getUserId();
    this.titleService.setTitle('QTS - Design Engineering');
    // setting up the default filter of 30 days from current day
    this.fromDate.setDate(this.toDate.getDate() - 30);
    this.checkIsAuthorized();
    this.getQuoteStatuses();
    await this.getSaleAssociate();
    this.initializeTable();
    this.isSales = this.sharedService.getRole() === Role.SALES_ROLE;
  }

  getRole(){
    return this.sharedService.getRole();
  }

  initializeTable() {
    this.filteredList = [];
    this.fetchNextChunk(this.initialPageIndex, this.pageSize).then(items => {
      this.appendItems(items);
    });
  }

  // used to get the quotes statuses
  getQuoteStatuses() {
    this.subscription.add(this.sharedService.getStatuesByTypeOrderByOrderNumber().subscribe((res: Statuses[]) => {
      if (res) {
        res.sort((a, b) => a.type < b.type ? 1 : -1);
        this.statuses = res;
      }
    }));
  }

  openTrackerField(quoteId) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {quotationId: quoteId, salesOrderNumber: this.filter.soNumber};
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-tracker-fields-model';
    this.matDialog
      .open(TrackerFieldsEditorComponent, matDataConfig)
      .afterClosed().subscribe(res => {
      if (res && res !== null && res !== undefined) {
        this.initializeTable();
      }
    });
  }

  retrieveAllZipFiles(quoteId: number) {
    const downloadZipRequestDTO = {
      quotationId: quoteId,
      jacketId: null,
    };
    this.downloadZipFiles(downloadZipRequestDTO);
  }

  downloadZipFiles(downloadZipRequestDTO: DownloadZipRequestDTO) {
    this.subscription.add(
      this.jacketListService.downloadZipFiles(downloadZipRequestDTO).subscribe((res) => {
        if (res.zips && res.zips.length > 0) {
          res.zips.forEach((zip) => {
            if (zip.zipFileBase64) {
              const blob = this.base64ToBlob(zip.zipFileBase64, 'application/zip');
              this.downloadFile(blob, zip.fileName);
            } else {
              this.snakbarService.error(zip.errorMessage, 10000);
            }
          });
        }

        if (res.errors && res.errors.length > 0) {
          const message = res.errors.join('\n').replace(/\r\n/g, '\n');
          this.snakbarService.error(message, 10000);
        }
      }),
    );
  }

  base64ToBlob(base64: string, contentType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i) => byteCharacters.charCodeAt(i));
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
  }

  downloadFile(blob: Blob, fileName: string) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }

  loadSalesUsers(){
    if(this.sharedService.getRole()==='ROLE_ADMIN'){
      this.designerId = this.userId;
      this.filter.designerIds = [this.userId];
      this.filter.designDashboardTracker = true;
    }
    else{
      if (this.userId && this.userId !== null && this.userId !== undefined) {
        this.designerId = this.userId
        this.filter.designEngAssignedId = [this.userId];
        this.filter.designDashboardTracker = true;
      }
    }
  }

  // used to get sales associates list
  getSaleAssociate() {
    this.showLoader = true;
    return new Promise<void>(resolve => {
      this.quoteTrackerService.getSalesAssociate(true).subscribe(
        (res: Array<SalesAssociate>) => {
          this.salesassociates = res;
          this.showLoader = false;
          this.loadSalesUsers();
          resolve();
        },
        () => {
          resolve();
          this.showLoader = false;
        }
      );
    });
  }

  // sets the filters and gets the design QTS quotes
  searchDesignQuotes() {
    if (this.filter.dateFilterCategory) {
      this.filter.startDate = this.datePipe.transform(this.fromDate, Values.dateFormat.formatHyphen);
      this.filter.endDate = this.datePipe.transform(this.toDate, Values.dateFormat.formatHyphen);
    }
    this.initializeTable();
  }


  openColorSelectionColumn(value, quoteId) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {quoteId: quoteId, value: value};
    this.matDialog
      .open(DashboardDesignEnggTrackerColumnColorComponent, matDataConfig)
      .afterClosed()
      .subscribe(res => {
        this.initializeTable();
      });
  }

  columnColorSelection(columnName: string, columnColors: DesignEngQuoteTrackerColumnColor[]) {
    const color = columnColors.find(item => item.designEngQuotationColumnName === columnName);
    if (color && color.colorValue) {
      return {'background': color.colorValue};
    }
    return {};
  }

  rowColorSelection(statusId:number){
    const status = this.statuses.find(data=> data.id === statusId);
    if(status!==null && status.rowColor!==null && status.rowColor!==undefined){
      return {'background': status.rowColor};
    }
    else{
      return {'background': 'white'};
    }
  }

  // used to get the app engg quotes for QTS
  getDesignEnggQuotes(pageIndex: number, pageSize: number) {
    return new Promise<DesignEngineeringQuoteTracker[]>((resolve, reject) => {
      this.showLoader = true;
      let newDesignList = [];
      let newDesignTypeList = [];
      let newBothTypeList = [];
      let newAppTypeList = [];
      this.newFilterDesignList = [];
      const pageable = {
        page: pageIndex, size: pageSize, sort: this.sortShipDate + ',' + this.sortOrder
      };

      if(this.designerId){
        this.filter.designerIds = [this.designerId];
      }
      // if (this.userId && this.userId !== null && this.userId !== undefined) {
      //   this.filter.designEngAssignedId = [this.userId];
      //   this.filter.designDashboardTracker = true;
      // }
      this.subscription.add(
        this.quoteTrackerService.getDesignEngQuoteTracker(pageable, this.filter).subscribe(
          (res: GenericPageable<DesignEngineeringQuoteTracker>) => {
            if (this.filter.quoteStatusId === null) {
              for (let k = 0; k < this.statuses.length; k++) {
                if (!this.statuses[k].defaultHidden) {
                  newDesignList = res.content.filter(x => x.designStatusId === this.statuses[k].id);
                  if (newDesignList.length > 0) {
                    for (let j = 0; j < newDesignList.length; j++) {
                      this.newFilterDesignList.push(...newDesignList[j]);
                    }
                  }
                }
              }
              for (let s = 0; s < res.content.length; s++) {
                if (res.content[s].quotationStatusType === Values.DesignEng) {
                  if (res.content[s].defaultRowColor === true || res.content[s].defaultRowColor === null) {
                    res.content[s].displayColorValue = res.content[s].defaultRowColorValue;
                  } else {
                    res.content[s].displayColorValue = res.content[s].primaryColorValue;
                  }
                  newDesignTypeList.push(res.content[s]);
                } else if (res.content[s].quotationStatusType === Values.Both) {
                  if (res.content[s].defaultRowColor === true || res.content[s].defaultRowColor === null) {
                    res.content[s].displayColorValue = res.content[s].defaultRowColorValue;
                  } else {
                    res.content[s].displayColorValue = res.content[s].primaryColorValue;
                  }
                  newBothTypeList.push(res.content[s]);
                } else if (res.content[s].quotationStatusType === Values.ApplicationEng) {
                  if (res.content[s].defaultRowColor === true || res.content[s].defaultRowColor === null) {
                    res.content[s].displayColorValue = res.content[s].defaultRowColorValue;
                  } else {
                    res.content[s].displayColorValue = res.content[s].primaryColorValue;
                  }
                  newAppTypeList.push(res.content[s]);
                }
              }
              this.newFilterDesignList.push(...newDesignTypeList);
              this.newFilterDesignList.push(...newBothTypeList);
              this.newFilterDesignList.push(...newAppTypeList);
            } else {
              this.newFilterDesignList = res.content;
            }
            this.length = res.totalElements;
            this.designEnggQTSPageable = res;
            this.totalNoOfPages = res.totalPages;
            this.pageIndex = res.number;
            this.numberOfElements = res.numberOfElements;
            this.showLoader = false;
            this.designEnggQTSPageable.content = [];
            this.designEnggQTSPageable.content = res.content;
            resolve(this.newFilterDesignList);
          },
          () => {
            (this.showLoader = false);
            reject();
          }
        )
      );
    });
  }

  // used to clear the selected/ applied filter
  clearFilter() {
    this.filter = new FilterQuoteTracker();
    this.initializeTable();
  }

  public appendItems(items): void {
    this.filteredList.push(...items);
  }

  async fetchMore(event: IPageInfo) {
    if (this.pageIndex >= this.totalNoOfPages) {
      return;
    }
    this.showLoader = true;
    await this.fetchNextChunk(this.pageIndex + 1, this.pageSize).then(chunk => {
      this.appendItems(chunk);
      this.showLoader = false;
    }, () => this.showLoader = false);
  }

  fetchNextChunk(skip: number, limit: number): Promise<DesignEngineeringQuoteTracker[]> {
    return new Promise(async (resolve) => {
      this.pageIndex = skip;
      resolve(await this.getDesignEnggQuotes(skip, limit));
    });
  }

  checkIsAuthorized() {
    this.isAuthorized = this.roleAuthorisationServiceService.isAuthorised(this.authorizedRole);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
