import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AppConfig} from 'src/app/app.config';
import {utils} from 'src/app/shared/helpers';
import {MaterialDTO} from './final-review.model';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/catch';

@Injectable({
  providedIn: 'root'
})
export class FinalReviewService {
  constructor(private http: HttpClient) { }

  // get design review DTO data
  getFinalReview(finalReview) {
    return this.http.post(AppConfig.FINAL_DESIGN_REVIEW, finalReview).map(utils.extractData).catch(utils.handleError);
  }

  finalReviewSave(finalReview) {
    return this.http.post(AppConfig.FINAL_DESIGN_REVIEW_POST, finalReview).map(utils.extractData).catch(utils.handleError);
  }

  // gets the common fields for Label
  getLabelCommonFields(jacketId: number, labelPartNumber: string,type:string) {
    return this.http
      .get(AppConfig.GET_ALL_LABELS_COMMON_FIELDS + 'jacket/' + jacketId + '/label/' + labelPartNumber+'/type/'+type)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  getAllMaterials(productType: string, type: string) {
    return <Observable<MaterialDTO[]>>this.http
      .get(AppConfig.GET_ALL_MATERIAL_LIST + `${productType}` + '/' + `${type}`)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  getPartByJacketId(id: number) {
    return this.http
      .get(AppConfig.PART_JACKET_API + id)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  // used to get the DMT logs once sync is completed
  getDMTLogs(jacketId: number, type: string) {
    return this.http.get(`${AppConfig.GET_FINAL_REVIEW_DMT_LOGS}${jacketId}/${type}`).map(utils.extractData).catch(utils.handleError);
  }

  // used to get the last sync to Epicor status
  checkLastSyncStatus(jacketId: number, type: string) {
    return this.http
      .get(`${AppConfig.GET_FINAL_REVIEW_LAST_SYNC_STATUS}${jacketId}/${type}`)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  // used to get the product type of a jacket by jacket id
  getProductTypeByJacketId(jacketId: number) {
    return this.http
      .get(AppConfig.GET_PRODUCT_TYPE_BY_JACKET_ID + jacketId)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  pollJacketDetailsById(partNumber: string) {
    return this.http
      .get(AppConfig.POLL_EPICOR + partNumber)
      .map(utils.extractData)
      .catch(utils.handleError);
  }
}
