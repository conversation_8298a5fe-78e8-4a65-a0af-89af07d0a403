import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {SharedService, SnakbarService} from '../../../shared';
import {Values} from '../../../shared/constants/values.constants';
import {
  GreenLight,
  JumperPlug,
  LeadPlug,
  LeadType,
  Plug,
  PluggingInformation,
  RedLight,
  SleevingType,
  StrainRelief,
  Units
} from '../ccdc-model/ccdc.model';
import {SalesOrderSummaryService} from '../summary-sales-order.service';
import {FormControl, NgForm} from '@angular/forms';
import {Observable, Subscription} from 'rxjs';
import {environment} from 'src/environments/environment';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {map, startWith} from 'rxjs/operators';
import {ManageUnitsService} from '../manage-units/manage-units.service';

@Component({
  selector: 'sfl-add-plugin',
  templateUrl: './add-plugin.component.html'
})
export class AddPluginComponent implements OnInit, OnDestroy {
  sleevingTypes: SleevingType[] = [];
  sleevingTypeDetails: SleevingType;
  sleevingTypeControl = new FormControl();
  sleevingTypesObservable$: Observable<SleevingType[]>;
  strainReliefs: StrainRelief[] = [];
  greenLights: GreenLight[] = [];
  redLights: RedLight[] = [];
  plugs: Plug[] = [];
  plugsTypesObservable$: Observable<Plug[]>;
  jmpPlugsTypesObservable$: Observable<Plug[]>;
  plugDetails: Plug;
  plugControl = new FormControl();
  leadPlug = new Plug();
  jumperPlug = new Plug();
  pluggingInformation = new PluggingInformation();
  jacketGroupId: number;
  subscription = new Subscription();
  measureUnit = '';
  isOtherLeadPlug = false;
  isOtherJumperPlug = false;
  isOtherSleevingType = false;
  isOtherStrainRelief = false;
  isOtherGreenLight = false;
  isOtherRedLight = false;
  leadPlugImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  jumperConnectorImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  leadTypesList: LeadType[] = [];
  leadType = new LeadType();
  leadTypeObservable$: Observable<LeadType[]>;
  leadTypeDetails: LeadType;
  leadTypeControl = new FormControl();
  connectorControl = new FormControl();
  activeJacketType: string;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  showReloadButton = false;
  selection: string;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  lastFilter = '';
  sorted: [];
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public pluginDialogRef: MatDialogRef<AddPluginComponent>,
    private summarySalesOrderService: SalesOrderSummaryService,
    private sharedService: SharedService,
    private readonly snakbarService: SnakbarService,
    private manageUnitService: ManageUnitsService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketGroupId = data.jacketGroupId;
    this.activeJacketType = data.jacketType;
  }

  ngOnInit() {
    this.getMeasurementUnit();
    this.pluggingInformation.jumperPlugDTO = new JumperPlug();
    this.pluggingInformation.leadPlugDTO = new LeadPlug();
    this.pluggingInformation.leadTypeDTO = new LeadType();
    this.getLeadTypesMaster();
    this.getPluginMasterData();
    this.getPlugsByJacketType();
    this.getConnectorByJacketType();
    this.getPluggingInformationByJacketGroupId();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.measureUnit = res.measurementUnit;
          }
        }
      )
    );
  }

  // used to set default values
  setDefualtValue() {
    this.pluggingInformation.sleevingTypeId = this.sleevingTypes.find(x => x.name.toLowerCase() === 'black sleeving').id;
    this.pluggingInformation.strainReliefId = this.strainReliefs.find(x => x.name.toLowerCase() === 'grommet').id;
    this.pluggingInformation.greenLightId = this.greenLights.find(x => x.name.toLowerCase() === 'none').id;
    this.pluggingInformation.redLightId = this.redLights.find(x => x.name.toLowerCase() === 'none').id;
  }

  // used to get master data for plugging
  getPluginMasterData() {
    this.showLoader = true;
    this.subscription.add(
      this.summarySalesOrderService.getPluginMasterData().subscribe(
        success => {
          this.sleevingTypes = success.sleevingTypes;
          this.sleevingTypesObservable$ = this.sleevingTypeControl.valueChanges.pipe(
            startWith<string | SleevingType[]>(''),
            map(value => (typeof value === 'string' ? value : this.lastFilter)),
            map(filter => this.filterSleevingTypes(filter)),
            map(values => values.sort(function (a, b) {
              return a.name > b.name ? 1 : -1;
            }))
          );
          this.strainReliefs = success.strainReliefs;
          this.redLights = success.redLights;
          this.greenLights = success.greenLights;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  filterSleevingTypes(filter: string): SleevingType[] {
    this.lastFilter = filter;
    if (filter) {
      return this.sleevingTypes.filter(option => {
        if (option.id !== null && option.name !== null) {
          return (
            option.name.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.sleevingTypes ? this.sleevingTypes.slice() : [];
    }
  }

  filterPlugTypes(filter: string): Plug[] {
    this.lastFilter = filter;
    if (filter) {
      return this.plugs.filter(option => {
        if (option.id !== null && option.plugName !== null) {
          return (
            option.plugName.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.plugs ? this.plugs.slice() : [];
    }
  }

  filterLeadTypes(filter: string): LeadType[] {
    this.lastFilter = filter;
    if (filter) {
      return this.leadTypesList.filter(option => {
        if (option.id !== null && option.leadName !== null) {
          return (
            option.leadName.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        } else if (option.id !== null && option.leadName == null) {
          return (
            option.leadName.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.leadTypesList ? this.leadTypesList.slice() : [];
    }
  }


  onSelectionChangesSleevingTypes(material: SleevingType) {
    this.isOtherSleevingType = false;
    if (material.name !== Values.Other) {
      this.isOtherSleevingType = false;
      this.pluggingInformation.sleevingTypeId = material.id;
      this.pluggingInformation.sleevingTypeName = material.name;
    } else {
      this.pluggingInformation.sleevingTypeName = Values.Other;
      this.pluggingInformation.sleevingTypeId = material.id;
      this.isOtherSleevingType = true;
    }
    this.sleevingTypeControl.setValue(this.pluggingInformation.sleevingTypeName);
  }

  onSelectionChangesPlugTypes(material: Plug) {
    if (material) {
      this.pluggingInformation.leadPlugDTO.plugId = material.id;
      this.pluggingInformation.leadPlugDTO.plugName = material.plugName;
      this.plugControl.setValue(this.pluggingInformation.leadPlugDTO.plugName);
      this.onLeadPlugChanged(this.pluggingInformation.leadPlugDTO.plugId);
    }
  }

  onSelectionChangesJmpPlugTypes(material: Plug) {
    if (material) {
      this.pluggingInformation.jumperPlugDTO.plugId = material.id;
      this.pluggingInformation.jumperPlugDTO.plugName = material.plugName;
      this.onJumperPlugChanged(this.pluggingInformation.jumperPlugDTO.plugId);
    }
  }

  onSelectionChangesLeadTypes(material: LeadType) {
    if (material) {
      this.pluggingInformation.leadTypeDTO.id = material.id;
      this.pluggingInformation.leadTypeDTO.leadName = material.leadName;
      this.onLeadTypeChanged(this.leadType.id);
    }
  }

  // gets the plug by currently active jacket type
  getPlugsByJacketType() {
    this.showLoader = true;
    this.subscription.add(
      this.summarySalesOrderService.getPlugsByJacketType(this.activeJacketType).subscribe(
        (success: Plug[]) => {
          this.plugs = success;
          if (this.plugControl) {
            this.plugsTypesObservable$ = this.plugControl.valueChanges.pipe(
              startWith<string | Plug[]>(''),
              map(value => (typeof value === 'string' ? value : this.lastFilter)),
              map(filter => this.filterPlugTypes(filter)),
              map(values => values.sort(function (a, b) {
                return a.plugName > b.plugName ? 1 : -1;
              }))
            );
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  getConnectorByJacketType() {
    this.showLoader = true;
    this.subscription.add(
      this.summarySalesOrderService.getPlugsByJacketType(this.activeJacketType).subscribe(
        (success: Plug[]) => {
          this.plugs = success;
          if (this.plugControl) {
            this.jmpPlugsTypesObservable$ = this.connectorControl.valueChanges.pipe(
              startWith<string | Plug[]>(''),
              map(value => (typeof value === 'string' ? value : this.lastFilter)),
              map(filter => this.filterPlugTypes(filter)),
              map(values => values.sort(function (a, b) {
                return a.plugName > b.plugName ? 1 : -1;
              }))
            );
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }


  onSleevingTypeChanged(sleeveType) {
    this.isOtherSleevingType = false;
    if (sleeveType.source.triggerValue === Values.Other) {
      this.isOtherSleevingType = true;
    }
  }

  onStrainReliefChanged(strainRelief) {
    this.isOtherStrainRelief = false;
    if (strainRelief.source.triggerValue === Values.Other) {
      this.isOtherStrainRelief = true;
    }
  }

  onGreenLightChanged(greenLight) {
    this.isOtherGreenLight = false;
    if (greenLight.source.triggerValue === Values.Other) {
      this.isOtherGreenLight = true;
    }
  }

  onRedLightChanged(redLight) {
    this.isOtherRedLight = false;
    if (redLight.source.triggerValue === Values.Other) {
      this.isOtherRedLight = true;
    }
  }

  // used to handle the lead plug change event
  onLeadPlugChanged(value) {
    this.showLoader = true;
    this.isOtherLeadPlug = false;
    this.pluggingInformation.leadPlugDTO.plugId = value;
    const leadPlug = this.plugs.find(data => data.id === value);
    if (leadPlug) {
      this.leadPlug.id = leadPlug.id;
      this.pluggingInformation.leadPlugDTO.plugName = leadPlug.plugName;
      if (leadPlug.plugName === Values.Other) {
        this.isOtherLeadPlug = true;
      } else {
        this.leadPlug.partNumber = leadPlug.partNumber;
        this.leadPlug.maxAmps = leadPlug.maxAmps;
        this.leadPlug.maxVolts = leadPlug.maxVolts;
        this.leadPlug.plugCost = leadPlug.plugCost;
        this.leadPlug.imageUrl = leadPlug.imageUrl;
      }

      if (leadPlug.imageUrl) {
        this.leadPlugImageUrl = environment.IMAGES_URL + this.leadPlug.imageUrl;
      } else {
        this.leadPlugImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
      }
    }
    this.showLoader = false;
  }

  // used to handle the jumper plug change event
  onJumperPlugChanged(value) {
    this.showLoader = true;
    this.isOtherJumperPlug = false;
    this.pluggingInformation.jumperPlugDTO.plugId = value;
    this.connectorControl.setValue(this.pluggingInformation.jumperPlugDTO.plugName);
    const jumperPlug = this.plugs.find(data => data.id === value);
    if (jumperPlug) {
      this.jumperPlug.id = jumperPlug.id;
      this.pluggingInformation.jumperPlugDTO.plugName = jumperPlug.plugName;
      if (jumperPlug.plugName === Values.Other) {
        this.isOtherJumperPlug = true;
      } else {
        this.jumperPlug.jumperPartNumber = jumperPlug.jumperPartNumber;
        this.jumperPlug.maxAmps = jumperPlug.maxAmps;
        this.jumperPlug.maxVolts = jumperPlug.maxVolts;
        this.jumperPlug.plugCost = jumperPlug.plugCost;
        this.jumperPlug.imageUrl = jumperPlug.imageUrl;
      }

      if (jumperPlug.imageUrl) {
        this.jumperConnectorImageUrl = environment.IMAGES_URL + this.jumperPlug.imageUrl;
      } else {
        this.jumperConnectorImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
      }
    }
    this.showLoader = false;
  }

  // used to save the plugging info
  savePluggingInformation(pluggingForm: NgForm, value) {
    this.showLoader = true;
    this.pluggingInformation.jacketGroupId = this.jacketGroupId;
    if (this.pluggingInformation.sleevingTypeId && !this.isOtherSleevingType) {
      this.pluggingInformation.otherSleevingType = null;
      this.pluggingInformation.otherSleevingTypePartNumber = null;
      this.pluggingInformation.otherSleevingTypeCost = null;
    }
    if (this.pluggingInformation.strainReliefId && !this.isOtherStrainRelief) {
      this.pluggingInformation.otherStrainRelief = null;
      this.pluggingInformation.otherStrainReliefPartNumber = null;
      this.pluggingInformation.otherStrainReliefCost = null;
    }
    if (this.pluggingInformation.greenLightId && !this.isOtherGreenLight) {
      this.pluggingInformation.otherGreenLight = null;
      this.pluggingInformation.otherGreenLightPartNumber = null;
      this.pluggingInformation.otherGreenLightCost = null;
    }
    if (this.pluggingInformation.redLightId && !this.isOtherRedLight) {
      this.pluggingInformation.otherRedLight = null;
      this.pluggingInformation.otherRedLightPartNumber = null;
      this.pluggingInformation.otherRedLightCost = null;
    }
    if (!this.isOtherLeadPlug) {
      this.pluggingInformation.otherPlug = null;
      this.pluggingInformation.otherPlugPartNumber = null;
      this.pluggingInformation.otherPlugCost = null;
    }
    if (!this.isOtherJumperPlug) {
      this.pluggingInformation.otherConnector = null;
      this.pluggingInformation.otherConnectorPartNumber = null;
      this.pluggingInformation.otherConnectorCost = null;
    }
    if (this.pluggingInformation.leadTypeDTO && !this.pluggingInformation.leadTypeDTO.id) {
      this.pluggingInformation.leadTypeDTO = null;
    }
    this.subscription.add(
      this.summarySalesOrderService.savePluggingInformation(this.pluggingInformation, this.pluggingInformation.id).subscribe(
        success => {
          if (success) {
            pluggingForm.reset();
            this.jumperConnectorImageUrl = Values.DEFAULT_PRODUCT_IMAGE;
            const obj = {data: success, mode: value === 'save' ? 0 : 1};
            this.pluginDialogRef.close(obj);
          }
          this.showLoader = false;
        },
        (error) => {
          if (error.applicationStatusCode === 3001) {
            this.snakbarService.error(error.message);
          }
          this.showReloadButton = true;
          this.showLoader = false;
        }
      )
    );
  }

  // used to get the plugging info by jacket group id
  getPluggingInformationByJacketGroupId() {
    this.showLoader = true;
    this.subscription.add(
      this.summarySalesOrderService.getPluggingInformationByJacketGroupId(this.jacketGroupId).subscribe(
        (success: PluggingInformation) => {
          if (success !== null) {
            this.pluggingInformation = success;
            if (this.pluggingInformation.leadPlugDTO !== null) {
              this.onLeadPlugChanged(this.pluggingInformation.leadPlugDTO.plugId);
            } else {
              this.pluggingInformation.leadPlugDTO = new LeadPlug();
            }
            if (this.pluggingInformation.jumperPlugDTO !== null) {
              this.onJumperPlugChanged(this.pluggingInformation.jumperPlugDTO.plugId);
            } else {
              this.pluggingInformation.jumperPlugDTO = new JumperPlug();
            }
            if (this.pluggingInformation.sleevingTypeName === Values.Other) {
              this.isOtherSleevingType = true;
            }
            if (this.pluggingInformation.strainReliefName === Values.Other) {
              this.isOtherStrainRelief = true;
            }
            if (this.pluggingInformation.greenLightName === Values.Other) {
              this.isOtherGreenLight = true;
            }
            if (this.pluggingInformation.redLightName === Values.Other) {
              this.isOtherRedLight = true;
            }
            if (this.pluggingInformation.leadTypeDTO !== null) {
              this.onLeadTypeChanged(this.pluggingInformation.leadTypeDTO.id);
            } else {
              this.pluggingInformation.leadTypeDTO = new LeadType();
            }
            this.showLoader = false;
          } else {
            this.setDefualtValue();
            this.showLoader = false;
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog() {
    this.pluginDialogRef.close();
  }

  // retrieve all the master leadtypes
  getLeadTypesMaster() {
    this.showLoader = true;
    this.subscription.add(
      this.summarySalesOrderService.getLeadTypeMaster().subscribe(
        (leadTypes: LeadType[]) => {
          this.leadTypesList = leadTypes;
          this.leadTypeObservable$ = this.leadTypeControl.valueChanges.pipe(
            startWith<string | LeadType[]>(''),
            map(value => (typeof value === 'string' ? value : this.lastFilter)),
            map(filter => this.filterLeadTypes(filter)),
            map(values => values.sort(function (a, b) {
              return a.leadName > b.leadName ? 1 : -1;
            }))
          );
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to handle the lead type change
  onLeadTypeChanged(leadTypeId: number) {
    const leadType = this.leadTypesList.find(data => data.id === leadTypeId);
    if (leadType) {
      this.leadType.id = leadType.id;
      this.pluggingInformation.leadTypeDTO = leadType;
      this.leadType.partNumber = leadType.partNumber;
      this.leadType.maxVolts = leadType.maxVolts;
      this.leadType.maxTemp = leadType.maxTemp;
      this.leadType.costPerFoot = leadType.costPerFoot;
    }
  }

  displaySleevingTypes(value: SleevingType[] | string): string | undefined {
    let displayValue = '';
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.name;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  displayPlugTypes(value: Plug[] | string): string {
    let displayValue = '';
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.plugName;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  displayLeadTypes(value: LeadType[] | string): string {
    let displayValue = '';
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.leadName;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }


  // used to handle page reload
  reloadPage(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
