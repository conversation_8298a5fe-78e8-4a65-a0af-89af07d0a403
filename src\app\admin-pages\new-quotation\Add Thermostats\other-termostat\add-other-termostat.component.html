<form #otherThermostateForm="ngForm">
  <h2 mat-dialog-title>
    Other Thermostat Information
    <hr />
  </h2>
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select
            placeholder="Type"
            [(ngModel)]="otherThermostat.otherThermostatType.id"
            name="otherthermostat"
            #otherthermostatInut="ngModel"
            required
          >
            <mat-option *ngFor="let type of thermostatType | orderBy:'id'" [value]="type.id">
              {{ type.id }}
            </mat-option>
            <mat-option>None</mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="otherthermostatInut.touched && otherthermostatInut.invalid">
          <small class="mat-text-warn" *ngIf="otherthermostatInut?.errors.required">Other Thermostat is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Other thermostat cost"
            name="otherthermostatcost"
            [(ngModel)]="otherThermostat.otherThermostatCost"
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Other thermostat partnumber"
            name="otherthermostatpartnumber"
            [(ngModel)]="otherThermostat.otherThermostatPartNumber"
          />
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>
  <hr />
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button
        mat-raised-button
        color="warn"
        type="submit"
        (click)="addOtherThermostat()"
        [disabled]="otherThermostateForm.form.invalid"
        name="add"
      >
        Add
      </button>
      <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
    </div>
  </mat-dialog-actions>
</form>
