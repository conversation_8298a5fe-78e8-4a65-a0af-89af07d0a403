<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Final Review
  <hr />
</h2>
<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <input
        matInput
        [matDatepicker]="date"
        placeholder="Date"
        name="finalReviewDate"
        [(ngModel)]="finalReview.finalReviewDate"
        #finalReviewDateDate="ngModel"
        autocomplete="off"
      />
      <mat-datepicker-toggle matSuffix [for]="date"></mat-datepicker-toggle>
      <mat-datepicker #date></mat-datepicker>
    </mat-form-field>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxLayout="column wrap" fxFlex="49">
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <div class="col">
          <mat-checkbox
            color="warn"
            name="elementDesignReviewed"
            [(ngModel)]="finalReview.elementDesignReviewed"
            #elementDesignReviewedCheckBox="ngModel"
            >Element Design Reviewed
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="tsNotInterfere" [(ngModel)]="finalReview.tsNotInterfere" #tsNotInterfereCheckBox="ngModel"
            >T/S Does Not Interfere (or N/A)
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="bomReviewed" [(ngModel)]="finalReview.bomReviewed" #bomReviewedCheckBox="ngModel"
            >Bom/BoO Reviewed
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox
            color="warn"
            name="sensorLengthReviewed"
            [(ngModel)]="finalReview.sensorLengthReviewed"
            #sensorLengthReviewedCheckBox="ngModel"
            >Sensor Length Reviewed
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="labelReviewed" [(ngModel)]="finalReview.labelReviewed" #labelReviewedCheckBox="ngModel"
            >Label Reviewed
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox
            color="warn"
            name="partsActiveInEpicor"
            [(ngModel)]="finalReview.partsActiveInEpicor"
            #partsActiveInEpicorCheckBox="ngModel"
            >Parts Fully Active in Epicor
          </mat-checkbox>
        </div>
      </div>
    </div>
    <div fxLayout="column wrap" fxFlex="49">
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <div class="col">
          <mat-checkbox color="warn" name="designReleased" [(ngModel)]="finalReview.designReleased" #designReleasedCheckBox="ngModel"
            >Design(s) Released to Patterns
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox
            color="warn"
            name="heaterManuallyVerified"
            [(ngModel)]="finalReview.heaterManuallyVerified"
            #heaterManuallyVerifiedCheckBox="ngModel"
            >Heater - Manual Verified (or N/A)
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox
            color="warn"
            name="engineeringCostSheet"
            [(ngModel)]="finalReview.engineeringCostSheet"
            #engineeringCostSheetCheckBox="ngModel"
            >Engineering Cost Spreadsheet
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox
            color="warn"
            name="customerPDFUpdated"
            [(ngModel)]="finalReview.customerPDFUpdated"
            #customerPDFUpdatedCheckBox="ngModel"
            >Customer PDF Updated (or N/A)
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="goldReportRun" [(ngModel)]="finalReview.goldReportRun" #goldReportRunCheckBox="ngModel"
            >Gold Report Run (If Heatermans)
          </mat-checkbox>
        </div>
      </div>
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <input matInput placeholder="Comments" name="comments" [(ngModel)]="finalReview.comments" #commentsInput="ngModel" />
    </mat-form-field>
  </div>
</mat-dialog-content>
<hr />
<mat-dialog-actions>
  <button mat-raised-button color="warn" type="submit" (click)="saveFinalReview()">Save</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button type="submit" (click)="undoFinalReview()">Undo Signature</button>
</mat-dialog-actions>
