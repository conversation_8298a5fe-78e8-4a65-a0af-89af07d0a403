<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Orders
  <span *ngIf="readingFromAssembly" class="take-awhile">{{ readingAssemblyMessage }}</span>
  <hr />
</h2>
<mat-dialog-content>
  <div class="gold-report-container example-container cust-table">
    <div>
      <!-- Table for Gold report active quotations -->
      <div class="less-peding">
        <mat-card>
          <table
            [ngClass]="goldReportDataSource.data.length > 0 ? 'quotes-table50' : 'quotes-table100'"
            class="w-auto"
            mat-table
            aria-hidden="true"
            matSort
            matSortDisableClear
            [dataSource]="goldReportQuotationDataSource"
            (matSortChange)="getGoldReportQuotationSorting($event)"
          >
            <ng-container matColumnDef="salesOrderNumberQuotation">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> SO Number </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">SO Number:</span>
                {{ element?.soNumber }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="customerName">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Customer </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Customer:</span>
                {{ element?.customerName }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="shipBy">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Ship by </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Ship by:</span>
                {{ element?.shipBy | date }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="quotationStatus">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Status </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Status:</span>
                {{ element?.status }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="reportDate">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Report Date </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Report Date:</span>
                {{ element?.reportDate | date }}
              </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="goldReportQuotationsColumns"></mat-header-row>
            <mat-row
              *matRowDef="let row; columns: goldReportQuotationsColumns"
              class="open-doc"
              (click)="openGoldReport(row)"
              [ngClass]="{ selected: selectedRow == row.soNumber }"
            ></mat-row>
          </table>
          <div class="no-records" *ngIf="goldReportQuotationDataSource.data.length == 0 && !showLoader">No data found</div>
          <mat-paginator
            [length]="lengthGoldReportQuotation"
            [pageSizeOptions]="pageSizeOptions"
            [pageSize]="pageSizeGoldReportQuotation"
            [pageIndex]="pageIndexGoldReportQuotation"
            (page)="getGoldReportQuotationPagination($event)"
            showFirstLastButtons
          >
          </mat-paginator>
        </mat-card>
      </div>
    </div>

    <!-- Table for Gold report -->
    <div *ngIf="goldReportDataSource.data.length > 0">
      <h2 mat-dialog-title fxFlex>
        Jackets
        <span class="float-right open-doc" (click)="closeGoldReport()" matTooltip="Close" matTooltipClass="cost-report-tooltip"
          ><em class="material-icons">close</em></span
        >
        <hr />
      </h2>
      <div class="less-peding">
        <mat-card>
          <table
            [ngClass]="goldReportDataSource.data.length > 0 ? 'w-auto quotes-table50' : 'w-auto'"
            mat-table
            matSort
            aria-hidden="true"
            matSortDisableClear
            [dataSource]="goldReportDataSource"
            (matSortChange)="getGoldReportSorting($event)"
          >
            <ng-container matColumnDef="salesOrderNumberGoldReport">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> SO Number </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">SO Number:</span>
                {{ element?.soNumber }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="partNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Part Number </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Part Number:</span>
                {{ element?.partNumber }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="heaterman">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Heaterman </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Heaterman:</span>
                {{ element?.heaterManPart }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="revision">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Revision </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Revision:</span>
                {{ element?.revision }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="element">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Element </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="20">
                <span class="mobile-label">Element:</span>
                {{ element?.element }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="quantity">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Quantity </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="10">
                <span class="mobile-label">Quantity:</span>
                {{ element?.quantity }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="golden">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Golden </mat-header-cell>
              <mat-cell *matCellDef="let element" fxFlex="10">
                <span class="mobile-label">Golden:</span>
                {{ element?.gold }}
              </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="goldReportColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: goldReportColumns"></mat-row>
          </table>
          <div class="no-records" *ngIf="goldReportDataSource.data.length == 0">No data found</div>
          <mat-paginator
            [length]="lengthGoldReport"
            [pageSizeOptions]="pageSizeOptions"
            [pageSize]="pageSizeGoldReport"
            [pageIndex]="pageIndexGoldReport"
            (page)="getGoldReportPagination($event)"
            showFirstLastButtons
          >
          </mat-paginator>
        </mat-card>
      </div>
    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </div>
  <div *ngIf="goldReportDataSource.data.length > 0" class="float-right">
    <div fxLayout="row wrap" fxLayoutGap="10px">
      <div class="sub-heder" fxLayoutAlign="start center" fxLayoutGap="10px" fxFlex>
        <em
          (click)="resetToolTip()"
          class="material-icons info"
          matTooltip="What does Reset do?"
          matTooltipClass="cost-report-tooltip"
          matTooltipPosition="above"
          >info</em
        >
        <button
          class="float-right"
          mat-raised-button
          type="submit"
          color="warn"
          (click)="resetGoldReport(soNumber)"
          matTooltip="Regenerate this Gold Report"
          matTooltipPosition="above"
          matTooltipClass="cost-report-tooltip"
        >
          Reset
        </button>
        <button
          class="float-right"
          mat-raised-button
          type="submit"
          color="warn"
          (click)="downloadGoldReportExcel(soId)"
          matTooltip="Download Gold Report Excel"
          matTooltipPosition="above"
          matTooltipClass="cost-report-tooltip"
        >
          Download
        </button>
      </div>
    </div>
  </div>
</mat-dialog-actions>
