<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
    class="sfl-global-spinner-loader"
    [mode]="mode"
    [color]="color"
    [diameter]="spinnerDiameter"
  >
  </mat-progress-spinner>
</div>
<div class="filter less-peding final-review-screen" fxLayout="row wrap">
  <!-- Order Information -->
  <div fxLayout="column" fxFlex.gt-lg="40" fxFlex.gt-md="40" fxFlex.gt-xs="100">
    <form #pollEpicoreForm="ngForm" role="form">
      <div fxLayout="row">
        <mat-card fxFlex="100">
          <div fxLayout="row wrap">
            <mat-card-title>Order Information</mat-card-title>
          </div>
          <hr />
          <div class="mb-10"></div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Part Number:</label>
              <mat-form-field
                fxFlex.gt-lg="100"
                fxFlex.gt-md="100"
                fxFlex.gt-sm="100"
                fxFlex.gt-xs="100"
              >
                <input
                  matInput
                  placeholder="Enter Part Number"
                  ng-trim="true"
                  [(ngModel)]="partNumber"
                  autofocus
                  name="partNumber"
                  #partNumberInput="ngModel"
                  required
                />
              </mat-form-field>
            </div>
            <button
              class="poll-epicor"
              (click)="pollEpicor()"
              [disabled]="pollEpicoreForm.invalid"
              mat-raised-button
              color="warn"
              type="button"
            >
              Poll Epicor
            </button>
          </div>
        </mat-card>
      </div>
    </form>

    <!-- Part Information -->
    <div fxLayout="row">
      <mat-card fxFlex="100">
        <div fxLayout="row wrap">
          <mat-card-title>Part</mat-card-title>
        </div>
        <hr />
        <div class="sfl-block-loader" *ngIf="partInfoLoader; else partInfo">
          <mat-progress-spinner
            class=""
            [mode]="mode"
            [color]="color"
            [diameter]="spinnerDiameter"
          >
          </mat-progress-spinner>
        </div>
        <ng-template #partInfo>
          <div class="mb-10"></div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Description</label>
              <label>{{ finalReviewDTO?.jacket?.partDescription }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Net Price</label>
              <label>{{ finalReviewDTO?.jacket?.netPrice }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Rev</label>
              <label>{{ finalReviewDTO?.jacket?.revision }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Group</label>
              <label>{{ finalReviewDTO?.jacket?.description }}</label>
            </div>
          </div>
        </ng-template>
      </mat-card>
    </div>

    <!-- Operations USA-->
    <mat-card fxFlex="100">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Operations</mat-card-title>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="operationsDataSourceUSA">
            <ng-container matColumnDef="sequence">
              <mat-header-cell *matHeaderCellDef fxFlex="18">
                Seq
              </mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="18">
                {{ operation?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="operation">
              <mat-header-cell *matHeaderCellDef fxFlex="22">
                Operation
              </mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="22">
                {{ operation?.operation }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="opNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="20">
                Code
              </mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                {{ operation?.opNumber }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="setupHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20">
                Setup Hours
              </mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                {{ operation?.setupHours }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="prodHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20">
                Prod Hrs
              </mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                {{ operation?.prodHours }}
              </mat-cell>
            </ng-container>
            <mat-header-row
              *matHeaderRowDef="operationsColumns"
            ></mat-header-row>
            <mat-row
              *matRowDef="let row; columns: operationsColumns"
              [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"
            ></mat-row>
          </mat-table>
          <div
            class="no-records"
            *ngIf="!operationsDataSourceUSA?.data?.length"
          >
            No data found
          </div>
        </div>
      </div>
    </mat-card>
  </div>

  <div
    fxLayout="column"
    fxFlex.gt-lg="60"
    fxFlex.gt-md="60"
    fxFlex.gt-xs="100"
    class="materials-box"
  >
    <!-- Materials USA -->
    <mat-card fxFlex="100">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Materials</mat-card-title>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="materialsDataSourceUSA" matSort matSortDirection="asc" matSortDisableClear>
            <ng-container matColumnDef="sequence">
              <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="10">
                Seq
              </mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="partNumber">
              <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="15">
                Part Number
              </mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="15">
                {{ material?.partNumber }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="35">
                Description
              </mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="35">
                {{ material?.description }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="quantity">
              <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="10">
                Qty
              </mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.quantity }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="10">
                UOM
              </mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.uom }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOp">
              <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="10">
                Rel Op
              </mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.relOp }}
              </mat-cell>
            </ng-container>
            <mat-header-row
              *matHeaderRowDef="materialsColumns"
            ></mat-header-row>
            <mat-row
              *matRowDef="let row; columns: materialsColumns"
              [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"
            ></mat-row>
          </mat-table>
          <div class="no-records" *ngIf="!materialsDataSourceUSA?.data?.length">
            No data found
          </div>
        </div>
      </div>
    </mat-card>

    <!-- Labels USA -->
  </div>
  <mat-card fxFlex="100">
    <div class="highlight-mat-table">
      <div fxLayout="row">
        <mat-card-title>Labels</mat-card-title>
      </div>

      <!-- Mat Table USA -->
      <div class="cust_table">
        <mat-table [dataSource]="labelsDataSourceUSA">
          <ng-container matColumnDef="number">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Number
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.number }}
            </mat-cell>
          </ng-container>

          <ng-container matColumnDef="format">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Format
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.format }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="volts">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              Volts
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.volts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="watts">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              Watts
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.watts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="amps">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              AMPS
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.amps }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="phase">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Phase
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.phase }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="size">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Size
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.size }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="width">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Width
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.width }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="length">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Length
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.length }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csv">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              CSV
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.csv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csa">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              CSA
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.csa }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhlv">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              MHLV
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.mhlv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhla">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              MHLA
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.mhla }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="tempRange">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Temp Range
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.tempRange }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="lowT">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              LowT
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.lowT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="highT">
            <mat-header-cell *matHeaderCellDef fxFlex="10">
              HighT
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.highT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="modelNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Model Number
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.modelNumber }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open1">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Open1
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open1 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open2">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Open2
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open2 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open3">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Open3
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open3 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15">
              Part Number
            </mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.partNumber }}
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="labelsColumns"></mat-header-row>
          <mat-row
            *matRowDef="let row; columns: labelsColumns"
            [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"
          ></mat-row>
        </mat-table>
        <div class="no-records" *ngIf="!labelsDataSourceUSA?.data?.length">
          No data found
        </div>
      </div>
    </div>
  </mat-card>
</div>
