import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { ECRStatusesMaster, GenericPageable, ECRStatusesFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageEcrStatusesComponent } from './manage-ecr-statuses/manage-ecr-statuses.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-ecr-statuses',
  templateUrl: './ecr-statuses.component.html'
})
export class EcrStatusesComponent implements OnInit, OnDestroy {
  pageTitle = 'ECR Statuses Master';
  statuses: ECRStatusesMaster;
  statusesPageable: GenericPageable<ECRStatusesMaster>;
  statusesDataSource = new MatTableDataSource<ECRStatusesMaster>();
  statusesColumns = DisplayColumns.Cols.ECRStatusesCols;

  dataSource = new MatTableDataSource<ECRStatusesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  statusesFilter: ECRStatusesFilter = new ECRStatusesFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldStatus = Values.FilterFields.status;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getECRStatusesMaster(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new ect statuses
  addStatus() {
    this.editECRStatus(new ECRStatusesMaster());
  }

  // used to add filter to ect statuses listing
  async addFilter() {
    this.filter = this.statusesFilter.status === '' ? [] : [{ key: this.filterFieldStatus, value: this.statusesFilter.status }];
    this.getECRStatusesMaster(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to ect statuses listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldStatus,
        value: fieldToClear === this.filterFieldStatus ? (this.statusesFilter.status = '') : this.statusesFilter.status
      }
    ];
    this.getECRStatusesMaster(this.initialPageIndex, this.pageSize);
  }

  getECRStatusesMaster(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getECRStatusesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<ECRStatusesMaster>) => {
          this.statusesPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createECRStatusesTable(this.statusesPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createECRStatusesTable(serviceRequestList: GenericPageable<ECRStatusesMaster>) {
    this.statusesDataSource.data = serviceRequestList.content;
  }

  getECRStatusesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getECRStatusesMaster(this.pageIndex, this.pageSize);
  }

  getECRStatusesSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getECRStatusesMaster(this.pageIndex, this.pageSize);
  }

  editECRStatus(status: ECRStatusesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = status;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-ecr-statuses-master-model';
    const dialogRef = this.matDialog.open(ManageEcrStatusesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          status.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getECRStatusesMaster(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteECRStatus(statusId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteECRStatus(statusId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getECRStatusesMaster(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
