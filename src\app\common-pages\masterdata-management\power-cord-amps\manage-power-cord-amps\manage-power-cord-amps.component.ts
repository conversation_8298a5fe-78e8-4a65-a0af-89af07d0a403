import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { PowerCordAmpsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-power-cord-amps',
  templateUrl: './manage-power-cord-amps.component.html'
})
export class ManagePowerCordAmpsComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  powerCordAmps: PowerCordAmpsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManagePowerCordAmpsComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.powerCordAmps = data;
  }

  ngOnInit() {
    this.powerCordAmps = this.powerCordAmps.id ? Object.assign({}, this.powerCordAmps) : new PowerCordAmpsMaster();
    this.powerCordAmps.id ? (this.title = 'Update Power Cord AMPS') : (this.title = 'Add Power Cord AMPS');
  }

  updatePowerCordAmps() {
    this.showLoader = true;
    if (this.powerCordAmps.id) {
      this.subscription.add(
        this.masterDataService.updatePowerCordAmps(this.powerCordAmps).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addPowerCordAmps(this.powerCordAmps).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
