<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #powerCordMaterialForm="ngForm" (ngSubmit)="updatePowerCordMaterial()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Power Cord Material Id"
            [(ngModel)]="powerCordMaterial.powerCordMaterialId"
            name="powerCordMaterialId"
            #powerCordMaterialIdInput="ngModel"
            required
            sflNoWhiteSpaces
            maxlength="1"
            sflToUpperCase
          />
        </mat-form-field>
        <div *ngIf="powerCordMaterialIdInput.touched && powerCordMaterialIdInput.invalid">
          <small class="mat-text-warn" *ngIf="powerCordMaterialIdInput?.errors.required">Power Cord Material Id is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="powerCordMaterialIdInput?.errors?.whitespace && !powerCordMaterialIdInput?.errors?.required">
          Invalid Power Cord Material Id.
        </small>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Power Cord Material"
            [(ngModel)]="powerCordMaterial.value"
            name="value"
            #valueInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="valueInput.touched && valueInput.invalid">
          <small class="mat-text-warn" *ngIf="valueInput?.errors.required">Power Cord Material is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="valueInput?.errors?.whitespace && !valueInput?.errors?.required">
          Invalid Power Cord Material.
        </small>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Price"
            [(ngModel)]="powerCordMaterial.price"
            name="price"
            #priceInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="priceInput.touched && priceInput.invalid">
          <small class="mat-text-warn" *ngIf="priceInput?.errors.required">Price is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="powerCordMaterial.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!powerCordMaterialForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
