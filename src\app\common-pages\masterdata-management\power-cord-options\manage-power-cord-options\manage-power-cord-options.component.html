<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #powerCordOptionsForm="ngForm" (ngSubmit)="updatePowerCordOptions()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Power Cord Options Id"
            [(ngModel)]="powerCordOptions.powerCordConstantId"
            name="powerCordOptionsId"
            #powerCordOptionsIdInput="ngModel"
            required
            maxlength="1"
            sflToUpperCase
          />
        </mat-form-field>
        <div *ngIf="powerCordOptionsIdInput.touched && powerCordOptionsIdInput.invalid">
          <small class="mat-text-warn" *ngIf="powerCordOptionsIdInput?.errors.required">Power Cord Options Id is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="powerCordOptionsIdInput?.errors?.whitespace && !powerCordOptionsIdInput?.errors?.required">
          Invalid Power Cord Options Id.
        </small>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Power Cord Options"
            [(ngModel)]="powerCordOptions.value"
            name="value"
            #valueInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="valueInput.touched && valueInput.invalid">
          <small class="mat-text-warn" *ngIf="valueInput?.errors.required">Power Cord Options is required.</small>
        </div>
        <small class="mat-text-warn" *ngIf="valueInput?.errors?.whitespace && !valueInput?.errors?.required">
          Invalid Power Cord Options.
        </small>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
          <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="powerCordOptions.isObsolete">Is Obsolete</mat-checkbox>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!powerCordOptionsForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
