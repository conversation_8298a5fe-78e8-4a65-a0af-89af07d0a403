<div class="app mat-typography" [dir]="options.dir">
    <sfl-design-topbar (toggleSidenav)="sidemenu.toggle()"></sfl-design-topbar>
    <mat-sidenav-container class="app-inner">
        <mat-sidenav #sidemenu class="sidebar-panel" id="sidebar-panel" [mode]="isOver() ? 'over' : 'side'" [opened]="!isOver()"
            (open)="sidePanelOpened = true" (close)="sidePanelOpened = false" (mouseover)="menuMouseOver()" (mouseout)="menuMouseOut()"
            [perfectScrollbar]="config" [disabled]="mediaMatcher.matches">
            <sfl-design-eng-navbar (click)="updatePS()"></sfl-design-eng-navbar>
        </mat-sidenav>
        <div [perfectScrollbar]="config" [disabled]="mediaMatcher.matches">
            <router-outlet></router-outlet>
        </div>
    </mat-sidenav-container>
</div>