<form #otherThermostateForm="ngForm">
  <h2 mat-dialog-title>
    Add Leadtime
    <hr />
  </h2>
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field>
        <input matInput placeholder="Lead Time" name="leadTime" sflIsNumber [(ngModel)]="time" required>
        <div matSuffix>In Weeks</div>
      </mat-form-field>
    </div>
  </mat-dialog-content>
  <hr>
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button color="warn" type="submit" name="add" (click)="addLeadTime()">Add</button>
      <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
    </div>
  </mat-dialog-actions>
</form>
