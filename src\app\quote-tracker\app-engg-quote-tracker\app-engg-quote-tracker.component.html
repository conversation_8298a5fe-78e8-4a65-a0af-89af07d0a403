<!-- So In Design Main Content for Grid -->
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter"></mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <!-- Date filter section -->
    <form #appEnggQTSFilterForm="ngForm">
      <div fxLayout="row wrap" class="mb-10 cust_fields">
        <div fxFlex fxLayoutAlign="start center" fxLayoutGap="10px">

          <mat-form-field appearance="outline" fxFlex.gt-lg="10" fxFlex.gt-md="10" fxFlex.gt-sm="8">
            <mat-label>Quote #</mat-label>
            <input matInput placeholder="Quote #" name="quoteNumber" [(ngModel)]="filter.quoteNumber" />
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-lg="17" fxFlex.gt-md="17" fxFlex.gt-sm="14">
            <mat-label>Quote Status</mat-label>
            <mat-select placeholder="Quote Status" name="quoteStatus" [(ngModel)]="filter.quoteStatusId">
              <mat-option>None</mat-option>
              <mat-option *ngFor="let status of statuses" [value]="status.id">
                {{ status?.status }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="20" fxFlex.gt-sm="14">
            <mat-label>Sales Associate</mat-label>
            <mat-select placeholder="Sales Associate" name="salesAssociate" [(ngModel)]="filter.salesAssociatesId" multiple>
              <mat-option *ngFor="let salesassociate of salesAssociates" [value]="salesassociate?.id">
                {{ salesassociate?.firstName }} {{ salesassociate?.lastName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-lg="15" fxFlex.gt-md="15">
            <mat-label>App Engineer</mat-label>
            <mat-select placeholder="App Engineer" name="appEngineer" [(ngModel)]="filter.appEngAssignedId" multiple>
              <mat-option *ngFor="let appengineer of salesAssociates" [value]="appengineer?.id">
                {{ appengineer?.firstName }} {{ appengineer?.lastName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="14">
            <mat-label>Select Category for Date Filter</mat-label>
            <mat-select placeholder="Select Category for Date Filter" name="dateFilterCategory" [(ngModel)]="filter.dateFilterCategory">
              <mat-option>None</mat-option>
              <mat-option value="Submitted to app">Submitted to App</mat-option>
              <mat-option value="App started">App Started</mat-option>
              <mat-option value="Completed">Completed</mat-option>
            </mat-select>
          </mat-form-field>

        </div>
      </div>
      <div fxLayout="row wrap" class="mb-10 cust_fields">
        <div fxFlex fxLayoutAlign="start center" fxLayoutGap="10px">
          <ng-container *ngIf="filter.dateFilterCategory">
            <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="14">
              <mat-label>From Date</mat-label>
              <input
                matInput
                (click)="fromdate.open()"
                [matDatepicker]="fromdate"
                placeholder="From Date"
                [(ngModel)]="fromDate"
                [max]="toDate"
                name="fromDate"
                autocomplete="off"
                required
              />
              <mat-datepicker-toggle matSuffix [for]="fromdate"></mat-datepicker-toggle>
              <mat-datepicker #fromdate></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-sm="14">
              <mat-label>To Date</mat-label>
              <input
                matInput
                (click)="todate.open()"
                [matDatepicker]="todate"
                placeholder="To Date"
                [(ngModel)]="toDate"
                name="toDate"
                autocomplete="off"
                required
              />
              <mat-datepicker-toggle matSuffix [for]="todate"></mat-datepicker-toggle>
              <mat-datepicker #todate></mat-datepicker>
            </mat-form-field>
          </ng-container>
          <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
            <mat-checkbox
              name="extQuoteRequired"
              color="warn"
              [indeterminate]="filter?.extQuoteRequired === null"
              [ngModel]="filter?.extQuoteRequired === true"
              (ngModelChange)="setCheckBoxTriStateValues(filter?.extQuoteRequired, ExternalQuoteCheckBox)"
            >
              External Quote Required -
              {{
                filter?.extQuoteRequired === true
                  ? checkBoxYesLabel
                  : filter?.extQuoteRequired === false
                  ? checkBoxNoLabel
                  : checkBoxNullLabel
              }}
            </mat-checkbox>
          </div>
          &nbsp; &nbsp; &nbsp;
          <div fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
            <mat-checkbox
              name="customerClarificationRequired"
              color="warn"
              [indeterminate]="filter?.customerClarificationRequired === null"
              [ngModel]="filter?.customerClarificationRequired === true"
              (ngModelChange)="setCheckBoxTriStateValues(filter?.customerClarificationRequired, CustomerQuoteCheckBox)"
            >
              Customer Clarification Required -
              {{
                filter?.customerClarificationRequired === true
                  ? checkBoxYesLabel
                  : filter?.customerClarificationRequired === false
                  ? checkBoxNoLabel
                  : checkBoxNullLabel
              }}
            </mat-checkbox>
          </div>
        </div>
        <div fxFlex.gt-lg="5" fxLayoutAlign="end" fxFlex.gt-md="7" fxFlex.gt-sm="8" fxFlex.gt-xs="100">
          <button class="p-2" mat-raised-button color="warn" (click)="searchAppQuotes()" [disabled]="appEnggQTSFilterForm?.invalid">
            Search
          </button>
        </div>
        <div fxFlex.gt-lg="5" fxLayoutAlign="end" fxFlex.gt-md="7" fxFlex.gt-sm="8" fxFlex.gt-xs="100">
          <button class="p-2" mat-raised-button (click)="clearFilter()">Clear</button>
        </div>
      </div>
    </form>
  </mat-card>
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ headingTitle }}</mat-card-title>
      </div>
      <div>
        <button class="p-2" mat-raised-button color="warn" (click)="downloadAppEnggReportExcel()">Export To Excel</button>
      </div>
    </div>
    <virtual-scroller #scroll [items]="filteredList" (vsEnd)="fetchMore($event)" [bufferAmount]="11">
      <table class="table">
        <thead #header class="sticky-table-header">
          <tr>
            <th *ngIf="isAuthorized">Actions</th>
            <th><span class="open-doc" (click)="getSorting({ active: 'quotationNumber', direction: sortOrder })">Quote Number</span></th>
            <th><span class="open-doc" (click)="getSorting({ active: 'customerName', direction: sortOrder })">Customer Name</span></th>
            <th>Project Title</th>
            <th>
              <span class="open-doc" (click)="getSorting({ active: 'dateSubmittedToApp', direction: sortOrder })">Submitted To App On</span>
            </th>
            <th><span class="open-doc" (click)="getSorting({ active: 'appStartedDate', direction: sortOrder })">App Started On</span></th>
            <th><span class="open-doc" (click)="getSorting({ active: 'appCompletedDate', direction: sortOrder })">Completed On</span></th>
            <th>Major Product Type</th>
            <th>Ext. Quote Req'd</th>
            <th>App Engineer Assigned</th>
            <th>Account Manager</th>
            <th># of Custom Designs</th>
            <th>Cust. Clarification Req</th>
            <th>Notes</th>
            <th>SQT Link</th>
            <th>Current Status</th>
          </tr>
        </thead>
        <tbody class="text-center" #container>
          <tr class="overflow-text-css" *ngFor="let item of scroll.viewPortItems" [ngStyle]="rowColorSelection(item?.designStatusId)">
            <td *ngIf="isAuthorized">
              <button mat-icon-button [matMenuTriggerFor]="menu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu class="menu" #menu="matMenu">
                <button mat-menu-item (click)="openColorSelectionRow(item?.id)">
                  <mat-icon>visibility</mat-icon>
                  <span>Highlight Row</span>
                </button>
                <button mat-menu-item (click)="openColorSelectionColumn(item?.id)">
                  <mat-icon>visibility</mat-icon>
                  <span>Highlight Column</span>
                </button>
                <button mat-menu-item *ngIf="isAuthorized; else other" [routerLink]="appJacketLink" [queryParams]="{ quotId: item?.id }">
                  <mat-icon>arrow_right_alt</mat-icon>
                  <span>Go To Quote</span>
                </button>
              </mat-menu>
            </td>

            <td [ngStyle]="columnColorSelection('quotationNumber', item?.appQuoteTrackerColumnColorList)" matTooltip="{{ item?.quotationNumber }}" matTooltipClass="sfl-formula-tooltip">
              <a *ngIf="isAuthorized; else otherQuotationNumber" class="link" (click)="openTrackerField(item?.id, item?.designStatusId)">
                {{ item?.quotationNumber }}
              </a>
              <ng-template #otherQuotationNumber> {{ item?.quotationNumber }}</ng-template>
            </td>
            <td [ngStyle]="columnColorSelection('customerName', item?.appQuoteTrackerColumnColorList)" matTooltip="{{ item?.customerName }}" matTooltipClass="sfl-formula-tooltip">{{ item?.customerName }}</td>
            <td [ngStyle]="columnColorSelection('projectTitle', item?.appQuoteTrackerColumnColorList)" matTooltip="{{ item?.projectTitle }}" matTooltipClass="sfl-formula-tooltip">{{ item?.projectTitle }}</td>
            <td [ngStyle]="columnColorSelection('dateSubmittedToApp', item?.appQuoteTrackerColumnColorList)">{{ item?.dateSubmittedToApp | date: 'MM-dd-yyyy' }}</td>
            <td [ngStyle]="columnColorSelection('appStartedDate', item?.appQuoteTrackerColumnColorList)">{{ item?.appStartedDate | date: 'MM-dd-yyyy' }}</td>
            <td [ngStyle]="columnColorSelection('appCompletedDate', item?.appQuoteTrackerColumnColorList)">{{ item?.appCompletedDate | date: 'MM-dd-yyyy' }}</td>
            <td [ngStyle]="columnColorSelection('productType', item?.appQuoteTrackerColumnColorList)">{{ item?.productType }}</td>
            <td [ngStyle]="columnColorSelection('extQuoteRequired', item?.appQuoteTrackerColumnColorList)">{{ item?.extQuoteRequired | convertToYesNo }}</td>
            <td
            [ngStyle]="columnColorSelection('assignedAppEngineerId', item?.appQuoteTrackerColumnColorList)"
              matTooltip="{{ item?.assignedAppEngineerId | getFirstNameLastName: salesAssociates }}"
              matTooltipClass="sfl-formula-tooltip"
            >
              {{ item?.assignedAppEngineerId | getFirstNameLastName: salesAssociates }}
            </td>
            <td [ngStyle]="columnColorSelection('accountManagerId', item?.appQuoteTrackerColumnColorList)" matTooltip="{{ item?.accountManagerId | getAccountManagerName: accountManager }}" matTooltipClass="sfl-formula-tooltip">
              {{ item?.accountManagerId | getAccountManagerName: accountManager }}
            </td>
            <td [ngStyle]="columnColorSelection('noOfDesigns', item?.appQuoteTrackerColumnColorList)">{{ item?.noOfDesigns }}</td>
            <td [ngStyle]="columnColorSelection('customerClarificationRequired', item?.appQuoteTrackerColumnColorList)">{{ item?.customerClarificationRequired | convertToYesNo }}</td>
            <td class="text-wrap notes-cell text-left" [ngStyle]="columnColorSelection('notes', item?.appQuoteTrackerColumnColorList)" style="max-width: 500px;min-width: 300px;">
              <div *ngIf="!item.isEditingNotes; else editNotes" (click)="!isSales && enableEditNotes(item)" class="notes-content">
                <pre *ngIf="item.notes" class="formatted-text">{{ item.notes }}</pre>
                <span *ngIf="!item.notes" class="empty-cell">{{ '\u00A0' }}</span>
              </div>
              <ng-template #editNotes>
                <div class="notes-editor">
                  <textarea #notesInput matInput [(ngModel)]="item.notes" name="notes"
                    (blur)="item.notes && item.notes.trim() ? saveNotes(item) : cancelNotesEdit(item)"
                    (keydown.enter)="$event.preventDefault(); item.notes && item.notes.trim() ? saveNotes(item) : cancelNotesEdit(item)"
                    (input)="autoGrow($event.target)"
                    class="notes-textarea auto-grow"></textarea>
                </div>
              </ng-template>
            </td>
            <td class="text-wrap sqt-link-cell text-left" [ngStyle]="columnColorSelection('sqtLink', item?.appQuoteTrackerColumnColorList)" style="max-width: 500px;min-width: 300px;">
              <div *ngIf="!item.isEditingSqtLink; else editSqtLink" (click)="!isSales && enableEditSqtLink(item)" class="sqt-link-content">
                <div *ngIf="item.sqtLink" class="formatted-text" [innerHTML]="formatLinks(item.sqtLink)"></div>
                <span *ngIf="!item.sqtLink" class="empty-cell">{{ '\u00A0' }}</span>
              </div>
              <ng-template #editSqtLink>
                <div class="sqt-link-editor">
                  <textarea #sqtLinkInput matInput [(ngModel)]="item.sqtLink" name="sqtLink"
                    (blur)="item.sqtLink && item.sqtLink.trim() ? saveSqtLink(item) : cancelSqtLinkEdit(item)"
                    (keydown.enter)="$event.preventDefault(); item.sqtLink && item.sqtLink.trim() ? saveSqtLink(item) : cancelSqtLinkEdit(item)"
                    (input)="autoGrow($event.target)"
                    class="sqt-link-textarea auto-grow"></textarea>
                </div>
              </ng-template>
            </td>
            <td [ngStyle]="columnColorSelection('currentStatusComment', item?.appQuoteTrackerColumnColorList)" matTooltip="{{ item?.currentStatusComment }}" matTooltipClass="sfl-formula-tooltip">{{ item?.currentStatusComment }}</td>
          </tr>
        </tbody>
      </table>
    </virtual-scroller>
    <div class="status">
      Showing <span>{{ scroll.viewPortInfo.startIndex }}</span> - <span>{{ scroll.viewPortInfo.endIndex }}</span> of
      <span>{{ filteredList?.length }}</span>
    </div>
  </mat-card>
</div>
