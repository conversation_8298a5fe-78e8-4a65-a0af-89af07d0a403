import { ElementBomModel } from './element-bom/element-bom.model';
import { FinalReviewModel } from './final-review/final-review.model';
import { LevelOneReview } from './level1-review/level1-review.model';
import { PatternDesign } from './pattern/pattern.model';

export class JacketListInfo {
  constructor(
    public customerName?: string,
    public quotationNumber?: string,
    public customerCode?: string,
    public quotationNo?: string,
    public customerAbbrev?: string,
    public revisionName?: string,
    public salesOrderNumber?: string,
    public jacketList?: Jacket[],
    public jacketGroupList?: JacketGroup[],
    public errorList?: string[],
    public measurementUnit?: string,
    public tempUnit?: string,
    public revisionId?: number,
    public entryMethod?: string
  ) {
  }
}

export class JacketPartNumberInfo {
  constructor(
    public customerName?: string,
    public customerCode?: string,
    public customerAbbrev?: string,
    public revisionName?: string,
    public salesOrderNumber?: string,
    public measurementUnit?: string,
    public tempUnit?: string,
    public rootPN?: string,
    public jacketList?: Jacket[],
    public ccdcParts?: CcdcParts,
    public errorList?: string[]
  ) {
  }
}

export class Jacket {
  constructor(
    public id?: number,
    public name?: string,
    public briskHeatPN?: string,
    public customerPN?: string,
    public partNumber?: string,
    public rootPartNumber?: string,
    public gCode?: string,
    public mCode?: string,
    public reference?: string,
    public revision?: string,
    public revisionId?: number,
    public sequenceNumber?: number,
    public bhc?: boolean,
    public dmtLogStatus?: string,
    public controlType?: string,
    public tempControlType?: string,
    public jacketGroupId?: number,
    public jacketGroupName?: string,
    public vietnamConversion?: boolean,
    public conversionReview?: boolean,
    public patternDesign?: PatternDesign,
    public levelOneReview?: LevelOneReview,
    public elementBOM?: ElementBomModel,
    public finalReview?: FinalReviewModel,
    public partFilePath?: string,
    public rootPN?: string,
    public ul?: boolean,
    public designRevision?: string,
    public newDesignRevision?: string,
    public tempPartNumber?: string,
    public tempSequenceNumber?: string,
    public bhcSequenceNumber?: number,
    public fieldWidth?: string,
    public jacketGroupChanged?: boolean,
    public nonStandardPartNumber?: boolean,
    public selectedPartFilePath?: string,
    public repeat: boolean = false
  ) {
  }
}

export class JacketRevisionIdDTO {
  constructor(
    public id?: number,
    public name?: string,
    public revisionId?: number,
  ) {
  }
}


export class HalfJacket {
  constructor(
    public id?: number,
    public name?: string,
    public customerPN?: string,
    public repeat?: string,
    public designRevision?: string,
    public reference?: string,
    public partFilePath?: string
  ) {
  }
}

export class SelectedJacketFilePathDTO {
  constructor(public commonSelectedPath?: string, public jacketDesignDTOS?: Jacket[]) {
  }
}

export class RepeatJacketDTO {
  constructor(
    public jacketId?: number,
    public repeat: boolean = false
  ) {
  }
}

export class VietnamConv {
  constructor(
    public checked?: boolean,
    public id?: number,
    public jacketId?: number,
    public name?: string,
    public vietnamDate?: string
  ) {
  }
}

export class ConvReview {
  constructor(
    public checked?: boolean,
    public id?: number,
    public jacketId?: number,
    public name?: string,
    public vietnamDate?: string
  ) {
  }
}

export class CcdcParts {
  constructor(
    public ul?: boolean,
    public voltage?: number,
    public phase?: string,
    public jacketGroupId?: number,
    public fastenerType?: string,
    public linerType?: string,
    public facingType?: string,
    public insulationType?: string,
    public jacketType?: string,
    public thermostatType?: string,
    public sensorType?: string,
    public controlType?: string,
    public productType?: string
  ) {
  }
}

export class JacketGroup {
  constructor(public id?: number, public name?: string, public quotationId?: number, public revisionId?: number) {
  }
}

export class FolderPathRes {
  constructor(public success?: boolean, public title?: string, public message?: string, public data?: PartFilePath[]) {
  }
}

export class PartFilePath {
  constructor(public partNumber?: string, public partFilePath?: string, public pathAlreadyExists: boolean = false) {
  }
}

export class FilePath {
  constructor(public path?: string, public partNumber?: string, public rev?: string) {
  }
}

export class FilePathDirectory {
  constructor(public path?: string) {
  }
}

export class APIRESULT {
  constructor(public success?: boolean, public title?: string, public message?: string) {
  }
}

export class Document {
  constructor(
    public activated?: boolean,
    public filePath?: string,
    public id?: number,
    public name?: string,
    public quotationId?: number,
    public fileData?: File
  ) {
    this.activated = true;
  }
}

export class JacketStatus {
  constructor(
    public SAVE: string = 'save',
    public UNDO: string = 'undo',
  ) {
  }
}

export class ConversionReviewDTO {
  public checked?: boolean;
  public conversionReviewDate?: string;
  public id?: number;
  public jacketId?: number;
  public name?: string;
}

export class JacketProjection {
  public conversionReviewChecked: boolean;
  public conversionReviewId: number;
  public customerPN: string;
  public designRevision: string;
  public elementBomChecked: boolean;
  public elementBomId: number;
  public finalReviewChecked: boolean;
  public finalReviewId: number;
  public id: number;
  public levelOneChecked: boolean;
  public simulationChecked: boolean;
  public jacketGroupName: string;
  public jacketGroupId: number;
  public levelOneId: number;
  public simulationId: number;
  public name: string;
  public partFilePath: string;
  public partNumber: string;
  public patternChecked: boolean;
  public patternId: number;
  public reference: string;
  public repeat: boolean;
  public vietnamConversionChecked: boolean;
  public vietnamConversionId: number;
  public salesOrderNumber: string;
  public customerName: string;
  public quotationNumber: string;
  public patternDesignUserName: string;
  public levelOneUserName: string;
  public elementBomUserName: string;
  public finalReviewUserName: string;
  public vietnamConversionUserName: string;
  public conversionReviewUserName: string;

}

export class ElementBOMDTO {
  public checked?: boolean;
  public comments?: string;
  public customerSpecificLabel?: boolean;
  public efNoConcerns?: boolean;
  public elementBOMDate?: string;
  public elementDetails?: string;
  public id?: number;
  public initialName?: string;
  public jacketId?: number;
  public labelEntered?: boolean;
  public laborHours?: boolean;
  public materialBill?: string;
  public name?: string;
  public newLabelCreated?: boolean;
  public operationBill?: boolean;
  public tempRatingNoConcerns?: boolean;
  public tsNotInterfere?: boolean;
  public wIn2NoConcerns?: boolean;
  public wInNoConcerns?: boolean;
}

export class FinalReviewDTO {
  public bomReviewed?: boolean;
  public checked?: boolean;
  public comments?: string;
  public customerPDFUpdated?: boolean;
  public designReleased?: boolean;
  public elementDesignReviewed?: boolean;
  public engineeringCostSheet?: boolean;
  public finalReviewDate?: string;
  public goldReportRun?: boolean;
  public heaterManuallyVerified?: boolean;
  public id?: number;
  public initialName?: string;
  public jacketId?: string;
  public labelReviewed?: boolean;
  public name?: string;
  public partsActiveInEpicor?: boolean;
  public sensorLengthReviewed?: boolean;
  public tsNotInterfere?: boolean;
}


export class TrackerFieldDialog {
  public quotationId?: number;
  public designStatusId?: number;
  public salesOrderNumber?: any;
}

export class EditableTrackerFields {
  constructor(
    public quotationId?: number,
    public designLocation?: string,
    public folderSubmittedDate?: string,
    public quotationStatusId?: number,
    public approvalStatusId?: number,
    public appEnggAssigned?: number,
    public ofa1Date?: string,
    public approval1Date?: string,
    public ofa2Date?: string,
    public approval2Date?: string,
    public releaseDate?: any,
    public designerUpdate?: boolean,
    public projectTitle?: string,
    public designerCountry?: string,
    public salesRepId?: number,
    public dollarAmount?: number,
    public salesRepName?: string,
    public productType?: string,
    public customerName?: string,
    public soNumber?: string,
    public assignedDesignerId?: number,
    public designComments?: string,
    public noOfCustomRevision?: number,
    public customNoOfDesigns?: number,
    public defaultRowColor?: boolean,
    public shipDate?: string,
    public defaultRowColorValue?: string,
    public primaryColorValue?: string,
    public soRevCount?: number,
    public revisionDate?: string,
    public lastOFADate?: string,
    public engReleaseDate?: string,
    public submittedDesignDate?: string,
    public designQueueDate?: string,
    public patternDesignDate?: string,
    public level1ReviewDate?: string,
    public level1ReviewDateIp?: string,
    public readyForOfaDate?: string,
    public outForApprovalDate?: string,
    public elementAndBomDate?: string,
    public elementAndBomDateIp?: string,
    public finalReviewDate?: string,
    public finalReviewDateIp?: string,
    public vietnamConversionDate?: string,
    public readyForReleaseDate?: string,
    public releasedDate?: string,
    public simulationDate?: string,
    public simulationDateIp?: string,

  ) {
    this.quotationId = null;
    this.designLocation = '';
    this.folderSubmittedDate = '';
    this.shipDate = '';
    this.soNumber = '';
    this.quotationStatusId = null;
    this.approvalStatusId = null;
    this.ofa1Date = '';
    this.projectTitle = '';
    this.approval1Date = '';
    this.ofa2Date = '';
    this.approval2Date = '';
    this.salesRepId = null;
    this.dollarAmount = null;
    this.salesRepName = null;
    this.productType = '';
    this.appEnggAssigned = null;
    this.assignedDesignerId = null;
    this.designComments = '';
    this.noOfCustomRevision = null;
    this.customNoOfDesigns = null;
    this.revisionDate = '';
    this.lastOFADate = '';
    this.engReleaseDate = '';
    this.soRevCount = 0;
    this.submittedDesignDate = null;
  }
}

export class DownloadZipRequestDTO {
  public quotationId: number;
  public jacketId: number | null;
}
