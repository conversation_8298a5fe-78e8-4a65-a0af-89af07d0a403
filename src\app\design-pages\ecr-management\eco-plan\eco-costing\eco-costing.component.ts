import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { Subscription } from 'rxjs';
import { EcoPlanService } from '../eco-plan.service';
import { EcoDto, ECOCostReportDto } from '../eco-plan.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatTableDataSource } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';

@Component({
  selector: 'sfl-eco-costing',
  templateUrl: './eco-costing.component.html'
})
export class EcoCostingComponent implements OnInit, OnDestroy {

  subscription: Subscription = new Subscription();
  showLoader = false;
  isDataAvailable = false;
  _ecoDto: EcoDto = new EcoDto;

  costReportList: ECOCostReportDto;
  dataSource = new MatTableDataSource<ECOCostReportDto>();
  toggleHiddenRow = [];
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  displayedColumns = DisplayColumns.Cols.CostReport;

  get ecoDto() {
    return this._ecoDto;
  }

  @Input()
  set ecoDto(val) {
    this._ecoDto = val;
  }

  constructor(private ecoPlanService: EcoPlanService) { }

  ngOnInit() {
    this.initCostReport();
  }

  initCostReport() {
    this.getEcoCosting();
  }

  getEcoCosting() {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.getEcoCostingByEcrId(this._ecoDto.ecrId).subscribe((ecoCosting: ECOCostReportDto[]) => {
      if (ecoCosting) {
        this.dataSource.data = ecoCosting;
        this.showLoader = false;
      } else {
        this.showLoader = false;
      }
    }, error => {this.showLoader = false; }
    ));
  }

  regenerateEcoCosting() {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.regenerateEcoCostingByEcrId(this._ecoDto.ecrId).subscribe((ecoCosting: ECOCostReportDto[]) => {
      if (ecoCosting) {
        this.dataSource.data = ecoCosting;
        this.showLoader = false;
      } else {
        this.showLoader = false;
      }
    }, error => {this.showLoader = false; }
    ));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
