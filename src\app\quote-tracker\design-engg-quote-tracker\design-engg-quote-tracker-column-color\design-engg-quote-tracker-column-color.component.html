<h2 mat-dialog-title>Select Colour To Highlight Row
  <hr>
</h2>

<form #colourSelectForm="ngForm" [formGroup]="trackerFieldsForm" role="form">
  <div>
    <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
      <mat-select placeholder="Select Colour" name="Select Color" [(ngModel)]="selectedColumnColor" [ngModelOptions]="{standalone: true}">
        <mat-option *ngFor="let color of highlightedColors" [value]="color?.value"
                    [ngStyle]="{'background': color?.value}">
          {{ color?.name }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <div [ngSwitch]="value">
    <div *ngSwitchCase="'designStatusId'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-select placeholder="Quotation Status" name="quoteStatus" #quoteStatus formControlName="quotationStatusId">
          <mat-option *ngFor="let status of statuses" [value]="status.id">
            {{ status?.status }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'approvalStatusId'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-select placeholder="Approval Status" name="approvalStatus" #approvalStatus formControlName="approvalStatusId">
          <mat-option *ngFor="let status of statuses" [value]="status.id">
            {{ status?.status }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'designerId'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-select placeholder="Assigned Designer" name="assignedDesigner" #assignedDesigner
                    formControlName="assignedDesignerId">

          <mat-option *ngFor="let designer of designers" [value]="designer?.id">
            {{ designer?.firstName }} {{ designer?.lastName }}
          </mat-option>
          <mat-option>None</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'designLocation'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-select placeholder="Design Location" name="designLocation" #designLocation color="warn"
                    formControlName="designLocation">
          <mat-option *ngFor="let country of countries" [value]="country">
            {{ country | uppercase }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'designComments'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <textarea matInput placeholder="Design Comments" #designComments name="designComments"
                  formControlName="designComments"></textarea>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'dollarAmount'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <input matInput placeholder="Dollar Amount" #dollarAmount name="dollarAmount" formControlName="dollarAmount">
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'soRevCount'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <input matInput placeholder="SO Revision Count" #soRevCount name="soRevCount" formControlName="soRevCount">
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'noOfDesigns'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <textarea matInput placeholder="No. Of Designs" #customNoOfDesigns name="noOfDesign" sflIsNumber
                  formControlName="customNoOfDesigns"></textarea>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'simulationDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Simulation Date</mat-label>
        <input
          matInput
          (click)="simulationDatePicker.open()"
          [matDatepicker]="simulationDatePicker"
          placeholder="Simulation Date"
          #simulationDate
          formControlName="simulationDate"
          name="simulationDateField"
          autocomplete="off"
        />
        <mat-datepicker-toggle matSuffix [for]="simulationDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #simulationDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'simulationDateIp'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Simulation Date (IP)</mat-label>
        <input
          matInput
          (click)="simulationDateIpPicker.open()"
          [matDatepicker]="simulationDateIpPicker"
          placeholder="Simulation Date (IP)"
          #simulationDateIp
          formControlName="simulationDateIp"
          name="simulationDateIpField"
          autocomplete="off"
        />
        <mat-datepicker-toggle matSuffix [for]="simulationDateIpPicker"></mat-datepicker-toggle>
        <mat-datepicker #simulationDateIpPicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'approval1Date'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Approval 1 Date</mat-label>
        <input
          matInput
          (click)="approval1DatePicker.open()"
          [matDatepicker]="approval1DatePicker"
          placeholder="Approval Date"
          #approval1Date
          formControlName="approval1Date"
          name="approval1DateField"
          autocomplete="off"
        />
        <mat-datepicker-toggle matSuffix [for]="approval1DatePicker"></mat-datepicker-toggle>
        <mat-datepicker #approval1DatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'ofa1Date'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>OFA 1 Date</mat-label>
        <input
          matInput
          (click)="ofa1DatePicker.open()"
          [matDatepicker]="ofa1DatePicker"
          placeholder="OFA 1 Date"
          name="ofa1DateField"
          autocomplete="off"
          #ofa1DateField
          formControlName="ofa1Date"
        />
        <mat-datepicker-toggle matSuffix [for]="ofa1DatePicker"></mat-datepicker-toggle>
        <mat-datepicker #ofa1DatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'releasedDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Released Date</mat-label>
        <input
          matInput
          (click)="releasedDatePicker.open()"
          [matDatepicker]="releasedDatePicker"
          placeholder="Released Date"
          name="releasedDateField"
          autocomplete="off"
          #releasedDateField
          formControlName="releasedDate"
        />
        <mat-datepicker-toggle matSuffix [for]="releasedDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #releasedDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'designQueueDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Pattern Design Date</mat-label>
        <input
          matInput
          (click)="designQueueDatePicker.open()"
          [matDatepicker]="designQueueDatePicker"
          placeholder="Pattern Design Date"
          name="designQueueDateField"
          autocomplete="off"
          #designQueueDateField
          formControlName="designQueueDate"
        />
        <mat-datepicker-toggle matSuffix [for]="designQueueDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #designQueueDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'patternDesignDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Pattern Design Date</mat-label>
        <input
          matInput
          (click)="patternDesignDatePicker.open()"
          [matDatepicker]="patternDesignDatePicker"
          placeholder="Pattern Design Date"
          name="patternDesignDateField"
          autocomplete="off"
          #patternDesignDateField
          formControlName="patternDesignDate"
        />
        <mat-datepicker-toggle matSuffix [for]="patternDesignDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #patternDesignDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'level1ReviewDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Level 1 Review Date</mat-label>
        <input
          matInput
          (click)="level1ReviewDatePicker.open()"
          [matDatepicker]="level1ReviewDatePicker"
          placeholder="Level 1 Review Date"
          name="level1ReviewDateField"
          autocomplete="off"
          #level1ReviewDateField
          formControlName="level1ReviewDate"
        />
        <mat-datepicker-toggle matSuffix [for]="level1ReviewDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #level1ReviewDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'level1ReviewDateIp'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Level 1 Review Date (IP)</mat-label>
        <input
          matInput
          (click)="level1ReviewDateIpPicker.open()"
          [matDatepicker]="level1ReviewDateIpPicker"
          placeholder="Level 1 Review Date (IP)"
          name="level1ReviewDateIpField"
          autocomplete="off"
          #level1ReviewDateIpField
          formControlName="level1ReviewDateIp"
        />
        <mat-datepicker-toggle matSuffix [for]="level1ReviewDateIpPicker"></mat-datepicker-toggle>
        <mat-datepicker #level1ReviewDateIpPicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'readyForOfaDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Ready for OFA Date</mat-label>
        <input
          matInput
          (click)="readyForOfaDatePicker.open()"
          [matDatepicker]="readyForOfaDatePicker"
          placeholder="Ready for OFA Date"
          name="readyForOfaDateField"
          autocomplete="off"
          #readyForOfaDateField
          formControlName="readyForOfaDate"
        />
        <mat-datepicker-toggle matSuffix [for]="readyForOfaDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #readyForOfaDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'outForApprovalDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>OFA Date</mat-label>
        <input
          matInput
          (click)="outForApprovalDatePicker.open()"
          [matDatepicker]="outForApprovalDatePicker"
          placeholder="OFA Date"
          name="outForApprovalDateField"
          autocomplete="off"
          #outForApprovalDateField
          formControlName="outForApprovalDate"
        />
        <mat-datepicker-toggle matSuffix [for]="outForApprovalDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #outForApprovalDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'elementAndBomDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Element and BOM Date</mat-label>
        <input
          matInput
          (click)="elementAndBomDatePicker.open()"
          [matDatepicker]="elementAndBomDatePicker"
          placeholder="Element and BOM Date"
          name="elementAndBomDateField"
          autocomplete="off"
          #elementAndBomDateField
          formControlName="elementAndBomDate"
        />
        <mat-datepicker-toggle matSuffix [for]="elementAndBomDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #elementAndBomDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'elementAndBomDateIp'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Element and BOM Date (IP)</mat-label>
        <input
          matInput
          (click)="elementAndBomDateIpPicker.open()"
          [matDatepicker]="elementAndBomDateIpPicker"
          placeholder="Element and BOM Date (IP)"
          name="elementAndBomDateIpField"
          autocomplete="off"
          #elementAndBomDateIpField
          formControlName="elementAndBomDateIp"
        />
        <mat-datepicker-toggle matSuffix [for]="elementAndBomDateIpPicker"></mat-datepicker-toggle>
        <mat-datepicker #elementAndBomDateIpPicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'finalReviewDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Final Review Date</mat-label>
        <input
          matInput
          (click)="finalReviewDatePicker.open()"
          [matDatepicker]="finalReviewDatePicker"
          placeholder="Final Review Date"
          name="finalReviewDateField"
          autocomplete="off"
          #finalReviewDateField
          formControlName="finalReviewDate"
        />
        <mat-datepicker-toggle matSuffix [for]="finalReviewDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #finalReviewDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'finalReviewDateIp'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Final Review Date (IP)</mat-label>
        <input
          matInput
          (click)="finalReviewDateIpPicker.open()"
          [matDatepicker]="finalReviewDateIpPicker"
          placeholder="Final Review Date (IP)"
          name="finalReviewDateField"
          autocomplete="off"
          #finalReviewDateIpField
          formControlName="finalReviewDateIp"
        />
        <mat-datepicker-toggle matSuffix [for]="finalReviewDateIpPicker"></mat-datepicker-toggle>
        <mat-datepicker #finalReviewDateIpPicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'vietnamConversionDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Vietnam Conversion Date</mat-label>
        <input
          matInput
          (click)="vietnamConversionDatePicker.open()"
          [matDatepicker]="vietnamConversionDatePicker"
          placeholder="Vietnam Conversion Date"
          name="vietnamConversionDateField"
          autocomplete="off"
          #vietnamConversionDateField
          formControlName="vietnamConversionDate"
        />
        <mat-datepicker-toggle matSuffix [for]="vietnamConversionDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #vietnamConversionDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'readyForReleaseDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Ready For Release Date</mat-label>
        <input
          matInput
          (click)="readyForReleaseDatePicker.open()"
          [matDatepicker]="readyForReleaseDatePicker"
          placeholder="Ready For Release Date"
          name="readyForReleaseDateField"
          autocomplete="off"
          #readyForReleaseDateField
          formControlName="readyForReleaseDate"
        />
        <mat-datepicker-toggle matSuffix [for]="readyForReleaseDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #readyForReleaseDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'submittedDesignDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Submitted to Design Date</mat-label>
        <input
          matInput
          (click)="submittedDesignDatePicker.open()"
          [matDatepicker]="submittedDesignDatePicker"
          placeholder="Submitted to Design Date"
          name="submittedDesignDateField"
          autocomplete="off"
          #submittedDesignDateField
          formControlName="submittedDesignDate"
        />
        <mat-datepicker-toggle matSuffix [for]="submittedDesignDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #submittedDesignDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'revisionDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Revision Date</mat-label>
        <input
          matInput
          (click)="revisionDatePicker.open()"
          [matDatepicker]="revisionDatePicker"
          placeholder="Revision Date"
          name="revisionDateField"
          autocomplete="off"
          #ofa1DateField
          formControlName="revisionDate"
        />
        <mat-datepicker-toggle matSuffix [for]="revisionDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #revisionDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'lastOFADate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Last OFA Date</mat-label>
        <input
          matInput
          (click)="lastOFADatePicker.open()"
          [matDatepicker]="lastOFADatePicker"
          placeholder="Last OFA Date"
          name="lastOFADateField"
          autocomplete="off"
          #ofa1DateField
          formControlName="lastOFADate"
        />
        <mat-datepicker-toggle matSuffix [for]="lastOFADatePicker"></mat-datepicker-toggle>
        <mat-datepicker #lastOFADatePicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngSwitchCase="'engReleaseDate'">
      <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <mat-label>Est. Eng. Release Date</mat-label>
        <input
          matInput
          (click)="engReleaseDatePicker.open()"
          [matDatepicker]="engReleaseDatePicker"
          placeholder="Est. Eng. Release Date"
          name="engReleaseDateField"
          autocomplete="off"
          #ofa1DateField
          formControlName="engReleaseDate"
        />
        <mat-datepicker-toggle matSuffix [for]="engReleaseDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #engReleaseDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
  </div>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" (click)="selectColumn()">Submit
    </button>
    <button mat-raised-button type="button" (click)="closeDialog(false)">Cancel</button>
  </mat-dialog-actions>
</form>
