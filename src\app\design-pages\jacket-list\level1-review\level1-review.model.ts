export class LevelOneReview {
  constructor(
    public id?: number,
    public name?: string,
    public checked?: boolean,
    public levelOneReviewDate?: any,
    public geometryPattern?: boolean,
    public layeredConstruction?: boolean,
    public wattageCalculation?: boolean,
    public wirringDiagram?: boolean,
    public stepJacketModels?: boolean,
    public checkedEngineering?: boolean,
    public verifiedUL?: boolean,
    public comments?: string,
    public jacketId?: number
  ) {}
}

export class ChecklistLevelOneReviewDTO {
  public levelOneReviewDTOs: LevelOneReview[] = [];
  public quotationID: number;
}
