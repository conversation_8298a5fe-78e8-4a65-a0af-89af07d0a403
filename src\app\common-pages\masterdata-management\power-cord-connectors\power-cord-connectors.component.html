<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex.gt-lg="30" fxFlex.gt-md="30">
          <mat-label>Search Part Number</mat-label>
          <input matInput [(ngModel)]="powerCordConnectorFilter.partNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldpartNumber)"
            *ngIf="powerCordConnectorFilter.partNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" (click)="addPowerCordConnector()">Add New Power Cord Connector</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="powerCordConnectorDataSource"
        (matSortChange)="getPowerCordConnectorSorting($event)"
      >
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="70"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="70"> {{ element?.name }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.partNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="price">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Price </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.price }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="value">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Value </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.value }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editPowerCordConnector(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deletePowerCordConnector(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="powerCordConnectorColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: powerCordConnectorColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!powerCordConnectorDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPowerCordConnectorPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
