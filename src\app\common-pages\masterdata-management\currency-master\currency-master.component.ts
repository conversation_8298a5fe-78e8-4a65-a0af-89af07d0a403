import { Component, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatTableDataSource } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { CurrencyMaster, GenericPageable } from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { Subscription } from 'rxjs';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Messages, SnakbarService } from 'src/app/shared';
import { ManageCurrencyMasterComponent } from './manage-currency-master/manage-currency-master.component';

@Component({
  selector: 'sfl-app-currency-master',
  templateUrl: './currency-master.component.html'
})
export class CurrencyMasterComponent implements OnInit {
  pageTitle = 'Currency Master';
  length: number;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  currencyMaterialMasterPageable: GenericPageable<CurrencyMaster>;
  numberOfElements: number;
  filter = [];
  currencyMaterialMasterDataSource = new MatTableDataSource<CurrencyMaster>();
  currencyMaterialMasterMasterColumns = DisplayColumns.Cols.CurrencyMasterDataColumn;
  showLoader = false;
  pageSizeOptions = Variable.pageSizeOptions;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  subscription = new Subscription();
  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) { }

  ngOnInit() {
    this.getCurrencyMaterialMasterData(this.initialPageIndex, this.initialPageSize);
  }

  createCurrencyTable(serviceRequestList: GenericPageable<CurrencyMaster>) {
    this.currencyMaterialMasterDataSource.data = serviceRequestList.content;
  }

  getCurrencyPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getCurrencyMaterialMasterData(this.pageIndex, this.pageSize);
  }

  getCurrencyMaterialMasterSorting(event) {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getCurrencyMaterialMasterData(this.pageIndex, this.pageSize);
  }

  //Add currency
  addCurrencyMaterial() {
    this.editCurrencyMaterial(new CurrencyMaster());
  }

  //Get the currency list
  getCurrencyMaterialMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getCurrencyList(this.filter, pageable).subscribe(
        (res: GenericPageable<CurrencyMaster>) => {
          this.currencyMaterialMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createCurrencyTable(this.currencyMaterialMasterPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.currencyMaterialMasterDataSource.data = [];
        }
      )
    );
  }

  //View & edit currency
  editCurrencyMaterial(currencyMaterial: CurrencyMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = currencyMaterial;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-app-currency-master-model';
    const dialogRef = this.matDialog.open(ManageCurrencyMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          currencyMaterial.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getCurrencyMaterialMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

}
