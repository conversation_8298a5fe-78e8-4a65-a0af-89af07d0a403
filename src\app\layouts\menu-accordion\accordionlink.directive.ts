import {
  Directive, HostBinding, Inject, Input, OnInit, OnD<PERSON>roy
} from '@angular/core';

import { AccordionDirective } from './accordion.directive';

@Directive({
  selector: '[sfl-appAccordionLink]'
})

export class AccordionLinkDirective implements OnInit, OnDestroy {
  @Input() public group: any;

  @HostBinding('class.open')
  @Input()
  get open(): boolean {
    return this._open;
  }

  set open(value: boolean) {
    this._open = value;
    if (value) {
      this.nav.closeOtherLinks(this);
    }
  }

  protected _open: boolean;
  protected nav: AccordionDirective;

  constructor(@Inject(AccordionDirective) nav: AccordionDirective) {
    this.nav = nav;
  }

  ngOnInit(): any {
    this.nav.addLink(this);
  }

  ngOnDestroy(): any {
    this.nav.removeGroup(this);
  }

  toggle(): any {
    this.open = !this.open;
  }
}
