<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #materialPropertyForm="ngForm" (ngSubmit)="updateMaterialProperty()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Material Property Name"
            [(ngModel)]="materialProperty.name"
            name="materialPropertyName"
            #materialPropertyNameInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="materialPropertyNameInput.touched && materialPropertyNameInput.invalid">
          <small class="mat-text-warn" *ngIf="materialPropertyNameInput?.errors?.required">Material property name is required.</small>
          <small
            class="mat-text-warn"
            *ngIf="materialPropertyNameInput?.errors?.whitespace && !materialPropertyNameInput?.errors?.required"
          >
            Invalid material property name.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select
            placeholder="Material Property Type"
            [(ngModel)]="materialProperty.materialType"
            name="materialPropertyType"
            #materialPropertyTypeInput="ngModel"
            required
          >
            <mat-option *ngFor="let type of materialPropertyTypes" [value]="type">
              {{ type }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="materialPropertyTypeInput.touched && materialPropertyTypeInput.invalid">
          <small class="mat-text-warn" *ngIf="materialPropertyTypeInput?.errors.required">Material property type is required.</small>
        </div>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Density"
            [(ngModel)]="materialProperty.density"
            name="density"
            #densityInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="densityInput.touched && densityInput.invalid">
          <small class="mat-text-warn" *ngIf="densityInput?.errors?.required">Density is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Specific Heat"
            [(ngModel)]="materialProperty.specificHeat"
            name="specificHeat"
            #specificHeatInput="ngModel"
            required
            sflIsDecimal
          />
        </mat-form-field>
        <div *ngIf="specificHeatInput.touched && specificHeatInput.invalid">
          <small class="mat-text-warn" *ngIf="specificHeatInput?.errors?.required">Specific heat is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="materialProperty.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!materialPropertyForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
