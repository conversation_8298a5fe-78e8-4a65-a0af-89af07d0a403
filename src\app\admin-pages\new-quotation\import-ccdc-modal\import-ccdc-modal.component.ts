import { SelectionModel } from '@angular/cdk/collections';
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatTableDataSource } from '@angular/material';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ApplicationInfoTemplateDTO, ClosureTemplateDTO, ImportCcdcFilter, PluggingInformationTemplateDTO } from 'src/app/common-pages/masterdata-management/ccdc-master-data/manage-ccdc/manage-ccdc.model';
import { GenericPageable } from 'src/app/common-pages/masterdata-management/masterdata-management.model';
import { MasterdataManagementService } from 'src/app/common-pages/masterdata-management/masterdata-management.service';
import { User } from 'src/app/common-pages/users/user.model';
import { EcrManagementService } from 'src/app/design-pages/ecr-management/ecr-management.service';
import { Messages, SharedService, SnakbarService, SweetAlertService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { CcdcTemplateDTO } from '../ccdc-model/ccdc.model';
import { JacketGroupService } from '../manage-jacketgroups/manage-jacket-groups.service';
import { SummarySalesOrderComponent } from '../summary-sales-order.component';

@Component({
  selector: 'app-import-ccdc-modal',
  templateUrl: './import-ccdc-modal.component.html',
  styleUrls: ['./import-ccdc-modal.component.css']
})
export class ImportCcdcModalComponent implements OnInit {
  subscription: Subscription = new Subscription();
  displayedColumns = ['userId', 'name', 'description', 'action'];
  userRole: string;
  length: number;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  CcdcMaterialPageable: GenericPageable<CcdcTemplateDTO>;
  sortOrder = Variable.defaultSortOrderDescending;
  numberOfElements: number;
  filter = [];
  users: User[];
  ccdcMaterialMasterDataSource = new MatTableDataSource<CcdcTemplateDTO>();
  selection = new SelectionModel<CcdcTemplateDTO>(false);
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  jacketGroupId: number;
  importFilter: ImportCcdcFilter = new ImportCcdcFilter();
  filterTemplateName = Values.FilterFields.name;
  filterDesc = Values.FilterFields.description;
  filterUserId = Values.FilterFields.userId;
  pageSizeOptions = Variable.pageSizeOptions;

  constructor(
    public dialogRef: MatDialogRef<SummarySalesOrderComponent>,
    private ecrManagementService: EcrManagementService,
    public jacketGroupService: JacketGroupService,
    private snakbarService: SnakbarService,
    private readonly masterDataService: MasterdataManagementService,
    private readonly sweetAlertService: SweetAlertService,
    private sharedService: SharedService,
    private router: Router,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.jacketGroupId = data.jacketGroupId;
  }

  ngOnInit() {
    this.getCCDCMasterData(this.initialPageIndex, this.initialPageSize);
    this.getAllUsers();
    this.userRole = this.sharedService.getRole();
    if (this.userRole !== 'ROLE_SALES') {
      this.displayedColumns = ['userId', 'name', 'description', 'action'];
    } else {
      this.displayedColumns = ['userId', 'name', 'description'];
    }
  }

  selectRow(row) {
    this.selection.toggle(row);
  }

 addFilter() {
    for (const property of Object.entries(this.importFilter)) {
      if (property[1].value) {
        this.filter.push(property[1]);
      }
    }
     this.getCCDCMasterData(this.pageIndex, this.pageSize);
  }

  async resetFilter() {
    this.importFilter = new ImportCcdcFilter();
    this.getCCDCMasterData(this.pageIndex, this.pageSize);
  }

  importCCDC(){
    this.subscription.add(
      this.jacketGroupService.importCcdcTemplateForJacketGroup(this.jacketGroupId, this.selection.selected[0]).subscribe(
        () => {
          this.closeDialog(true);
          this.snakbarService.success(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.ccdc_import_success);
        }
      )
    );
  }

  getCcdcPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getCCDCMasterData(this.pageIndex, this.pageSize);
  }

  getAllUsers() {
    // getUsers
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.getSalesAssociate(true).subscribe((res: User[]) => {
      if (res) {
        this.users = res;
        this.showLoader = false;
      }
    }, error => {
      this.showLoader = false;
    }));
  }


  getCCDCMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.jacketGroupService.getCCDCMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<CcdcTemplateDTO>) => {
          this.CcdcMaterialPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createCcdcMaterialsTable(this.CcdcMaterialPageable);
          this.filter = [];
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.filter = [];
        }
      )
    );
  }

  editCCDC(element: CcdcTemplateDTO) {
    if(!element.applicationInfoTemplateDTO){
      element.applicationInfoTemplateDTO = new ApplicationInfoTemplateDTO();
    }
    if(!element.pluggingInformationTemplateDTO){
      element.pluggingInformationTemplateDTO = new PluggingInformationTemplateDTO();
    }
    if(!element.closureTemplateDTO){
      element.closureTemplateDTO = new ClosureTemplateDTO();
    }
    this.masterDataService.setCcdcTemplateObs(element);
    this.closeDialog(false);
    this.router.navigate(['master-data/management/manage-ccdc']);
  }
  
  async deleteCcdcTemplateData(ccdcApplicationInfoDTO: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteCcdcTemplate(ccdcApplicationInfoDTO).subscribe(
        () => {
          this.snakbarService.success('CCDC Master' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getCCDCMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  createCcdcMaterialsTable(serviceRequestList: GenericPageable<CcdcTemplateDTO>) {
    this.ccdcMaterialMasterDataSource.data = serviceRequestList.content;
  }

  closeDialog(success: boolean): void {
    this.dialogRef.close(success);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
