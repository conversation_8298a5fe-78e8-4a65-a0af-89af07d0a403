
import {catchError, map} from 'rxjs/operators';
import { ApplicationInfo, ClosureInformation } from './../ccdc-model/ccdc.model';
import { utils } from './../../../shared/helpers/app.helper';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class AddClosureService {

    constructor(private http: HttpClient) { }

    getClosureMaterialList() {
        return this.http.get(AppConfig.GET_CLOSURE_MATERIAL_LIST).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    saveClosureInfo(closureInfo: ClosureInformation) {
        if (closureInfo.id) {
            return this.http.put(AppConfig.SAVE_CLOSURE, closureInfo).pipe(map(utils.extractData),
                catchError(utils.handleError),);
        } else {
            return this.http.post(AppConfig.SAVE_CLOSURE, closureInfo).pipe(map(utils.extractData),
                catchError(utils.handleError),);
        }
    }

    getClosureInfoByJacketGroup(id: number) {
        return this.http.get(AppConfig.SAVE_CLOSURE + '/jacketGroup/' + id).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    getEstTemp(jacketGroupId) {
        return this.http.get(AppConfig.GET_EST_TEMP + jacketGroupId).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }
}
