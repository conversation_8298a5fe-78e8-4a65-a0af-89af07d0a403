<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-xs="100" fxFlex="100">
          <h3>{{ pageTitle }}</h3>
        </div>
      </div>
    </div>
  </mat-card>
</div>
