<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #formulaForm="ngForm" (ngSubmit)="addExpression()">
  <mat-dialog-content>
    <div class="mb-10 label-btn" fxLayout="row wrap">
      <div fxLayout="column" fxLayoutAlign="start">
        <div class="mb-10 label">
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.LABEL_PART_NUMBER)">
            {{ labelConfigVariables.LABEL_PART_NUMBER }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.VOLTAGE)">
            {{ labelConfigVariables.VOLTAGE }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.WATTAGE)">
            {{ labelConfigVariables.WATTAGE }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.AMPERAGE)">
            {{ labelConfigVariables.AMPERAGE }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.PHASE)">
            {{ labelConfigVariables.PHASE }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.CUSTOMER_PART_NUMBER)">
            {{ labelConfigVariables.CUSTOMER_PART_NUMBER }}
          </button>
        </div>
        <div class="mb-10">
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.DIMENSIONS)">
            {{ labelConfigVariables.DIMENSIONS }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.LENGTH)">
            {{ labelConfigVariables.LENGTH }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.WIDTH)">
            {{ labelConfigVariables.WIDTH }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.JACKET_PART_NUMBER)">
            {{ labelConfigVariables.JACKET_PART_NUMBER }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable(labelConfigVariables.DESC_2)">
            {{ labelConfigVariables.DESC_2 }}
          </button>
          <button type="button" mat-stroked-button color="warn" (click)="addExpressionVariable('+')">Separator</button>
        </div>
      </div>
    </div>
    <div class="mb-10" fxLayout="row">
      <mat-form-field>
        <textarea
          matInput
          placeholder="Expression"
          name="formulaExpression"
          rows="3"
          required
          class="formular-text"
          [(ngModel)]="expression"
          name="expression"
          #formulaExpressionInput="ngModel"
        >
        </textarea>
      </mat-form-field>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog(null)">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <div class="sfl-add-formula-btn">
        <button mat-raised-button type="submit" color="warn">Add Expression</button>
      </div>
    </div>
  </mat-dialog-actions>
</form>
