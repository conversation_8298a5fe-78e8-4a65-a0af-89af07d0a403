import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';

import { SharedModule } from '../shared/shared.module';

import {
    LoginComponent,
    ForgotPasswordComponent,
} from '.';
import { accountRoutes } from './account.route';
import { AccountService } from './account.service';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { SetInitialNameComponent } from './login/set-initial-name/set-initial-name.component';

@NgModule({
    imports: [
        RouterModule.forChild(accountRoutes),
        SharedModule,
    ],
    declarations: [
        LoginComponent,
        ForgotPasswordComponent,
        ResetPasswordComponent,
        SetInitialNameComponent
    ],
    entryComponents: [
    ],
    providers: [
        AccountService
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class AccountModule { }
