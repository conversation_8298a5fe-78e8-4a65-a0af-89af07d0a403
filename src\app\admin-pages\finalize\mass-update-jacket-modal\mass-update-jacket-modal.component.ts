import { Component, Inject, OnDestroy } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { SnakbarService } from 'src/app/shared';
import { JacketAccessoriesList } from '../../accessories/accessories.model';
import { FinalizeService } from '../finalize.service';

@Component({
  selector: 'sfl-mass-update-jacket-modal',
  templateUrl: './mass-update-jacket-modal.component.html',
})
export class MassUpdateJacketModalComponent implements OnDestroy {

  subscriptionManager = new Subscription();
  selectedJacketList = new Array<JacketAccessoriesList>();
  addedLaborHours = 0;
  addedMaterialCost = 0;
  addedCost = 0;
  addedListPrice = 0;

  constructor(
    private dialogRef: MatDialogRef<MassUpdateJacketModalComponent>,
    private finalizeService: FinalizeService,
    private readonly snakbarService: SnakbarService,
    @Inject(MAT_DIALOG_DATA) data) {
    this.selectedJacketList = data;
  }

  massUpdateSelectedJacketsCost() {
    this.selectedJacketList.forEach((selectedJacket: JacketAccessoriesList) => {
      selectedJacket.addedMaterialCost = this.addedMaterialCost;
      selectedJacket.addedCost = this.addedCost;
      selectedJacket.addedListPrice = this.addedListPrice;
      selectedJacket.addedLaborHours = this.addedLaborHours;
    });
    this.subscriptionManager.add(this.finalizeService.massUpdateJacketCosts(this.selectedJacketList)
      .subscribe((res: JacketAccessoriesList[]) => {
        if (res) {
          this.dialogRef.close(res);
        }
      },
      error => {
        if (error.applicationStatusCode === 1217) {
          this.snakbarService.error(error.message);
        }
      }
    ));
  }

  closeDialog() {
    this.dialogRef.close();
  }


  ngOnDestroy() {
    if (this.subscriptionManager) {
      this.subscriptionManager.unsubscribe();
    }
  }

}
