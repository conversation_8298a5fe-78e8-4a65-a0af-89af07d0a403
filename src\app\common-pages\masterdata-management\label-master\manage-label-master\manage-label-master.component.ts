import { HttpHeaders } from '@angular/common/http';
import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { AddAttachmentComponent } from 'src/app/design-pages/ecr-management/add-attachment/add-attachment.component';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Messages } from 'src/app/shared/constants/messages.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SnakbarService } from 'src/app/shared/service/snakbar.service';
import { environment } from 'src/environments/environment';
import { LabelConfigMaster } from '../../masterdata-management.model';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { LabelConfigurationGeneratorComponent } from './label-configuration-generator/label-configuration-generator.component';

@Component({
  selector: 'sfl-manage-label-master',
  templateUrl: './manage-label-master.component.html',
  styleUrls: ['./manage-label-master.component.css']
})
export class ManageLabelMasterComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  title = ''
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  labelMaster: LabelConfigMaster;
  labelMasterColumns = DisplayColumns.Cols.LabelMasterDataColumn;
  imageUrl: string;
  formData = new FormData();

  constructor(
    public readonly dialogRef: MatDialogRef<ManageLabelMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly matDialog: MatDialog,
    private readonly masterDataService: MasterdataManagementService,
    private readonly snakbarService: SnakbarService
  ) {
    this.labelMaster = Object.assign({}, data);
  }

  ngOnInit() {
    this.labelMaster = this.labelMaster.id ? Object.assign({}, this.labelMaster) : new LabelConfigMaster();
    this.labelMaster.id ? (this.title = 'Update Label Configuration') : (this.title = 'Add Label Configuration');
    this.labelMaster.imageUrl
    ? (this.imageUrl = environment.IMAGES_URL + this.labelMaster.imageUrl)
    : (this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE);
  }

  addConfiguration(fieldToUpdate: string) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.data = { fieldToUpdate: this.labelMaster[fieldToUpdate] };
    matDataConfig.panelClass = 'sfl-formula-generator-model';
    const dialogRef = this.matDialog.open(LabelConfigurationGeneratorComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((res: string) => {
      if (res !== null) {
        this.labelMaster[fieldToUpdate] = res;
      }
    });
  }

  saveLabelConfiguration() {
    this.showLoader = true;
    this.formData.append('labelConfigurationMaster', JSON.stringify(this.labelMaster));
    if (this.labelMaster.id) {
      this.subscription.add(this.masterDataService.saveLabelConfigurationMaster(this.formData).subscribe(res => {
        if (res) {
          this.closeDialog(true);
          this.showLoader = false;
        }
      }, err => {
        this.showLoader = false;
      }));
    } else {
      this.subscription.add(this.masterDataService.saveLabelConfigurationMaster(this.formData).subscribe(res => {
        if (res) {
          this.closeDialog(true);
          this.showLoader = false;
        }
      }, err => {
        this.showLoader = false;
      }));
    }
  }

  addAttachment() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-upload-material-file-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      if (uploadedAttachment && uploadedAttachment.type.match(/image\/*/)) {
        const reader = new FileReader();
        reader.readAsDataURL(uploadedAttachment);
        reader.onload = _event => {
          this.imageUrl = reader.result.toString();
          this.labelMaster.imageUrl = reader.result.toString();
        };
        this.formData.append('file', uploadedAttachment, uploadedAttachment.name);
      } else if (uploadedAttachment) {
        this.snakbarService.error(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Only_Image_Type_Allowed);
      }
    });
  }

  removeAttachment() {
    this.formData.delete('file');
    this.labelMaster.imageUrl = '';
    this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  }

  closeDialog(done: boolean): void {
    this.dialogRef.close(done);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
