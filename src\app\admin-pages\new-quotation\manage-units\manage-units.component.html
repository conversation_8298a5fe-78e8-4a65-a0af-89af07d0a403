<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Manage measurement unit
  <hr />
</h2>
<div class="cust_table">
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
      <mat-select placeholder="Measurement Unit" name="measurementunit" [(ngModel)]="units.measurementUnit">
        <mat-option value="IN">IN</mat-option>
        <mat-option value="MM">MM</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
      <mat-select placeholder="Temperature Unit" name="temperatureunit" [(ngModel)]="units.tempUnit">
        <mat-option value="°C">°C</mat-option>
        <mat-option value="°F">°F</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
</div>
<hr />
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button type="submit" color="warn" (click)="setUnits('save')">Save</button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="submit" (click)="setUnits('saveandnext')" name="saveandnext">Save And Next</button>
  </div>
</mat-dialog-actions>
