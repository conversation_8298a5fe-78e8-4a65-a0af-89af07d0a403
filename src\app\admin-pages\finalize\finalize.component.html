<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div fxLayout="column">
  <div fxLayoutAlign="end" class="mt-5 cust_fields">
    <div fxLayout="row wrap" fxLayoutAlign="space-between" class="geo-header" fxFlex.gt-lg="50" fxFlex.gt-md="60">
      <mat-form-field appearance="outline" *ngIf="madeInArray === 'Vietnam' || madeInArray === 'All'" fxFlex.gt-lg="25"
                      fxFlex.gt-md="25">
        <mat-label>Vietnam Discount</mat-label>
        <input matInput sflIsDecimal (focusout)="applyVietnamDiscount()" [(ngModel)]="discountValue"
               autocomplete="off"/>
        <div matSuffix>
        </div>
      </mat-form-field>&nbsp;
      <mat-form-field appearance="outline" *ngIf="madeInArray === 'Costa Rica' || madeInArray === 'All'"
                      fxFlex.gt-lg="25"
                      fxFlex.gt-md="25">
        <mat-label>Costa Rica Discount</mat-label>
        <input matInput sflIsDecimal (focusout)="applyVietnamDiscount()" [(ngModel)]="costaRicadiscountValue"
               autocomplete="off"/>
        <div matSuffix>
        </div>
      </mat-form-field>&nbsp;
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="25" appearance="outline">
        <mat-label>Made In</mat-label>
        <mat-select [(ngModel)]="madeInArray" [disabled]="manufacturedInPresent"
                    (selectionChange)="onMadeInChange($event.value)">
          <mat-option *ngFor="let key of madeInNewArray" [value]="key.value"> {{ key.name }}</mat-option>
          <mat-option value="All"> All</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    &nbsp;
    <mat-form-field fxFlex.gt-lg="20" fxFlex.gt-md="20" appearance="outline">
      <mat-label>Currency</mat-label>
      <mat-select placeholder="Select Currency" [(ngModel)]="currencyId">
        <mat-option *ngFor="let curr of currency" [value]="curr?.id">
          {{ curr?.abbreviation }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline" fxFlex.gt-lg="10" fxFlex.gt-md="15">
      <mat-label>Lead Time</mat-label>
      <input matInput sflIsNumber autocomplete="off" [(ngModel)]="leadTime"/>
      <div matSuffix><a class="open-doc">In Weeks</a></div>
    </mat-form-field>
  </div>
  <br/>
  <mat-card fxFlex="100" class="cust_table">
    <h1 *ngIf="madeInArray === undefined">USA Jackets</h1>
    <h1 *ngIf="madeInArray === 'All'">USA Jackets</h1>
    <h1 *ngIf="madeInArray === 'Usa'">USA Jackets</h1>
    <h1 *ngIf="madeInArray === 'Vietnam'">Vietnam Jackets</h1>
    <h1 *ngIf="madeInArray === 'Costa Rica'">Costa Rica</h1>
    <div class="cust_table finalize-table">
      <mat-table [dataSource]="jacketDataSource" matSort matSortDirection="asc" matSortDisableClear>
        <!-- selection checkboxes -->
        <ng-container matColumnDef="select">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <mat-checkbox color="warn" (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </mat-header-cell>
          <mat-cell *matCellDef="let row" fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <mat-checkbox *ngIf="row.jacketId" color="warn" (click)="$event.stopPropagation()"
                          [checked]="selection.isSelected(row)" (change)="onSelectedJacket($event, row)">
            </mat-checkbox>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5"></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="quantity">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
            Qty
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <input class="effect quantity" matInput [(ngModel)]="element.quantity" sflIsNumber
                   (change)="updateQty(element)" type="number"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="jacketRepeat">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="30">
            Jacket Repeat
          </mat-header-cell>
          <mat-cell *matCellDef="let jacket" fxFlex="30">
            <div *ngIf="jacket.jacketId">
              <mat-checkbox [(ngModel)]="jacket.jacketRepeat" color="warn"
                            (change)="markJacketAsRepeat(jacket?.jacketId, $event)">
              </mat-checkbox>
            </div>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5"></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-md="7">
            Name
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="7">
            {{ element?.name }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="7">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="customerPN">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Cust. P/N
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.jacketId ? element?.customerPN : "" }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="briskheatPN">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Part Number
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.partNumber }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="20" fxFlex.gt-md="20">
            Description
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="20" fxFlex.gt-md="20">
            {{ element?.description }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="20" fxFlex.gt-md="20">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="laborHours">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Labor Hours
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [(ngModel)]="element.laborHours"
                   (change)="updateQty(element, changedFields.LABORHOUR)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="materialCost">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Material Cost
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="element?.madeIn === usaCountry
                ? element?.usMaterialCost
                : (element?.madeIn === vietnamCountry
                    ? element?.vietnamMaterialCost
                    : element?.costaRicaMaterialCost)"
                   (ngModelChange)="
                  element?.madeIn === usaCountry
                  ? (element.usMaterialCost = $event)
                  : element?.madeIn === vietnamCountry
                  ? (element.vietnamMaterialCost = $event)
                  : (element.costaRicaMaterialCost = $event)"
                   (change)=" updateQty(element, changedFields.MATERIALCOST)"
                   [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="usCost">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="8">
            Cost
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="8">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="
                element?.madeIn === vietnamCountry
                ? element?.revisionAccessoryId !== null
                  ? element?.usCost
                  : element?.vietnamCost
                : element?.madeIn === costaRicaCountry
                  ? element?.revisionAccessoryId !== null
                    ? element?.usCost
                    : element?.costaRicaCost
                  : element?.usCost
              " (ngModelChange)="
                element?.madeIn === vietnamCountry
              ? element?.revisionAccessoryId !== null
                ? (element.usCost = $event)
                : (element.vietnamCost = $event)
              : element?.madeIn === costaRicaCountry
                ? element?.revisionAccessoryId !== null
                  ? (element.usCost = $event)
                  : (element.costaRicaCost = $event)
                : (element.usCost = $event)
              " (change)="updateQty(element, changedFields.COST)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="8">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="listPrice">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            List Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [(ngModel)]="element.listPrice"
                   (change)="updateQty(element, changedFields.LISTPRICE)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="discount">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Discount
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="
                element?.madeIn === usaCountry
                ? element?.discount
                : element?.madeIn === vietnamCountry
                  ? element?.vietnamDiscount
                  : element?.costaRicaDiscount
              " (ngModelChange)="
                element?.madeIn === usaCountry
                ? (element.discount = $event)
                : element?.madeIn === costaRicaCountry
                  ? (element.costaRicaDiscount = $event)
                  : (element.vietnamDiscount = $event)
              " (change)="applyVietnamDiscountOnSingleJacket(element)" [readonly]="element?.revisionAccessoryId"/>
            <span>%</span>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="netPrice">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Net Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              element?.madeIn === usaCountry
                ? (element?.netPrice | currency: "USD":"symbol")
                : element?.madeIn === costaRicaCountry
                  ? (element?.costaRicaNetPrice | currency: "USD":"symbol")
                  : (element?.vietnamNetPrice | currency: "USD":"symbol")

            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong>Total</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="extendedNetPrice">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Ext. Net Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? (element?.extendedNetPrice | currency: "USD":"symbol")
                  : element?.madeIn === costaRicaCountry
                    ? (element?.costaRicaExtendedNetPrice | currency: "USD":"symbol")
                    : (element?.vietnamExtendedNetPrice | currency: "USD":"symbol")
              )
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong *ngIf="madeInArray === 'Usa' || madeInArray === 'All' || madeInArray === undefined">{{
                totalExtendedNetPrice | currency: "USD":"symbol":"1.1-2"
              }}</strong>
            <strong *ngIf="madeInArray === 'Vietnam'">{{
                vietnamTotalExtendedNetPrice | currency: "USD":"symbol":"1.1-2"
              }}</strong>
            <strong *ngIf="madeInArray === 'Costa Rica'">{{
                costaRicaTotalExtendedNetPrice | currency: "USD":"symbol":"1.1-2"
              }}</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="margin">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Margin
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                ? element?.margin
                  ? element?.margin + "%"
                  : ""
                : element?.madeIn === vietnamCountry
                  ? element?.vietnamMargin + "%"
                  : element?.madeIn === costaRicaCountry
                    ? element?.costaRicaMargin + "%"
                    : "")
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="grossMargin">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Gross Margin
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? (element?.grossMargin | number: "1.1-2")
                  : element?.madeIn === costaRicaCountry
                    ? (element?.costaRicaGrossMargin | number: "1.1-2")
                    : (element?.vietnamGrossMargin | number: "1.1-2")
              )
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong *ngIf="madeInArray === 'Usa' || madeInArray === 'All' || madeInArray === undefined">{{
                totalGrossMargin | currency: "USD":"symbol":"1.1-2"
              }}</strong>
            <strong *ngIf="madeInArray === 'Vietnam'">{{
                vietnamTotalGrossMargin | currency: "USD":"symbol":"1.1-2"
              }}</strong>
            <strong *ngIf="madeInArray === 'Costa Rica'">{{
                costaRicaTotalGrossMargin | currency: "USD":"symbol":"1.1-2"
              }}</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="madeIn">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Made In
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.madeIn | uppercase }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="massUpdated">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Mass Updated
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <span *ngIf="element?.massUpdated" (click)="openAddedValuePopUp(element)" class="material-icons open-doc"
                  matTooltip="Click to see the additional added value summary">
              done</span>
          </mat-cell>

          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
        <mat-footer-row *matFooterRowDef="displayedColumns; sticky: true"
                        [ngClass]="{ hide: jacketDataSource.data.length === 0 }">
        </mat-footer-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="isNoDataFound | async">No data found</div>
  </mat-card>


  <mat-card fxFlex="100" class="cust_table" *ngIf="madeInArray==='All'">
    <h1>Vietnam Jackets</h1>
    <div class="cust_table finalize-table">
      <mat-table [dataSource]="jacketDataSourceVietnam" matSort matSortDirection="asc" matSortDisableClear>
        <!-- selection checkboxes -->
        <ng-container matColumnDef="select">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <mat-checkbox color="warn" (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </mat-header-cell>
          <mat-cell *matCellDef="let row" fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <mat-checkbox *ngIf="row.jacketId" color="warn" (click)="$event.stopPropagation()"
                          [checked]="selection.isSelected(row)" (change)="onSelectedJacket($event, row)">
            </mat-checkbox>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5"></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="quantity">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
            Qty
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <input class="effect quantity" matInput [(ngModel)]="element.quantity" sflIsNumber
                   (change)="updateQty(element)" type="number"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="jacketRepeat">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="30">
            Jacket Repeat
          </mat-header-cell>
          <mat-cell *matCellDef="let jacket" fxFlex="30">
            <div *ngIf="jacket.jacketId">
              <mat-checkbox [(ngModel)]="jacket.jacketRepeat" color="warn"
                            (change)="markJacketAsRepeat(jacket?.jacketId, $event)">
              </mat-checkbox>
            </div>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5"></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-md="7">
            Name
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="7">
            {{ element?.name }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="7">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="customerPN">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Cust. P/N
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.jacketId ? element?.customerPN : "" }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="briskheatPN">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Part Number
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.partNumber }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="20" fxFlex.gt-md="20">
            Description
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="20" fxFlex.gt-md="20">
            {{ element?.description }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="20" fxFlex.gt-md="20">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="laborHours">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Labor Hours
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [(ngModel)]="element.laborHours"
                   (change)="updateQty(element, changedFields.LABORHOUR)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="materialCost">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Material Cost
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="element?.madeIn === usaCountry
                ? element?.usMaterialCost
                : element?.madeIn === vietnamCountry
                  ? element?.vietnamMaterialCost
                  : element?.costaRicaMaterialCost"
                   (change)="updateQty(element, changedFields.MATERIALCOST)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="usCost">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="8">
            Cost
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="8">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="
                element?.madeIn === vietnamCountry
                  ? element?.revisionAccessoryId !== null
                    ? element?.usCost
                    : element?.vietnamCost
                  : element?.usCost
              " (ngModelChange)="
                element?.madeIn === vietnamCountry
                  ? element?.revisionAccessoryId !== null
                    ? (element.usCost = $event)
                    : (element.vietnamCost = $event)
                  : (element.usCost = $event)
              " (change)="updateQty(element, changedFields.COST)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="8">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="listPrice">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            List Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [(ngModel)]="element.listPrice"
                   (change)="updateQty(element, changedFields.LISTPRICE)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="discount">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Discount
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="
                element?.madeIn === usaCountry || element?.vietnamDiscount == null
                  ? element?.discount
                  : element?.vietnamDiscount
              " (ngModelChange)="
                element?.madeIn === usaCountry || element.vietnamDiscount == null
                  ? (element.discount = $event)
                  : (element.vietnamDiscount = $event)
              " (change)="applyVietnamDiscountOnSingleJacket(element)" [readonly]="element?.revisionAccessoryId"/>
            <span>%</span>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="netPrice">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Net Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? element?.netPrice
                  : element?.vietnamNetPrice
              ) | currency: "USD":"symbol"
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong>Total</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="extendedNetPrice">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Ext. Net Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? element?.extendedNetPrice
                  : element?.vietnamExtendedNetPrice
              ) | currency: "USD":"symbol"
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong>{{
                vietnamTotalExtendedNetPrice | currency: "USD":"symbol":"1.1-2"
              }}</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="margin">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Margin
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              element?.madeIn === usaCountry
                ? element?.margin
                  ? element?.margin + "%"
                  : ""
                : element?.vietnamMargin
                  ? element?.vietnamMargin + "%"
                  : ""
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="grossMargin">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Gross Margin
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? element?.grossMargin
                  : element?.vietnamGrossMargin
              ) | number: "1.1-2"
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong>{{
                vietnamTotalGrossMargin | currency: "USD":"symbol":"1.1-2"
              }}</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="madeIn">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Made In
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.madeIn | uppercase }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="massUpdated">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Mass Updated
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <span *ngIf="element?.massUpdated" (click)="openAddedValuePopUp(element)" class="material-icons open-doc"
                  matTooltip="Click to see the additional added value summary">
              done</span>
          </mat-cell>

          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
        <mat-footer-row *matFooterRowDef="displayedColumns; sticky: true"
                        [ngClass]="{ hide: jacketDataSource.data.length === 0 }">
        </mat-footer-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="isNoDataFound | async">No data found</div>
  </mat-card>

  <mat-card fxFlex="100" class="cust_table" *ngIf="madeInArray==='All'">
    <h1>Costa Rica Jackets</h1>
    <div class="cust_table finalize-table">
      <mat-table [dataSource]="jacketDataSourceCosta" matSort matSortDirection="asc" matSortDisableClear>
        <!-- selection checkboxes -->
        <ng-container matColumnDef="select">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <mat-checkbox color="warn" (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()">
            </mat-checkbox>
          </mat-header-cell>
          <mat-cell *matCellDef="let row" fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <mat-checkbox *ngIf="row.jacketId" color="warn" (click)="$event.stopPropagation()"
                          [checked]="selection.isSelected(row)" (change)="onSelectedJacket($event, row)">
            </mat-checkbox>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5"></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="quantity">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
            Qty
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="5" fxFlex.gt-md="5">
            <input class="effect quantity" matInput [(ngModel)]="element.quantity" sflIsNumber
                   (change)="updateQty(element)" type="number"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="jacketRepeat">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex="30">
            Jacket Repeat
          </mat-header-cell>
          <mat-cell *matCellDef="let jacket" fxFlex="30">
            <div *ngIf="jacket.jacketId">
              <mat-checkbox [(ngModel)]="jacket.jacketRepeat" color="warn"
                            (change)="markJacketAsRepeat(jacket?.jacketId, $event)">
              </mat-checkbox>
            </div>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5"></mat-footer-cell>
        </ng-container>

        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-md="7">
            Name
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="7">
            {{ element?.name }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="7">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="customerPN">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Cust. P/N
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.jacketId ? element?.customerPN : "" }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="briskheatPN">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Part Number
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.partNumber }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="description">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="20" fxFlex.gt-md="20">
            Description
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="20" fxFlex.gt-md="20">
            {{ element?.description }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="20" fxFlex.gt-md="20">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="laborHours">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Labor Hours
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [(ngModel)]="element.laborHours"
                   (change)="updateQty(element, changedFields.LABORHOUR)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="materialCost">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Material Cost
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="element?.madeIn === usaCountry
                ? element?.usMaterialCost
                : element?.madeIn === vietnamCountry
                  ? element?.vietnamMaterialCost
                  : element?.costaRicaMaterialCost"
                   (change)="updateQty(element, changedFields.MATERIALCOST)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="usCost">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="8">
            Cost
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="8">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="
                element?.madeIn === costaRicaCountry
                  ? element?.revisionAccessoryId !== null
                    ? element?.usCost
                    : element?.costaRicaCost
                  : element?.usCost
              " (ngModelChange)="
                element?.madeIn === costaRicaCountry
                  ? element?.revisionAccessoryId !== null
                    ? (element.usCost = $event)
                    : (element.vietnamCost = $event)
                  : (element.usCost = $event)
              " (change)="updateQty(element, changedFields.COST)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="8">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="listPrice">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            List Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [(ngModel)]="element.listPrice"
                   (change)="updateQty(element, changedFields.LISTPRICE)" [readonly]="element?.revisionAccessoryId"/>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="discount">
          <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Discount
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <input step="any" type="number" [ngClass]="{ 'effect quantity': !element?.revisionAccessoryId }" matInput
                   [ngModel]="
                element?.madeIn === usaCountry || element?.costaRicaDiscount == null
                  ? element?.discount
                  : element?.costaRicaDiscount
              " (ngModelChange)="
                element?.madeIn === usaCountry || element.costaRicaDiscount == null
                  ? (element.discount = $event)
                  : (element.vietnamDiscount = $event)
              " (change)="applyVietnamDiscountOnSingleJacket(element)" [readonly]="element?.revisionAccessoryId"/>
            <span>%</span>
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="netPrice">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Net Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? element?.netPrice
                  : element?.costaRicaNetPrice
              ) | currency: "USD":"symbol"
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong>Total</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="extendedNetPrice">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Ext. Net Price
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? element?.extendedNetPrice
                  : element?.costaRicaExtendedNetPrice
              ) | currency: "USD":"symbol"
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong>{{
                costaRicaTotalExtendedNetPrice | currency: "USD":"symbol":"1.1-2"
              }}</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="margin">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Margin
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              element?.madeIn === usaCountry
                ? element?.margin
                  ? element?.margin + "%"
                  : ""
                : element?.costaRicaMargin
                  ? element?.costaRicaMargin + "%"
                  : ""
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10"></mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="grossMargin">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Gross Margin
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{
              (element?.madeIn === usaCountry
                  ? element?.grossMargin
                  : element?.costaRicaGrossMargin
              ) | number: "1.1-2"
            }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <strong>{{
                costaRicaTotalGrossMargin | currency: "USD":"symbol":"1.1-2"
              }}</strong>
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="madeIn">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Made In
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            {{ element?.madeIn | uppercase }}
          </mat-cell>
          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <ng-container matColumnDef="massUpdated">
          <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
            Mass Updated
          </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="10">
            <span *ngIf="element?.massUpdated" (click)="openAddedValuePopUp(element)" class="material-icons open-doc"
                  matTooltip="Click to see the additional added value summary">
              done</span>
          </mat-cell>

          <mat-footer-cell *matFooterCellDef fxFlex.gt-lg="8" fxFlex.gt-md="10">
          </mat-footer-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
        <mat-footer-row *matFooterRowDef="displayedColumns; sticky: true"
                        [ngClass]="{ hide: jacketDataSource.data.length === 0 }">
        </mat-footer-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="isNoDataFound | async">No data found</div>
  </mat-card>


  <div fxLayoutAlign="end" class="geo-header">
    <mat-action-row fxLayoutAlign="space-between">
      <button mat-raised-button color="warn" type="button" (click)="resetFinalize()">
        Reset All
      </button>&nbsp;
      <button mat-raised-button color="warn" type="button" (click)="confirmDelete()"
              [disabled]="!selection?.selected?.length">
        Delete Selected Jackets
      </button>&nbsp;
      <button mat-raised-button color="warn" type="button" (click)="resetSelectedJacketLines()"
              [disabled]="!selection?.selected?.length">
        Reset Selected Jackets
      </button>&nbsp;
      <button mat-raised-button color="warn" type="button" (click)="partNumber()">
        Generate P/N
      </button>&nbsp;
      <button mat-raised-button color="warn" type="button" (click)="downloadQuoteExcel()">
        Generate Quote
      </button>&nbsp;
      <button mat-raised-button color="warn" type="button" (click)="downloadSOExcel()">
        Download SO
      </button>&nbsp;
      <button mat-raised-button color="warn" type="button" (click)="massUpdateJacketCosts()"
              [disabled]="!selection?.selected?.length">
        Mass Update Jackets
      </button>&nbsp;
      <button mat-raised-button color="warn" type="button" (click)="sendToDesign()" [disabled]="_manufacturedIn !== ''">
        Send To Design
        <em *ngIf="_manufacturedIn" class="material-icons"> done </em>
      </button>
    </mat-action-row>
  </div>
</div>
