import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {Subscription} from 'rxjs';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {Units} from '../ccdc-model/ccdc.model';
import {ManageUnitsService} from './manage-units.service';
import {SharedService, SweetAlertService} from 'src/app/shared';
import {Variable} from 'src/app/shared/constants/Variable.constants';

@Component({
  selector: 'sfl-manage-units',
  templateUrl: './manage-units.component.html'
})
export class ManageUnitsComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  quotationId: number;
  units: Units;
  jacketGroupId: number;
  revisionId: number;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    private manageUnitDialogRef: MatDialogRef<ManageUnitsComponent>,
    private manageUnitService: ManageUnitsService,
    private sharedService: SharedService,
    private sweetAlertService: SweetAlertService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.quotationId = data.quotationId;
    this.jacketGroupId = data.jacketGroupId;
    this.revisionId = data.revId;
  }

  ngOnInit() {
    this.units = new Units();
    this.getMeasurementUnit();
    this.units.revisionId = this.revisionId;
  }

  closeDialog(): void {
    this.manageUnitDialogRef.close();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.units.measurementUnit = res.measurementUnit;
            this.units.tempUnit = res.tempUnit;
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to set the mesurement units
  async setUnits(value) {
    if (await this.sweetAlertService.warningWhenChangeUnit()) {
      this.showLoader = true;
      this.subscription.add(
        this.manageUnitService.saveUnit(this.quotationId, this.units).subscribe(
          (res: Units) => {
            if (res) {
              const obj = {data: res, mode: value === 'save' ? 0 : 1};
              this.showLoader = false;
              this.manageUnitDialogRef.close(obj);
            }
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
