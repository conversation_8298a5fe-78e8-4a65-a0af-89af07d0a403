import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig, MatTableDataSource, } from '@angular/material';
import { ActivatedRoute } from '@angular/router';
import { DragulaService } from 'ng2-dragula';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { DMTLogComponent } from 'src/app/shared/component/dml-log/dmt-log.component';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { JacketRevisionIdDTO } from '../../jacket-list/jacket-list.model';
import { JacketListService } from '../../jacket-list/jacket-list.service';
import { AddBOmMaterialsComponent } from './add-materials/add-bom-materials.component';
import { AddOperationsComponent } from './add-operations/add-operations.component';
import {
  BOMData,
  BomEpicor,
  CommonPartDTO,
  ConfirmSyncDto,
  ElementOnlyDTO,
  ElementSensors,
  FacingLinerClosure,
  Label,
  LabelDetailedEntry,
  OldBOMValues,
  Operation,
  Part,
  PartClassDisplayDTO,
  SensorsOnlyDTO,
  WirePlugging,
} from './bom-editor.model';
import { BomEditorService } from './bom-editor.service';
import { CopyBomComponent } from './copy-bom/copy-bom.component';
import { EditDialogComponent } from './edit-dialog/edit-dialog.component';
import { PromptUpdateAllComponent } from './prompt-update-all/prompt-update-all.component';
import { SearchMfgPartComponent } from './search-mfg-part/search-mfg-part-component';
import { ViewImageComponent } from './view-image/view-image.component';

@Component({
  selector: 'sfl-bom-editor',
  templateUrl: './bom-editor.component.html',
  styleUrls: ['./bom-editor.component.css']
})
export class BOMEditorComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  form: FormGroup;
  private _JacketID: number;
  bomData: BOMData;
  bomEpicor: BomEpicor;
  confirmBomSync: ConfirmSyncDto = new ConfirmSyncDto();
  checkDMTStatus: boolean;
  relOp: object = Values.relOp;
  interval: any;
  loading: boolean;
  type = 'Usa';
  isEdited: boolean;
  part: Part;
  syncInProcess = false;
  lastSyncStatus: string;
  isSyncSuccess = true;
  syncInProcessLab = false;
  lastSyncStatusLab: string;
  isSyncSuccessLab = true;
  private _revisionId: number;
  color = Variable.warnRed;
  showLoader = false;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  operationsdisplayedColumns = Values.operationsdisplayedColumnsValues;
  operationsDataSource: Operation[] = [];

  labelsDisplayedColumns = Values.labelsDisplayedColumnsValues;
  labelsDataSource: Label[] = [];
  labelDetailedEntryDataSource = new MatTableDataSource<LabelDetailedEntry>();

  labelsColumns = DisplayColumns.Cols.LabelEntryColumns;

  elementsSensorsDataSource: ElementSensors[] = [];
  elementAndSensors: ElementSensors = new ElementSensors();
  facingLinerClosureDisplayedColumns = Values.facingLinerClosureDisplayedColumnsValues;
  facingLinerClosureDataSource: FacingLinerClosure[] = [];

  elementsSensorsDisplayedColumns = Values.elementsSensorsDisplayedColumnsValues;
  elementsOnlyDataSource: ElementOnlyDTO[];

  sensorsDisplayedColumns = Values.sensorsDisplayedColumnsValues;
  sensorsOnlyDataSource: SensorsOnlyDTO[];


  wiringPluggingDisplayedColumns = Values.wiringPluggingDisplayedColumnsValues;
  wiringPluggingDataSource: WirePlugging[] = [];

  oldPropertyValues: OldBOMValues = new OldBOMValues();
  setInterval: any;
  newJackGroupName: JacketRevisionIdDTO[] = [];
  quotationId: number;
  partClass: PartClassDisplayDTO[];

  get jacketID() {
    return this._JacketID;
  }

  @Input()
  set jacketID(val) {
    this._JacketID = val;
    this.getPartByJacketId();
    this.getAllBomDataByJacketId(false);
    this.checkLastSyncStatusByJacketId('All');
    this.checkLastSyncStatusByJacketId('Lab');
    this.checkDMTStatus = false;
  }

  get revisionId() {
    return this._revisionId;
  }

  @Input()
  set revisionId(val) {
    this._revisionId = val;
  }

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly snakbarService: SnakbarService,
    private readonly matDialog: MatDialog,
    private readonly jacketListService: JacketListService,
    private readonly bomeditorService: BomEditorService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly dragulaService: DragulaService,
  ) {
  }

  ngOnInit() {
    this.bomData = new BOMData();
    this.part = new Part();
    this.activatedRoute.queryParams.subscribe(params => {
      this.jacketID = +params['jacketId'];
      this.quotationId = +params['quotId'];
    });
    this.getJacketGroupByRevisionId(this.quotationId);
    this.getPartClassDisplay();
    this.subscription.add(this.dragulaService.dragend('OPERATION')
      .subscribe(() => {
        this.onDraggedOperation();
      })
    ).add(this.dragulaService.dragend('FACING')
      .subscribe(() => {
        this.onDraggedFacing();
      })).add(this.dragulaService.dragend('ELEMENT')
      .subscribe(() => {
        this.onDraggedElement();
      })).add(this.dragulaService.dragend('SENSORS')
      .subscribe(() => {
        this.onDraggedSensors();
      })).add(this.dragulaService.dragend('WIRE')
      .subscribe(() => {
        this.onDraggedWire();
      })).add(this.dragulaService.dragend('LABELS')
      .subscribe(() => {
        this.onDraggedLabels();
      }));
  }

  private onDraggedOperation() {
    let index: number = 0;
    this.operationsDataSource.forEach(data => {
      data.operationIndex = index;
      index++;
    });
    this.updateAllOperation();
  }

  private onDraggedFacing() {
    let index: number = 0;
    this.facingLinerClosureDataSource.forEach(data => {
      data.facingIndex = index;
      index++;
    });
    this.updateAllFacing();
  }

  private onDraggedElement() {
    let index: number = 0;
    this.elementsOnlyDataSource.forEach(data => {
      data.elementIndex = index;
      index++;
    });
    this.updateAllElement();
  }

  private onDraggedSensors() {
    let index: number = 0;
    this.sensorsOnlyDataSource.forEach(data => {
      data.sensorsIndex = index;
      index++;
    });
    this.updateAllSensors();
  }

  private onDraggedWire() {
    let index: number = 0;
    this.wiringPluggingDataSource.forEach(data => {
      data.wireIndex = index;
      index++;
    });
    this.updateAllWires();
  }

  private onDraggedLabels() {
    let index: number = 0;
    this.labelsDataSource.forEach(data => {
      data.labelIndex = index;
      index++;
    });
    this.updateAllLabels();
  }

  getJacketGroupByRevisionId(revisionId) {
    this.jacketListService.getJacketGroupByRevId(revisionId).subscribe((response: JacketRevisionIdDTO[]) => {
      this.newJackGroupName = response;
    });
  }


  getPartByJacketId() {
    this.showLoader = true;
    this.subscription.add(
      this.bomeditorService.getPartByJacketId(this._JacketID).subscribe(
        (res: Part) => {
          if (res) {
            this.part = res;
            this.showLoader = false;
          }
        },
        (error) => {
          this.showLoader = false;
          if (error.applicationStatusCode === 1228) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  exportBOMToExcel() {
    this.bomeditorService.downloadBOMExcel(this.jacketID, this.type).subscribe(success => {
      let fileName = 'BOMSheet-' + (this.part.jacketPartNumber !== null && this.part.jacketPartNumber !== undefined ? this.part.jacketPartNumber : this.jacketID) + '-' + this.type + '.xlsx';
      this.downloadExcel(success, fileName);
    });
  }

  downloadExcel(response, sheetName: string) {
    const dwldLink = document.createElement('a');
    const url = URL.createObjectURL(response);
    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('target', '_blank');
    dwldLink.setAttribute('download', sheetName);
    dwldLink.style.visibility = 'hidden';
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  checkPartInEpicor() {
    let partNum: string[] = this.facingLinerClosureDataSource.map(data => data.partNumber)
      .concat(this.elementsOnlyDataSource.map(data => data.partNumber))
      .concat(this.sensorsOnlyDataSource.map(data => data.partNumber))
      .concat(this.wiringPluggingDataSource.map(data => data.partNumber))
      .concat(this.labelsDataSource.map(data => data.partNumber));
    let commonPartDTO: CommonPartDTO = new CommonPartDTO();
    commonPartDTO.partNumbers = partNum;
    if (this.type && this.type === 'Usa') {
      commonPartDTO.company = 'BHC';
    }
    if (this.type && this.type === 'Vietnam') {
      commonPartDTO.company = 'BRIVI';
    }
    if (this.type && this.type === 'Costa Rica') {
      commonPartDTO.company = 'BRICR';
    }
    this.bomeditorService
      .getEpicorPartDetails(commonPartDTO).subscribe((res) => {
      if (res && res.partString && res.partString !== undefined && res.partString !== null) {
        const partString: string [] = res.partString.replace(/\s+/g, '').split(',');
        this.facingLinerClosureDataSource.forEach(data => {
          if (partString.includes(data.partNumber)) {
            data.partIncluded = true;
          }
        });
        this.elementsOnlyDataSource.forEach(data => {
          if (partString.includes(data.partNumber)) {
            data.partIncluded = true;
          }
        });
        this.sensorsOnlyDataSource.forEach(data => {
          if (partString.includes(data.partNumber)) {
            data.partIncluded = true;
          }
        });
        this.wiringPluggingDataSource.forEach(data => {
          if (partString.includes(data.partNumber)) {
            data.partIncluded = true;
          }
        });
        this.labelsDataSource.forEach(data => {
          if (partString.includes(data.partNumber)) {
            data.partIncluded = true;
          }
        });
      }
    });
  }

  async refreshPartAndBomDataByJacketId() {
    if (await this.sweetAlertService.confirmResetBOM()) {
      this.showLoader = true;
      this.subscription.add(
        this.bomeditorService
          .resetBomDataByJacketId(this._JacketID, this.type)
          .subscribe(
            (res: BOMData) => {
              if (res) {
                this.operationsDataSource = res.operationDTO;
                this.labelsDataSource = res.labelDTO;
                this.setLabelDetailsDataSource(res.labelDTO);
                this.facingLinerClosureDataSource =
                  res.facingLinerClosureDTO;
                // this.elementsSensorsDataSource.data = res.elementSensorsDTO;
                this.elementsOnlyDataSource = res.elementsOnlyDTO;

                this.sensorsOnlyDataSource = res.sensorsOnlyDTO;

                this.wiringPluggingDataSource = res.wirePluggingDTO;
                this.checkPartInEpicor();
                this.showLoader = false;
                this.isEdited = false;
                this.checkPartInEpicor();
              }
            },
            () => (this.showLoader = false)
          )
      );
      this.resetPartDetails();
    }
  }

  private setLabelDetailsDataSource(labelDTO: Label[]) {
    const labelDetails = new Array<LabelDetailedEntry>();
    labelDTO.forEach((dto) => {
      dto.labelDetailedEntryList.forEach((lblDetail) => {
        lblDetail.labelPartNumber = dto.partNumber;
        labelDetails.push(lblDetail);
      });
    });
    this.labelDetailedEntryDataSource.data = labelDetails;
  }

  viewImage(viewLabelImage: string) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_sm;
    matDataConfig.data = {imageUrl: viewLabelImage};
    this.matDialog.open(ViewImageComponent, matDataConfig);
  }

  resetPartDetails() {
    this.subscription.add(
      this.bomeditorService
        .resetPartDetails(this.part.id)
        .subscribe((res: Part) => {
          if (res) {
            this.part = res;
          }
        })
    );
  }

  updatePartListPrice(part: Part) {
    part.listPriceUpdatedManually = true;
    this.updatePartDetails(part);
  }

  updatePartNetPrice(part: Part) {
    part.netPriceUpdatedManually = true;
    this.updatePartDetails(part);
  }

  updatePartDetails(part: Part) {
    this.showLoader = true;
    if (!part.listPrice) {
      part.listPrice = 0;
    }
    this.subscription.add(
      this.bomeditorService.updatePart(part).subscribe(
        () => {
          this.getPartByJacketId();
          this.showLoader = false;
        },
        (error) => {
          this.showLoader = false;
          if (error.applicationStatusCode === 1228) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  updatePartDescription(part: Part) {
    part.descriptionUpdatedManually = true;
    this.updatePartDetails(part);
  }

  mapResetResponses(res) {
    this.operationsDataSource = res.operationDTO;
    this.setLabelDetailsDataSource(res.labelDTO);
    this.elementsOnlyDataSource = res.elementsOnlyDTO;
    this.sensorsOnlyDataSource = res.sensorsOnlyDTO;
    this.facingLinerClosureDataSource =
      res.facingLinerClosureDTO;
    this.wiringPluggingDataSource = res.wirePluggingDTO;
  }

  //Switch Case for Each Section Reset
  resetBomBySection(resetSection) {
    this.subscription.add(
      this.bomeditorService
        .resetBySection(this._JacketID, this.type, resetSection)
        .subscribe(
          (res: BOMData) => {
            if (res) {
              switch (resetSection) {
                case 'Operations': {
                  this.bomeditorService.resetBySection(
                    this._JacketID,
                    this.type,
                    'Operations'
                  );
                  this.mapResetResponses(res);
                  this.snakbarService.success(
                    Messages.RESET_MESSAGE.reset_successfull_operations
                  );
                  break;
                }
                case 'Labels': {
                  this.bomeditorService.resetBySection(
                    this._JacketID,
                    this.type,
                    'Labels',
                  );
                  this.labelsDataSource = res.labelDTO;
                  this.mapResetResponses(res);
                  this.snakbarService.success(
                    Messages.RESET_MESSAGE.reset_successfull_labels
                  );
                  this.isEdited = false;
                  break;
                }
                case 'Elements': {
                  this.bomeditorService.resetBySection(
                    this._JacketID,
                    this.type,
                    'Elements'
                  );
                  this, this.mapResetResponses(res);
                  this.snakbarService.success(
                    Messages.RESET_MESSAGE.reset_successfull_element
                  );
                  break;
                }
                case 'Sensors': {
                  this.bomeditorService.resetBySection(
                    this._JacketID,
                    this.type,
                    'Sensors'
                  );
                  this.mapResetResponses(res);
                  this.snakbarService.success(
                    Messages.RESET_MESSAGE.reset_successfull_sensor
                  );
                  break;
                }
                case 'FacingLinerClosure': {
                  this.bomeditorService.resetBySection(
                    this._JacketID,
                    this.type,
                    'FacingLinerClosure'
                  );
                  this.mapResetResponses(res);
                  this.snakbarService.success(
                    Messages.RESET_MESSAGE.reset_successfull_fce
                  );
                  break;
                }
                case 'WirePlugging': {
                  this.bomeditorService.resetBySection(
                    this._JacketID,
                    this.type,
                    'WirePlugging'
                  );
                  this.mapResetResponses(res);
                  this.snakbarService.success(
                    Messages.RESET_MESSAGE.reset_successfull_wireplugging
                  );
                  break;
                }
              }
            }
            this.checkPartInEpicor();
          },
          () => (this.showLoader = false)
        )
    );
  }

  openOperations() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {jacketId: this._JacketID, type: this.type};
    this.matDialog
      .open(AddOperationsComponent, matDataConfig)
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.operationsDataSource = result;
        }
      });
  }

  openEditDialog(type: string, data) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {data: data, type: type};
    this.matDialog
      .open(EditDialogComponent, matDataConfig)
      .afterClosed().subscribe((res) => {
      if (res && res !== null && res !== undefined && res === 'save') {
        setTimeout(() => {
          this.getAllBomDataByJacketId(true);
        }, 1000);
      }
    });
  }

  openMaterial(value) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.data = {
      jacketId: this._JacketID,
      groupingId: value,
      type: this.type,
    };
    this.matDialog
      .open(AddBOmMaterialsComponent, matDataConfig)
      .afterClosed()
      .subscribe((result) => {
        this.getAllBomDataByJacketId(true);
      });
  }

  onEpicorTypeChanged() {
    this.checkDMTStatus = false;
    this.getAllBomDataByJacketId(false);
    this.checkLastSyncStatusByJacketId('All');
  }

  getAllBomDataByJacketId(isModified: boolean) {
    this.showLoader = true;
    this.subscription.add(
      this.bomeditorService.getAllBOMData(this._JacketID, this.type, isModified).subscribe(
        (res: BOMData) => {
          if (res) {
            this.operationsDataSource = res.operationDTO;
            this.labelsDataSource = res.labelDTO;
            this.setLabelDetailsDataSource(res.labelDTO);
            this.facingLinerClosureDataSource = res.facingLinerClosureDTO;
            this.elementsOnlyDataSource = res.elementsOnlyDTO;
            this.sensorsOnlyDataSource = res.sensorsOnlyDTO;
            this.wiringPluggingDataSource = res.wirePluggingDTO;
            this.showLoader = false;
            this.checkPartInEpicor();
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  async deleteOperation(element) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.subscription.add(
        this.bomeditorService.deleteOperations(element.id).subscribe(
          () => {
            this.operationsDataSource.splice(
              this.operationsDataSource.indexOf(element),
              1
            );
            this.getAllBomDataByJacketId(true);
            this.showLoader = false;
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  updatelabelConfig() {
    this.showLoader = true;
    this.subscription.add(
      this.bomeditorService
        .updateAllLabelEntries(this.labelDetailedEntryDataSource.data)
        .subscribe(
          (res: LabelDetailedEntry[]) => {
            if (res) {
              this.labelDetailedEntryDataSource.data = res;
              this.setLabelDetailsDataSource(this.labelsDataSource);
            }
            this.showLoader = false;
            this.isEdited = false;
          },
          (err) => {
            this.showLoader = false;
          }
        )
    );
  }

  resetlabelConfig() {
    this.showLoader = true;
    this.subscription.add(
      this.bomeditorService
        .resetLabelConfig(this._JacketID, this.type)
        .subscribe(
          (res: LabelDetailedEntry[]) => {
            if (res) {
              this.labelDetailedEntryDataSource.data = res;
              this.getAllBomDataByJacketId(true);
            }
            this.showLoader = false;
            this.isEdited = false;
          },
          (error) => {
            this.getAllBomDataByJacketId(true);
            if (error.applicationStatusCode === 1224) {
              this.snakbarService.error(error.message);
            }
            this.showLoader = false;
          }
        )
    );
  }

  async deleteLabels(element) {
    if (await this.sweetAlertService.deleteAlert()) {
      element.multiple = false;
      this.subscription.add(
        this.bomeditorService
          .checkMultipleLabelsDelete(element)
          .subscribe(async (res: Label) => {
            if (res.multiple && res.multiple !== null) {
              if (await this.confirmUpdate()) {
                element.multiple = true;
              } else {
                element.multiple = false;
              }
              this.subscription.add(
                this.bomeditorService
                  .deleteLabels(element.id, false)
                  .subscribe(() => {
                    this.labelsDataSource.splice(
                      this.labelsDataSource.indexOf(element),
                      1
                    );
                    this.setLabelDetailsDataSource(
                      this.labelsDataSource
                    );
                    this.getAllBomDataByJacketId(true);
                  })
              );
            } else {
              // update directly as index is there and no repeating records on either side (USA / Vietnam)
              this.subscription.add(
                this.bomeditorService
                  .deleteLabels(element.id, false)
                  .subscribe(() => {
                    this.labelsDataSource.splice(
                      this.labelsDataSource.indexOf(element),
                      1
                    );
                    this.setLabelDetailsDataSource(this.labelsDataSource);
                    this.getAllBomDataByJacketId(true);
                  })
              );
            }
          })
      );
    }
  }

  getPartClassDisplay() {
    this.subscription.add(
      this.bomeditorService.getPartClassDisplayList().subscribe((res: PartClassDisplayDTO[]) => {
        this.partClass = res;
      })
    );
  }

  async deleteElement(element: ElementOnlyDTO) {
    if (await this.sweetAlertService.deleteAlert()) {
      element.multiple = false;
      if (element.esIndex === null) {
        this.subscription.add(
          this.bomeditorService
            .checkMultipleElementDelete(element)
            .subscribe(async (res: ElementOnlyDTO) => {
              if (res.multiple && res.multiple !== null) {
                if (await this.confirmUpdate()) {
                  element.multiple = true;

                } else {
                  element.multiple = false;
                }
                this.subscription.add(
                  this.bomeditorService
                    .deleteElement(element)
                    .subscribe(() => {
                      this.elementsOnlyDataSource.splice(
                        this.elementsOnlyDataSource.indexOf(element),
                        1
                      );

                    })
                );
              } else {
                this.elementsOnlyDataSource.splice(
                  this.elementsOnlyDataSource.indexOf(element),
                  1
                );

              }
            })
        );
      } else {
        // update directly as index is there and no repeating records on either side (USA / Vietnam)
        this.subscription.add(
          this.bomeditorService.deleteElement(element).subscribe(() => {
            this.elementsOnlyDataSource.splice(
              this.elementsOnlyDataSource.indexOf(element),
              1
            );
          })
        );
      }
    }
  }


  async deleteSensors(element: SensorsOnlyDTO) {
    if (await this.sweetAlertService.deleteAlert()) {
      element.multiple = false;
      if (element.esIndex === null) {
        this.subscription.add(
          this.bomeditorService
            .checkMultipleSensor(element)
            .subscribe(async (res: SensorsOnlyDTO) => {
              if (res.multiple && res.multiple !== null) {
                if (await this.confirmUpdate()) {
                  element.multiple = true;
                } else {
                  element.multiple = false;
                }
                this.subscription.add(
                  this.bomeditorService
                    .deleteSensors(element)
                    .subscribe(() => {
                      this.getAllBomDataByJacketId(true);
                    })
                );
              } else {
                this.getAllBomDataByJacketId(true);
              }
            })
        );
      } else {
        // update directly as index is there and no repeating records on either side (USA / Vietnam)
        this.subscription.add(
          this.bomeditorService.deleteSensors(element).subscribe(() => {
            this.getAllBomDataByJacketId(true);
          })
        );
      }
    }
  }

  async deleteFacingLinerClosures(element: FacingLinerClosure) {
    if (await this.sweetAlertService.deleteAlert()) {
      element.multiple = false;
      if (element.flcIndex === null) {
        this.subscription.add(
          this.bomeditorService
            .checkMultipleFacingLinerClosuresDelete(element)
            .subscribe(async (res: FacingLinerClosure) => {
              if (res.multiple && res.multiple !== null) {
                if (await this.confirmUpdate()) {
                  element.multiple = true;
                } else {
                  element.multiple = false;
                }
                this.subscription.add(
                  this.bomeditorService
                    .deleteFacingLinerClosures(element)
                    .subscribe(() => {
                      this.facingLinerClosureDataSource.splice(
                        this.facingLinerClosureDataSource.indexOf(
                          element
                        ),
                        1
                      );
                    })
                );
              } else {
                this.facingLinerClosureDataSource.splice(
                  this.facingLinerClosureDataSource.indexOf(element),
                  1
                );
              }
              this.getAllBomDataByJacketId(true);
            })
        );
      } else {
        // update directly as index is there and no repeating records on either side (USA / Vietnam)
        this.subscription.add(
          this.bomeditorService
            .deleteFacingLinerClosures(element)
            .subscribe(() => {
              this.facingLinerClosureDataSource.splice(
                this.facingLinerClosureDataSource.indexOf(element),
                1
              );
              this.getAllBomDataByJacketId(true);
            })
        );
      }
    }
  }

  async deleteWirePluggings(element: WirePlugging) {
    if (await this.sweetAlertService.deleteAlert()) {
      element.multiple = false;
      if (element.wpIndex === null) {
        this.subscription.add(
          this.bomeditorService
            .checkMultipleWirePluggingsDelete(element)
            .subscribe(async (res: WirePlugging) => {
              if (res.multiple && res.multiple !== null) {
                if (await this.confirmUpdate()) {
                  element.multiple = true;
                } else {
                  element.multiple = false;
                }
                this.subscription.add(
                  this.bomeditorService
                    .deleteWirePluggings(element)
                    .subscribe(() => {
                      this.wiringPluggingDataSource.splice(
                        this.wiringPluggingDataSource.indexOf(element),
                        1
                      );
                    })
                );
              } else {
                this.wiringPluggingDataSource.splice(
                  this.wiringPluggingDataSource.indexOf(element),
                  1
                );
              }
            })
        );
      } else {
        // update directly as index is there and no repeating records on either side (USA / Vietnam)
        this.subscription.add(
          this.bomeditorService.deleteWirePluggings(element).subscribe(() => {
            this.wiringPluggingDataSource.splice(
              this.wiringPluggingDataSource.indexOf(element),
              1
            );
          })
        );
      }
    }
    this.getAllBomDataByJacketId(true);
  }

  epicoreBomSync(fileType?: string) {
    if (fileType) {
      if (fileType.toLowerCase() === 'all') {
        this.syncInProcess = true;
        this.loading = true;
      } else if (fileType.toLowerCase() === 'lab') {
        this.syncInProcessLab = true;
      }
    }
    this.subscription.add(
      this.bomeditorService.syncBomToEpicorV1(this.bomEpicor).subscribe(
        (res) => {
          this.checkLastSyncStatusByJacketId(fileType);
          if (res) {
            if (fileType) {
              if (fileType.toLowerCase() === 'all') {
                this.loading = false;
                this.syncInProcess = true;
              } else if (fileType.toLowerCase() === 'lab') {
                this.syncInProcessLab = true;
              }
            }
            setTimeout(() => {
              this.syncInProcess = false;
            }, 1200000);
          }
        },
        (error) => {
          this.loading = false;
          this.syncInProcess = false;
          this.syncInProcessLab = false;
        }
      )
    );
  }

  async syncToEpicor(fileType?: string) {
    this.bomEpicor = new BomEpicor();
    this.bomEpicor.jacketId = this._JacketID;
    this.bomEpicor.epicorType = this.type;
    this.bomEpicor.fileType = fileType;
    this.confirmBomSync.jacketId = this._JacketID;
    this.confirmBomSync.partNum = this.part.jacketPartNumber;
    this.confirmBomSync.rev = this.part.revisionName;
    if (this.part.jacketPartNumber) {
      if (this.isEdited) {
        if (await this.sweetAlertService.confirmLabelEntrySync()) {
          this.subscription.add(
            this.bomeditorService.checkSync(this.confirmBomSync).subscribe(
              async (isValid) => {
                if (isValid) {
                  if (await this.sweetAlertService.confirmSync()) {
                    this.epicoreBomSync(fileType);
                  }
                }
              },
              error => {
                this.loading = false;
                this.syncInProcess = false;
              }
            )
          );
        }
      } else {
        this.bomEpicoreSyncConfirmation(fileType);
      }
    } else {
      this.snakbarService.error(Messages.warning_part_number_not_exist.confirm_msg_title);
    }
  }

  async syncToEpicorLabels(fileType: string) {
    this.bomEpicor = new BomEpicor();
    this.bomEpicor.jacketId = this._JacketID;
    this.bomEpicor.epicorType = this.type;
    this.bomEpicor.fileType = fileType;
    if (this.part.jacketPartNumber) {
      if (await this.sweetAlertService.confirmLabelEntrySync()) {
        if (await this.sweetAlertService.confirmSync()) {
          this.epicoreBomSync(fileType);
        }
        error => {
          this.loading = false;
          this.syncInProcess = false;
          this.syncInProcessLab = false;
        };
      }
    } else {
      this.snakbarService.error(Messages.warning_part_number_not_exist.confirm_msg_title);
    }
  }

  private bomEpicoreSyncConfirmation(fileType: string) {
    this.subscription.add(
      this.bomeditorService.checkSync(this.confirmBomSync).subscribe(
        async (isValid) => {
          if (isValid) {
            if (await this.sweetAlertService.confirmSync()) {
              this.epicoreBomSync(fileType);
            }
          } else {
            this.epicoreBomSync(fileType);
          }
          error => {
            this.loading = false;
            this.syncInProcess = false;
            this.syncInProcessLab = false;
          };
        }
      )
    );
  }

  checkIfAddOrEdited() {
    this.isEdited = true;
  }


  openDMTLog(fileType: string) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_lg;
    matDataConfig.data = {jacketId: this._JacketID, epicorType: this.type, fileType: fileType};
    this.matDialog
      .open(DMTLogComponent, matDataConfig)
      .afterClosed()
      .subscribe(() => {
        this.checkLastSyncStatusByJacketId(fileType);
      });
  }

  mapStatusResponse(res: string, fileType: string): Promise<any> {
    return new Promise((resolve) => {
      let lastSyncStatus = '';
      let syncInProcess = false;
      let loading = false;
      let isSyncSuccess = false;

      if (res) {
        if (res === Messages.Bom_Element.In_progress) {
          lastSyncStatus = Messages.Bom_Element.sync_In_progress;
          syncInProcess = true;
          loading = true;
        } else {
          lastSyncStatus = res;
          isSyncSuccess = !res.includes(Messages.Bom_Element.sync_failed);
        }
      }

      resolve({fileType, lastSyncStatus, syncInProcess, loading, isSyncSuccess});
    });
  }

  updateStatusValues({
                       fileType,
                       lastSyncStatus,
                       syncInProcess,
                       loading,
                       isSyncSuccess,
                     }: {
    fileType: string;
    lastSyncStatus: string;
    syncInProcess: boolean;
    loading: boolean;
    isSyncSuccess: boolean;
  }): Promise<void> {
    return new Promise((resolve) => {
      if (!fileType || fileType.toLowerCase() === 'all') {
        this.lastSyncStatus = lastSyncStatus;
        this.syncInProcess = syncInProcess;
        this.loading = loading;
        this.isSyncSuccess = isSyncSuccess;
      } else if (fileType.toLowerCase() === 'lab') {
        this.lastSyncStatusLab = lastSyncStatus;
        this.syncInProcessLab = syncInProcess;
        this.isSyncSuccessLab = isSyncSuccess;
      }
      resolve();
    });
  }

// Function to execute the methods sequentially
  executeSequentially(res: string, fileType: string) {
    this.mapStatusResponse(res, fileType)
      .then(res => this.updateStatusValues(res))
      .catch((error) => {
        console.error('Error:', error);
      });
  }

  checkLastSyncStatusByJacketId(fileType: string) {
    // this.setInterval = setInterval(() => {
    this.subscription.add(
      this.bomeditorService
        .checkLastSyncStatus(this._JacketID, this.type, fileType)
        .subscribe((res: string) => {
          this.executeSequentially(res, fileType);
        })
    );
  }

  updateOperation(element) {
    this.subscription.add(
      this.bomeditorService.editOperation(element).subscribe()
    );
  }

  updateAllOperation() {
    this.subscription.add(
      this.bomeditorService.editAllOperation(this.operationsDataSource).subscribe()
    );
  }

  updateAllFacing() {
    this.subscription.add(
      this.bomeditorService.editAllFacing(this.facingLinerClosureDataSource).subscribe()
    );
  }

  updateAllElement() {
    this.subscription.add(
      this.bomeditorService.editAllElement(this.elementsOnlyDataSource).subscribe()
    );
  }

  updateAllSensors() {
    this.subscription.add(
      this.bomeditorService.editAllSensors(this.sensorsOnlyDataSource).subscribe()
    );
  }

  updateAllWires() {
    this.subscription.add(
      this.bomeditorService.editAllWires(this.wiringPluggingDataSource).subscribe()
    );
  }

  updateAllLabels() {
    this.subscription.add(
      this.bomeditorService.editAllLabels(this.labelsDataSource).subscribe()
    );
  }

  updateLabel(element, propertyName: string) {
    element = this.setUpOldValues(element, propertyName);
    this.subscription.add(
      this.bomeditorService
        .checkMultipleLabels(element)
        .subscribe(async (res: Label) => {
          if (res.multiple && res.multiple !== null) {
            const updateAll = await this.confirmUpdate();
            if (updateAll) {
              element.multiple = true;
              this.subscription.add(
                this.bomeditorService.editLabels(element).subscribe()
              );
            } else if (updateAll === false) {
              element.multiple = false;
              this.subscription.add(
                this.bomeditorService.editLabels(element).subscribe()
              );
            } else {
              // updateAll === Undefined that mean user has cancelled the action
              this.getAllBomDataByJacketId(true);
            }
          } else {
            // update directly as index is there and no repeating records on either side (USA / Vietnam)
            this.subscription.add(
              this.bomeditorService.editLabels(element).subscribe()
            );
          }
        })
    );
  }

  setUpOldValues(element: ElementSensors, propertyName: string) {
    this.oldPropertyValues[propertyName] =
      this.oldPropertyValues[propertyName] === ''
        ? null
        : this.oldPropertyValues[propertyName];
    element[propertyName] =
      element[propertyName].trim() === '' ? null : element[propertyName];
    switch (propertyName) {
      case Values.proprtyNamePartNumber:
        element.oldPartNumber = this.oldPropertyValues[propertyName];
        element.oldQty = element.qty;
        element.oldRelOperation = null;
        break;
      case Values.proprtyNameQuantity:
        element.oldQty = this.oldPropertyValues[propertyName];
        element.oldPartNumber = element.partNumber;
        element.oldRelOperation = null;
        break;
      case Values.propertyNameRelOp:
        element.oldRelOperation = this.oldPropertyValues[propertyName];
        element.oldPartNumber = null;
        element.oldQty = null;
        break;
      case Values.propertyNameRelDesc:
        element.oldRelDescription = this.oldPropertyValues[propertyName];
        element.oldPartNumber = null;
        element.oldQty = null;
        break;
      case Values.propertyNameRelUOM:
        element.oldRelUOM = this.oldPropertyValues[propertyName];
        element.oldPartNumber = null;
        element.oldQty = null;
        break;
    }
    element.multiple = false;
    return element;
  }

  async updateElementsAndSensor(element: ElementSensors, propertyName: string) {
    element = this.setUpOldValues(element, propertyName);
    if (element.esIndex === null) {
      this.subscription.add(
        this.bomeditorService
          .checkMultipleElementSensor(element)
          .subscribe(async (res: ElementSensors) => {
            if (res.multiple && res.multiple !== null) {
              const updateAll = await this.confirmUpdate();
              if (updateAll) {
                element.multiple = true;
                this.subscription.add(
                  this.bomeditorService
                    .editElementsAndSensor(element)
                    .subscribe()
                );
              } else if (updateAll === false) {
                element.multiple = false;
                this.subscription.add(
                  this.bomeditorService
                    .editElementsAndSensor(element)
                    .subscribe()
                );
              } else {
                // updateAll === Undefined that mean user has cancelled the action
                this.getAllBomDataByJacketId(true);
              }
            }
          })
      );
    } else {
      // update directly as index is there and no repeating records on either side (USA / Vietnam)
      this.subscription.add(
        this.bomeditorService.editElementsAndSensor(element).subscribe()
      );
    }
  }

  async updateFacingLinerClosures(
    element: FacingLinerClosure,
    propertyName: string
  ) {
    element = this.setUpOldValues(element, propertyName);
    if (element.flcIndex === null) {
      this.subscription.add(
        this.bomeditorService
          .checkMultipleFacingLiner(element)
          .subscribe(async (res: FacingLinerClosure) => {
            if (res.multiple && res.multiple !== null) {
              const updateAll = await this.confirmUpdate();
              if (updateAll) {
                element.multiple = true;
                this.subscription.add(
                  this.bomeditorService
                    .editFacingLinerClosures(element)
                    .subscribe()
                );
              } else if (updateAll === false) {
                element.multiple = false;
                this.subscription.add(
                  this.bomeditorService
                    .editFacingLinerClosures(element)
                    .subscribe()
                );
              } else {
                // updateAll === Undefined that mean user has cancelled the action
                this.getAllBomDataByJacketId(true);
              }
            }
          })
      );
    } else {
      // update directly as index is there and no repeating records on either side (USA / Vietnam)
      this.subscription.add(
        this.bomeditorService.editFacingLinerClosures(element).subscribe()
      );
    }
  }

  async updateWirePluggings(element: WirePlugging, propertyName: string) {
    element = this.setUpOldValues(element, propertyName);
    if (element.wpIndex === null) {
      this.subscription.add(
        this.bomeditorService
          .checkMultipleWirePluggings(element)
          .subscribe(async (res: WirePlugging) => {
            if (res.multiple && res.multiple !== null) {
              // Multiple update
              const updateAll = await this.confirmUpdate();
              if (updateAll) {
                element.multiple = true;
                this.subscription.add(
                  this.bomeditorService.editWirePluggings(element).subscribe()
                );
              } else if (updateAll === false) {
                element.multiple = false;
                this.subscription.add(
                  this.bomeditorService.editWirePluggings(element).subscribe()
                );
              } else {
                // updateAll === Undefined that mean user has cancelled the action
                this.getAllBomDataByJacketId(true);
              }
            }
          })
      );
    } else {
      // update directly as index is there and no repeating records on either side (USA / Vietnam)
      this.subscription.add(
        this.bomeditorService.editWirePluggings(element).subscribe()
      );
    }
  }

  onFocusCloneOldValue(updatedPropertyValue, propertyName: string) {
    this.oldPropertyValues[propertyName] = updatedPropertyValue;
  }

  confirmUpdate() {
    return new Promise(async (resolve) => {
      const matDataConfig = new MatDialogConfig();
      const oppositeType = this.type === Values.Countries.countryUsa ? Values.Countries.countryVietnam : Values.Countries.countryUsa;
      matDataConfig.data = {
        oppositeType: oppositeType,
        currentType: this.type,
      };
      matDataConfig.width = PopupSize.size.popup_md;
      matDataConfig.panelClass = 'sfl-update-confirm-model';
      const dialogRef = this.matDialog.open(
        PromptUpdateAllComponent,
        matDataConfig
      );
      await dialogRef
        .afterClosed()
        .toPromise()
        .then((res) => {
          resolve(res);
        });
    });
  }

  openCopyBOMDialog() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.data = {
      jacketId: this._JacketID,
      type: this.type,
      revisionId: this.newJackGroupName[0].revisionId,
    };
    this.matDialog
      .open(CopyBomComponent, matDataConfig)
      .afterClosed()
      .subscribe(() => {
      });
  }

  searchMfgPart() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {};
    matDataConfig.width = PopupSize.size.popup_xlg;
    matDataConfig.panelClass = 'sfl-search-mfg-part';
    const dialogRef = this.matDialog.open(SearchMfgPartComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(() => {
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
