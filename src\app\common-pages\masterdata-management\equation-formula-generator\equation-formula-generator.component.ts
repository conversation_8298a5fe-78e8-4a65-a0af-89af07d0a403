import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { Subscription } from 'rxjs';
import { Messages, SnakbarService } from 'src/app/shared';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MasterdataManagementService } from '../masterdata-management.service';

@Component({
  selector: 'sfl-equation-formula-generator',
  templateUrl: './equation-formula-generator.component.html'
})
export class EquationFormulaGeneratorComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  isLayerBtnEnabled: boolean;
  isOtherBtnEnabled: boolean;

  // Formula vars
  Formula = {
    CLOSURE_LENGTH: 'A',
    LEAD_LENGTH: 'B',
    SMALL_BAR: 'C',
    THERMOSTAT_COUNT: 'D',
    JUMPER_QTY: 'E',
    BIG_BAR: 'F',
    JACKET_LENGTH: 'G',
    JUMPER_LEAD_LENGTH: 'H',
    DOUBLE_BAR: 'I',
    TAPE_THREAD: 'Z',
    WELDMEN_LENGTH: 'J',
    WELDMEN_DIAMETER: 'K',
    SILICONE_WIDTH: 'N',
    REMOVABLE_LAYER: 'REMOVABLE_LAYER',
    REMOVABLE_QTY: 'P',
    LAYER: 'LAYER',
    ROUND_UP:'ROUND_UP',
    ROUND_DOWN:'ROUND_DOWN',
  };
  Numbers = {
    NUM_1: '1',
    NUM_2: '2',
    NUM_3: '3',
    NUM_4: '4',
    NUM_5: '5',
    NUM_6: '6',
    NUM_7: '7',
    NUM_8: '8',
    NUM_9: '9',
    NUM_0: '0',
    DEC: '.'
  };
  Operators = {
    PLUS: '+',
    MINUS: '-',
    MUL: '*',
    DIV: '/',
    POW: '^',
    SQRT: '2R'
  };
  Braces = {
    OP_SquareBraces: '[',
    CL_SquareBraces: ']',
    OP_RoundBraces: '(',
    CL_RoundBraces: ')'
  };

  expression = [];
  arrExpression = [];
  strExpression = '';

  allowAddFormula = false;

  constructor(
    public readonly dialogRef: MatDialogRef<EquationFormulaGeneratorComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly snakbarService: SnakbarService
  ) {
    this.strExpression = data.strFormula ? data.strFormula : '';
    this.arrExpression = data.formula ? data.formula.split('') : [];
    this.toggleButtonsOnInit();
    this.expression = this.strExpression ? this.masterDataService.equationFormatterFromFullNameToConst(this.strExpression).split('') : [];
    this.transformFormulaConstToFullName();
  }

  private toggleButtonsOnInit() {
    if (this.arrExpression.length === 0) {
      this.isLayerBtnEnabled = false;
      this.isOtherBtnEnabled = false;
    }
    else if (this.arrExpression.join('').includes(this.Formula.LAYER) || this.arrExpression.join('').includes(this.Formula.REMOVABLE_LAYER)) {
      this.isLayerBtnEnabled = true;
      this.isOtherBtnEnabled = false;
    }
    else {
      this.isLayerBtnEnabled = false;
      this.isOtherBtnEnabled = true;
    }
  }

  ngOnInit() {
    this.title = 'BHX Equation - Formula Generator';
  }

  saveFormula() {
    const response = {
      expression: this.expression,
      strExpression: this.strExpression,
      success: true
    };
    this.dialogRef.close(response);
  }

  addExpression(char) {
    this.expression.push(char);
    this.arrExpression.push(char);
    this.strExpression += char;
    this.allowAddFormula = false;
  }

  addExpressionVariable(char) {
    this.toggleVisiblityOfBtn(char);
    this.convertFormulaChars(char);
  }

  toggleVisiblityOfBtn(char: string) {
    if (char === this.Formula.LAYER || char === this.Formula.REMOVABLE_LAYER) {
      this.isLayerBtnEnabled = true;
    } else {
      this.isOtherBtnEnabled = true;
    }
  }

  convertFormulaChars(formulaChar) {
    let fullFormula = '';
    this.expression.push(formulaChar);
    switch (formulaChar) {
      case 'A':
        fullFormula = 'CLOSURE LENGTH';
        break;
      case 'B':
        fullFormula = 'LEAD LENGTH';
        break;
      case 'C':
        fullFormula = 'SMALL BAR';
        break;
      case 'D':
        fullFormula = 'THERMOSTAT COUNT';
        break;
      case 'E':
        fullFormula = 'JUMPER QTY';
        break;
      case 'F':
        fullFormula = 'BIG BAR';
        break;
      case 'G':
        fullFormula = 'JACKET LENGTH';
        break;
      case 'H':
        fullFormula = 'JUMPER LEAD LENGTH';
        break;
      case 'I':
        fullFormula = 'DOUBLE BAR';
        break;
      case 'LAYER':
        fullFormula = 'LAYER';
        break;
      case 'Z':
        fullFormula = 'TAPE THREAD';
        break;
      case 'J':
        fullFormula = 'WELDMENT LENGTH';
        break;
      case 'K':
        fullFormula = 'WELDMENT DIAMETER';
        break;
      case 'N':
        fullFormula = 'Silicone Width';
        break;
      case 'REMOVABLE_LAYER':
        fullFormula = 'REMOVABLE LAYER';
        break;
      case 'P':
        fullFormula = 'REMOVABLE QTY';
        break;
      case 'ROUND_DOWN':
        fullFormula = 'ROUND_DOWN';
        break;
      case 'ROUND_UP':
        fullFormula = 'ROUND_UP';
        break;
    }
    this.strExpression += fullFormula;
    this.arrExpression.push(fullFormula);
    this.allowAddFormula = false;
  }

  transformFormulaConstToFullName() {
    this.arrExpression.forEach((e, i) => {
      switch (e) {
        case 'A':
          this.arrExpression[i] = 'CLOSURE LENGTH';
          break;
        case 'B':
          this.arrExpression[i] = 'LEAD LENGTH';
          break;
        case 'C':
          this.arrExpression[i] = 'SMALL BAR';
          break;
        case 'D':
          this.arrExpression[i] = 'THERMOSTAT COUNT';
          break;
        case 'E':
          this.arrExpression[i] = 'JUMPER QTY';
          break;
        case 'F':
          this.arrExpression[i] = 'BIG BAR';
          break;
        case 'G':
          this.arrExpression[i] = 'JACKET LENGTH';
          break;
        case 'H':
          this.arrExpression[i] = 'JUMPER LEAD LENGTH';
          break;
        case 'I':
          this.arrExpression[i] = 'DOUBLE BAR';
          break;
        case 'LAYER':
          this.arrExpression[i] = 'LAYER';
          break;
        case 'Z':
          this.arrExpression[i] = 'TAPE THREAD';
          break;
        case 'J':
          this.arrExpression[i] = 'WELDMENT LENGTH';
          break;
        case 'K':
          this.arrExpression[i] = 'WELDMENT DIAMETER';
          break;
        case 'N':
          this.arrExpression[i] = 'Silicone Width';
          break;
        case 'REMOVABLE_LAYER':
          this.arrExpression[i] = 'REMOVABLE LAYER';
          break;
        case 'P':
          this.arrExpression[i] = 'REMOVABLE QTY';
          break;
        case 'ROUND_UP':
          this.arrExpression[i] = 'ROUND_UP';
          break;
        case 'ROUND_DOWN':
          this.arrExpression[i] = 'ROUND_DOWN';
          break;
      }
    });
  }


  backspace() {
    this.expression.pop();
    this.arrExpression.pop();
    this.strExpression = this.arrExpression.join('');
    this.allowAddFormula = false;
    if (!this.arrExpression.length) {
      this.isLayerBtnEnabled = false;
      this.isOtherBtnEnabled = false;
    }
  }
  clearExpression() {
    this.expression = [];
    this.arrExpression = [];
    this.strExpression = '';
    this.allowAddFormula = false;
    this.isLayerBtnEnabled = false;
    this.isOtherBtnEnabled = false;
  }
  validateExpression() {
    this.showLoader = true;
    const encodedEquation = this.fixedEncodeURIComponent(this.expression.join(''));

    this.masterDataService.equationValidator(encodedEquation).subscribe(
      (isValidExpression: boolean) => {
        if (isValidExpression) {
          this.allowAddFormula = true;
          this.snakbarService.success(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.FormulaValidatedSuccess);
          this.showLoader = false;
        } else {
          this.allowAddFormula = false;
          this.snakbarService.error(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.FormulaValidatedFailure);
          this.showLoader = false;
        }
      },
      error => {
        this.allowAddFormula = false;
        this.showLoader = false;
      }
    );
  }
  fixedEncodeURIComponent = equation => {
    return encodeURIComponent(equation).replace(/[!'()*]/g, c => {
      return '%' + c.charCodeAt(0).toString(16);
    });
  };

  closeDialog(): void {
    this.dialogRef.close(false);
  }
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
