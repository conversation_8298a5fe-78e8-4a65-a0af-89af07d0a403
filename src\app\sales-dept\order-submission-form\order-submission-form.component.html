<div class="container" fxLayout="column" xmlns="http://www.w3.org/1999/html">
  <div fxLayout="row wrap" class="mb-20 cust_fields">
    <div fxFlex fxLayoutAlign="start center">
      <mat-card-title>{{ headingTitle }}</mat-card-title>
    </div>
  </div>
  <form
    fxFlex
    fxLayout="column"
    [formGroup]="salesSubmissionForm"
    fxLayoutAlign="center center"
    fxLayoutGap="20px"
  >
    <div fxLayout="row wrap">
      <!---------------------------------------Form <PERSON>les-------------------------------------------------------->
      <div fxLayout="column" fxFlex="40">
        <div class="lable1_SO" style="margin-top: 20px">
          <h4>SO #:</h4>
        </div>
        <div class="lable_quote" style="margin-top: 16px">
          <h4>Quote #:</h4>
        </div>
        <div class="lable_project_name" style="margin-top: 16px">
          <h4>Project Name:</h4>
        </div>
        <div class="lable_customer" style="margin-top: 16px">
          <h4>Customer:</h4>
        </div>
        <div class="lable_ship_date" style="margin-top: 16px">
          <h4>Ship Date:</h4>
        </div>
        <div class="lable_dollar_amount" style="margin-top: 16px">
          <h4>Dollar Amount:</h4>
        </div>
        <div class="lable_dollar_amount" style="margin-top: 16px">
          <h4>No Of Designs:</h4>
        </div>
        <div class="lable_date" style="margin-top: 18px">
          <h4>Folder Submitted Date:</h4>
        </div>
        <div class="lable_sales_dropdown" style="margin-top: 14px">
          <h4>Sales Rep:</h4>
        </div>
        <div class="lable_app_engg" style="margin-top: 14px">
          <h4>App Engineer:</h4>
        </div>
        <div class="lable_app_engg" style="margin-top: 26px">
          <h4>SQT Link:</h4>
        </div>
        <div class="notes" style="margin-top: 37px">
          <h4>Notes:</h4>
        </div>
      </div>

      <!---------------------------------------Form Fields-------------------------------------------------------->
      <div fxLayout="column wrap" fxFlex="60" style="margin-bottom: 30px">
        <mat-form-field>
          <mat-label>SO</mat-label>
          <input
            matInput
            formControlName="salesOrderNumber"
            placeholder="Enter SO"
            [(ngModel)]="soNumber"
            (change)="checkIfSoNumberExist()"
            required
          />
        </mat-form-field>

        <mat-form-field>
          <mat-label>Quote</mat-label>
          <input
            matInput
            formControlName="quotationNumber"
            placeholder="Enter Quote"
            [(ngModel)]="quotationNumber"
            (change)="getQuotationNumber()"
            required
          />
        </mat-form-field>

        <mat-form-field>
          <mat-label>Project Name</mat-label>
          <input matInput formControlName="projectTitle" placeholder="Enter Project Name" required />
        </mat-form-field>

        <mat-form-field formGroupName="customerDTO">
          <mat-label>Customer</mat-label>
          <input matInput formControlName="name" formControlName="name" placeholder="Enter Customer" required />
        </mat-form-field>

        <mat-form-field>
          <mat-label>Ship Date</mat-label>
          <input matInput formControlName="shipDate" [matDatepicker]="shipDatePicker" placeholder="Select a Date" />
          <mat-datepicker-toggle matSuffix [for]="shipDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #shipDatePicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field>
          <mat-label>Dollar Amount</mat-label>
          <input matInput formControlName="dollarAmount" placeholder="Enter Dollar Amount" required />
        </mat-form-field>

        <mat-form-field>
          <mat-label>Number Of Designs</mat-label>
          <input matInput formControlName="customNoOfDesigns" placeholder="No of Designs" required />
        </mat-form-field>

        <mat-form-field>
          <mat-label>Folder Submitted Date</mat-label>
          <input
            matInput
            formControlName="folderSubmittedDate"
            [matDatepicker]="folderSubmittedDatePicker"
            placeholder="Select Folder Submitted Date"
            (dateChange)="setSubmittedToApp()"
            [(ngModel)]="folderSubmittedDate"
          />
          <mat-datepicker-toggle matSuffix [for]="folderSubmittedDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #folderSubmittedDatePicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field>
          <mat-label>Select Sales Representative</mat-label>
          <mat-select formControlName="salesAssociateId" placeholder="Select Sales Rep.">
            <mat-option *ngFor="let option of salesassociates" [value]="option.id">
              {{ option?.firstName + ' ' + option?.lastName }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field>
          <mat-label>Select App Engineer</mat-label>
          <mat-select formControlName="assignedAppEngineerId" placeholder="App Engg.">
            <mat-option *ngFor="let option of salesassociates" [value]="option.id">
              {{ option?.firstName + ' ' + option?.lastName }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field formGroupName="customerDTO">
          <mat-label>SQT Link</mat-label>
          <textarea matInput formControlName="sqtLink" placeholder="SQT Link"></textarea>
        </mat-form-field>

        <mat-form-field formGroupName="customerDTO">
          <mat-label>Notes</mat-label>
          <textarea matInput formControlName="notes" placeholder="Notes"></textarea>
        </mat-form-field>
      </div>

      <div fxLayout="column" fxFlex="100">
        <mat-dialog-content style="overflow: hidden;">
            <mat-checkbox color="warn">Send Me a Copy Of My Responses</mat-checkbox>
        </mat-dialog-content>
        <div fxLayout="row" fxFlex fxLayoutAlign="space-between" class="mt-10">
          <div fxLayoutAlign="start">
            <button mat-raised-button type="submit" (click)="saveSoFormDetails()" color="warn" style="margin-top: 40px;">Submit</button>
          </div>
          <div fxLayoutAlign="end">
            <button mat-raised-button (click)="clearFormFields()" class="order-cancel">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
