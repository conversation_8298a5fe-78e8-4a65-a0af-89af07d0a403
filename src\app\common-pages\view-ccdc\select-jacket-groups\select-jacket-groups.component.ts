import {Compo<PERSON>, Inject, <PERSON><PERSON><PERSON>roy, OnIni<PERSON>, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef, MatOption} from '@angular/material';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {JacketGroup} from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';

@Component({
  selector: 'sfl-select-jacket-groups',
  templateUrl: './select-jacket-groups.component.html'
})
export class SelectJacketGroupsComponent implements OnInit, OnDestroy {
  jacketGroupSelectionForm: FormGroup;
  jacketGroups: JacketGroup[] = [];
  slectedJacketGroupsIds: number[] = [];
  revisionId: number;
  title = 'Select the jacket group for printing.';
  @ViewChild('allSelected') private allSelected: MatOption;
  constructor(
    public readonly dialogRef: MatDialogRef<SelectJacketGroupsComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private formBuilder: FormBuilder
  ) {
    this.jacketGroups = data.jacketGroups;
    this.revisionId = data.revision;
  }

  // generates the form for jacket group on init
  ngOnInit() {
    this.jacketGroupSelectionForm = this.formBuilder.group({
      jacketGroupIds: new FormControl('', Validators.required)
    });
    this.defaultSelected();
  }

  defaultSelected(){
    this.allSelected.select();
    this.toggleAllSelection();
  }

  // user have selected the jacket group(s) to be send for printing
  sendToPrint() {
    // we will need to remove the jgId with - as that points to `All` in selection drop down
    this.slectedJacketGroupsIds = this.jacketGroupSelectionForm.controls.jacketGroupIds.value.filter(jg => jg !== 0);
    this.dialogRef.close(this.slectedJacketGroupsIds);
  }

  // when user selects only single checkbox from the drop down, it also checks all when user selects all the check boxes individually
  singleSelection() {
    if (this.allSelected.selected) {
      this.allSelected.deselect();
      return false;
    }
    if (this.jacketGroupSelectionForm.controls.jacketGroupIds.value.length === this.jacketGroups.length) {
      this.allSelected.select();
    }
  }
  // used to handle selection/ deselection of all available jacket groups from the drop down
  toggleAllSelection() {
    if (this.allSelected.selected) {
      this.jacketGroupSelectionForm.controls.jacketGroupIds.patchValue([...this.jacketGroups.map(item => item.id), 0]);
    } else {
      this.jacketGroupSelectionForm.controls.jacketGroupIds.patchValue([]);
    }
  }

  // closes the selection of jacket group dialog
  closeDialog() {
    this.dialogRef.close(this.slectedJacketGroupsIds);
  }

  ngOnDestroy() {}
}
