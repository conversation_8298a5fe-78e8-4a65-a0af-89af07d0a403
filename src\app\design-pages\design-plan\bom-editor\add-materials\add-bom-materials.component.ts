import { Component, OnInit, Input, On<PERSON><PERSON>roy, Inject } from "@angular/core";
import { Subscription, Observable } from "rxjs";
import { SnakbarService, Messages, Utils } from "src/app/shared";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material";
import { BOMEditorComponent } from "../bom-editor.component";
import { BomEditorService } from "../bom-editor.service";
import {
  OperationsMasterData,
  Label,
  ElementSensors,
  FacingLinerClosure,
  WirePlugging,
  JacketProductType,
  SensorsOnlyDTO,
  ElementOnlyDTO,
} from "../bom-editor.model";
import { FormControl } from "@angular/forms";
import { map, startWith } from "rxjs/operators";
import { MaterialDTO } from "../../final-review/final-review.model";
import { Values } from "src/app/shared/constants/values.constants";

@Component({
  selector: "sfl-add-bom-materials",
  templateUrl: "./add-bom-materials.component.html",
})
export class AddBOmMaterialsComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  jacketId: number;
  groupingId: number;
  selection: string;
  type: string;
  isElement: boolean;
  productType: string;
  masterDataList: OperationsMasterData[];
  masterDataDetails: OperationsMasterData;
  materialObservable$: Observable<OperationsMasterData[]>;
  materialControl = new FormControl();
  lastFilter = "";
  productTypeSilicone = Values.valueByProductTypeSilicone;
  productTypeCloth = Values.valueByProductTypeCloth;
  productTypeInseparable = Values.valueByProductTypeInseparable;
  constructor(
    public dialogRef: MatDialogRef<BOMEditorComponent>,
    private snakbarService: SnakbarService,
    private bomeditorService: BomEditorService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketId = data.jacketId;
    this.groupingId = data.groupingId;
    this.type = data.type;
  }

  ngOnInit() {
    this.masterDataDetails = new OperationsMasterData();
    this.getProductType();
  }

  filter(filter: string): OperationsMasterData[] {
    this.lastFilter = filter;
    if (filter) {
      return this.masterDataList.filter((option) => {
        if (option.partNumber !== null && option.description !== null) {
          return (
            option.partNumber.toLowerCase().indexOf(filter.toLowerCase()) >=
              0 ||
            option.description.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        } else if (option.partNumber !== null && option.description == null) {
          return (
            option.partNumber.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.masterDataList ? this.masterDataList.slice() : [];
    }
  }

  getAllLabels() {
    this.subscription.add(
      this.bomeditorService
        .getAllMaterialByGroupId(this.productType, this.groupingId, this.type)
        .subscribe((res: OperationsMasterData[]) => {
          if (res) {
            this.masterDataList = res;
            this.materialObservable$ = this.materialControl.valueChanges.pipe(
              startWith<string | OperationsMasterData[]>(""),
              map((value) =>
                typeof value === "string" ? value : this.lastFilter
              ),
              map((filter) => this.filter(filter))
            );
          }
        })
    );
  }

  // onSelectionChanges(index) {
  //   index !== 'other' ? (this.masterDataDetails = this.masterDataList[index]) : (this.masterDataDetails = {});
  // }

  onSelectionChanges(index) {
    if (index !== "other") {
      this.selection = "";
      this.masterDataDetails = index;
      this.materialControl.setValue(
        this.masterDataDetails.partNumber +
          ", " +
          this.masterDataDetails.description
      );
    } else {
      this.selection = "other";
      this.masterDataDetails = {};
    }
  }

  displayFn(value: MaterialDTO[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.partNumber + ", " + material.description;
        } else {
          displayValue +=
            ", " + material.partNumber + ", " + material.description;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  saveData() {
    this.masterDataDetails.jacketId = this.jacketId;
    this.masterDataDetails.id = null;
    this.masterDataDetails.type = this.type;
    switch (this.productType) {
      case this.productTypeCloth:
        this.masterDataDetails.relOperation = this.masterDataDetails.clothOpr;
        this.masterDataDetails.relOperation = this.masterDataDetails.relOpr;
        break;
      case this.productTypeSilicone:
        this.masterDataDetails.relOperation = this.masterDataDetails.siliconOpr;
        this.masterDataDetails.relOperation = this.masterDataDetails.relOpr;
        break;
      case this.productTypeInseparable:
        this.masterDataDetails.relOperation =
          this.masterDataDetails.inseparableOpr;
          this.masterDataDetails.relOperation = this.masterDataDetails.relOpr;
        break;
    }
    if (this.groupingId === 1) {
      this.subscription.add(
        this.bomeditorService
          .saveLabels(this.masterDataDetails)
          .subscribe((res: Label[]) => {
            if (res.length) {
              const result = { type: "labels", data: res };
              this.dialogRef.close(result);
            }
          })
      );
    } else if (this.groupingId === 2) {
      this.subscription.add(
        this.bomeditorService
          .saveSensor(this.masterDataDetails)
          .subscribe((res: ElementSensors) => {
            if (res) {
              const result = { type: "sensorsOnly", data: res };
              this.dialogRef.close(result);
            }
          })
      );
    } else if (this.groupingId === 6) {
      this.subscription.add(
        this.bomeditorService
          .saveElements(this.masterDataDetails)
          .subscribe((res: ElementOnlyDTO) => {
            if (res) {
              const result = { type: "elementOnly", data: res };
              this.dialogRef.close(result);
            }
          })
      );
    } else if (this.groupingId === 3) {
      this.subscription.add(
        this.bomeditorService
          .saveFacingLinerClosures(this.masterDataDetails)
          .subscribe((res: FacingLinerClosure) => {
            if (res) {
              const result = { type: "flc", data: res };
              this.dialogRef.close(result);
            }
          })
      );
    } else if (this.groupingId === 4) {
      this.subscription.add(
        this.bomeditorService
          .saveWirePluggings(this.masterDataDetails)
          .subscribe((res: WirePlugging) => {
            if (res) {
              const result = { type: "wp", data: res };
              this.dialogRef.close(result);
            }
          })
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  // used to get the product type of a jacket by jacket id
  getProductType() {
    this.subscription.add(
      this.bomeditorService
        .getProductTypeByJacketId(this.jacketId)
        .subscribe((typeObject: JacketProductType) => {
          this.productType = typeObject.productType;
          this.getAllLabels();
        })
    );
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
