

.bom-editor{
  border-width: 0;
  border-bottom-width: 1px;
  border-style: solid;
  border-bottom-color: rgba(0,0,0,.12)
}
.example-container {
  border-width: 0;
  border-style: solid;
}

.mat-table {
  overflow: auto;
  margin: 3px;
  max-height: 500px;
}

.column{
  /*background-color: #f1f1f1;*/

  margin-left: 5px;
  margin-right: 0px;
  padding-left: 0px;
  padding-right: 0px;
  border-bottom: 1px black;
}

.column-label{
  /*background-color: #f1f1f1;*/

  margin-left: 5px;
  margin-right: 0px;
  padding-left: 0px;
  padding-right: 0px;
  border-bottom: 1px black;
  width: 100px;
}
.column-label-width{
  /*background-color: #f1f1f1;*/

  margin-left: 5px;
  margin-right: 0px;
  padding-left: 0px;
  padding-right: 0px;
  border-bottom: 1px black;
  width: 50px;
}

.header{
  margin-left: 5px;
  margin-right: 0px;
  padding-left: 0px;
  padding-right: 0px;
  text-align: center;
  border: 10px;
  display: flex;
  font-size: 15px;
  color: black;
}
.header-label{
  margin-left: 5px;
  margin-right: 0px;
  padding-left: 0px;
  padding-right: 0px;
  text-align: center;
  border: 10px;
  display: flex;
  font-size: 15px;
  color: black;
  width: 100px;
}
.header-label-width{
  margin-left: 5px;
  margin-right: 0px;
  padding-left: 0px;
  padding-right: 0px;
  text-align: center;
  border: 10px;
  display: flex;
  font-size: 15px;
  color: black;
  width: 50px;
}
.desc-column{
  /*background-color: #f1f1f1;*/
  margin-left: 5px;
  margin-right: 25px;
  padding-left: 0px;
  padding-right: 25px;
}

.desc-header{
  margin-left: 5px;
  margin-right: 25px;
  padding-left: 0px;
  padding-right: 25px;
  text-align: center;
  border: 10px;
  display: flex;
  font-size: 15px;
  color: black;
}
.header-text{
  font-size: 18px;
  margin: 10px;
  padding: 5px;
}
.partNumColor{
  color: red;
}
