import { Component, OnInit, On<PERSON><PERSON>roy, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MatDialog, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { AddLeadTime } from '../finalize.model';
import { FinalizeService } from '../finalize.service';

@Component({
    selector: 'sfl-add-leadtime',
    templateUrl: './add-leadtime.component.html'
})

export class AddLeadtimeComponent implements OnInit, OnDestroy {

    quotationId: number;
    leadTime: AddLeadTime;
    time: number;
    subscription: Subscription = new Subscription();

    constructor(
        public dialogRef: MatDialogRef<AddLeadtimeComponent>,
        private matDialog: MatDialog,
        private finalizeService: FinalizeService,
        @Inject(MAT_DIALOG_DATA) data,
    ) {
        this.quotationId = +data.quotationId;
        this.time = data.leadTime;
    }

    ngOnInit() {
        this.leadTime = new AddLeadTime();
    }

    addLeadTime() {
        this.leadTime.quotationId = this.quotationId;
        this.leadTime.leadTime = +this.time;
        this.subscription.add(this.finalizeService.addLeadTime(this.leadTime).subscribe((res: AddLeadTime) => {
            if (res) {
                this.dialogRef.close(res);
            }
        }));
    }


    closeDialog() {
        this.dialogRef.close();
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
