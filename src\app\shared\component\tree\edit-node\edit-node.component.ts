import { Component, EventEmitter, Inject, Input, Output } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { TodoItemFlatNode } from '../../Part Number/open-file-path-edit-modal/open-file-path-edit-modal.component';
import { TreeData } from '../tree-data.model';

@Component({
  selector: 'sfl-edit-node',
  templateUrl: './edit-node.component.html',
})
export class EditNodeComponent {

  @Input() isTop: boolean;
  @Input() currentNode: TodoItemFlatNode;
  @Output() edittedNode = new EventEmitter;

  constructor(public dialog: MatDialog) { }

  openDialog(): void {
    const dialogRef = this.dialog.open(EditNodeDialog, {
      width: PopupSize.size.popup_md,
      data: { Name: this.currentNode.item, Component: 'Edit' }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const node = {
          Name: result.nodeName.trim(),
        };
        this.edittedNode.emit({ currentNode: this.currentNode, node: node });
      }
    });
  }
}



@Component({
  selector: 'sfl-edit-node-dialog',
  templateUrl: '../node-dialog/node-dialog.html',
})

export class EditNodeDialog {
  constructor(
    public dialogRef: MatDialogRef<EditNodeDialog>,
    @Inject(MAT_DIALOG_DATA) public data) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

}
