import { Utils } from './../../../shared/utils/utils';
import { DatePipe } from '@angular/common';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { JacketListComponent } from '../jacket-list.component';
import { Values } from './../../../shared/constants/values.constants';
import { CoverPageModel, CoverPagePqpFamilyDTO, CoverPageProductTypeDTO } from './cover-page.model';
import { CoverPageService } from './cover-page.service';

@Component({
    selector: 'sfl-cover-page',
    templateUrl: './cover-page.component.html'
})

export class CoverPageComponent implements OnInit, OnDestroy {
    quotationId: string;
    coverPageReviewMeetingDate: Date;
    coverPageMeetingDate: Date;

    pqpFamilyList: CoverPagePqpFamilyDTO[] = new Array();
    productTypeList: CoverPageProductTypeDTO[] = new Array();
    coverPage: CoverPageModel;
    anticipatedOptions: object = Values.AnticipatedValuesConstant;
    testingDeptOptions: object = Values.TestingDepartmentValuesConstant;

    products = new FormControl();
    families = new FormControl();
    subscription = new Subscription();

    constructor(
        public dialogRef: MatDialogRef<JacketListComponent>,
        private coverPageService: CoverPageService,
        private datePipe: DatePipe,
        @Inject(MAT_DIALOG_DATA) data
    ) {
        this.quotationId = data.quotationId;
    }

    ngOnInit() {
        this.coverPage = new CoverPageModel();
        this.coverPage.coverPageProductType = new Array();
        this.coverPage.coverPagePqpFamilies = new Array();
        this.getMasterData();
    }

    getCoverPageDataByQuotationId(quotationId) {
        this.subscription.add(this.coverPageService.getCoverPageDataByQuotationData(quotationId).subscribe((res: CoverPageModel) => {
            if (res) {
                this.coverPage = res;
                const selectedProducts = [];
                const selectedFamilies = [];
                if (this.coverPage.coverPageProductType) {
                    for (let i = 0; i < this.coverPage.coverPageProductType.length; i++) {
                        selectedProducts.push(this.coverPage.coverPageProductType[i].productTypeId);
                        this.products.setValue(selectedProducts);
                    }
                }
                if (this.coverPage.coverPagePqpFamilies) {
                    for (let i = 0; i < this.coverPage.coverPagePqpFamilies.length; i++) {
                        selectedFamilies.push(this.coverPage.coverPagePqpFamilies[i].pqpFamilyId);
                        this.families.setValue(selectedFamilies);
                    }
                }
                this.coverPageMeetingDate = Utils.converStringToDate(this.coverPage.meetingDate);
                this.coverPageReviewMeetingDate = Utils.converStringToDate(this.coverPage.reviewMeetingDate);
            }
        }));
    }

    getMasterData() {
        this.subscription.add(this.coverPageService.getMasterData().subscribe((res: any) => {
            if (res && res.ProductTypeList) {
                this.productTypeList = res.ProductTypeList;
            }
            this.getCoverPageDataByQuotationId(this.quotationId);
            if (res && res.PQPFamilyList) {
                this.pqpFamilyList = res.PQPFamilyList;
            }
        }));
    }

    saveCoverPage() {
        let productId;
        let familyId;
        if (this.products.value) {
            for (let i = 0; i < this.products.value.length; i++) {
                productId = this.products.value[i];
                this.coverPage.coverPageProductType[i] = { 'productTypeId': productId };
            }
        }
        if (this.families.value) {
            for (let i = 0; i < this.families.value.length; i++) {
                familyId = this.families.value[i];
                this.coverPage.coverPagePqpFamilies[i] = { 'pqpFamilyId': familyId };
            }
        }

        this.coverPage.quotationId = this.quotationId;
        this.coverPage.meetingDate = this.datePipe.transform(this.coverPageMeetingDate, Values.dateFormat.format);
        this.coverPage.reviewMeetingDate = this.datePipe.transform(this.coverPageReviewMeetingDate, Values.dateFormat.format);
        if (this.coverPage.id) {
            this.subscription.add(this.coverPageService.updateCoverPage(this.coverPage).subscribe(res => {
                this.closeDialog();
            }));
        } else {
            this.subscription.add(this.coverPageService.saveCoverPage(this.coverPage).subscribe(res => {
                if (res) {
                    this.closeDialog();
                }
            }));
        }

    }

    closeDialog(): void {
        this.dialogRef.close();
    }

    ngOnDestroy(): void {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
