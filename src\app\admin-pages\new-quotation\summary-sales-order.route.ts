import { Routes } from '@angular/router';
import {
    SummarySalesOrderComponent
} from './summary-sales-order.component';
import { AddApplicationComponent } from './Add Applicaton/add-application.component';
import { FindCustomerComponent } from './Find Customer/find-customer.component';
import { ManageJacketComponent } from './manage-jacketgroups/manage-jacket-groups.component';
import { ManageRevisionsComponent } from './manage-revisions/manage-revisions.component';
import { AddMaterialComponent } from './Add Material/add-material.component';
import { AddPluginComponent } from './Add Plugin/add-plugin.component';
import { AddSensorsComponent } from './Add Sensors/add-sensors.component';
import { AddNoteComponent } from './Add Note/add-note.component';
import { AddWorkflowComponent } from './Add Workflow/add-workflow.component';
import { PartNumberComponent } from '../../shared/component/Part Number/part-number.component';
import { AddAccessoriesComponent } from '../accessories/Add Accessories/add-accessories.component';
import { AddThermostatsComponent } from './Add Thermostats/add-thermostats.component';
import { AddClosureComponent } from './Add Closure/add-closure.component';
import { PowerCordComponent } from '../accessories/Power-Cord/power-cord.component';
import { PNErrorComponent } from 'src/app/shared/component/Part Number/partnumber-error.component';
import { ManageUnitsComponent } from './manage-units/manage-units.component';
import { AddOthrerThermostatsComponent } from './Add Thermostats/other-termostat/add-other-termostat.component';
import { OtherMaterialComponent } from './Add Material/Other/other-material.component';
import { SolidworksDownloadComponent } from 'src/app/shared/component/solidworks-download/solidworks-download.component';
import { AddLeadtimeComponent } from '../finalize/add-leadtime/add-leadtime.component';

export const SummarySalesOrderRoutes: Routes = [
    {
        path: '',
        component: SummarySalesOrderComponent,
    },
    {
        path: 'add-app',
        component: AddApplicationComponent
    },
    {
        path: 'find-cust',
        component: FindCustomerComponent
    },
    {
        path: 'manage-jacket',
        component: ManageJacketComponent
    },
    {
        path: 'manage-revision',
        component: ManageRevisionsComponent
    },
    {
        path: 'add-material',
        component: AddMaterialComponent
    },
    {
        path: 'add-plugin',
        component: AddPluginComponent
    },
    {
        path: 'add-sensor',
        component: AddSensorsComponent
    },
    {
        path: 'add-note',
        component: AddNoteComponent
    },
    {
        path: 'add-workflow',
        component: AddWorkflowComponent
    },
    {
        path: 'part-number',
        component: PartNumberComponent
    },
    {
        path: 'add-accessories',
        component: AddAccessoriesComponent
    },
    {
        path: 'add-thermostats',
        component: AddThermostatsComponent
    },
    {
        path: 'add-closure',
        component: AddClosureComponent
    }, {
        path: 'power-cord',
        component: PowerCordComponent
    },
    {
        path: 'pn-error',
        component: PNErrorComponent
    },
    {
        path: 'manage-units',
        component: ManageUnitsComponent
    },
    {
        path: 'add-other',
        component: AddOthrerThermostatsComponent
    },
    {
        path: 'other-matrial',
        component: OtherMaterialComponent
    },
    {
        path: 'solidworks-download',
        component: SolidworksDownloadComponent
    },
    {
        path: 'add-lead-time',
        component: AddLeadtimeComponent
    }
];
