<h2 mat-dialog-title>
  Send To Design
  <hr />
</h2>
<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
      <mat-select placeholder="Select Made In:" name="manufacturedIn" #selectedCountry>
        <mat-option *ngFor="let country of countries" [value]="country">
          {{ country | uppercase }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <div fxFlex.gt-lg="16" fxFlex.gt-md="16" fxFlex.gt-sm="16" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="expedite" [(ngModel)]="sendToDesignConfigurationDTO.expedite">Expedite</mat-checkbox>
    </div>
    <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="stimulationRequired" [(ngModel)]="sendToDesignConfigurationDTO.stimulationRequired"
        >Simulation Required</mat-checkbox
      >
    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button
      mat-raised-button
      type="button"
      [disabled]="!selectedCountry.value"
      (click)="setManufacturedIn(selectedCountry.value)"
      color="warn"
    >
      Submit
    </button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="button" (click)="closeDialog(null)">Cancel</button>
  </div>
</mat-dialog-actions>
