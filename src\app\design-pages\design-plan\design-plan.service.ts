
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from '../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../shared/helpers/app.helper';



@Injectable({ providedIn: 'root' })
export class DesignPlanService {

    constructor(private http: HttpClient) { }

    getGeometryJacketListByQuotationId(quotationId) {
        return this.http.get(AppConfig.GET_ALL_JACKET_LIST_BY_QID + quotationId).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }
}
