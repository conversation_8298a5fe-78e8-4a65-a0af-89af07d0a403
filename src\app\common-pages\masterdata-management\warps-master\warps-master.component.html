<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Min Tape Width</mat-label>
          <input matInput [(ngModel)]="warpFilter.minTapeWidth" (change)="addFilter()" sflIsDecimal autocomplete="off" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(minTapeWidth)"
            *ngIf="warpFilter.minTapeWidth"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Max Tape Width</mat-label>
          <input matInput [(ngModel)]="warpFilter.maxTapeWidth" (change)="addFilter()" sflIsDecimal autocomplete="off" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(maxTapeWidth)"
            *ngIf="warpFilter.maxTapeWidth"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addWarp()">Add New Warp</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="warpDataSource" (matSortChange)="getWarpMasterSorting($event)">
        <ng-container matColumnDef="tapeWidth">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Tape Width </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.tapeWidth }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="warp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="65"> Warp </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="65"> {{ element?.warp }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editWarp(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteWarp(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="warpColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: warpColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!warpDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getWarpMasterPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
