import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import 'rxjs/add/operator/catch';
import 'rxjs/add/operator/map';
import { AppConfig } from '../app.config';
import { utils } from '../shared/helpers';

@Injectable({
  providedIn: 'root'
})
export class SalesDeptService {

  constructor(private readonly http: HttpClient) { }

  getAllAccountManagers() {
    return this.http
      .get(AppConfig.ACCOUNT_MANAGERS)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  getSalesAssociate(isGlobalSearch: boolean) {
    return this.http
      .get(AppConfig.SalesAssociate + '/' + isGlobalSearch)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  checkSoNumber(soNumber)  {
    return this.http
      .get(AppConfig.CHECK_SO_NUMBER + soNumber + '/exists')
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  getSoDetails(soNumber) {
    return this.http
      .get(AppConfig.GET_SO_DETAILS + '/' + soNumber)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  getQuotation(quoteNumber) {
    return this.http
      .get(AppConfig.GET_RFQ_QUOTATION + '/' + quoteNumber)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  getSOQuotation(quotationNumber) {
    return this.http
      .get(AppConfig.GET_SO_QUOTATION + quotationNumber)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  saveRfqForm(rfqFormDTO) {
    return this.http.post(AppConfig.RFQ_FORM, rfqFormDTO).map(utils.extractData).catch(utils.handleError);
  }

  saveSOForm(salesOrderFormDTO) {
    return this.http.post(AppConfig.SO_FORM, salesOrderFormDTO).map(utils.extractData).catch(utils.handleError);
  }

  uploadDocument(formData: FormData) {
    return this.http
      .post(AppConfig.DOCUMENT_API, formData)
      .map(utils.extractData)
      .catch(utils.handleError);
  }

  removeListItemByIndex(listToUpdate, selectedItem) {
    listToUpdate.splice(listToUpdate.indexOf(selectedItem), 1);
    return listToUpdate;
  }
}
