import { SelectionModel } from '@angular/cdk/collections';
import { ChangeDetectorRef, Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { MatCheckboxChange, MatDialog, MatDialogConfig } from '@angular/material';
import { ActivatedRoute } from '@angular/router';
import { DragulaService } from 'ng2-dragula';
import { fromEvent, merge, Observable, Subscription, timer } from 'rxjs';
import { debounce, map } from 'rxjs/operators';
import { ElementService } from 'src/app/design-pages/design-plan/element/element.service';
import { TitleBlock, TitleBlockHighlighterDTO, TitleBlockSuccess } from 'src/app/design-pages/design-plan/title-block-editor/title-block-editor.model';
import { TitleBlockService } from 'src/app/design-pages/design-plan/title-block-editor/title-block-editor.service';
import { AddAttachmentComponent } from 'src/app/design-pages/ecr-management/add-attachment/add-attachment.component';
import { JacketListInfo } from 'src/app/design-pages/jacket-list/jacket-list.model';
import { Messages, SharedService, SnakbarService, SweetAlertService } from 'src/app/shared';
import { Units } from '../../../admin-pages/new-quotation/ccdc-model/ccdc.model';
import { ManageUnitsService } from '../../../admin-pages/new-quotation/manage-units/manage-units.service';
import { PopupSize } from '../../constants/popupsize.constants';
import { Values } from '../../constants/values.constants';
import { Variable } from '../../constants/Variable.constants';
import { AddJackets, AddNewJackets, Features, Fets, JacketGroup } from './geometry.model';
import { GemometryService } from './geometry.service';
import { MoveJacketFeatureComponent } from './move-jacket-feature/move-jacket-feature.component';
import { ViewFeatureImageComponent } from './view-feature-image/view-feature-image.component';
import { ViewJacketImageComponent } from './view-jacket-image/view-jacket-image.component';

@Component({
  selector: 'sfl-geometry',
  templateUrl: './geometry.component.html',
  styleUrls: ['./geometry.component.css'],
})
export class GeometryComponent implements OnInit, OnChanges, OnDestroy {
  fetList: Fets[];
  fetShorcuts = Values.GeometryFeaturesShortCuts;
  private _jacketGroups: JacketGroup[];
  feature: Features = new Features();
  geometryVCRValues = Values.geometryVCR;
  geometryWayValues = Values.geometryWay;
  selected = Values.geometryWay[1].name;
  subscription: Subscription = new Subscription();
  hideme = [];
  icontype: string;
  addjacket: AddNewJackets;
  jacketList: AddJackets;
  optJacketList: AddNewJackets[];
  updatedJacketList: AddNewJackets[];
  addNewJacketHolder: AddNewJackets[];
  jacketListFilter: AddNewJackets[];
  jacket: AddNewJackets[];
  updatedJacketId: number[] = [];
  jacketId: number;
  private _revisionId: number;
  diameter: number;
  isRevisionActive: boolean;
  isData: boolean;
  elbowLength: number;
  entryMethod: string;
  isDoNotAdd: boolean;
  private _JacketID: number;
  isDesignEng: boolean;
  jacketGroupList: JacketGroup[];
  jacketListInfo: JacketListInfo;
  quotationId: number;
  showLoader: boolean;
  saveAllJacekts: boolean;
  filterJG = 'All';
  measureUnit: string;
  visibility = false;
  _docList: boolean;
  featuresDiameter: number;
  showDash = false;
  tmpPartNumber: string;
  tmpSeqNumber: string;
  dragged: boolean;
  loadNextJacketBatch = true;
  oldWattsVal: number;
  jacketGroupId: number;
  jacketVCR: string;
  selection = new SelectionModel<AddNewJackets>(true, []);
  newFeatureId = [];
  isDisabledDelete: boolean;
  isDisabledMove: boolean;
  showNoJacket: boolean;
  imageUrl: string;
  formData = new FormData();
  imageFile: File;
  newImageUrl: any;
  hidden: boolean;
  jacketImageUrl: any;
  titleBlock: TitleBlock;

  get jacketGroups() {
    return this._jacketGroups;
  }

  @Input()
  set jacketGroups(val) {
    this._jacketGroups = val;
  }

  get revisionId() {
    return this._revisionId;
  }

  @Input()
  set revisionId(val) {
    this._revisionId = val;
    this.getJacketsByRevId();
  }

  get jacketID() {
    return this._JacketID;
  }

  @Input()
  set jacketID(val) {
    this._JacketID = val;
    this.getJacketByJacketId();
  }

  get isDocumentsListOpen() {
    return this._docList;
  }

  @Input()
  set isDocumentsListOpen(val) {
    this._docList = val;
  }

  get quotEntryMethod() {
    return this.entryMethod;
  }

  @Input()
  set quotEntryMethod(val) {
    this.entryMethod = val;
  }

  @Input() quotId;
  @Input() isEnable;

  pageSize = Variable.jacketsToLoad;
  maxIndex = Variable.maxIndex;
  pageNumber = Variable.pageNumber;
  eoj = Variable.endOfJackets;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter;
  totalNumberOfJackets = 0;
  totalPages = 0;
  titleBlockHighlighter: TitleBlockHighlighterDTO = new TitleBlockHighlighterDTO();

  constructor(
    private readonly matDialog: MatDialog,
    private readonly geometryService: GemometryService,
    private readonly elementService: ElementService,
    private readonly dragulaService: DragulaService,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly sharedService: SharedService,
    private readonly activatedRoute: ActivatedRoute,
    private manageUnitService: ManageUnitsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly titleBlockService: TitleBlockService,
  ) {
    this.dragulaService.createGroup('JACKETS', {
      moves: (el, source, handle) => handle.className === 'group-handle',
    });
    this.dragulaService.dragend('FEATURES').subscribe(() => {
      this.saveAllWork();
    });
  }

  ngOnInit() {
    this.addjacket = new AddNewJackets();
    this.jacketList = new AddJackets();
    this.optJacketList = new Array<AddNewJackets>();
    this.addNewJacketHolder = new Array<AddNewJackets>();
    this.getFeatures();
    this.feature = new Features();
    this.titleBlock = new TitleBlock();
    this.dragulaService.dropModel('FEATURES').subscribe(({ sourceModel, targetModel, item }) => {
      targetModel.find((feature) => feature.featuresId === item.featuresId && feature.id === item.id).jacketId = targetModel[0].jacketId;
    });

    this.dragulaService.drop('JACKETS').subscribe((dragModuleInstance: any) => {
      this.onDropJacket(dragModuleInstance);
    });
    this.dragulaService.dropModel('FEATURES').subscribe((dragModuleInstance: any) => {
      this.onDropJacketFeature(dragModuleInstance);
    });

    this.activatedRoute.queryParams.subscribe((params) => {
      this.quotationId = params['quotId'];
      if (params['jacketId']) {
        this.isRevisionActive = true;
        this.isDoNotAdd = false;
        this.isDesignEng = true;
        this.entryMethod = params['entryMethod'];
      } else {
        this.isDoNotAdd = true;
        this.isDesignEng = false;
      }
    });
    this.getEntryMethod();
    this.getMeasurementUnit();
    // this.autoSave();
    this.updatedJacketList = [];
    this.bindKeypressEvent().subscribe(($event: KeyboardEvent) => this.onKeyPress($event));
  }

  calculateFormula(item: any, fieldName: string) {
    try {
      let formula = item[fieldName];
      if (typeof formula === 'string') {
        if (formula.includes('π')) {
          formula = formula.replace('π', 'Math.PI');
        }
      }
      item[fieldName] = eval(formula);
    } catch (error) {
      item[fieldName] = 0;
      this.snakbarService.error('Invalid Formula, Please Enter Correct Formula');
    }
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnitByQuotationId(this.quotationId).subscribe((res: Units) => {
        if (res) {
          this.measureUnit = res.measurementUnit;
        }
      }),
    );
  }

  getEntryMethod() {
    this.subscription.add(
      this.geometryService.getEntryMethodByQuotation(this.quotationId).subscribe((res) => {
        if (res) {
          this.entryMethod = res.entryMethod;
        }
      }),
    );
  }

  private onDropJacket(dragModuleInstance) {
    this.dragged = true;
    this.saveAllWorkForDrag();
  }

  private onDropJacketFeature(dragModuleInstance) {
    for (let index = 0; index < this.optJacketList.length; index++) {
      if (this.optJacketList[index].id === dragModuleInstance.item.jacketId) {
        this.optJacketList[index].dirty = true;
      }
    }
    this.updatedJacketId.push(dragModuleInstance.item.jacketId);
  }

  ngOnChanges(changes: SimpleChanges) {
    // when switching between active and in active revision consider changes['isEnable'] and when we create a new revision consider changes['jacketGroups'], we 'll need to re-load jackets whenever we create a new revision.
    if ((changes['isEnable'] || changes['jacketGroups']) && !this.isDesignEng) {
      if (changes['jacketGroups'] !== undefined && changes['jacketGroups'].currentValue !== null) {
        this.pageNumber = 0;
        this.eoj = false;
        this.optJacketList = [];
        this.onScrollDown();
      }
    }
  }

  getInactiveJacketsByRevisionId() {
    this.subscription.add(
      this.geometryService.getJacketsByRevId(this._revisionId).subscribe(
        (res: AddNewJackets[]) => {
          if (res.length > 0) {
            this.optJacketList = res;
            this.jacketList.content = res;
            this.jacketListFilter = res;
            this.getRevisionActive();
            this.showHideDashSeperator();
            this.showLoader = false;
          }
        },
        (error) => {
          this.showLoader = false;
        },
      ),
    );
  }

  getAactiveJacketsByRevisionId(jacketGroupId, pageable) {
    this.subscription.add(
      this.geometryService.getJacketsByRevIdPageable(this._revisionId, jacketGroupId, pageable).subscribe(
        async (res: AddJackets) => {
          if (res.content.length > 0 && !this.eoj) {
            this.jacketList.content = res.content;
            this.jacketListFilter = res.content;
            if (this.optJacketList.length > 0) {
              this.maxIndex = this.optJacketList.length;
            } else {
              this.maxIndex = 0;
            }
            if (!this.isRevisionActive) {
              this.pageSize = res.totalElements;
            }
            await this.setUpOptJacketList();
            this.optJacketList.length === res.totalElements ? (this.eoj = true) : (this.eoj = false);
            this.totalNumberOfJackets = res.totalElements;
            this.totalPages = res.totalPages;
            this.getRevisionActive();
            this.showHideDashSeperator();
            await this.loadDynamicJacket();
            this.showLoader = false;
            this.showNoJacket = false;
          } else {
            this.optJacketList.length === res.totalElements ? (this.eoj = true) : (this.eoj = false);
            this.totalNumberOfJackets = res.totalElements;
            this.showHideDashSeperator();
            this.showLoader = false;
            this.showNoJacket = false;
          }
          this.cdr.detectChanges();
          this.showNoJacket = false;
          this.showLoader = false;
        },
        (error) => {
          this.showLoader = false;
        },
      ),
    );
  }

  setUpOptJacketList() {
    return new Promise<void>((resolve) => {
      let countPage = 0;
      for (let i = 0; i < this.pageSize; i++) {
        if (this.jacketList.content[i] !== undefined) {
          this.optJacketList.push(this.jacketList.content[i]);
          this.maxIndex++;
          this.showLoader = false;
          countPage++;
        } else {
          break;
        }
        if (countPage > 9) {
          countPage = 0;
          this.pageNumber++;
        }
      }
      this.showLoader = false;
      this.showNoJacket = false;
      resolve();
    });
  }

  loadDynamicJacket() {
    return new Promise<void>((resolve) => {
      if (this.loadNextJacketBatch) {
        if (this.pageNumber < this.totalPages - 1) {
          this.pageSize = Variable.jacketsToLoad;
          this.preLoadJackets();
          resolve();
        } else if (this.pageNumber === this.totalPages - 1) {
          this.preLoadJackets();
          resolve();
        }
      }
    });
  }

  // load jackets in buffer as soon as user request for first batch of jacket
  private async preLoadJackets() {
    this.loadNextJacketBatch = false;
    await this.jacketScroller();
  }

  async onScrollDown() {
    this.loadNextJacketBatch = true;
    await this.jacketScroller();
  }

  private jacketScroller() {
    return new Promise<void>((resolve) => {
      this.pageSize = Variable.jacketsToLoad;
      this.showLoader = true;
      const jacketGroupId = this.filterJG === 'All' ? 0 : this.filterJG;
      let pageable;
      pageable = { page: this.pageNumber, size: this.pageSize };
      if (!this.eoj) {
        if (this.isRevisionActive === true) {
          this.getAactiveJacketsByRevisionId(jacketGroupId, pageable);
        } else if (this.isRevisionActive === false) {
          this.getInactiveJacketsByRevisionId();
        } else {
          this.showLoader = false;
        }
        resolve();
      } else {
        this.showLoader = false;
        resolve();
      }
    });
  }

  createJacket() {
    // this will add new jacket to the end of the jacket list
    if (this.addNewJacketHolder !== undefined && this.addNewJacketHolder.length > 0) {
      this.optJacketList.push(...this.addNewJacketHolder);
      this.showHideDashSeperator();
      this.addNewJacketHolder = [];
      this.autoExpand();
    }
  }

  // autoSave() {
  //   this.subscription.add(
  //     observableInterval(300000).subscribe(val => {
  //       this.saveAllWork();
  //     })
  //   );
  // }

  viewHide() {
    if (this.visibility) {
      this.visibility = false;
      this.collapseAll();
    } else {
      this.visibility = true;
      this.expandAll();
    }
  }

  filterJacketGroup(values) {
    return new Promise((resolve) => {
      this.pageNumber = 0;
      this.eoj = false;
      this.optJacketList = [];
      this.jacketId = undefined;
      // this is to create Jacket according to the filtered jacket-group when add item is clicked.
      if (this.filterJG !== 'All') {
        this.sharedService.setJacketGroup(this.revisionId, values);
      }
      this.onScrollDown();
    });
  }

  getJacketByJacketId() {
    if (this.jacketID) {
      this.isData = false;
      this.optJacketList = [];
      this.subscription.add(
        this.geometryService.getJacketByJacketId(this.jacketID).subscribe((res: AddJackets[]) => {
          this.optJacketList.push(res);
          this.optJacketList.forEach((jacket) => (jacket.watts = Math.round(jacket.watts)));
          this.getid(this.jacketID);
          this._revisionId = this.optJacketList[0].revisionId;
          this.showHideDashSeperator();
        }),
      );
    }
  }

  expand(i) {
    this.hideme[i] = !this.hideme[i];
  }

  expandAll() {
    if (this.optJacketList) {
      for (let i = 0; i < this.optJacketList.length; i++) {
        this.hideme[i] = true;
      }
    }
  }

  autoExpand() {
    const i = this.optJacketList.length - 1;
    this.hideme[i] = true;
  }

  autoExpandFeature() {
    const i = this.optJacketList.length - 1;
    this.hideme[i] = true;
  }

  collapseAll() {
    for (let i = 0; i < this.optJacketList.length; i++) {
      this.hideme[i] = false;
    }
  }

  CopyJacket(jacket) {
    const objCopy = Object.assign({}, jacket);
    objCopy.oldId = objCopy.id;
    objCopy.id = null;
    if (objCopy.jacketFeaturesList.length > 0) {
      for (let i = 0; i < objCopy.jacketFeaturesList.length; i++) {
        objCopy.jacketFeaturesList[i].id = null;
        objCopy.jacketFeaturesList[i].jacketId = null;
        objCopy.jacketFeaturesList[i].featureIndex = i;
      }
    }
    this.subscription.add(
      this.geometryService.copyJacket(objCopy).subscribe((res: AddJackets) => {
        this.pageNumber = 0;
        this.eoj = false;
        this.optJacketList = [];
        this.getJacketsByRevId();
        this.onScrollDown();
      }),
    );
  }

  copyFeatures(feature, jacket) {
    const objCopyFeatures = Object.assign({}, feature);
    objCopyFeatures.id = null;
    jacket.jacketFeaturesList.splice(jacket.jacketFeaturesList.indexOf(feature) + 1, 0, objCopyFeatures);
    this.optJacketList.find((optJacket) => optJacket.id === jacket.id).dirty = true;
  }

  addJacket() {
    this.collapseAll();
    return new Promise<void>(async (resolve) => {
      this.addjacket.revisionId = this.revisionId;
      this.addjacket.discount = 0;
      this.addjacket.jacketGroupId = this.sharedService.getJacketGroup(this.revisionId);
      this.copyDescription(this.addjacket);
      this.subscription.add(
        this.geometryService.saveJacket(this.addjacket, this.revisionId).subscribe((res: AddNewJackets) => {
          this.addNewJacketHolder.push(res);
          this.jacketId = res.id;
          if (this.eoj) {
            this.createJacket();
            this.autoExpand();
            this.updatedJacketId.push(res.id);
          } else {
            this.addNewJacketHolder = [];
          }
        }),
      );
      resolve();
      this.saveAllWork();
    });
  }

  addFeatures(feats) {
    this.sharedService.currentFeatureForAutoFocus = 0;
    if (this.jacketId !== undefined) {
      this.createFeature(feats);
      this.fetchDefaultsGeometry(feats);
    }
  }

  private createFeature(feats) {
    this.feature = new Features();
    this.feature.jacketId = this.jacketId;
    this.feature.featuresId = feats.id;
    this.feature.featuresName = feats.name;
    const jacketObj = this.optJacketList.find((jacket) => jacket.id === this.jacketId).jacketFeaturesList;

    if (jacketObj.length >= 1) {
      const lastFet = jacketObj.slice(-1)[0];
      switch (lastFet.featuresName) {
        case 'Straight':
          this.featuresDiameter = lastFet.straightDiameter;
          break;
        case 'Flange':
          this.featuresDiameter = lastFet.flangeDiameterOut;
          break;
        case 'Unistrut':
          this.featuresDiameter = lastFet.unistrutPipeDiameter;
          break;
        case 'Elbow':
          this.featuresDiameter = lastFet.elbowDiameter;
          break;
        case 'Tee':
          this.featuresDiameter = lastFet.teeDiameterBranch1;
          break;
        case 'Reducer':
          this.featuresDiameter = lastFet.reducerDiameterOut;
          break;
        case 'Port':
          this.featuresDiameter = lastFet.pipeDiameter;
          break;
        case 'Valve':
          this.featuresDiameter = lastFet.valveDiameterOut;
          break;
        case 'Removable Section':
          this.featuresDiameter = lastFet.removableDiameter;
          break;
        case 'Removable VCR':
          this.featuresDiameter = lastFet.flangeDiameterOut;
          break;
      }
    }
  }

  private fetchDefaultsGeometry(feats) {
    const defaultGeometry = {
      quotationId: this.quotId ? this.quotId : this.quotationId,
      name: feats.name,
      value: this.featuresDiameter,
    };
    return new Promise<void>(async (resolve) => {
      this.subscription.add(
        this.geometryService.getDefaultsGeometry(defaultGeometry).subscribe((res: Features) => {
          if (res) {
            this.updateFeatureWithDefaults(feats, res);
            this.updateJacketList(feats);
            this.updateJacketIdList(this.feature.jacketId);
          }
        }),
      );
      resolve();
    });
  }

  private updateFeatureWithDefaults(feats, res) {
    switch (feats.name) {
      case 'Straight': {
        this.feature.straightDiameter = res.straightDiameter;
        break;
      }
      case 'Flange': {
        this.feature.flangeDiameter = res.flangeDiameter;
        this.feature.flangeDiameterIn = res.flangeDiameterIn;
        this.feature.flangeDiameterOut = res.flangeDiameterOut;
        this.feature.flangeWidth = res.flangeWidth;
        break;
      }
      case 'Unistrut': {
        this.feature.unistrutPipeDiameter = res.unistrutPipeDiameter;
        this.feature.unistrutWidth = res.unistrutWidth;
        this.feature.unistrutHeight = res.unistrutHeight;
        this.feature.unistrutDepth = res.unistrutDepth;
        break;
      }
      case 'Elbow': {
        this.feature.elbowDiameter = res.elbowDiameter;
        this.feature.elbowRadius = res.elbowRadius;
        this.feature.elbowCubed = res.elbowCubed;
        break;
      }
      case 'Tee': {
        this.feature.teeDiameterIn = res.teeDiameterIn;
        this.feature.teeCubed = res.teeCubed;
        break;
      }
      case 'Reducer': {
        this.feature.reducerDiameterIn = res.reducerDiameterIn;
        break;
      }
      case 'Port': {
        this.feature.pipeDiameter = res.pipeDiameter;
        break;
      }
      case 'Valve': {
        this.feature.valveDiameterIn = res.valveDiameterIn;
        this.feature.valveDiameterOut = res.valveDiameterOut;
        break;
      }
      case 'Removable Section': {
        this.feature.removableDiameter = res.removableDiameter;
        break;
      }
      case 'Removable VCR': {
        this.feature.flangeDiameter = res.flangeDiameter;
        this.feature.flangeDiameterIn = res.flangeDiameterIn;
        this.feature.flangeDiameterOut = res.flangeDiameterOut;
        this.feature.flangeWidth = res.flangeWidth;
        break;
      }
      case 'Other': {
        this.feature.otherDiameter = res.otherDiameter;
        break;
      }
    }
  }

  private updateJacketList(feats) {
    this.optJacketList.find((jacket) => jacket.id === this.jacketId).jacketFeaturesList.push(this.feature);
    this.optJacketList.find((jacket) => jacket.id === this.jacketId).dirty = true;
    const currJAcket = this.optJacketList.find((jacket) => jacket.id === this.jacketId);
    this.jacketLength(currJAcket);
    this.setOptJacketIndexes();
    this.updateJacketIdList;
  }

  changeJacketGroup(jacketGroupId: number, jId: number) {
    this.sharedService.setJacketGroup(this.revisionId, jacketGroupId);
    this.optJacketList.forEach((jacket) => {
      if (jacket.id === jId) {
        jacket.jacketGroupChanged = true;
        jacket.dirty = true;
      }
    });
    this.updatedJacketId.push(jId);
  }

  getFeatures() {
    this.subscription.add(
      this.geometryService.getFeatures().subscribe((res: Fets[]) => {
        this.fetList = res;
        this.fetList.forEach((fet, i) => {
          fet.shortCut = this.fetShorcuts[i];
        });
      }),
    );
  }

  getJacketsByRevId() {
    this.getRevisionActive();
  }

  getRevisionActive() {
    this.sharedService.isCurrentRevisionActive.subscribe((res) => {
      this.isRevisionActive = res;
      if (!this.isRevisionActive) {
        this.expandAll();
      }
    });
  }

  saveAllWorkForDrag() {
    if (this.optJacketList.length > 0) {
      return new Promise<void>(async (resolve) => {
        if (this.isRevisionActive) {
          this.saveAllJacekts = true;
          this.setOptJacketIndexes();
          this.checkIfJacketDragged();
          await this.saveUpdatedJackets(true);
          resolve();
        } else {
          resolve();
        }
      });
    }
  }

  saveAllWork() {
    if (this.optJacketList.length > 0) {
      return new Promise<void>(async (resolve) => {
        if (this.isRevisionActive) {
          this.saveAllJacekts = true;
          this.setOptJacketIndexes();
          this.checkIfJacketDragged();
          await this.saveUpdatedJackets(false);
          resolve();
        } else {
          resolve();
        }
      });
    }
  }

  setOptJacketIndexes() {
    for (let i = 0; i < this.optJacketList.length; i++) {
      if (this.quotId) {
        this.optJacketList[i].jacketIndex = i;
      }
      if (this.dragged) {
        this.optJacketList[i].dirty = true;
      }
      for (let j = 0; j < this.optJacketList[i].jacketFeaturesList.length; j++) {
        this.optJacketList[i].jacketFeaturesList[j].featureIndex = j;
      }
    }
  }

  checkIfJacketDragged() {
    this.updatedJacketList = this.optJacketList;
  }

  saveUpdatedJackets(dragged: boolean) {
    this.showLoader = true;
    let entry: boolean;
    this.entryMethod === 'centerLine' ? (entry = true) : (entry = false);
    return new Promise<void>((resolve, reject) => {
      let tempJacketList: AddNewJackets[] = [];
      const uniqueIds: number[] = Array.from(new Set(this.updatedJacketId));
      if (!dragged) {
        tempJacketList = uniqueIds.map((data) => this.updatedJacketList.find((x) => x.id === data));
      } else {
        tempJacketList = this.updatedJacketList;
      }

      const jacketObj = {
        centerLine: entry,
        revisionId: this._revisionId,
        jacketList: tempJacketList,
      };
      this.subscription.add(
        this.geometryService.saveAllJackets(jacketObj).subscribe(
          (updatedJackets: AddNewJackets[]) => {
            // set the updated lengths/ other values which are being calculated from backend while jackets were saved
            this.optJacketList.forEach(function (optJacket) {
              const result = updatedJackets.filter((updated) => updated.id === optJacket.id);
              if (result.length > 0) {
                optJacket = Object.assign(optJacket, result[0]);
              }
              return optJacket;
            });
            // setting the flag back to dirty false as we don't need to send it over again
            this.optJacketList.forEach((jacket) => {
              jacket.dirty = true;
              jacket.watts = Math.round(jacket.watts);
            });
            this.updatedJacketList = [];
            this.dragged = false;
            this.saveAllJacekts = false;
            this.showLoader = false;
            resolve();
          },
          (error) => {
            this.optJacketList.forEach((jacket) => {
              jacket.dirty = true;
            });
            this.updatedJacketList = [];
            this.dragged = false;
            this.showLoader = false;
            this.saveAllJacekts = false;
            reject();
          },
        ),
      );
      this.saveAllJacekts = false;
      this.updatedJacketList = [];
      this.dragged = false;
      this.updatedJacketId = [];
      resolve();
    });
  }

  viewImage(viewFeatureImage: string) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.data = { imageUrl: viewFeatureImage };
    this.matDialog.open(ViewFeatureImageComponent, matDataConfig);
  }

  viewJacketImage(viewFeatureImage: string) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.data = { imageUrl: viewFeatureImage };
    this.matDialog.open(ViewJacketImageComponent, matDataConfig);
  }

  addAttachment(jacketFeatureId) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-upload-material-file-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      this.subscription.add(
        this.geometryService.uploadImageGeometry(uploadedAttachment, jacketFeatureId).subscribe((res) => {
          if (res) {
            this.feature.imageUrl = res;
            this.getJacketByJacketId();
            this.showLoader = false;
            this.snakbarService.success(Messages.JACKET_IMAGE_UPLOAD.image_upload_successfully);
          }
        }),
      );
    });
  }

  addJacketAttachment(jacketId) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-upload-material-file-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    // const jacketGroupId = this.filterJG === 'All' ? 0 : this.filterJG;
    // let pageable;
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      this.subscription.add(
        this.geometryService.uploadJacketImageGeometry(uploadedAttachment, jacketId).subscribe((res) => {
          if (res) {
            this.getJacketByJacketId();
            // this.getAactiveJacketsByRevisionId(jacketGroupId, pageable);
            this.showLoader = false;
            this.snakbarService.success(Messages.JACKET_IMAGE_UPLOAD.image_upload_successfully);
          }
        }),
      );
    });
  }

  copyDescription(jacket: AddNewJackets) {
    // when we select 'All' from the geometry screen as jacket group filter
    if (this.filterJG === 'All' && this.optJacketList.length) {
      this.addjacket.reference = this.optJacketList[this.optJacketList.length - 1].reference; // We copy the second last jacket's description into new Jacket description.
    } else {
      // here copy the jacket's description according to the selected jacket-group.
      const jacketsByGroup = this.optJacketList.filter((filterJacket) => filterJacket.jacketGroupId === jacket.jacketGroupId);
      this.addjacket.reference = jacketsByGroup.length ? jacketsByGroup[jacketsByGroup.length - 1].reference : '';
    }
  }

  async confirmDelete(jacketId) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.deleteAppFeatures(jacketId);
    }
  }

  async deleteFeatures(jacket) {
    this.showLoader = true;
    if (await this.sweetAlertService.deleteAlert()) {
      this.subscription.add(
        this.geometryService.deleteGeoJacketFeatures(jacket.id).subscribe((res: Features[]) => {
          this.optJacketList[0].jacketFeaturesList = [];
          this.snakbarService.success(Messages.FEATURE.features_deleted);
          this.saveAllWork();
          this.showLoader = false;
        }),
      );
    }
  }

  async deleteJacket(jacket) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.subscription.add(
        this.geometryService.deleteGeoJacket(jacket.id).subscribe((res) => {
          this.optJacketList.splice(this.optJacketList.indexOf(jacket), 1);
          this.saveAllWork();
        }),
      );
    }
  }

  async deleteAppFeatures(jacketId) {
    this.showLoader = true;
    this.subscription.add(
      this.geometryService.deleteGeoJacketFeatures(jacketId).subscribe((res) => {
        for (let i = 0; i < this.optJacketList.length; i++) {
          if (this.optJacketList[i].id === jacketId) {
            this.optJacketList[i].jacketFeaturesList = [];
            // this.saveAllWork();
          }
        }
        this.snakbarService.success(Messages.FEATURE.features_deleted);
        this.showLoader = false;
      }),
    );
  }

  async deleteJacketFeature(jacketFeature, jacket) {
    if (await this.sweetAlertService.deleteAlert()) {
      let entry: boolean;
      this.entryMethod === 'centerLine' ? (entry = true) : (entry = false);
      if (!jacketFeature.id) {
        jacket.jacketFeaturesList.splice(jacket.jacketFeaturesList.indexOf(jacketFeature), 1);
      } else {
        this.saveAllJacekts = true;
        this.subscription.add(
          this.geometryService.deleteJacketFeature(jacket, jacketFeature.id, entry).subscribe(
            (res) => {
              jacket.jacketFeaturesList.splice(jacket.jacketFeaturesList.indexOf(jacketFeature), 1);
              this.jacketLength(jacket);
              this.saveAllJacekts = false;
              this.titleBlockHighlighter.highlightedGeometry.jacketDiams = true;
              // call the api to update the title block highlighter fields
              this.titleBlockHighlighter.jacketIds = [jacket.id];
              this.subscription.add(
                this.sharedService.highlightTitleBlockFields(this.titleBlockHighlighter).subscribe(
                  () => {
                    this.titleBlockHighlighter = new TitleBlockHighlighterDTO();
                    this.showLoader = false;
                  },
                  () => {
                    this.showLoader = false;
                  },
                ),
              );
            },
            (error) => {
              this.saveAllJacekts = false;
              if (error.applicationStatusCode === 3001) {
                this.snakbarService.error(error.message);
              }
            },
          ),
        );
      }
    }
  }

  onSelectedFeature(event: MatCheckboxChange, row) {
    if (event.checked) {
      this.isDisabledDelete = true;
      this.newFeatureId.push(row.id);
    } else {
      this.isDisabledDelete = false;
      this.newFeatureId.splice(this.newFeatureId.indexOf(row.id), 1);
    }
  }

  onSelectMoveFeatures(event: MatCheckboxChange, row) {
    if (event.checked) {
      this.isDisabledDelete = true;
      this.newFeatureId.push(row.id);
    } else {
      this.isDisabledDelete = false;
      this.newFeatureId.splice(this.newFeatureId.indexOf(row.id), 1);
    }
  }

  moveSelectedFeatures() {
    this.showNoJacket = true;
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = {
      newFeatureId: this.newFeatureId,
      _revisionId: this._revisionId,
      jacketID: this.jacketId,
      jacketGroupId: this.optJacketList[0].jacketGroupId,
      discount: this.optJacketList[0].discount,
      reference: this.optJacketList[0].reference,
    };
    matDataConfig.width = PopupSize.size.popup_md;
    const dialogRef = this.matDialog.open(MoveJacketFeatureComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((res) => {
      this.pageNumber = 0;
      this.eoj = false;
      this.optJacketList = [];
      const jacketGroupId = this.filterJG === 'All' ? 0 : this.filterJG;
      let pageable;
      pageable = { page: this.pageNumber, size: this.pageSize };
      this.getAactiveJacketsByRevisionId(jacketGroupId, pageable);
      this.cdr.detectChanges();
    });
  }

  async confirmDeleteFeatures() {
    if (await this.sweetAlertService.deleteAllFeaturesAlert()) {
      this.deleteSelectedFeature();
    }
  }

  async deleteSelectedFeature() {
    this.showLoader = true;
    this.showNoJacket = true;
    this.subscription.add(
      this.geometryService.deleteSelectedFeatures(this.newFeatureId).subscribe(
        (res) => {
          this.pageNumber = 0;
          const jacketGroupId = this.filterJG === 'All' ? 0 : this.filterJG;
          let pageable;
          pageable = { page: this.pageNumber, size: this.pageSize };
          this.getAactiveJacketsByRevisionId(jacketGroupId, pageable);
          this.eoj = false;
          this.optJacketList = [];
          this.selection.clear();
          this.snakbarService.success(Messages.JACKET_DELETE.features_delete);
        },
        (error) => {
          this.showNoJacket = false;
          this.showLoader = false;
          this.selection.clear();
        },
      ),
    );
  }

  getid(id) {
    this.jacketId = id;
  }

  diameterStoreInLocalStorage(diameter: any) {
    this.sharedService.setDiameter(this.revisionId, diameter);
  }

  calculateElbowLength(feature) {
    this.calculateFormula(feature, 'elbowAngle');
    this.calculateFormula(feature, 'elbowLength');
    this.calculateFormula(feature, 'elbowDiameter');
    this.calculateFormula(feature, 'elbowRadius');
    if (this.entryMethod === 'centerLine') {
      feature.elbowLength = 0;
    } else {
      const elbowJson = {
        angle: feature.elbowAngle,
        diam: feature.elbowDiameter,
        radius: feature.elbowRadius,
      };
      if (elbowJson.angle !== undefined && elbowJson.diam !== undefined && elbowJson.radius !== undefined) {
        const jacketGroupId = this.optJacketList.find((x) => x.id === feature.jacketId).jacketGroupId;
        this.subscription.add(
          this.geometryService.elbowLengthCalculator(jacketGroupId, elbowJson).subscribe((res) => {
            feature.elbowLength = res as number;
          }),
        );
      }
    }
  }

  jacketLength(jacket: AddNewJackets) {
    let entry: boolean;
    this.entryMethod === 'centerLine' ? (entry = true) : (entry = false);
    this.saveAllJacekts = true;
    this.subscription.add(
      this.geometryService.jacketLengthCalculator(jacket, entry).subscribe(
        (res: number) => {
          jacket.length = res;
          this.saveAllJacekts = false;
        },
        (error) => {
          this.saveAllJacekts = false;
        },
      ),
    );
  }

  setTempSequenceNumber(seqNumber) {
    this.tmpSeqNumber = seqNumber;
  }

  setTempPartNumber(tempPartNumber) {
    this.tmpPartNumber = tempPartNumber;
  }

  validatePartNumber(jacketIndex) {
    if (!this.optJacketList[jacketIndex].tempPartNumber.match(Values.PartNumber_Regex)) {
      this.snakbarService.error(Messages.error.part_error_msg, 5000);
      this.optJacketList[jacketIndex].tempPartNumber = this.tmpPartNumber;
      this.setPartSeqTextFieldWidth(this.optJacketList[jacketIndex]);
    } else {
      this.optJacketList[jacketIndex].dirty = true;
    }
    this.updatedJacketId.push(this.optJacketList[jacketIndex].id);
  }

  validateSequenceNumber(jacketIndex) {
    if (!this.optJacketList[jacketIndex].tempSequenceNumber.match(Values.SequenceNumber_Regex)) {
      this.snakbarService.error(Messages.error.part_error_msg, 5000);
      this.optJacketList[jacketIndex].tempSequenceNumber = this.tmpSeqNumber;
    } else {
      this.optJacketList[jacketIndex].dirty = true;
    }
  }

  showHideDashSeperator() {
    if (this.optJacketList.length > 0) {
      this.optJacketList.forEach((jacket) => {
        jacket.fieldWidth = '50%';
        if (jacket.briskHeatPN) {
          jacket.briskHeatPN.includes('-') ? (jacket.showDash = true) : (jacket.showDash = false);
          jacket.fieldWidth = this.setPartSeqTextFieldWidth(jacket);
        }
      });
    }
  }

  setPartSeqTextFieldWidth(jacket: AddNewJackets): string {
    jacket.fieldWidth = '50%';
    if (jacket.tempPartNumber) {
      jacket.fieldWidth = (jacket.tempPartNumber.length + 1) * 8 + 'px';
    }
    return jacket.fieldWidth;
  }

  isAnyElementSelected(jacketId: number) {
    return new Promise((resolve) => {
      this.elementService.isAnyElementSelectedForJacket(jacketId).subscribe((res: boolean) => {
        resolve(res);
      });
    });
  }

  copyOldWattsValue(watts: number) {
    this.oldWattsVal = watts;
  }

  async checkAndSetUpdatedJacket(jacketId: number, fieldName = '') {
    let isAnyElementSelected = false;
    await this.isAnyElementSelected(jacketId).then((val: boolean) => {
      isAnyElementSelected = val;
    });

    if (!isAnyElementSelected) {
      this.setUpdatedJacket(jacketId, fieldName);
      this.saveAllWork();
    } else if (isAnyElementSelected && (await this.sweetAlertService.warningGeometryWattsUpdate())) {
      this.setUpdatedJacket(jacketId, fieldName);
      this.saveAllWork();
      this.clearSelectedElements(jacketId);
    } else {
      this.optJacketList.forEach((jacket) => {
        if (jacket.id === jacketId) {
          jacket.watts = this.oldWattsVal;
        }
      });
    }
    this.updatedJacketId.push(jacketId);
  }

  updateJacketIdList(jacketId: number) {
    this.updatedJacketId.push(jacketId);
  }

  clearSelectedElements(jacketId: number) {
    this.showLoader = true;
    this.subscription.add(
      this.elementService.deleteSelectedElementsByJacketId(jacketId).subscribe(
        () => {
          this.showLoader = false;
        },
        (err) => {
          this.showLoader = false;
        },
      ),
    );
  }

  async setUpdatedJacket(jacketId: number, fieldName: string = '') {
    try {
      // Step 2: Highlight title block fields based on field name
      if (fieldName === 'wattage') {
        this.titleBlockHighlighter.highlightedCommon.watts = true;
        this.titleBlockHighlighter.highlightedCommon.amps = true;
      } else if (fieldName === 'dims') {
        this.titleBlockHighlighter.highlightedGeometry.jacketDiams = true;
      }

      // Step 3: Set jacket IDs and call API to highlight title block fields
      this.titleBlockHighlighter.jacketIds = [jacketId];
      await this.highlightTitleBlockFields();

      this.showLoader = false;
      this.updatedJacketId.push(jacketId);
    } catch (error) {
      console.error('Error updating jacket:', error);
      this.showLoader = false;
    }
  }

  private async highlightTitleBlockFields() {
    return new Promise<void>((resolve, reject) => {
      this.subscription.add(
        this.sharedService.highlightTitleBlockFields(this.titleBlockHighlighter).subscribe(
          () => {
            resolve();
          },
          (error) => {
            reject(error);
          },
        ),
      );
    });
  }

  openDoc(imageUrl) {
    window.open(imageUrl, '_target');
  }

  onKeyPress($event: KeyboardEvent) {
    if ($event.altKey || $event.metaKey) {
      switch ($event.keyCode) {
        case 49: {
          this.addFeatures(this.fetList[0]);
          break;
        }
        case 50: {
          this.addFeatures(this.fetList[1]);
          break;
        }
        case 51: {
          this.addFeatures(this.fetList[2]);
          break;
        }
        case 52: {
          this.addFeatures(this.fetList[3]);
          break;
        }
        case 53: {
          this.addFeatures(this.fetList[4]);
          break;
        }
        case 54: {
          this.addFeatures(this.fetList[5]);
          break;
        }
        case 55: {
          this.addFeatures(this.fetList[6]);
          break;
        }
        case 56: {
          this.addFeatures(this.fetList[7]);
          break;
        }
        case 57: {
          this.addFeatures(this.fetList[8]);
          break;
        }
        case 65: {
          this.addFeatures(this.fetList[9]);
          break;
        }
        case 83: {
          this.addFeatures(this.fetList[10]);
          break;
        }
        case 71: {
          this.addFeatures(this.fetList[11]);
          break;
        }
      }
    }
  }

  private bindKeypressEvent(): Observable<KeyboardEvent> {
    const eventsType$ = [fromEvent(window, 'keypress'), fromEvent(window, 'keydown')];
    // we merge all kind of event as one observable.
    return merge(...eventsType$).pipe(
      // We prevent multiple next by wait 10ms before to next value.
      debounce(() => timer(10)),
      // We map answer to KeyboardEvent, typescript strong typing...
      map((state) => state as KeyboardEvent),
    );
  }

  fileTransform(value: string) {
    let userLocation: string = this.sharedService.getUsersCountry();
    if (userLocation && value) {
      if (userLocation.toLowerCase() === 'usa') {
        if (value.includes('************')) {
          value = value.replace('************\\PUBLIC\\ENGINEERING', '**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering');
        }
      } else {
        if (value.includes('**********')) {
          value = value.replace('**********\\2311Briskheat_Common_(1)\\Public\\01 Engineering', '************\\PUBLIC\\ENGINEERING');
        }
      }
      return value;
    } else {
      return value;
    }
  }

  private getMatchingSWBlockForDrawing(userCountry: string) {
    return new Promise<string[]>((resolve, reject) => {
      this.subscription.add(
        this.geometryService.getMatchingSWBlockPartFilesLocationByJacketIdAndUserCountry(this.optJacketList[0].id, userCountry).subscribe((res: string[]) => {
          if (res) {
            resolve(res);
          }
        }),
      );
    });
  }

  async drawJacketDesign(isJacketModel: boolean) {
    this.showLoader = true;
    this.optJacketList[0].dxfRequest = false;
    let userCountry = this.sharedService.getUsersCountry();
    let matchingSolidWorksBlocks: string[] = [];
    matchingSolidWorksBlocks = await this.getMatchingSWBlockForDrawing(userCountry);
    //api call here
    this.subscription.add(
      this.geometryService.drawJacketDesign(this.optJacketList[0], matchingSolidWorksBlocks, isJacketModel).subscribe(
        (res) => {
          this.getTitleBlock(isJacketModel);
          this.showLoader = false;
        },
        (error) => {
          if (error) {
            this.snakbarService.error(error)
          } else {
            this.snakbarService.error(Messages.Generate_File.file_generation_fail.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part))
          }
          this.showLoader = false;
        },
      ),
    );
  }

  getTitleBlock(isJacketModel: boolean) {
    this.subscription.add(
      this.titleBlockService.getTitleBlock(this._JacketID).subscribe(
        (res: TitleBlock) => {
          if (res) {
            this.titleBlock = res;
            this.titleBlock.oldRevision = this.titleBlock.revision;
            this.titleBlock.oldPartNumber = this.titleBlock.partNumber;
            this.titleBlock.pick = 'PICKS';
            this.titleBlock.warp = 'WARPS';
            this.updateTitleBlock(isJacketModel);
          }
        },
        (error) => {
          this.snakbarService.error(Messages.Generate_File.fail_both.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part));
        },
      ),
    );
  }

  // used to update the changes done in the Title Block Screen to the Solid works design and to BHX service
  updateTitleBlock(isJacketModel: boolean) {
    this.showLoader = true;
    this.titleBlock.partFilePath = this.fileTransform(this.titleBlock.partFilePath);
    this.subscription.add(
      this.titleBlockService.updateTitleBlock(this.titleBlock).subscribe(
        (res: TitleBlockSuccess) => {
          if (res.success) {
            this.showLoader = false;
            this.updateTitleBlockBHX(res.data, true, isJacketModel);
          } else {
            this.showLoader = false;
            this.updateTitleBlockBHX(this.titleBlock, false, isJacketModel, true, res.message ? res.message : null);
          }
        },
        (error) => {
          this.showLoader = false;
          this.updateTitleBlockBHX(this.titleBlock, false, isJacketModel, false);
        },
      ),
    );
  }

  updateTitleBlockBHX(data, isDrawingSuccess: boolean, isJacketModel: boolean, isDrawingFailedWithResMessage?: boolean, drawingFailedMessage?: string) {
    this.showLoader = true;
    this.titleBlock.partNumber = this.titleBlock.oldPartNumber;
    this.titleBlock.revision = this.titleBlock.oldRevision;
    this.titleBlock.watts = Math.round(this.titleBlock.watts);
    this.subscription.add(
      this.titleBlockService.updateTitleBlockBHX(data).subscribe(
        (res: TitleBlock) => {
          if (res) {
            this.titleBlock = res;
            this.titleBlock.oldPartNumber = this.titleBlock.partNumber;
            this.titleBlock.oldRevision = this.titleBlock.revision;
            this.showLoader = false;
            if (isDrawingSuccess) {
              this.snakbarService.success(Messages.Generate_File.success_both.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part), 10000);
            } else if (!isDrawingSuccess && isDrawingFailedWithResMessage) {
              if (drawingFailedMessage) {
                this.snakbarService.error(`${Messages.Generate_File.success_bhx_fail_drawing_file.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part)} ${drawingFailedMessage}`, 10000);
              } else {
                this.snakbarService.error(`${Messages.Generate_File.success_bhx_fail_drawing_file.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part)}`, 10000);
              }
            } else if (!isDrawingSuccess && !isDrawingFailedWithResMessage) {
              this.snakbarService.error(`${Messages.Generate_File.success_bhx_fail_drawing_file_fail_due_to_SolidWork.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part)}`, 10000);
            }
          }
        },
        (error) => {
          if (isDrawingSuccess) {
            this.snakbarService.error(Messages.Generate_File.success_drawing_file_fail_bhx.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part), 10000);
          } else if (!isDrawingSuccess && isDrawingFailedWithResMessage) {
            if (drawingFailedMessage) {
              this.snakbarService.error(`${Messages.Generate_File.fail_both.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part)} ${drawingFailedMessage}`, 10000);
            } else {
              this.snakbarService.error(`${Messages.Generate_File.fail_both.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part)}`, 10000);
            }
          } else if (!isDrawingSuccess && !isDrawingFailedWithResMessage) {
            this.snakbarService.error(Messages.Generate_File.fail_both_drawing_file_fail_due_to_SolidWork.replace('{File}', isJacketModel ? Messages.Generate_File.jacket_model : Messages.Generate_File.part), 10000);
          }
          this.showLoader = false;
        },
      ),
    );
  }

  async ngOnDestroy() {
    this.dragulaService.destroy('JACKETS');
    await this.saveAllWork();
    if (this.subscription) {
      this.subscription.unsubscribe();
      // clearInterval(this.interval);
    }
  }
}
