import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DesignEnggQuoteTrackerRowColourComponent } from './design-engg-quote-tracker-row-colour.component';

describe('DesignEnggQuoteTrackerRowColourComponent', () => {
  let component: DesignEnggQuoteTrackerRowColourComponent;
  let fixture: ComponentFixture<DesignEnggQuoteTrackerRowColourComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DesignEnggQuoteTrackerRowColourComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DesignEnggQuoteTrackerRowColourComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
