export class CoverPageModel {
    constructor(
        public id?: number,
        public quotationId?: string,
        public anticipatedVolume?: string,
        public meetingDate?: string,
        public note?: string,
        public reviewMeeting?: boolean,
        public reviewMeetingDate?: string,
        public specialRequirement?: boolean,
        public specialRequirementNote?: string,
        public newMaterial?: boolean,
        public newMaterialComments?: string,
        public qualificationTestingRequirement?: boolean,
        public qualificationTestingDept?: string,
        public contactedReliability?: boolean,
        public generalComments?: string,
        public greenSheetRequired?: boolean,
        public criticalPart?: boolean,
        public mtbfgoal?: string,
        public yearsAt?: string,
        public newPQPFamily?: boolean,
        public contactedQc?: boolean,
        public stayAsNonPQP?: boolean,
        public msds?: boolean,
        public pqpComments?: string,
        public coverPagePqpFamilies?: CoverPagePqpFamilyDTO[],
        public coverPageProductType?: CoverPageProductTypeDTO[]
    ) { }
}

export class CoverPagePqpFamilyDTO {
    constructor(
        public id?: number,
        public pqpFamilyId?: number,
        public coverPageId?: number,
        public name?: string
    ) { }
}

export class CoverPageProductTypeDTO {
    constructor(
        public id?: number,
        public productTypeId?: number,
        public coverPageId?: number,
        public name?: string
    ) { }
}
