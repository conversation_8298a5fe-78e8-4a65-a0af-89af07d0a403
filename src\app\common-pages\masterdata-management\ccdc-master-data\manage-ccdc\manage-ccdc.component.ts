import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { User } from 'src/app/common-pages/users/user.model';
import { EcrManagementService } from 'src/app/design-pages/ecr-management/ecr-management.service';
import { Messages, SnakbarService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { CcdcTemplateDTO } from './manage-ccdc.model';


@Component({
  selector: 'app-manage-ccdc',
  templateUrl: './manage-ccdc.component.html',
  styleUrls: ['./manage-ccdc.component.css']
})
export class ManageCcdcComponent implements OnInit {
  showReloadButton = false;
  showLoader = false;
  subscription = new Subscription();
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  users: User[];
  ccdcMasterDataObs: CcdcTemplateDTO;

  constructor(private snakBarService: SnakbarService, private ecrManagementService: EcrManagementService, private router: Router, private location: Location, private masterDataService: MasterdataManagementService) {

  }

  ngOnInit() {
    this.getAllUsers();
    this.masterDataService.getCcdcTemplateObs()
      .subscribe((ccdcTemplateMasterData) => {
        this.ccdcMasterDataObs = ccdcTemplateMasterData
      })
  }

  getAllUsers() {
    // getUsers
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.getSalesAssociate(true).subscribe((res: User[]) => {
      if (res) {
        this.users = res;
        this.showLoader = false;
      }
    }, error => {
      this.showLoader = false;
    }));
  }

  saveCcdcTemplate() {
    this.masterDataService.saveAllCcdcTemplates(this.ccdcMasterDataObs).subscribe(res => {
      // this.router.navigate(['master-data/management/ccdc-master-data']);
      this.location.back();
      if (res) {
        this.snakBarService.success(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.ccdc_template_success);
      }
    }
  );
  }

  reloadPage(): void {
    window.location.reload();
  }
}
