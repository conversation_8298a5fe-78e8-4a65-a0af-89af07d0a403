import { Component, OnInit, <PERSON><PERSON>hild, On<PERSON><PERSON>roy } from '@angular/core';
import { GenericPageable, SleevingTypesAndStrainReliefsMaster, SleevingTypesAndStrainReliefsFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageStrainReliefsComponent } from './manage-strain-reliefs/manage-strain-reliefs.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-strain-reliefs',
  templateUrl: './strain-reliefs.component.html'
})
export class StrainReliefsComponent implements OnInit, OnDestroy {
  pageTitle = 'Strain Reliefs Master';
  strainReliefs: SleevingTypesAndStrainReliefsMaster;
  strainReliefsPageable: GenericPageable<SleevingTypesAndStrainReliefsMaster>;
  strainReliefsDataSource = new MatTableDataSource<SleevingTypesAndStrainReliefsMaster>();
  strainReliefsColumns = DisplayColumns.Cols.SleevingTypesAndStrainReliefsCols;

  dataSource = new MatTableDataSource<SleevingTypesAndStrainReliefsMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  strainReliefsFilter: SleevingTypesAndStrainReliefsFilter = new SleevingTypesAndStrainReliefsFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldPartNumber = Values.FilterFields.partNumber;
  filterFieldName = Values.FilterFields.name;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getStrainReliefsMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new strain reliefs
  addStrainReliefs() {
    this.editStrainReliefs(new SleevingTypesAndStrainReliefsMaster());
  }

  // used to add filter to strain reliefs listing
  async addFilter() {
    this.filter = [
      { key: this.filterFieldPartNumber, value: !this.strainReliefsFilter.partNumber ? '' : this.strainReliefsFilter.partNumber },
      { key: this.filterFieldName, value: !this.strainReliefsFilter.name ? '' : this.strainReliefsFilter.name }
    ];
    this.getStrainReliefsMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of strain reliefs listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber,
        value:
          fieldToClear === this.filterFieldPartNumber ? (this.strainReliefsFilter.partNumber = '') : this.strainReliefsFilter.partNumber
      },
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.strainReliefsFilter.name = '') : this.strainReliefsFilter.name
      }
    ];
    this.getStrainReliefsMasterData(this.initialPageIndex, this.pageSize);
  }

  getStrainReliefsMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getStrainReliefsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<SleevingTypesAndStrainReliefsMaster>) => {
          this.strainReliefsPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createStrainReliefsTable(this.strainReliefsPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createStrainReliefsTable(serviceRequestList: GenericPageable<SleevingTypesAndStrainReliefsMaster>) {
    this.strainReliefsDataSource.data = serviceRequestList.content;
  }

  getStrainReliefsPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getStrainReliefsMasterData(this.pageIndex, this.pageSize);
  }

  getStrainReliefsSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getStrainReliefsMasterData(this.pageIndex, this.pageSize);
  }

  editStrainReliefs(strainReliefs: SleevingTypesAndStrainReliefsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = strainReliefs;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-strain-reliefs-master-model';
    const dialogRef = this.matDialog.open(ManageStrainReliefsComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          strainReliefs.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getStrainReliefsMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteStrainReliefs(strainReliefsId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteStrainReliefs(strainReliefsId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getStrainReliefsMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
