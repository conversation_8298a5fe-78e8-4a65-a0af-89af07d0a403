import { Component, OnInit, <PERSON>Child, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { AlloyMaster, GenericPageable, AlloyFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageAlloyMasterComponent } from './manage-alloy-master/manage-alloy-master.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-alloy-master',
  templateUrl: './alloy-master.component.html'
})
export class AlloyMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Alloy Master';
  alloy: AlloyMaster;
  alloyPageable: GenericPageable<AlloyMaster>;
  alloyDataSource = new MatTableDataSource<AlloyMaster>();
  alloyColumns = DisplayColumns.Cols.AlloyMasterCols;

  dataSource = new MatTableDataSource<AlloyMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  alloyFilter: AlloyFilter = new AlloyFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldGNDPartNumber = Values.FilterFields.gndPartNumber;
  filterFieldAlloyName = Values.FilterFields.alloyName;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getAlloyMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new alloy
  addalloy() {
    this.editalloy(new AlloyMaster());
  }

  // used to add filter for alloy listing
  async addFilter() {
    this.filter = [
      { key: this.filterFieldGNDPartNumber, value: !this.alloyFilter.gndPartNumber ? '' : this.alloyFilter.gndPartNumber },
      { key: this.filterFieldAlloyName, value: !this.alloyFilter.alloyName ? '' : this.alloyFilter.alloyName }
    ];
    this.getAlloyMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear alloy listing filter
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldGNDPartNumber,
        value: fieldToClear === this.filterFieldGNDPartNumber ? (this.alloyFilter.gndPartNumber = '') : this.alloyFilter.gndPartNumber
      },
      {
        key: this.filterFieldAlloyName,
        value: fieldToClear === this.filterFieldAlloyName ? (this.alloyFilter.alloyName = '') : this.alloyFilter.alloyName
      }
    ];
    this.getAlloyMasterData(this.initialPageIndex, this.pageSize);
  }

  getAlloyMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getAlloysMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<AlloyMaster>) => {
          this.alloyPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createalloyTable(this.alloyPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createalloyTable(serviceRequestList: GenericPageable<AlloyMaster>) {
    this.alloyDataSource.data = serviceRequestList.content;
  }

  getalloyPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getAlloyMasterData(this.pageIndex, this.pageSize);
  }

  getalloySorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getAlloyMasterData(this.pageIndex, this.pageSize);
  }

  editalloy(alloy: AlloyMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = alloy;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-alloy-master-model';
    const dialogRef = this.matDialog.open(ManageAlloyMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          alloy.id
            ? 'Alloy' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Alloy' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getAlloyMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deletealloy(alloyId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteAlloys(alloyId).subscribe(
        () => {
          this.snakbarService.success('Alloy' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getAlloyMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
