const AreYouSure = 'Are you sure?';
const updateYourGeometry = 'Update your Geometry entries';
const SequenceAlreadyExist = 'Sequence Already Exists!';
const PartNumberNotExist = 'Part Number Does not Exists!';
export const Messages = {
  PROFILE: {
    success_profile: 'You have successfully saved your account information.',
    success_Password: 'You have successfully updated your password.',
  },
  Quotation: {
    success_customer: 'You have successfully saved customer information.',
    success_application: 'You have successfully saved application information.',
    update_application: 'You have successfully updated application information.',
    success_quotation: 'You have successfully saved quotation.',
    success_create: 'You have created a new quotation.',
    success_retrived: 'You have successfully retrived quotation.',
    success_sensor: 'You have successfully saved sensor information.',
    update_sensor: 'You have successfully updated sensor information.',
    delete_sensor: 'You have successfully deleted sensor information.',
    copy_quotation_success: 'New Quotation Created Successfully!',
    success_notes: 'You have successfully saved note.',
    success_material: 'You have successfully saved material information.',
    update_material: 'You have successfully updated material information.',
    success_closure: 'You have successfully saved closure information.',
    update_closure: 'You have successfully updated closure information.',
    quotation_list_error: 'Something went wrong, unable to get quotations.',
    success_delete: 'You have successfully deleted a quotation.',
    upload_message: 'Drop here files to upload or browse',
    update_quot: 'You have successfully update quotation.',
    update_notes: 'You have successfully update note.',
    already_added: 'Thermostat already added.',
    select_thermostat: 'Please select thermostat type & installation method.',
    success_workflow: 'You have successfully saved workflow.',
    updated_workflow: 'You have successfully updated workflow.',
    success_plugging: 'You have successfully saved plugging information.',
    update_plugging: 'You have successfully updated plugging information.',
    success_thermostates: 'You have successfully saved thermostat information.',
  },
  Geometry: {
    success_jacket: 'You have successfully saved jacket.',
    success_features: 'You have successfully saved jacket feature.',
    success_saveall: 'You have successfully saved all jackets',
    success_delete_jacket: 'You have successfull deleted jacket',
    select_jacket: 'Please select jacket.',
  },
  JacketList: {
    success_pattern: 'You have successfully saved Pattern Details.',
    success_level: 'You have successfully saved Level 1 Review Details.',
    success_final: 'You have successfully saved Final Review Details.',
    success_element: 'You have successfully saved Element & BoM Details.',
    success_vietnam: 'You have successfully saved Vietnam Conv. Details.',
    success_jacket: 'You have successfully saved Jacket.',
    success_imp_jacket: 'You have successfully import Jackets.',
    part_number_reset: 'Part Number Reset Successfully!',
  },

  JacketReference: {
    success_poll_epicore: 'Success',
  },

  User: {
    find_user: 'You have successfully get user informaion.',
    success_save: 'You have successfully saved user informaion.',
    error_save: 'Something went wrong, unable to save a user.',
    list_error: 'Something went wrong, unable to get users.',
    error_delete: 'Something went wrong, unable to delete a user.',
    success_delete: 'You have successfully deleted a user.',
  },

  error: {
    error_msg: 'Something went wrong, please try again later.',
    part_error_msg: 'Part Number can contains only Alpha Numeric & Sequence number: Example: SC001, F001, 001.',
    rootPN_error_msg: 'Invalid root P/N.Please enter valid root P/N',
    excel_error_msg: 'Error occured while generating Quote Sheet',
    tape_invalid_partnumber: 'Invalid Tape Part Number. Example: HTX-00001',
    select_tape_type_first: 'Please select tape type first to generate Tape Part Number',
    tape_partnumber_already_exist: 'Tape Part Number already exist!',
    so_number_already_exist: 'SO Number already exist!',
  },
  Login: {
    invalid_email: 'You have not enter valid email address.',
    email_exits: 'This email address is already exits.',
    user_exits: 'This username is already exits.',
    email_not_exists: 'Email address not registered',
    email_not_registered: 'Email address is not registered.',
    fogot_Password_success: 'Check your email for details on how to reset your password.',
    key_missing_message: 'Key is Missing.',
    reset_Password_success: 'You have successfully updated your account password.',
    Password_not_matched: 'Confirm password does not match with new password.',
    invalid_resetkey: 'No user was found for this reset key',
    invalid_login: 'Failed to sign in! Please check your credentials and try again.',
  },
  sweet_error: {
    confirm_msg_title: AreYouSure,
    confirm_msg_title_generate_PN: 'Non-Standard PN# Confirmation',
    part_number_found: 'Part Number Found',
    in_epicor_unchecked: 'Forbidden!',
    in_epicor_unchecked_message: 'Wire must exist in Epicor for element to be selected',
    part_number_not_associated: 'Part Number Not Associated',
    api_response_error: 'API Error',
    confirm_msg_text: 'You want to delete?',
    confirm_msg_feature_text: 'You want to delete the features?',
    confirm_msg_text_features: 'You want to delete the selected features?',
    confirm_sync_text: ' It will overwrite the existing epicor BOM',
    confirm_archive_text: 'You want to archive?',
    consirm_label_entry_sync: 'There are unsaved changes in Labels Entry!',
    confirm_non_stdandard_PN:
      '"The Part number is not according to the standard PN matrix , it would be considered as a non standard PN# and no sequence will be maintained for it", do you wish to proceed?',
    confirm_update_watts_msg: 'Updating watts will reset all selected elements',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    confirm_btn_text_part_number: 'Ok',
    cancel_btn_text: 'Cancel',
    continue_sync_btn_text: 'Continue',
  },

  Solid_Work_API_service_error: {
    solidwork_service_error: 'SolidWorks API windows service not running',
  },

  quotation_sweet_error: {
    confirm_msg_title: 'Quotation not found',
    confirm_msg_text: 'Do you want to create a new?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },
  missing_customer_info_sweet_error: {
    confirm_msg_title: 'Customer details are not assigned to the quotation in epicor',
    confirm_msg_text: 'Do you still want to create a new quotation?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },
  warning_unitchange: {
    confirm_msg_title: updateYourGeometry,
    confirm_msg_text: 'Do update your Geometry entries in order to reflect the changes',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Ok',
    cancel_btn_text: 'Cancel',
  },

  warning_undoPattern: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to undo the Signature?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },

  warning_undoLevelOne: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to undo the Signature?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },

  warning_ElementAndBom: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to undo the Signature?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },

  warning_FinalReview: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to undo the Signature?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },

  warning_entryMethod_change: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to change entry method? This will not update your geometry entries.',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },
  warning_delete_folder: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to delete',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },

  root_part_reset: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to reset Root Part Number?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },

  error_msg: {
    invalidPassword: 'Incorrect password',
    emailnotreg: 'Email address not registered',
    emailused: 'Email is already in use!',
    usernameused: 'Login name already used!',
    session_timeout: 'Your session has expired. Please login again.',
    nodata: 'No data found',
  },
  jacket_group: {
    save_success: 'You have successfully saved a jacket group.',
    update_success: 'You have successfully updated a jacket group.',
    copy_success: 'You have successfully updated a jacket group.',
  },

  Generate_File: {
    success_both: '{File} generation is successfully completed. Title block also updated for both drawing file and BHX.',
    success_drawing_file_fail_bhx: '{File} generation is successfully completed. Title block also updated for drawing file, but failed for BHX.',
    success_bhx_fail_drawing_file: '{File} generation is successfully completed. Title block also updated for BHX, but failed for drawing file.',
    success_bhx_fail_drawing_file_fail_due_to_SolidWork:
      '{File} generation is successfully completed. Title block also updated for BHX, but failed for drawing file due to SFL Solidwork api service is not running on your computer.',
    fail_both: '{File} generation is successfully completed, but Title block update failed for both drawing file and BHX.',
    fail_both_drawing_file_fail_due_to_SolidWork:
      '{File} generation is successfully completed, but Title block update failed for both drawing file and BHX. Title block update for drawing file is failed due to SFL Solidwork api service is not running on your computer.',
    file_generation_fail: '{File} generation has failed. Please try again.',
    jacket_model: 'Jacket Model',
    part: 'Part'
  },

  Title_Block: {
    save_success: 'Title block successfully updated for BHX.',
    save_success_drawing_file: 'Title block successfully updated with drawing file.',
    error_msg: 'Something went wrong! Please try again later.',
  },
  Bom_Element: {
    confirm_sync_msg_titile: 'Do you want to sync tape info of this element to epicor?',
    sync_In_progress: 'Sync Initialized. Click here for Status',
    In_progress: 'IN_PROGRESS',
    sync_failed: 'Failed',
    sync_success: 'Completed',
  },
  Solid_Work: {
    success_mag: 'Solidwork template and folders succesfully created.',
    not_install: 'SFL Solidwork api service is not running on your computer',
    no_file: 'File Not exist!',
  },

  Customer_PN_Success: {
    part_number_save: 'Customer P/N saved successfully!',
  },

  new_Revision: {
    same_revision: 'New revision can not be same as current revision.',
  },
  element_Selection: {
    success_select_element: 'Element selected successfully.',
    success_clear_element: 'Element cleared successfully',
  },
  save_Chnages: {
    success: 'Changes successfully updated.',
  },
  dmtLogTitle: {
    bom: 'BOM DMT Logs',
    element: 'Element DMT Logs',
    finalReviewScreen: 'Final Review Screen DMT Logs',
  },
  goldReport: {
    quotation_incomplete: 'Quotation is not completed yet for SO Number ',
  },
  ECR_MANAGEMENT: {
    Partnumber_Not_Exist: 'Can not sync as Partnumber doesn\'t exist in Epicor',
    sync_In_progress: 'Sync in progress...',
    In_progress: 'IN_PROGRESS',
    sync_failed: 'failed',
    Ecr_Created_Success: 'ECR Submitted Successfully.',
  },
  Initial_Name: {
    Invalid: 'Invalid initial name',
    TryAgain: 'Couldn\'t set initial name, please try again or referesh the page.'
  },
  SENSOR_INFO: {
    Sensor_Temp_Type: 'For Lynx controlled jackets there can be only one controlling sensor',
  },
  COPY_BOM: {
    Copy_Success: 'BOM Copied Successfully',
    Copy_Error: 'Error in Copying BOM, please try again!',
  },
  // Master data management
  MASTER_DATA_MANAGEMENT_MESSAGES: {
    Added_Success: ' added successfully.',
    Duplicate_Success: ' Duplicate Added Successfully.',
    Updated_Success: ' updated successfully.',
    Deleted_Success: ' deleted successfully.',
    Only_Image_Type_Allowed: 'Please select only image type.',
    FormulaValidatedSuccess: 'Formula evaluated successfully.',
    FormulaValidatedFailure: 'Could not evaluated formula.',
    Reset_success: ' BHX Material Filter reset successfully',
    ccdc_template_success: 'CCDC Template has been saved successfully!',
    ccdc_export_success: 'CCDC Template has been Exported successfully!',
    ccdc_import_success: 'CCDC Template has been Imported successfully!',
  },
  ECO_LINE_MASTER_MESSAGES: {
    Added_Success: ' added successfully.',
    Updated_Success: ' updated successfully.',
    Deleted_Success: ' deleted successfully.',
  },
  FEATURE: {
    features_deleted: 'Features deleted successfully!',
    features_moved: 'Selected Features Moved Successfully!',
  },
  warning_sequence_duplication: {
    confirm_msg_title: SequenceAlreadyExist,
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Ok',
    cancel_btn_text: 'Cancel',
  },
  warning_part_number_not_exist: {
    confirm_msg_title: PartNumberNotExist,
    confirm_msg_text: 'Part number does not exists for this Jacket, so you will not be able to perform any operations',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Ok',
    cancel_btn_text: 'Cancel',
  },
  warning_reset_file_paths: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to Reset file Paths?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },
  warning_reset_title_block: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to Reset Title Block?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },
  warning_reset_bom: {
    confirm_msg_title: AreYouSure,
    confirm_msg_text: 'Do you want to Reset BOM?',
    confirm_btn_color: '#f44336',
    cancel_btn_color: '#ffffff',
    confirm_btn_text: 'Yes',
    cancel_btn_text: 'Cancel',
  },
  FINAL_REVIEW_SCREEN_MESSAGES: {
    sync_In_progress: 'Sync in progress...',
    Sync_Success: 'Sync Success',
    Sync_Failed: 'Sync Failed - One or more materials have failed to sync to Epicor',
    Sync_Complete: 'Sync Completed',
  },

  RFQ_FORM: {
    form_successfull: 'RFQ Form created successfully',
  },

  SO_FORM: {
    form_successfull: 'SO Form created successfully',
  },

  JACKET_DELETE: {
    jacket_delete: 'Selected Jacket Deleted Successfully',
    features_delete: 'Selected Features Deleted Successfully!',
  },

  JACKET_ON_HOLD: {
    jacket_on_hold: 'Jacket put on hold',
    jacket_remove_hold: 'Jacket removed from hold',
  },

  JACKET_IMAGE_UPLOAD: {
    image_upload_successfully: 'Image Uploaded Successfully!',
  },

  RESET_MESSAGE: {
    reset_successfull_operations: 'Operations Reset Sucessfully',
    reset_successfull_labels: 'Labels Reset Sucessfully',
    reset_successfull_element: 'Element Reset Sucessfully',
    reset_successfull_sensor: 'Sensor Reset Sucessfully',
    reset_successfull_fce: 'Facing/Liner/Clouser Reset Sucessfully',
    reset_successfull_wireplugging: 'Wire/Plugging Reset Sucessfully',
  },
};
