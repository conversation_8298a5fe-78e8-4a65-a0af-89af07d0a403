<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
  class="sfl-global-spinner-loader"
  [mode]="mode"
  [color]="color"
  [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<div class="">
  <mat-tab-group mat-stretch-tabs class="example-stretched-tabs mat-elevation-z4">
    <mat-tab label="App Engineering Master Data">
      <sfl-app-engg-master-data></sfl-app-engg-master-data>
    </mat-tab>
    <mat-tab label="Design Engineering Master Data">
      <sfl-design-engg-master-data></sfl-design-engg-master-data>
    </mat-tab>
  </mat-tab-group>
</div>