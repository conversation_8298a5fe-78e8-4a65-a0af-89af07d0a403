<h2 mat-dialog-title>
  Additional Cost Adder
  <hr>
</h2>
<mat-dialog-content>
  <div class="mt-10" fxLayout="row wrap" fxLayoutGap="25px">
    <mat-form-field  fxFlex="calc(50% - 25px)">
      <input type="number" step="any" matInput placeholder="Labor Hours Adder" [(ngModel)]="addedLaborHours" name="addLaborHours">
    </mat-form-field>
    <mat-form-field  fxFlex="calc(50% - 25px)">
      <input type="number" step="any" matInput placeholder="Material Cost Adder" [(ngModel)]="addedMaterialCost" name="addMaterialCost">
    </mat-form-field>
  </div>
  <div class="mt-10" fxLayout="row wrap" fxLayoutGap="25px">
    <mat-form-field  fxFlex="calc(50% - 25px)">
      <input type="number" step="any" matInput placeholder="Cost Adder" [(ngModel)]="addedCost" name="addCost">
    </mat-form-field>
    <mat-form-field fxFlex="calc(50% - 25px)">
      <input type="number" step="any"  matInput placeholder="List Price Adder" [(ngModel)]="addedListPrice" name="addPrice">
    </mat-form-field>
  </div>
</mat-dialog-content>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button type="button" (click)="massUpdateSelectedJacketsCost()" color="warn" >Submit</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="button" (click)="closeDialog()" >Cancel</button>
  </div>
</mat-dialog-actions>
