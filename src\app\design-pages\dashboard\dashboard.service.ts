
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../shared/helpers/app.helper';


import { PageableQuery, createRequestOption } from 'src/app/shared';
import { QuotationSearchFilter } from 'src/app/admin-pages/dashboard/dashboard.model';

@Injectable({ providedIn: 'root' })
export class DashboardService {
  constructor(private readonly http: HttpClient) {}

  searchQuotations(quotationSearchFilter, pageableObject) {
    return this.http.post(AppConfig.SEARCH_QUOTATION_DESIGN, quotationSearchFilter, {
      params: createRequestOption(pageableObject)
    }).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  // filters the Design Quotations list using quotationNumber
  searchDesignQuotations(projectTitle: string, pageableObject: PageableQuery) {
    const quotationSearchFilter = new QuotationSearchFilter();
    quotationSearchFilter.projectTitle = projectTitle;
    return this.http
      .post(AppConfig.SEARCH_QUOTATION_DESIGN, quotationSearchFilter, {
        params: createRequestOption(pageableObject)
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  // gets the number of quotations based on differnt statuses
  getQuotationCount() {
    return this.http.get(AppConfig.DESIGN_STATUS_COUNT).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getSalesAssociate(isGlobalSearch: boolean) {
    return this.http.get(AppConfig.SalesAssociate + '/' + isGlobalSearch).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  countStatus() {
    return this.http.get(
      AppConfig.STATUS_COUNT
    ).pipe(map(utils.extractData),
      catchError(utils.handleError),);
  }
}
