import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { HeatingTapesMaster, GenericPageable, HeatingTapesFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { ManageHeatingTapesComponent } from './manage-heating-tapes/manage-heating-tapes.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-heating-tapes',
  templateUrl: './heating-tapes.component.html'
})
export class HeatingTapesComponent implements OnInit, OnDestroy {
  pageTitle = 'Heating Tape Master';
  heatingTapes: HeatingTapesMaster;
  heatingTapesPageable: GenericPageable<HeatingTapesMaster>;
  heatingTapesDataSource = new MatTableDataSource<HeatingTapesMaster>();
  heatingTapesColumns = DisplayColumns.Cols.HeatingTapesMasterCols;

  dataSource = new MatTableDataSource<HeatingTapesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  heatingTapesFilter: HeatingTapesFilter = new HeatingTapesFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldTapePartNumber = Values.FilterFields.tapePartNumber;
  filterFieldAlloyName = Values.FilterFields.alloyName;
  filterFieldTapeWidth = Values.FilterFields.newTapeWidth;
  filterFieldTapeType = Values.FilterFields.tapeType;
  tapeTypes = Values.HeatingTapesTypes;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getHeatingTapesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new heating tapes
  addHeatingTapes() {
    this.editHeatingTapes(new HeatingTapesMaster());
  }

  // used to add filter to heating tapes listing
  async addFilter() {
    this.filter = [
      { key: this.filterFieldTapePartNumber, value: !this.heatingTapesFilter.tapePartNumber ? '' : this.heatingTapesFilter.tapePartNumber },
      { key: this.filterFieldAlloyName, value: !this.heatingTapesFilter.alloyName ? '' : this.heatingTapesFilter.alloyName }
    ];
    this.getHeatingTapesMasterData(this.initialPageIndex, this.pageSize);
  }

  async addFilterTapeWidth() {
    this.filter = [
      { key: this.filterFieldTapeWidth, value: !this.heatingTapesFilter.width ? '' : this.heatingTapesFilter.width, dataType: "Double", operator: "Eq" }
    ];
    this.getHeatingTapesMasterData(this.initialPageIndex, this.pageSize);
  }

  async addFilterTapeType() {
    this.filter = [
      { key: this.filterFieldTapeType, value: !this.heatingTapesFilter.tapeType ? '' : this.heatingTapesFilter.tapeType }
    ];
    this.getHeatingTapesMasterData(this.initialPageIndex, this.pageSize);
  }

  resetFilter(){
    this.heatingTapesFilter = new HeatingTapesFilter();
    this.heatingTapesFilter.tapeType = null;
    this.heatingTapesFilter.width = null;
    this.heatingTapesFilter.tapePartNumber = null;
    this.heatingTapesFilter.alloyName = null;
    this.getHeatingTapesMasterData(this.initialPageIndex, this.pageSize);

  }

  // used to clear filter to heating tapes listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldTapePartNumber,
        value:
          fieldToClear === this.filterFieldTapePartNumber
            ? (this.heatingTapesFilter.tapePartNumber = '')
            : this.heatingTapesFilter.tapePartNumber
      },
      {
        key: this.filterFieldAlloyName,
        value: fieldToClear === this.filterFieldAlloyName ? (this.heatingTapesFilter.alloyName = '') : this.heatingTapesFilter.alloyName
      },
      {
        key: this.filterFieldTapeWidth,
        value: fieldToClear === this.filterFieldTapeWidth ? (this.heatingTapesFilter.width = '') : this.heatingTapesFilter.width,
        dataType: "Double",
        operator: "Eq"
      },
      {
        key: this.filterFieldTapeType,
        value: fieldToClear === this.filterFieldTapeType ? (this.heatingTapesFilter.tapeType = '') : this.heatingTapesFilter.tapeType
      }
    ];
    this.getHeatingTapesMasterData(this.initialPageIndex, this.pageSize);
  }

  getHeatingTapesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getHeatingTapesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<HeatingTapesMaster>) => {
          this.heatingTapesPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createHeatingTapesTable(this.heatingTapesPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createHeatingTapesTable(serviceRequestList: GenericPageable<HeatingTapesMaster>) {
    this.heatingTapesDataSource.data = serviceRequestList.content;
  }

  getHeatingTapesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getHeatingTapesMasterData(this.pageIndex, this.pageSize);
  }

  getHeatingTapesSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getHeatingTapesMasterData(this.pageIndex, this.pageSize);
  }

  editHeatingTapes(heatingTapes: HeatingTapesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = heatingTapes;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-heating-tapes-master-model';
    const dialogRef = this.matDialog.open(ManageHeatingTapesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          heatingTapes.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getHeatingTapesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteHeatingTapes(heatingTapesId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteHeatingTapes(heatingTapesId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getHeatingTapesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
