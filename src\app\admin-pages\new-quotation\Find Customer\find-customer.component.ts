import { Component } from '@angular/core';
import { MatDialogRef, MatTableDataSource } from '@angular/material';
import { Messages, SnakbarService } from '../../../shared';
import { SummarySalesOrderComponent } from '../summary-sales-order.component';

@Component({
    selector: 'sfl-find-customer',
    templateUrl: './find-customer.component.html'
})

export class FindCustomerComponent {

    displayedColumns = ['name', 'code', 'abrev', 'contact'];
    dataSource = new MatTableDataSource<UserData>(User_Data);

    constructor(
        private snakbarService: SnakbarService,
        public dialogRef: MatDialogRef<SummarySalesOrderComponent>
    ) { }

    saveCustomer() {
        this.dialogRef.close();
    }

    closeDialog(): void {
        this.dialogRef.close();
    }

    applyFilter(filterValue: string) {
        this.dataSource.filter = filterValue.trim().toLowerCase();
      }
}


export interface UserData {
    name: string;
    code: string;
    abrev: string;
    contact: string;
}

export const User_Data: UserData[] = [
    { name: 'keval', code: '879798', abrev: 'KYMTE', contact: '987654123' },
    { name: 'Nikunj', code: '657268', abrev: 'HCGWY', contact: '741258963' },
    { name: 'Jeeten', code: '52662', abrev: 'CEGY', contact: '321456987' },
    { name: 'Mitesh', code: '56729687', abrev: 'EITFE', contact: '951357456' },
];
