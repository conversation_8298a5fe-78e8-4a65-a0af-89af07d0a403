import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { AlloyMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-alloy-master',
  templateUrl: './manage-alloy-master.component.html'
})
export class ManageAlloyMasterComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  alloys: AlloyMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageAlloyMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.alloys = data;
  }

  ngOnInit() {
    this.alloys = this.alloys.id ? Object.assign({}, this.alloys) : new AlloyMaster();
    this.alloys.id ? (this.title = 'Update Alloys') : (this.title = 'Add Alloys');
  }

  updateAlloy() {
    this.showLoader = true;
    if (this.alloys.id) {
      this.subscription.add(
        this.masterDataService.updateAlloys(this.alloys).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addAlloys(this.alloys).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
