import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SharedService } from 'src/app/shared';
import { AuditCountResponseDTO, Types } from './design-eng-layout.model';

import { DesignEngNavbarService } from './design-eng-navbar.service';

@Component({
  selector: 'sfl-design-eng-navbar',
  templateUrl: './design-eng-navbar.component.html',
  styleUrls: ['./design-eng-navbar.scss']
})

export class DesignEngNavbarComponent implements OnInit, OnDestroy {
  showLoader: boolean;
  subscription = new Subscription();
  topFiveSo: AuditCountResponseDTO[];
  type = Types;
  constructor(
    public readonly adminNavbarService: DesignEngNavbarService,
    public readonly sharedService: SharedService,
    private router: Router
  ) {
  }



  ngOnInit(): void {
    this.getTopFiveSoDesign();
  }

  getTopFiveSoDesign() {
    const userId = this.sharedService.getUserId();
    this.showLoader = true;
    this.subscription.add(
      this.adminNavbarService.getTopFiveSo(userId, this.type.DESIGN).subscribe(
        (res) => {
          this.topFiveSo = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }


  addMenuItem(): void {
    this.adminNavbarService.add({
      state: 'menu',
      name: 'MENU',
      type: 'sub',
      icon: 'trending_flat',
      children: [
        { state: 'menu', name: 'MENU' },
        { state: 'timeline', name: 'MENU' }
      ]
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
