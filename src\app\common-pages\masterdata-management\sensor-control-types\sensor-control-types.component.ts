import { Component, OnInit, <PERSON>Child, OnD<PERSON>roy } from '@angular/core';
import { SensorConnectorsAndTypesMaster, GenericPageable, SensorConnectorsAndTypesFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageSensorControlTypesComponent } from './manage-sensor-control-types/manage-sensor-control-types.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-sensor-control-types',
  templateUrl: './sensor-control-types.component.html'
})
export class SensorControlTypesComponent implements OnInit, OnDestroy {
  pageTitle = 'Sensor Control Types Master';
  sensorControlTypes: SensorConnectorsAndTypesMaster;
  sensorControlTypesPageable: GenericPageable<SensorConnectorsAndTypesMaster>;
  sensorControlTypesDataSource = new MatTableDataSource<SensorConnectorsAndTypesMaster>();
  sensorControlTypesColumns = DisplayColumns.Cols.SensorControlTypes;

  dataSource = new MatTableDataSource<SensorConnectorsAndTypesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  sensorControlTypesFilter: SensorConnectorsAndTypesFilter = new SensorConnectorsAndTypesFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldId = Values.FilterFields.id;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getSensorControlTypesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  addSensorControlTypes() {
    this.editSensorControlTypes(new SensorConnectorsAndTypesMaster());
  }

  async addFilter() {
    this.filter = this.sensorControlTypesFilter.id === '' ? [] : [{ key: this.filterFieldId, value: this.sensorControlTypesFilter.id }];
    this.getSensorControlTypesMasterData(this.initialPageIndex, this.pageSize);
  }

  getSensorControlTypesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getSensorControlTypesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<SensorConnectorsAndTypesMaster>) => {
          this.sensorControlTypesPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createSensorControlTypesTable(this.sensorControlTypesPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createSensorControlTypesTable(serviceRequestList: GenericPageable<SensorConnectorsAndTypesMaster>) {
    this.sensorControlTypesDataSource.data = serviceRequestList.content;
  }

  getSensorControlTypesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getSensorControlTypesMasterData(this.pageIndex, this.pageSize);
  }

  getSensorControlTypesSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getSensorControlTypesMasterData(this.pageIndex, this.pageSize);
  }

  editSensorControlTypes(sensorControlTypes: SensorConnectorsAndTypesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = sensorControlTypes;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-sensor-control-types-master-model';
    const dialogRef = this.matDialog.open(ManageSensorControlTypesComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          sensorControlTypes.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getSensorControlTypesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteSensorControlTypes(sensorControlTypesId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteSensorControlTypes(sensorControlTypesId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getSensorControlTypesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
