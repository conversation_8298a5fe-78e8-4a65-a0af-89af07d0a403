import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { PlugLightsMaster, GenericPageable, PlugLightsFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManagePlugLightsComponent } from './manage-plug-lights/manage-plug-lights.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-plug-lights',
  templateUrl: './plug-lights.component.html'
})
export class PlugLightsComponent implements OnInit, OnDestroy {
  pageTitle = 'Plug Lights Master';
  plugLights: PlugLightsMaster;
  plugLightsPageable: GenericPageable<PlugLightsMaster>;
  plugLightsDataSource = new MatTableDataSource<PlugLightsMaster>();
  plugLightsColumns = DisplayColumns.Cols.PlugLightsCols;

  dataSource = new MatTableDataSource<PlugLightsMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  plugLightsFilter: PlugLightsFilter = new PlugLightsFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldpartNumber = Values.FilterFields.partNumber;
  filterFieldplugName = Values.FilterFields.name;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getPlugLightsMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new plug light
  addPlugLights() {
    this.editPlugLights(new PlugLightsMaster());
  }

  // used to add filter to plug light listing
  async addFilter() {
    this.filter = [
      { key: this.filterFieldpartNumber, value: !this.plugLightsFilter.partNumber ? '' : this.plugLightsFilter.partNumber },
      { key: this.filterFieldplugName, value: !this.plugLightsFilter.name ? '' : this.plugLightsFilter.name }
    ];
    this.getPlugLightsMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of plug light listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldpartNumber,
        value: fieldToClear === this.filterFieldpartNumber ? (this.plugLightsFilter.partNumber = '') : this.plugLightsFilter.partNumber
      },
      {
        key: this.filterFieldplugName,
        value: fieldToClear === this.filterFieldplugName ? (this.plugLightsFilter.name = '') : this.plugLightsFilter.name
      }
    ];
    this.getPlugLightsMasterData(this.initialPageIndex, this.pageSize);
  }

  getPlugLightsMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getPlugLightsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<PlugLightsMaster>) => {
          this.plugLightsPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPlugLightsTable(this.plugLightsPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createPlugLightsTable(serviceRequestList: GenericPageable<PlugLightsMaster>) {
    this.plugLightsDataSource.data = serviceRequestList.content;
  }

  getPlugLightsPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPlugLightsMasterData(this.pageIndex, this.pageSize);
  }

  getPlugLightsSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPlugLightsMasterData(this.pageIndex, this.pageSize);
  }

  editPlugLights(plugLight: PlugLightsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = plugLight;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-plug-lights-model';
    const dialogRef = this.matDialog.open(ManagePlugLightsComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          plugLight.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPlugLightsMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deletePlugLight(plugLightId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePlugLight(plugLightId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPlugLightsMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
