<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Search Parameters
  <hr />
</h2>
<div fxLayout="row wrap" fxLayoutAlign="space-between">
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input matInput type="number" placeholder="Strands" [(ngModel)]="wireSelector.searchParameterDTO.strandsStart" name="strandsStart" />
  </mat-form-field>
  <mat-icon>settings_ethernet</mat-icon>
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input matInput type="number" placeholder="To" [(ngModel)]="wireSelector.searchParameterDTO.strandsEnd" name="strandsEnd" />
  </mat-form-field>
</div>
<div *ngIf="isTapOrDualWire" fxLayout="row wrap" fxLayoutAlign="space-between">
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input matInput type="number" placeholder="Picks" [(ngModel)]="wireSelector.searchParameterDTO.picksStart" name="picksStart" />
  </mat-form-field>
  <mat-icon>settings_ethernet</mat-icon>
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input matInput type="number" placeholder="To" [(ngModel)]="wireSelector.searchParameterDTO.picksEnd" name="picksEnd" />
  </mat-form-field>
</div>
<div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!(productType?.toLowerCase() === 'silicone')">
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input matInput type="number" placeholder="TPI" [(ngModel)]="wireSelector.searchParameterDTO.tpiStart" name="tpiStart" />
  </mat-form-field>
  <mat-icon>settings_ethernet</mat-icon>
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input matInput type="number" placeholder="To" [(ngModel)]="wireSelector.searchParameterDTO.tpiEnd" name="tpiEnd" />
  </mat-form-field>
</div>
<div *ngIf="isTapOrDualWire && !(productType?.toLowerCase() === 'silicone')" fxLayout="row wrap" fxLayoutAlign="space-between">
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input
      matInput
      type="number"
      placeholder="TPI Picks"
      [(ngModel)]="wireSelector.searchParameterDTO.tpiPicksStart"
      name="tpiPicksStart"
    />
  </mat-form-field>
  <mat-icon>settings_ethernet</mat-icon>
  <mat-form-field fxFlex.gt-lg="47" fxFlex.gt-md="47">
    <input matInput placeholder="To" type="number" [(ngModel)]="wireSelector.searchParameterDTO.tpiPicksEnd" name="tpiPicksEnd" />
  </mat-form-field>
</div>
<div *ngIf="isTapOrDualWire" fxLayout="row wrap" fxLayoutAlign="space-between">
  <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
    <input matInput type="number" placeholder="Increment" [(ngModel)]="wireSelector.searchParameterDTO.increment" name="increment" />
  </mat-form-field>
</div>
<hr />
<mat-dialog-actions>
  <button mat-raised-button type="submit" color="warn" (click)="searchWire()">Submit</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
