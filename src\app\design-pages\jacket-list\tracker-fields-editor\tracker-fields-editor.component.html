<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" [formGroup]="trackerFieldsForm" (ngSubmit)="saveTrackerFields()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="SO Number" formControlName="soNumber" name="soNumber">
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Design Location" name="designLocation" formControlName="designLocation" color="warn">
            <mat-option *ngFor="let country of countries" [value]="country">
              {{ country | uppercase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Sales Representative" name="salesRepresentative" formControlName="salesRepId">
            <mat-option *ngFor="let associate of salesassociates" [value]="associate.id">
              {{ associate.firstName }} {{ associate.lastName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Quotation Status" name="quoteStatus" formControlName="quotationStatusId">
            <mat-option *ngFor="let status of statuses | orderBy:'orderNumber'" [value]="status.id">
              {{ status?.status }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field appearance="outline">
          <mat-label>OFA 1 Date</mat-label>
          <input
            matInput
            (click)="ofa1DatePicker.open()"
            [matDatepicker]="ofa1DatePicker"
            placeholder="OFA 1 Date"
            formControlName="ofa1Date"
            name="ofa1DateField"
            autocomplete="off"
          />
          <mat-datepicker-toggle matSuffix [for]="ofa1DatePicker"></mat-datepicker-toggle>
          <mat-datepicker #ofa1DatePicker></mat-datepicker>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field appearance="outline">
          <mat-label>Approval 1 Date</mat-label>
          <input
            matInput
            (click)="approval1DatePicker.open()"
            [matDatepicker]="approval1DatePicker"
            placeholder="Approval 1 Date"
            formControlName="approval1Date"
            name="approval1DateField"
            autocomplete="off"
          />
          <mat-datepicker-toggle matSuffix [for]="approval1DatePicker"></mat-datepicker-toggle>
          <mat-datepicker #approval1DatePicker></mat-datepicker>
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field appearance="outline">
          <mat-label>Folder Submitted Date</mat-label>
          <input
            matInput
            (click)="folderSubmittedDatePicker.open()"
            [matDatepicker]="folderSubmittedDatePicker"
            placeholder="Folder Submitted Date"
            formControlName="folderSubmittedDate"
            name="folderSubmittedDateField"
            autocomplete="off"
          />
          <mat-datepicker-toggle matSuffix [for]="folderSubmittedDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #folderSubmittedDatePicker></mat-datepicker>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field appearance="outline">
          <mat-label>Ship Date</mat-label>
          <input
            matInput
            (click)="shipDatePicker.open()"
            [matDatepicker]="shipDatePicker"
            placeholder="Ship Date"
            formControlName="shipDate"
            name="shipDateField"
            autocomplete="off"
          />
          <mat-datepicker-toggle matSuffix [for]="shipDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #shipDatePicker></mat-datepicker>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="99" fxFlex.gt-md="99" fxFlex.gt-sm="99" fxFlex.gt-xs="100">
        <mat-form-field appearance="outline">
          <mat-label>Release Date</mat-label>
          <input
            matInput
            (click)="releaseDatePicker.open()"
            [matDatepicker]="releaseDatePicker"
            placeholder="Release Date"
            formControlName="releaseDate"
            name="releaseDateField"
            autocomplete="off"
          />
          <mat-datepicker-toggle matSuffix [for]="releaseDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #releaseDatePicker></mat-datepicker>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="100">
        <mat-form-field>
          <textarea matInput placeholder="No. Of Designs" formControlName="customNoOfDesigns" name="noOfDesign" sflIsNumber></textarea>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="100">
        <mat-form-field>
          <textarea matInput placeholder="Project Name" formControlName="projectTitle" name="projectTitle"></textarea>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="32" fxFlex.gt-xs="100">
        <mat-form-field>
          <textarea matInput placeholder="Customer Name" formControlName="customerName" name="customerName"></textarea>
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="22" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Product Type" name="productType" formControlName="productType">
            <mat-option *ngFor="let product of productTypes" [value]="product.id">
              {{ product.id }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="22" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Dollar Amount" formControlName="dollarAmount" name="dollarAmount">
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Assigned App Enggineer" name="assignedAppEngineerId" formControlName="appEnggAssigned">
            <mat-option *ngFor="let associate of salesassociates" [value]="associate.id">
              {{ associate.firstName }} {{ associate.lastName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="22" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Number Of Custom Revisions"
            formControlName="noOfCustomRevision"
            name="noOfCustomRevisionInput"
            sflIsNumber
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="48" fxFlex.gt-xs="100">
        <mat-form-field>
          <textarea matInput placeholder="Design Comments" formControlName="designComments" name="designComments"></textarea>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="48" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Assigned Designer" name="assignedDesigner"  formControlName="assignedDesignerId">

            <mat-option *ngFor="let designer of designers" [value]="designer?.id">
              {{ designer?.firstName }} {{ designer?.lastName }}
            </mat-option>
            <mat-option>None</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>

  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn">Save</button>
    </div>
  </mat-dialog-actions>
</form><br><br><br><br>

<!-- <app-design-engg-quote-tracker-row-colour
[id] = "_quotationId"
[designStatusId] = "trackerFields.quotationStatusId"
[primaryColorValue] = "primaryColorValue"
></app-design-engg-quote-tracker-row-colour> -->
