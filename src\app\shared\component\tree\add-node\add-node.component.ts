import { Component, EventEmitter, Inject, Input, Output } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { TreeData } from '../tree-data.model';



@Component({
  selector: 'sfl-add-node',
  templateUrl: './add-node.component.html',
})
export class AddNodeComponent {
  @Input() isTop: boolean;
  @Input() currentNode: TreeData;
  @Output() addedNode = new EventEmitter;
  name: string;

  constructor(public dialog: MatDialog) { }

  openDialog(): void {
    const dialogRef = this.dialog.open(NewNodeDialog, {
      width: PopupSize.size.popup_md,
      data: { nodeName: this.name, Component: 'Add' }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const node: TreeData = {
          Id: null,
          Name: result.nodeName.trim(),
          isExpanded: false,
          isRootNode: false,
          Children: []
        };
        if (this.isTop) {
          this.addedNode.emit(node);
        } else {
          this.addedNode.emit({ currentNode: this.currentNode, newAddedNode: node });
        }
      }
    });
  }
}

@Component({
  selector: 'sfl-new-node',
  templateUrl: '../node-dialog/node-dialog.html',
})
export class NewNodeDialog {

  constructor(
    public dialogRef: MatDialogRef<NewNodeDialog>,
    @Inject(MAT_DIALOG_DATA) public data) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

}
