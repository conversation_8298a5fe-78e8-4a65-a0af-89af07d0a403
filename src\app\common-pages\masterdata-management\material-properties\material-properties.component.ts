import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import {
  MaterialPropertiesMaster,
  MaterialPropertiesMasterPageable,
  MaterialPropertiesFilter,
  GenericPageable
} from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageMaterialPropertyComponent } from './manage-material-property/manage-material-property.component';
import { SnakbarService, Messages, SweetAlertService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-material-properties',
  templateUrl: './material-properties.component.html'
})
export class MaterialPropertiesComponent implements OnInit, OnDestroy {
  pageTitle = 'Material Properties Master';
  materialProperty: MaterialPropertiesMaster;
  materialPropertyPageable: GenericPageable<MaterialPropertiesMaster>;
  materialPropertyDataSource = new MatTableDataSource<MaterialPropertiesMaster>();
  materialPropertyMasterColumns = DisplayColumns.Cols.MaterialProperty;

  dataSource = new MatTableDataSource<MaterialPropertiesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  materialPropertiesFilter: MaterialPropertiesFilter = new MaterialPropertiesFilter();

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldplugName = Values.FilterFields.name;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) {}

  ngOnInit() {
    this.getMaterialPropertiesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new material properties
  addMaterialProperties() {
    this.editMaterialProperties(new MaterialPropertiesMaster());
  }

  // used to add filter to material properties listing
  async addFilter() {
    this.filter =
      this.materialPropertiesFilter.name === '' ? [] : [{ key: this.filterFieldplugName, value: this.materialPropertiesFilter.name }];
    this.getMaterialPropertiesMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter of material properties listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldplugName,
        value: fieldToClear === this.filterFieldplugName ? (this.materialPropertiesFilter.name = '') : this.materialPropertiesFilter.name
      }
    ];
    this.getMaterialPropertiesMasterData(this.initialPageIndex, this.pageSize);
  }

  getMaterialPropertiesMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getMaterialProperties(this.filter, pageable).subscribe(
        (res: GenericPageable<MaterialPropertiesMaster>) => {
          this.materialPropertyPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createMaterialPropertiesTable(this.materialPropertyPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.materialPropertyDataSource.data = [];
        }
      )
    );
  }

  createMaterialPropertiesTable(serviceRequestList: GenericPageable<MaterialPropertiesMaster>) {
    this.materialPropertyDataSource.data = serviceRequestList.content;
  }

  getMaterialPropertiesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getMaterialPropertiesMasterData(this.pageIndex, this.pageSize);
  }

  getMaterialPropertiesSorting(event) {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getMaterialPropertiesMasterData(this.pageIndex, this.pageSize);
  }

  async deleteMaterialProperties(materialPropertyId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteMaterialProperties(materialPropertyId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getMaterialPropertiesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }
  editMaterialProperties(materialProperty: MaterialPropertiesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = materialProperty;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-material-property-master-model';
    const dialogRef = this.matDialog.open(ManageMaterialPropertyComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          materialProperty.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getMaterialPropertiesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
