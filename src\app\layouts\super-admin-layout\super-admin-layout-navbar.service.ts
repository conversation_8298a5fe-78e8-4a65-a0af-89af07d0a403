import { Injectable } from '@angular/core';

export interface BadgeItem {
  type: string;
  value: string;
}

export interface ChildrenItems {
  state: string;
  name: string;
  type: string;
}

export interface Menu {
  state: string;
  name: string;
  type: string;
  icon: string;
  badge?: BadgeItem[];
  children?: ChildrenItems[];
}


const MENUITEMS = [
  {
    state: 'super-admin/users',
    name: 'User Management',
    type: 'link',
    icon: 'person_add'
  }
];

@Injectable()
export class SuperAdminNavbarService {
  sidePanelOpened: boolean;
  getAll(): Menu[] {
    return MENUITEMS;
  }

  add(menu) {
    MENUITEMS.push(menu);
  }
}
