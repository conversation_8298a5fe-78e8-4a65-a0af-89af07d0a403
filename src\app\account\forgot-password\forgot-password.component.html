<section class="user">
    <div class="user_options-container">
        <mat-card>
            <mat-card-content>
                <div class="user_options-text">
                    <div class="user_options-unregistered">
                        <div class="user_unregistered-title">
                            <img src="../../../assets/images/logo.png" alt="logo">
                        </div>
                        <p class="user_unregistered-text">Enter your email and we'll send you instructions on how to reset your password.</p>
                        <div class="mat-badge-warn" *ngIf="errorEmailNotExists">
                                {{message}}
                        </div>
                        <div class="mat-badge-warn" *ngIf="isError">
                                {{message}}
                        </div>
                    </div>
                </div>
            </mat-card-content>
            <div class="user_options-forms" id="user_options-forms">
                <div class="user_forms-login">
                    <h2 class="forms_title">Forgot password</h2>
                    <form class="forms_form" #forgotpasswordForm="ngForm" (ngSubmit)="sendForgotPasswordEmail(forgotpasswordForm)">
                        <mat-form-field class="forms_fieldset"> 
                            <input matInput type="email" placeholder="Email" name="email" [(ngModel)]="email" #emailInput="ngModel" autofocus required email>
                        </mat-form-field>
                        <div *ngIf="emailInput.touched && emailInput.invalid">
                            <small class="mat-text-warn" *ngIf="emailInput?.errors.required">Email is required.</small>
                            <small class="mat-text-warn" *ngIf="emailInput?.errors.email && !emailInput?.errors.required">Email is not valid.</small>
                        </div>
                        <div class="forms_buttons">
                            <button type="button" class="forms_buttons-forgot" [routerLink]="['/login']">Access your account</button>
                            <button mat-raised-button type="submit" color="warn" [disabled]="forgotpasswordForm.form.invalid">Reset Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </mat-card>
    </div>
</section>