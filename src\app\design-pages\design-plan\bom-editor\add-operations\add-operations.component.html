<h2 mat-dialog-title>Add Operation
  <hr>
</h2>
<form #operationForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-form-field class="example-full-width">
      <input
        type="text"
        placeholder="Select Operation"
        aria-label="Select Operation"
        matInput
        [matAutocomplete]="auto"
        [formControl]="operationControl"
      />
      <mat-hint>Enter text to find Materials</mat-hint>
    </mat-form-field>
    <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn" placeholder="Select" name="operation"
                      (optionSelected)="onOperationSelect($event.option.value)" required>
      <mat-option *ngFor="let operation of operationsObservable$ | async;" [value]="operation">
        {{ operation.operationName }}
      </mat-option>
      <mat-option value="other">Other</mat-option>
    </mat-autocomplete>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Sequence" [(ngModel)]="operationDetails.sequence" name="sequence"
               autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Op. Number" [(ngModel)]="operationDetails.opNumber" name="opNumber"
               autocomplete="off">
      </mat-form-field>
    </div>
    <div>
      <mat-form-field>
        <input matInput placeholder="Operation" [(ngModel)]="operationDetails.operationName" name="operationName"
               autocomplete="off">
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Prod Hrs" [(ngModel)]="operationDetails.prodHrs" name="prodHrs" autocomplete="off">
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Setup Hrs" [(ngModel)]="operationDetails.setupHrs" name="setupHrs"
               autocomplete="off">
      </mat-form-field>
    </div>
  </mat-dialog-content>
  <hr>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" [disabled]="operationForm.invalid" (click)="saveOperaton()">Add
      Operation
    </button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</form>
