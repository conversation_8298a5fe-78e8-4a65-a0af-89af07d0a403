export class SolidWorksBlockMasterDTO {
  approvalLevel?: string;
  connector?: string;
  controlType?: string;
  elementType?: string;
  usaFilePath?: string;
  vietnamFilePath?: string;
  id?: number;
  maxJumperLength?: number;
  maxJumperQty?: number;
  maxLeadLength?: number;
  minJumperLength?: number;
  minJumperQty?: number;
  minLeadLength?: number;
  phase?: string;
  plug?: string;
  productType?: string;
  redLight: boolean = null;
  greenLight: boolean = null;
  anySensor: boolean = null;
  anyThermostat: boolean = null;
  solidWorksBlockSensorInformations?: SolidWorksBlockSensorInformationDTO[] = [];
  solidWorksBlockThermostatInformations?: SolidWorksBlockThermostatInformationDTO[] = [];
}

export class SolidWorksBlockSensorInformationDTO {
  connector?: string;
  connectorsArray?: string[] = [];
  id?: string;
  location?: string;
  locationArray?: string[] = [];
  maxLength?: number;
  minLength?: number;
  solidWorkBlockMasterId?: number;
  tempType?: string;
  tempTypeArray?: string[] = [];
  type?: string;
  typeArray?: string[] = [];
}

export class SolidWorksBlockThermostatInformationDTO {
  id?: number;
  installationMethod?: string;
  installationMethodArray?: string[] = [];
  manualReset: boolean = null;
  maxCloseTemp?: number;
  maxOpenTemp?: number;
  maxTolerance?: number;
  minCloseTemp?: number;
  minOpenTemp?: number;
  minTolerance?: number;
  openOnRise: boolean = null;
  solidWorkBlockMasterId?: number;
  thermostatType?: string;
  thermostatTypeArray?: string[] = [];
}
