import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { PowerCordMaterialsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-power-cord-material',
  templateUrl: './manage-power-cord-material.component.html'
})
export class ManagePowerCordMaterialComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  powerCordMaterial: PowerCordMaterialsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManagePowerCordMaterialComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.powerCordMaterial = data;
  }

  ngOnInit() {
    this.powerCordMaterial = this.powerCordMaterial.id ? Object.assign({}, this.powerCordMaterial) : new PowerCordMaterialsMaster();
    this.powerCordMaterial.id ? (this.title = 'Update Power Cord Material') : (this.title = 'Add Power Cord Material');
  }

  updatePowerCordMaterial() {
    this.showLoader = true;
    this.powerCordMaterial.powerCordMaterialId = this.powerCordMaterial.powerCordMaterialId.toUpperCase();
    if (this.powerCordMaterial.id) {
      this.subscription.add(
        this.masterDataService.updatePowerCordMaterials(this.powerCordMaterial).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addPowerCordMaterials(this.powerCordMaterial).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
