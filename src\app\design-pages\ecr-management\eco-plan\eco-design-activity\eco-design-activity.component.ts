import {Component, Input, OnDestroy, OnInit} from '@angular/core';
import {User} from 'src/app/common-pages/users/user.model';
import {Subscription} from 'rxjs';
import {DesignActivityDto, DesignActivityMasterDataDto, EcoDto} from '../eco-plan.model';
import {SweetAlertService} from 'src/app/shared';
import {DatePipe} from '@angular/common';
import {EcoPlanService} from '../eco-plan.service';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {Values} from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-eco-design-activity',
  templateUrl: './eco-design-activity.component.html',
  styleUrls: ['./eco-design-activity.component.css']
})
export class EcoDesignActivityComponent implements OnInit, OnDestroy {

  designActivityDto: DesignActivityDto[];
  designActivityData: DesignActivityDto[];
  designACtivityMasterData: DesignActivityMasterDataDto[];
  users: User[];
  offset: number;
  subscription: Subscription = new Subscription();
  showLoader = false;
  isDataAvailable = false;
  updateInformQualityEmail = false;
  updateDesignActivityComments = false;
  _ecoDto: EcoDto = new EcoDto;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  designActivityDataChild: any;
  designActivityDataHeaterMan: any;
  designActivityDataDrawing: any;
  designActivityDataEpicor: any;

  get ecoDto() {
    return this._ecoDto;
  }

  @Input()
  set ecoDto(val) {
    this._ecoDto = val;
  }

  get _users() {
    return this.users;
  }

  @Input()
  set _users(users) {
    this.users = users;
  }

  constructor(private ecoPlanService: EcoPlanService, private datePipe: DatePipe, private sweetAlertService: SweetAlertService) {
  }

  async ngOnInit() {
    await this.getDesignActivityMasterData();
    await this.getEcoDesignActivity();
  }

  getMasterData() {
    this.getDesignActivityMasterData();
  }

  refreshResult() {
    this.getEcoDesignActivity();
  }

  getEcoDesignActivity() {
    return new Promise<void>((resolve, reject) => {
      this.subscription.add(this.ecoPlanService.getDesignActivityByEcoId(this.ecoDto.id).subscribe((res: DesignActivityDto[]) => {
        this.designActivityDto = res;
        this.createDesignActivity();
        resolve();
      }, () => {
        reject();
      }));
    });
  }

  getDesignActivityMasterData() {
    return new Promise<void>((resolve, reject) => {
      this.subscription.add(this.ecoPlanService.getDesignActivityMasterData().subscribe((designActivityMasterData: DesignActivityMasterDataDto[]) => {
        if (designActivityMasterData) {
          this.designACtivityMasterData = designActivityMasterData;
          this.designActivityData = this.groupBy(this.designACtivityMasterData, 'description');
          this.designActivityDataDrawing = this.groupBy(this.designActivityData['Drawing and Design Files'][0].childDesignActivity, 'title');
          this.designActivityDataHeaterMan = this.groupBy(this.designActivityData['Heater Man'][0].childDesignActivity, 'title');
          this.designActivityDataEpicor = this.groupBy(this.designActivityData['Epicor'][0].childDesignActivity, 'title');
          setTimeout(() => {
            this.isDataAvailable = true;
          }, 500);
          resolve();
        }
      }, error => {
        this.showLoader = false;
        reject();
      }));
    });
  }

  // Group by is a custom function which generates new array which is grouped by provided key of the array
  groupBy = (array, key) => {
    return array.reduce((result, currentValue) => {
      // If an array already present for key, push it to the array. Else create an array and push the object
      (result[currentValue[key]] = result[currentValue[key]] || []).push(currentValue);
      // Return the current iteration `result` value, this will be taken as next iteration `result` value and accumulate
      return result;
    }, {});
  };

  createDesignActivity() {
    if (this.designActivityData) {
      this.designActivityData['Drawing and Design Files'].filter((x) => {
        if (this.designActivityDto) {
          this.designActivityDto.filter((desAct) => {
            if (desAct.designActivityId === x.id) {
              x.designedBy = desAct.designedBy;
              x.designDate = new Date(desAct.designDate + 'T06:00:00.000Z');
              x.reviewedBy = desAct.reviewedBy;
              x.reviewDate = new Date(desAct.reviewDate + 'T06:00:00.000Z');
              x.ecoId = desAct.ecoId;
              x.ecoDesignActivityId = desAct.id;
              x.designActivityId = desAct.designActivityId;
              x.status = true;
              x.isChecked = true;
            }
          });
        }
      });
      this.designActivityDataDrawing['More Actions Required'].filter((x) => {
        if (this.designActivityDto) {
          this.designActivityDto.filter((desAct) => {
            if (desAct.designActivityId === x.id) {
              x.designDate = new Date(desAct.designDate + 'T06:00:00.000Z');
              x.designedBy = desAct.designedBy;
              x.reviewedBy = desAct.reviewedBy;
              x.reviewDate = new Date(desAct.reviewDate + 'T06:00:00.000Z');
              x.ecoId = desAct.ecoId;
              x.ecoDesignActivityId = desAct.id;
              x.designActivityId = desAct.designActivityId;
              x.status = true;
              x.isChecked = true;
            }
          });
        }
      });
      this.designActivityDataHeaterMan['More Actions Required'].filter((x) => {
        if (this.designActivityDto) {
          this.designActivityDto.filter((desAct) => {
            if (desAct.designActivityId === x.id) {
              x.designDate = new Date(desAct.designDate + 'T06:00:00.000Z');
              x.designedBy = desAct.designedBy;
              x.reviewedBy = desAct.reviewedBy;
              x.reviewDate = new Date(desAct.reviewDate + 'T06:00:00.000Z');
              x.ecoId = desAct.ecoId;
              x.ecoDesignActivityId = desAct.id;
              x.designActivityId = desAct.designActivityId;
              x.status = true;
              x.isChecked = true;
            }
          });
        }
      });
      this.designActivityDataEpicor['More Actions Required'].filter((x) => {
        if (this.designActivityDto) {
          this.designActivityDto.filter((desAct) => {
            if (desAct.designActivityId === x.id) {
              x.designDate = new Date(desAct.designDate + 'T06:00:00.000Z');
              x.designedBy = desAct.designedBy;
              x.reviewedBy = desAct.reviewedBy;
              x.reviewDate = new Date(desAct.reviewDate + 'T06:00:00.000Z');
              x.ecoId = desAct.ecoId;
              x.ecoDesignActivityId = desAct.id;
              x.designActivityId = desAct.designActivityId;
              x.status = true;
              x.isChecked = true;
            }
          });
        }
      });
      this.designActivityData['Heater Man'].filter((x) => {
        if (this.designActivityDto) {
          this.designActivityDto.filter((moreDesAct) => {
            if (moreDesAct.designActivityId === x.id) {
              x.designDate = new Date(moreDesAct.designDate + 'T06:00:00.000Z');
              x.designedBy = moreDesAct.designedBy;
              x.reviewedBy = moreDesAct.reviewedBy;
              x.reviewDate = new Date(moreDesAct.reviewDate + 'T06:00:00.000Z');
              x.ecoId = moreDesAct.ecoId;
              x.ecoDesignActivityId = moreDesAct.id;
              x.designActivityId = moreDesAct.designActivityId;
              x.status = true;
              x.isChecked = true;
            }
          });
        }
      });
      this.designActivityData['Epicor'].filter((e) => {
        if (this.designActivityDto) {
          this.designActivityDto.filter((moreDesAct) => {
            if (moreDesAct.designActivityId === e.id) {
              e.designDate = new Date(moreDesAct.designDate + 'T06:00:00.000Z');
              e.designedBy = moreDesAct.designedBy;
              e.reviewedBy = moreDesAct.reviewedBy;
              e.reviewDate = new Date(moreDesAct.reviewDate + 'T06:00:00.000Z');
              e.ecoId = moreDesAct.ecoId;
              e.ecoDesignActivityId = moreDesAct.id;
              e.designActivityId = moreDesAct.designActivityId;
              e.status = true;
              e.isChecked = true;
            }
          });
        }
      });
      this.designActivityData['Comments'].filter((e) => {
        if (this.designActivityDto) {
          this.designActivityDto.filter((moreDesAct) => {
            if (moreDesAct.designActivityId === e.id) {
              e.designDate = new Date(moreDesAct.designDate + 'T06:00:00.000Z');
              e.designedBy = moreDesAct.designedBy;
              e.reviewedBy = moreDesAct.reviewedBy;
              e.reviewDate = new Date(moreDesAct.reviewDate + 'T06:00:00.000Z');
              e.ecoId = moreDesAct.ecoId;
              e.ecoDesignActivityId = moreDesAct.id;
              e.comment=moreDesAct.comment;
              e.designActivityId = moreDesAct.designActivityId;
              e.status = true;
              e.isChecked = true;
            }
          });
        }
      });
    }
  }

  getChildDate(childId) {
    let childData = this.designActivityDto.filter(newId => newId.designActivityId === childId);
    if (childData.length > 0) {
      return childData[0].designDate;
    }
  }

  updateEcoDesignActivityMain(designActivityData: DesignActivityMasterDataDto, index) {
    this.showLoader = true;
    return new Promise<void>((resolve, reject) => {
      //set dates
      if(designActivityData.designedBy){
        if (!designActivityData.designDate) {
          designActivityData.designDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        } else {
          designActivityData.designDate = this.datePipe.transform(designActivityData.designDate, Values.dateFormat.formatHyphenTimeZone);
        }
      }
      if(designActivityData.reviewedBy){
        if (!designActivityData.reviewDate) {
          designActivityData.reviewDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        } else {
          designActivityData.reviewDate = this.datePipe.transform(designActivityData.reviewDate, Values.dateFormat.formatHyphenTimeZone);
        }
      }

      designActivityData.ecoId = this._ecoDto.id;
      designActivityData.designActivityId = designActivityData.id;
      if (designActivityData.status) {
        let obj;
        obj = Object.assign({}, designActivityData);
        obj.id = null;
        // create Design Activity
        this.subscription.add(this.ecoPlanService.createDesignActivityForEco(obj).subscribe(async (createResponse: any) => {
          this.designACtivityMasterData[index].newEcrId = createResponse.id;
          this.showLoader = false;
          resolve();
        }, error => {
          this.showLoader = false;
          obj.status = false;
          reject();
        }));
      } else {
        // remove Design Activity
        this.subscription.add(this.ecoPlanService.deleteDesignActivityEco(designActivityData.id).subscribe(async () => {
          this.designActivityDto = this.designActivityDto.filter(e => e.designActivityId !== designActivityData.id);
          designActivityData.designedBy = null;
          designActivityData.designDate = null;
          designActivityData.reviewedBy = null;
          designActivityData.reviewDate = null;
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
          designActivityData.status = true;
        }));

      }
    });
  }

  updateEcoDesignActivityChild(designActivityData: DesignActivityMasterDataDto, index) {
    this.showLoader = true;
    return new Promise<void>((resolve, reject) => {
      designActivityData.ecoId = this._ecoDto.id;
      designActivityData.designActivityId = designActivityData.id;
      designActivityData.designDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
      designActivityData.reviewDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);

      if (designActivityData.status) {
        let obj;
        obj = Object.assign({}, designActivityData);
        obj.id = null;
        // create Design Activity
        this.subscription.add(this.ecoPlanService.createDesignActivityForEco(obj).subscribe(async (createResponse: any) => {
          designActivityData.ecoDesignActivityId = createResponse.id;
          this.designActivityDto[index].newEcrId = createResponse.id;
          designActivityData.id = createResponse.designActivityId;
          this.designActivityDto.push(createResponse);
          this.showLoader = false;
          resolve();
        }, error => {
          this.showLoader = false;
          obj.status = false;
          reject();
        }));
      } else {
        // remove Design Activity
        this.subscription.add(this.ecoPlanService.deleteDesignActivityEco(designActivityData.id).subscribe(async () => {
          this.designActivityDto = this.designActivityDto.filter(e => e.designActivityId !== designActivityData.id);
          designActivityData.designedBy = null;
          designActivityData.designDate = null;
          designActivityData.reviewedBy = null;
          designActivityData.reviewDate = null;
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
          designActivityData.status = true;
        }));

      }
    });
  }

  updateDesignActivity(designActivityData: DesignActivityMasterDataDto, index) {
    this.showLoader = true;
    if (!designActivityData.designDate) {
      designActivityData.designDate = new Date().toISOString();
    }
    if (designActivityData.status) {
      let obj;
      obj = Object.assign({}, designActivityData);
      obj.id = this.designACtivityMasterData[index].newEcrId;
      obj.designDate = new Date(designActivityData.designDate).toISOString();

      this.subscription.add(this.ecoPlanService.updateDesignActivityEco(obj).subscribe((res: any) => {
        this.designActivityDto.push(res);
        this.showLoader = false;
      }, error => {
        this.showLoader = false;
      }));
    }
  }

  updateReviewActivity(designActivityData: DesignActivityMasterDataDto, index) {
    this.showLoader = true;
    if (!designActivityData.reviewDate) {
      designActivityData.reviewDate = new Date().toISOString();
    }
    if (designActivityData.status) {
      let obj;
      obj = Object.assign({}, designActivityData);
      // obj.id = designActivityData.ecoDesignActivityId;
      obj.reviewDate = new Date(designActivityData.reviewDate).toISOString();
      obj.designDate = new Date(designActivityData.designDate).toISOString();
      obj.id = this.designACtivityMasterData[index].newEcrId;
      this.subscription.add(this.ecoPlanService.updateDesignActivityEco(obj).subscribe((res: any) => {
        this.showLoader = false;
      }, error => {
        this.showLoader = false;
      }));
    }
  }

  updateInformQuality(designActivityData: DesignActivityMasterDataDto) {
    // if we got inform quality in eco design activity dto we update else we will create
    this.showLoader = true;
    if (this.updateInformQualityEmail) {
      designActivityData.informDate = this.datePipe.transform(designActivityData.informDate, Values.dateFormat.formatHyphen);
      this.subscription.add(this.ecoPlanService.updateDesignActivityEco(designActivityData).subscribe((res) => {
        this.refreshResult();
        this.showLoader = false;
      }, error => {
        this.showLoader = false;
      }));
    } else {
      // set id null as we need to create inform quality for eco and this id will be masterdata id and api need that id as designActivityId and not as id
      designActivityData.designActivityId = designActivityData.id;
      designActivityData.ecoId = this._ecoDto.id;
      designActivityData.id = null;
      designActivityData.informDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphen);
      this.subscription.add(this.ecoPlanService.createDesignActivityForEco(designActivityData).subscribe((res) => {
        this.refreshResult();
        this.showLoader = false;
      }, error => {
        this.showLoader = false;
      }));
    }
  }

  updateComments(designActivityData: DesignActivityMasterDataDto) {
    // if we got comment in eco design activity dto we update else we will create
    this.showLoader = true;
    if (this.updateDesignActivityComments) {
      this.subscription.add(this.ecoPlanService.updateDesignActivityEco(designActivityData).subscribe((res) => {
        this.refreshResult();
        this.showLoader = false;
      }, error => {
        this.showLoader = false;
      }));
    } else {
      // set id null as we need to create inform quality for eco and this id will be masterdata id and api need that id as designActivityId and not as id
      designActivityData.designActivityId = designActivityData.id;
      designActivityData.ecoId = this._ecoDto.id;
      designActivityData.id = null;
      this.subscription.add(this.ecoPlanService.createDesignActivityForEco(designActivityData).subscribe((res) => {
        this.refreshResult();
        this.showLoader = false;
      }, error => {
        this.showLoader = false;
      }));
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
