<div class="sfl-loading" *ngIf="saveAllJacekts || showLoader || showNoJacket">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div fxLayout="column" fxLayoutAlign="space-between">

  <div class="less-peding" fxFlex.gt-lg="{{ _docList ? 100 : 100 }}" fxFlex.gt-md="{{ _docList ? 89 : 90.5 }}">
    <mat-card class="matcard-no-padding" fxLayout="column">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">

        <div fxFlex.gt-lg="50" fxFlex.gt-md="50" class="cust_fields" fxLayoutAlign="start center">
          <mat-form-field fxFlex.gt-lg="30" fxFlex.gt-md="30" appearance="outline" *ngIf="!isDesignEng">
            <mat-label>Jacket Group</mat-label>
            <mat-select placeholder="Jacket Group" (selectionChange)="filterJacketGroup($event.value)"
                        [(ngModel)]="filterJG">
              <mat-option *ngFor="let value of jacketGroups" [value]="value.id">
                {{ value?.name }}
              </mat-option>
              <mat-option value="All"> All</mat-option>
            </mat-select>
          </mat-form-field>&nbsp;&nbsp;&nbsp;&nbsp;
          <div fxFlex.gt-lg="70" fxFlex.gt-md="70" appearance="outline">
            <mat-label>Entry Method:
              <strong>{{
                  entryMethod === 'true'
                    ? 'True Dimensions'
                    : entryMethod === 'centerLine'
                      ? 'Centerline Dimensions'
                      : 'Entry Method Not Specified'
                }}</strong>
            </mat-label>
          </div>
        </div>
        <div fxLayoutAlign="end center">
          <button mat-raised-button color="warn" class="mr-2" *ngIf="isDesignEng" [disabled]="optJacketList.length === 0"
                  (click)="drawJacketDesign(true)">
            Generate Jacket Model
          </button>
          <button mat-raised-button color="warn" *ngIf="isDesignEng" [disabled]="optJacketList.length === 0"
                  (click)="drawJacketDesign(false)">
            Generate Part
          </button>
          &nbsp;&nbsp;&nbsp;
          <span *ngIf="isRevisionActive" (click)="viewHide()">
            <span *ngIf="visibility">
              <mat-icon class="open-doc" id="visibility">visibility</mat-icon>
            </span>
            <span *ngIf="!visibility">
              <mat-icon class="open-doc" id="notvisibility">visibility_off</mat-icon>
            </span> </span>

        </div>

      </div>
    </mat-card>
  </div>

  <div class="geo-card" fxLayout="row wrap" fxLayoutAlign="space-between">
    <div class="feature-class sidenav pr-10 fix-nav side-nav-sm">
      <div class="mb-10" fxLayoutAlign="center">
        <button *ngIf="isRevisionActive" mat-raised-button color="warn" [disabled]="optJacketList.length <= 0"
                type="submit" (click)="saveAllWork()" id="saveall">
          Save All
        </button>
      </div>
      <div *ngIf="isDoNotAdd" class="mb-10" fxLayoutAlign="center">
        <button *ngIf="isRevisionActive" mat-raised-button color="warn" type="submit" (click)="addJacket()">Add Jacket
        </button>
      </div>
      <div *ngIf="isDoNotAdd" class="mb-10" fxLayoutAlign="center">
        <button *ngIf="isRevisionActive" mat-raised-button color="warn" type="submit" (click)="confirmDeleteFeatures()"
                [disabled]="!isDisabledDelete">Delete Selected
        </button>
      </div>
      <div *ngIf="isDoNotAdd" class="mb-10" fxLayoutAlign="center">
        <button *ngIf="isRevisionActive" mat-raised-button color="warn" type="submit" (click)="moveSelectedFeatures()"
                [disabled]="!isDisabledDelete">Move Selected
        </button>
      </div>
      <mat-card fxLayout="column" class="features-list">
        <mat-card-title fxFlex fxLayoutAlign="center center" matTooltip="Add Features Using Alt+Num">Features
        </mat-card-title>
        <div *ngFor="let feats of fetList; index as i">
          <div fxLayout="column wrap" fxLayoutAlign="center center" class="features">
            <div>
              <mat-label class="open-doc" (click)="addFeatures(feats)" id="{{ feats.name }}"
                         matTooltip="Alt+ {{ feats.shortCut }}">{{
                  feats.name
                }}
              </mat-label>
            </div>
          </div>
          <hr/>
        </div>
      </mat-card>
    </div>

    <div *ngIf="optJacketList?.length > 0" class="geo-card cust_fields jackets-details" fxLayout="column wrap"
         fxFlex.gt-lg="{{ _docList ? 91 : 93 }}" fxFlex.gt-md="{{ _docList ? 87 : 89 }}" fxFlex.gt-sm="90"
         fxFlex.gt-xs="100">
      <div dragula="JACKETS" [(dragulaModel)]="optJacketList" [ngClass]="{ 'search-results': isEnable }" infinite-scroll
           [infiniteScrollDistance]="1" [infiniteScrollThrottle]="50" [immediateCheck]="true"
           (scrolled)="onScrollDown()"
           [scrollWindow]="false">
        <div *ngFor="let jacket of optJacketList; let i = index" class="mb-10">
          <mat-card class="dark-gray" [class.active]="jacketId === jacket.id" (click)="getid(jacket.id)">
            <div class="group-handle">
              <br/>
              <div fxLayout="row wrap" fxLayoutAlign="space-between center">
                <div fxFlex.gt-lg="10" fxFlex.gt-md="10">
                  <mat-form-field appearance="outline">
                    <mat-label>Jacket Name</mat-label>
                    <input maxlength="12" matInput placeholder="Jacket Name" [(ngModel)]="jacket.name"
                           (change)="setUpdatedJacket(jacket.id)"/>
                  </mat-form-field>
                </div>
                <div fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>Jacket Group</mat-label>
                    <mat-select placeholder="Jacket Group"
                                (selectionChange)="changeJacketGroup($event.value, jacket?.id)"
                                [(ngModel)]="jacket.jacketGroupId">
                      <mat-option *ngFor="let value of jacketGroups" [value]="value.id">
                        {{ value?.name }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
                <div *ngIf="!isDesignEng" fxFlex.gt-lg="6" fxFlex.gt-md="6">
                  <mat-form-field appearance="outline">
                    <mat-label>Type</mat-label>
                    <input matInput placeholder="Type" [(ngModel)]="jacket.jacketType"
                           (change)="setUpdatedJacket(jacket.id)"/>
                  </mat-form-field>
                </div>
                <div fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>Description</mat-label>
                    <input matInput placeholder="Description" [(ngModel)]="jacket.reference"
                           (change)="setUpdatedJacket(jacket.id)"/>
                  </mat-form-field>
                </div>
                <div fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                    <input matInput placeholder="Length" [(ngModel)]="jacket.length" type="number" name="length"
                           class="quantity" readonly/>
                    <div matSuffix>
                      <mat-icon class="open-doc" (click)="jacketLength(jacket)" matTooltip="Calculate length">cached
                      </mat-icon>
                    </div>
                  </mat-form-field>
                </div>
                <div *ngIf="!isDesignEng" fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>Customer PN</mat-label>
                    <input maxlength="15" matInput placeholder="Customer PN" [(ngModel)]="jacket.customerPN"
                           name="customerPnNumber"
                           (change)="setUpdatedJacket(jacket?.id)"/>
                  </mat-form-field>
                </div>
                <div *ngIf="!isDesignEng" fxFlex.gt-lg="23" fxFlex.gt-md="19" class="partnumberBox">
                  <input maxlength="18" class="quantity sfl-part-no" matInput placeholder="Part Number"
                         [(ngModel)]="jacket.partNumber" name="partNumber"
                         (focus)="setTempPartNumber(jacket?.partNumber)" (change)="validatePartNumber(i)"
                         [disabled]="jacket.partNumber" (keyup)="setPartSeqTextFieldWidth(jacket)"/>
                </div>
                <div *ngIf="isDesignEng" fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>G-Code</mat-label>
                    <input matInput placeholder="G-Code" [(ngModel)]="jacket.gCode" type="text" name="gCode"
                           (change)="setUpdatedJacket(jacket?.id)"/>
                  </mat-form-field>
                </div>
                <div *ngIf="isDesignEng" fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>M-Code</mat-label>
                    <input matInput placeholder="M-Code" [(ngModel)]="jacket.mCode" name="mCode" type="text"
                           (change)="setUpdatedJacket(jacket?.id)"/>
                  </mat-form-field>
                </div>
                <div *ngIf="isDesignEng" fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>Calc Wattag</mat-label>
                    <input matInput placeholder="Calc Wattag" [(ngModel)]="jacket.calcWatts" name="calcwattag"
                           type="text" readonly/>
                  </mat-form-field>
                </div>
                <div *ngIf="isDesignEng" fxFlex.gt-lg="13" fxFlex.gt-md="13">
                  <mat-form-field appearance="outline">
                    <mat-label>Wattag</mat-label>
                    <input matInput placeholder="Wattag" [(ngModel)]="jacket.watts" name="wattag" type="text"
                           (focus)="copyOldWattsValue(jacket?.watts)"
                           (change)="checkAndSetUpdatedJacket(jacket?.id, 'wattage')"/>
                  </mat-form-field>
                </div>
                <div *ngIf="!isDesignEng" fxFlex.gt-lg="15" fxFlex.gt-md="15">
                  <mat-form-field appearance="outline">
                    <mat-label>Notes</mat-label>
                    <input matInput placeholder="Notes" (change)="updateJacketIdList(jacket.id)"
                           [(ngModel)]="jacket.notes" name="notes"
                           type="text"/>
                  </mat-form-field>
                </div>
                <div *ngIf="isDesignEng" fxFlex.gt-lg="15" fxFlex.gt-md="15" class="notes-field">
                  <mat-form-field appearance="outline">
                    <mat-label>Notes</mat-label>
                    <input matInput placeholder="Notes" (change)="updateJacketIdList(jacket.id)"
                           [(ngModel)]="jacket.notes" name="notes"
                           type="text"/>
                  </mat-form-field>
                </div>
                <button (click)="viewJacketImage(jacket.imageUrl)">
                  <mat-icon *ngIf="jacket.imageUrl" class='image-jacket'>image</mat-icon>
                </button>
                <div *ngIf="isDesignEng" fxFlex.gt-lg="1" fxFlex.gt-md="1" class="action">
                  <mat-icon class="open-doc-action-design" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                </div>
                <div *ngIf="!isDesignEng" fxFlex.gt-lg="1" fxFlex.gt-md="1" class="action">
                  <mat-icon class="open-doc-action" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                </div>
                <div *ngIf="!isDesignEng" fxFlex.gt-lg="1" fxFlex.gt-md="1" class="action">
                  <mat-icon title="Expand" class="expand-action"
                            (click)="expand(i)">{{ hideme[i] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                  </mat-icon>
                </div>
                <div *ngIf="isDesignEng" fxFlex.gt-lg="1" fxFlex.gt-md="1" class="action">
                  <mat-icon title="Expand" class="expand-action-design"
                            (click)="expand(i)">{{ hideme[i] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                  </mat-icon>
                </div>
                <mat-menu #menu="matMenu" x-position="before">
                  <button *ngIf="isDesignEng" mat-menu-item (click)="deleteFeatures(jacket)">Delete Features</button>
                  <button mat-menu-item (click)="deleteJacket(jacket)">Delete Jacket</button>
                  <button *ngIf="!isDesignEng" mat-menu-item (click)="confirmDelete(jacket.id)">Delete Features</button>
                  <button *ngIf="!isDesignEng" mat-menu-item (click)="CopyJacket(jacket)">Copy</button>
                  <button mat-menu-item id="image" (click)="addJacketAttachment(jacket?.id)">Add Image</button>
                </mat-menu>
              </div>
            </div>
          </mat-card>
          <div dragula="FEATURES" [(dragulaModel)]="jacket.jacketFeaturesList" *ngIf="hideme[i]"
               style="min-height: 30px">
            <div class="empty-zone" *ngIf="jacket.jacketFeaturesList.length === 0">
              <p>Drag here to add features</p>
            </div>
            <div *ngFor="let feature of jacket.jacketFeaturesList; let i = index" id="features{{ i }}"
                 (click)="getid(jacket.id)">
              <form #featuresForm="ngForm">
                <div class="featlist" *ngIf="feature.featuresName === 'Straight'">
                  <mat-card class="angular-ui-tree-drag light-gray" fxLayout="row wrap" fxLayoutGap="5px"
                            fxLayoutAlign="start center">
                    <div fxFlex="3">
                      <mat-checkbox #confirmed color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Straight' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter</mat-label>
                        <input matInput placeholder="Diameter"  (focusout)="calculateFormula(feature,'straightDiameter')" [(ngModel)]="feature.straightDiameter"
                               name="Diameter"
                               [sflAutoFocus]="feature?.straightDiameter" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                        <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'straightLength')" [(ngModel)]="feature.straightLength"
                               name="Length"
                               [sflAutoFocus]="feature?.straightLength" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Flange'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="start center" fxLayoutGap="5px" class="light-gray">
                    <div fxFlex="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Flange' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter In</mat-label>
                        <input matInput placeholder="Diameter In"  (focusout)="calculateFormula(feature,'flangeDiameterIn')"
                               [sflAutoFocus]="feature?.flangeDiameterIn"
                               [feature]="feature?.featuresId" [(ngModel)]="feature.flangeDiameterIn" name="DiameterIn"
                               #DiameterIn
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter</mat-label>
                        <input matInput placeholder="Diameter"  (focusout)="calculateFormula(feature,'flangeDiameter')" [sflAutoFocus]="feature?.flangeDiameter"
                               [feature]="feature?.featuresId" [(ngModel)]="feature.flangeDiameter" name="Diameter"
                               #Diameter
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter Out</mat-label>
                        <input matInput placeholder="Diameter Out"  (focusout)="calculateFormula(feature,'flangeDiameterOut')"
                               [sflAutoFocus]="feature?.flangeDiameterOut"
                               [feature]="feature?.featuresId" name="DiameterOut"
                               [(ngModel)]="feature.flangeDiameterOut" name="DiameterOut"
                               #DiameterOut (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                        <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'flangeWidth')" [sflAutoFocus]="feature?.flangeWidth"
                               [feature]="feature?.featuresId" [(ngModel)]="feature.flangeWidth" name="Length" #Length
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Unistrut'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="start center" fxLayoutGap="5px" class="light-gray">
                    <div fxFlex="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Unistrut' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Pipe Diameter</mat-label>
                        <input matInput placeholder="Pipe Diameter"  (focusout)="calculateFormula(feature,'unistrutPipeDiameter')"
                               [(ngModel)]="feature.unistrutPipeDiameter"
                               name="PipeDiameter" [sflAutoFocus]="feature?.unistrutPipeDiameter"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Width</mat-label>
                        <input matInput placeholder="Width"  (focusout)="calculateFormula(feature,'unistrutWidth')" [(ngModel)]="feature.unistrutWidth"
                               name="Width"
                               [sflAutoFocus]="feature?.unistrutWidth" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Height</mat-label>
                        <input matInput placeholder="Height"  (focusout)="calculateFormula(feature,'unistrutHeight')" [(ngModel)]="feature.unistrutHeight"
                               name="Height"
                               [sflAutoFocus]="feature?.unistrutHeight" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                        <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'unistrutDepth')" [(ngModel)]="feature.unistrutDepth"
                               name="Length"
                               [sflAutoFocus]="feature?.unistrutDepth" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Direction</mat-label>
                        <input matInput placeholder="Direction"  (focusout)="calculateFormula(feature,'unistrutDirection')" [(ngModel)]="feature.unistrutDirection"
                               name="Direction"
                               [sflAutoFocus]="feature?.unistrutDirection" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Elbow'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="start center" fxLayoutGap="5px" class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Elbow' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter</mat-label>
                        <input matInput placeholder="Diameter"  (focusout)="calculateFormula(feature,'elbowDiameter')" [(ngModel)]="feature.elbowDiameter"
                               name="Diameter" (change)="calculateElbowLength(feature)"
                               [sflAutoFocus]="feature?.elbowDiameter"
                               [feature]="feature?.featuresId" (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Radius</mat-label>
                        <input matInput placeholder="Radius"   (focusout)="calculateFormula(feature,'elbowRadius')" [(ngModel)]="feature.elbowRadius"
                               name="Radius"
                               (change)="calculateElbowLength(feature)" [sflAutoFocus]="feature?.elbowRadius"
                               [feature]="feature?.featuresId" (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Angle</mat-label>
                        <input matInput placeholder="Angle" sflIsNumber (focusout)="calculateFormula(feature,'elbowAngle')" [(ngModel)]="feature.elbowAngle" name="Angle"
                               (change)="calculateElbowLength(feature)" [sflAutoFocus]="feature?.elbowAngle"
                               [feature]="feature?.featuresId" (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                        <input matInput placeholder="Length" [(ngModel)]="feature.elbowLength" name="Length"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')" readonly/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Direction</mat-label>
                        <input matInput placeholder="Direction"  (focusout)="calculateFormula(feature,'elbowDirection')" [(ngModel)]="feature.elbowDirection"
                               name="Direction" [sflAutoFocus]="feature?.elbowDirection" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="5">
                      <mat-checkbox color="warn" name="elbowCubed" [(ngModel)]="feature.elbowCubed"
                                    [value]="feature?.elbowCubed" (change)="setUpdatedJacket(jacket?.id)">Cubed
                      </mat-checkbox>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>

                <div class="featlist" *ngIf="feature.featuresName === 'Tee'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="start center" fxLayoutGap="5px" class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Tee' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field class="full-width" appearance="outline">
                        <mat-label>Way</mat-label>
                        <mat-select [(value)]="selected" [(ngModel)]="feature.teeWay" placeholder="way" name="way"
                                    (selectionChange)="setUpdatedJacket(jacket?.id)">
                          <mat-option *ngFor="let way of geometryWayValues" [sflAutoFocus]="feature?.teeWay"
                                      [feature]="feature?.featuresId" [value]="way?.id">
                            {{ way?.name }}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter In</mat-label>
                        <input matInput placeholder="Diameter In"  (focusout)="calculateFormula(feature,'teeDiameterIn')" [(ngModel)]="feature.teeDiameterIn"
                               name="DiameterIn" [sflAutoFocus]="feature?.teeDiameterIn" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter Branch 1</mat-label>
                        <input matInput placeholder="Diameter Branch 1"  (focusout)="calculateFormula(feature,'teeDiameterBranch1')"
                               [(ngModel)]="feature.teeDiameterBranch1" name="DiameterBranch1"
                               [sflAutoFocus]="feature?.teeDiameterBranch1" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Angle Branch 1</mat-label>
                        <input matInput placeholder="Angle Branch 1" sflIsNumber (focusout)="calculateFormula(feature,'teeAngleBranch1')" [(ngModel)]="feature.teeAngleBranch1"
                               name="AngleBranch1" [sflAutoFocus]="feature?.teeAngleBranch1"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter Branch 2</mat-label>
                        <input matInput placeholder="Diameter Branch 2"  (focusout)="calculateFormula(feature,'teeDiameterBranch2')"
                               [(ngModel)]="feature.teeDiameterBranch2" name="DiameterBranch2"
                               [sflAutoFocus]="feature?.teeDiameterBranch2" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Angle Branch 2</mat-label>
                        <input matInput placeholder="Angle Branch 2" sflIsNumber (focusout)="calculateFormula(feature,'teeAngleBranch2')" [(ngModel)]="feature.teeAngleBranch2"
                               name="AngleBranch2" [sflAutoFocus]="feature?.teeAngleBranch2"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="5">
                      <div appearance="outline" class="full-width-cube">
                        <mat-checkbox color="warn" name="teeCubed" [(ngModel)]="feature.teeCubed"
                                      value="{{ feature?.teeCubed }}" (change)="setUpdatedJacket(jacket?.id)">Cubed
                        </mat-checkbox>
                      </div>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width-notes">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>

                <div class="featlist" *ngIf="feature.featuresName === 'Reducer'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="start center" fxLayoutGap="5px" class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Reducer' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter In</mat-label>
                        <input matInput placeholder="Diameter In"  (focusout)="calculateFormula(feature,'reducerDiameterIn')" [(ngModel)]="feature.reducerDiameterIn"
                               name="DiameterIn" [sflAutoFocus]="feature?.reducerDiameterIn"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter Out</mat-label>
                        <input matInput placeholder="Diameter Out"  (focusout)="calculateFormula(feature,'reducerDiameterOut')" [(ngModel)]="feature.reducerDiameterOut"
                               name="DiameterOut" [sflAutoFocus]="feature?.reducerDiameterOut"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                        <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'reducerLength')" [(ngModel)]="feature.reducerLength"
                               name="Length" [sflAutoFocus]="feature?.reducerLength" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Port'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px"
                            class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Port' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Pipe Diameter</mat-label>
                        <input matInput placeholder="Pipe Diameter"  (focusout)="calculateFormula(feature,'pipeDiameter')" [(ngModel)]="feature.pipeDiameter"
                               name="Diameter" [sflAutoFocus]="feature?.pipeDiameter" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Port Diameter</mat-label>
                        <input matInput placeholder="Port Diameter"  (focusout)="calculateFormula(feature,'portDiameter')" [(ngModel)]="feature.portDiameter"
                               name="PortDiameter" [sflAutoFocus]="feature?.portDiameter"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Direction</mat-label>
                        <input matInput placeholder="Direction"  (focusout)="calculateFormula(feature,'portDirection')" [(ngModel)]="feature.portDirection"
                               name="Direction" [sflAutoFocus]="feature?.portDirection" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <!-- <div fxFlex.gt-lg="18" fxFlex.gt-md="18"></div>
                    <div fxFlex.gt-lg="18" fxFlex.gt-md="18"></div> -->
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Valve'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px"
                            class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Valve' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter In</mat-label>
                        <input matInput placeholder="Diameter In"  (focusout)="calculateFormula(feature,'valveDiameterIn')" [(ngModel)]="feature.valveDiameterIn"
                               name="DiameterIn" [sflAutoFocus]="feature?.valveDiameterIn"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter Out</mat-label>
                        <input matInput placeholder="Diameter Out"  (focusout)="calculateFormula(feature,'valveDiameterOut')" [(ngModel)]="feature.valveDiameterOut"
                               name="DiameterOut" [sflAutoFocus]="feature?.valveDiameterOut"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                        <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'valveLength')" [(ngModel)]="feature.valveLength"
                               name="Length"
                               [sflAutoFocus]="feature?.valveLength" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-checkbox color="warn" name="valveSingle" [(ngModel)]="feature.valveSingle"
                                    value="{{ feature?.valveSingle }}" (change)="setUpdatedJacket(jacket?.id)">
                        Single Valve
                      </mat-checkbox>
                    </div>
                    <div fxFlex="13">
                      <mat-checkbox color="warn" name="valveDouble" [(ngModel)]="feature.valveDouble"
                                    value="{{ feature?.valveDouble }}" (change)="setUpdatedJacket(jacket?.id)">
                        Double Valve
                      </mat-checkbox>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <!-- <div fxFlex.gt-lg="14" fxFlex.gt-md="14"></div> -->
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Other'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px"
                            class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Other' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Diameter</mat-label>
                        <input matInput placeholder="Diameter"  (focusout)="calculateFormula(feature,'otherDiameter')" [(ngModel)]="feature.otherDiameter"
                               name="Diameter" [sflAutoFocus]="feature?.otherDiameter" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Labor</mat-label>
                        <input matInput placeholder="Labor"  (focusout)="calculateFormula(feature,'otherLabor')" [(ngModel)]="feature.otherLabor" name="Labor"
                               [sflAutoFocus]="feature?.otherLabor" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                        <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'otherLength')" [(ngModel)]="feature.otherLength"
                               name="Length"
                               [sflAutoFocus]="feature?.otherLength" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Material Cost</mat-label>
                        <input matInput placeholder="Material Cost"  (focusout)="calculateFormula(feature,'otherMaterialCost')" [(ngModel)]="feature.otherMaterialCost"
                               name="MaterialCost" [sflAutoFocus]="feature?.otherMaterialCost"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Rectangular'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px"
                            class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Rectangular' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Length</mat-label>
                        <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'rectangleLength')" [(ngModel)]="feature.rectangleLength"
                               name="rectangleLength" [sflAutoFocus]="feature?.rectangleLength"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Width</mat-label>
                        <input matInput placeholder="Width"  (focusout)="calculateFormula(feature,'rectangleWidth')" [(ngModel)]="feature.rectangleWidth"
                               name="rectangleWidth" [sflAutoFocus]="feature?.rectangleWidth"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Cut Out QTY</mat-label>
                        <input matInput placeholder="Cut Out QTY"  (focusout)="calculateFormula(feature,'rectangleCutOutQty')" [(ngModel)]="feature.rectangleCutOutQty"
                               name="rectangleCutOutQty" [sflAutoFocus]="feature?.rectangleCutOutQty"
                               [feature]="feature?.featuresId" (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Attachement Length</mat-label>
                        <input matInput placeholder="Attachement Length"  (focusout)="calculateFormula(feature,'rectangleAttachmentLength')"
                               [(ngModel)]="feature.rectangleAttachmentLength" name="rectangleAttachmentLength"
                               [sflAutoFocus]="feature?.rectangleAttachmentLength" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Circular'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px"
                            class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Circular' }}
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Radius of Curve</mat-label>
                        <input matInput placeholder="Radius of Curve"  (focusout)="calculateFormula(feature,'circleRadiusOfCurve')"
                               [(ngModel)]="feature.circleRadiusOfCurve" name="circleRadiusOfCurve"
                               [sflAutoFocus]="feature?.circleRadiusOfCurve" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Angle of Curve</mat-label>
                        <input matInput placeholder="Angle of Curve"  (focusout)="calculateFormula(feature,'circleAngleOfCurve')"
                               [(ngModel)]="feature.circleAngleOfCurve"
                               name="circleAngleOfCurve" [sflAutoFocus]="feature?.circleAngleOfCurve"
                               [feature]="feature?.featuresId" (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Cut Out QTY</mat-label>
                        <input matInput placeholder="Cut Out QTY "  (focusout)="calculateFormula(feature,'circleCutOutQty')" [(ngModel)]="feature.circleCutOutQty"
                               name="circleCutOutQty" [sflAutoFocus]="feature?.circleCutOutQty"
                               [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Attachment Length</mat-label>
                        <input matInput placeholder="Attachment Length"  (focusout)="calculateFormula(feature,'circleAttachmentLength')"
                               [(ngModel)]="feature.circleAttachmentLength" name="circleAttachmentLength"
                               [sflAutoFocus]="feature?.circleAttachmentLength" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id)"/>
                      </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Notes</mat-label>
                        <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                               [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                               (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                      </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Removable VCR'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px" class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature.featuresName ? feature.featuresName : 'Removable VCR' }}
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Diameter In</mat-label>
                      <input matInput placeholder="Diameter In"  (focusout)="calculateFormula(feature,'removableVcrDiameterIn')"
                             [sflAutoFocus]="feature?.removableVcrDiameterIn" [feature]="feature?.featuresId"
                             [(ngModel)]="feature.removableVcrDiameterIn" name="DiameterIn" #DiameterIn
                             (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                    </mat-form-field>
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Diameter</mat-label>
                      <input matInput placeholder="Diameter"  (focusout)="calculateFormula(feature,'removableVcrDiameter')" [sflAutoFocus]="feature?.removableVcrDiameter"
                             [feature]="feature?.featuresId" [(ngModel)]="feature.removableVcrDiameter" name="Diameter"
                             #Diameter (change)="setUpdatedJacket(jacket?.id)"/>
                    </mat-form-field>
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Diameter Out</mat-label>
                      <input matInput placeholder="Diameter Out"  (focusout)="calculateFormula(feature,'removableVcrDiameterOut')"
                             [sflAutoFocus]="feature?.removableVcrDiameterOut" [feature]="feature?.featuresId"
                             name="DiameterOut" [(ngModel)]="feature.removableVcrDiameterOut" name="DiameterOut"
                             #DiameterOut
                             (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                    </mat-form-field>
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                      <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'removableVcrWidth')" [sflAutoFocus]="feature?.removableVcrWidth"
                             [feature]="feature?.featuresId" [(ngModel)]="feature.removableVcrWidth" name="Length"
                             #Length
                             (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                    </mat-form-field>
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Notes</mat-label>
                      <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                             [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                             (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                    </mat-form-field>
                    </div>
                    <div fxFlex="13">
                      <mat-checkbox color="warn" name="vcrOnlySection" [(ngModel)]="feature.vcrOnlySection"
                                    [value]="feature?.vcrOnlySection" (change)="setUpdatedJacket(jacket?.id)">VCR Only
                        Section
                      </mat-checkbox>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
                <div class="featlist" *ngIf="feature.featuresName === 'Removable Section'">
                  <mat-card fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="5px" class="light-gray">
                    <div fxFlex.gt-lg="3" fxFlex.gt-md="3">
                      <mat-checkbox color="warn" (click)="$event.stopPropagation()"
                                    [checked]="selection.isSelected(feature)"
                                    (change)="onSelectedFeature($event, feature)">
                        {{ feature.featureId }}
                      </mat-checkbox>
                    </div>
                    <div fxFlex="7">
                      {{ feature?.featuresName ? feature?.featuresName : 'Removable Section' }}
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Length ({{ measureUnit ? measureUnit : '' }})</mat-label>
                      <input matInput placeholder="Length"  (focusout)="calculateFormula(feature,'removableLength')" [(ngModel)]="feature.removableLength"
                             name="removableLength" [sflAutoFocus]="feature?.removableLength"
                             [feature]="feature?.featuresId"
                             (change)="setUpdatedJacket(jacket?.id)"/>
                    </mat-form-field>
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Diameter ({{ measureUnit ? measureUnit : '' }})</mat-label>
                      <input matInput placeholder="Diameter"  (focusout)="calculateFormula(feature,'removableDiameter')" [(ngModel)]="feature.removableDiameter"
                             name="circleCutOutQty" [sflAutoFocus]="feature?.removableDiameter"
                             [feature]="feature?.featuresId" (change)="setUpdatedJacket(jacket?.id , 'dims')"/>
                    </mat-form-field>
                    </div>
                    <div fxFlex="13">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Notes</mat-label>
                      <input matInput placeholder="Notes" [(ngModel)]="feature.notes" name="Notes"
                             [sflAutoFocus]="feature?.notes" [feature]="feature?.featuresId"
                             (change)="setUpdatedJacket(jacket?.id, 'dims')"/>
                    </mat-form-field>
                    </div>
                    <button (click)="viewImage(feature.imageUrl)" fxFlex="auto">
                      <mat-icon *ngIf="feature.imageUrl" class='image'>image</mat-icon>
                    </button>
                    <div fxFlex.gt-lg="1" fxFlex.gt-md="1">
                      <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
                    </div>
                    <mat-menu #menu="matMenu" x-position="before">
                      <button mat-menu-item id="delete" (click)="deleteJacketFeature(feature, jacket)">Delete</button>
                      <button mat-menu-item id="copy" (click)="copyFeatures(feature, jacket)">Copy</button>
                      <button mat-menu-item id="image" (click)="addAttachment(feature?.id)">Add Image</button>
                    </mat-menu>
                  </mat-card>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="optJacketList?.length === 0 && !showLoader && !isDesignEng && !showNoJacket" class="no-jackets">
      <div class="details-box text-center" fxFlex.gt-lg="98" fxFlex.gt-md="98" fxFlex.gt-sm="98" fxFlex.gt-xs="100">
        <h1>
          No jackets available, click
          <button *ngIf="isRevisionActive" mat-raised-button color="warn" type="submit" (click)="addJacket()">Add
            Item
          </button>
          to add new
          jacket.
        </h1>
      </div>
    </div>
  </div>
</div>
