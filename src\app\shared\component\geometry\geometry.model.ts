export class JacketGroup {
  constructor(public id?: number, public name?: string) {
  }
}

export class Jackets {
  constructor(
    public id?: number,
    public jacketGroupId?: number,
    public name?: string,
    public jacketType?: string,
    public length?: number,
    public gCode?: number,
    public mCode?: number,
    public jacketFeatureList?: Features[]
  ) {
  }
}

export class Features {
  constructor(
    public circleCutoutDiameter?: number,
    public circleDiameter?: number,
    public circleRadiusOfCurve?: number,
    public circleAngleOfCurve?: number,
    public circleCutOutQty?: number,
    public circleAttachmentLength?: number,
    public elbowAngle?: number,
    public elbowCubed?: boolean,
    public elbowDiameter?: number,
    public elbowLength?: number,
    public elbowDirection?: number,
    public elbowRadius?: number,
    public featureIndex?: number,
    public featuresId?: number,
    public featuresName?: string,
    public flangeDiameterIn?: number,
    public flangeDiameter?: number,
    public flangeDiameterOut?: number,
    public flangeWidth?: number,
    public id?: number,
    public jacketId?: number,
    public notes?: string,
    public otherDiameter?: number,
    public otherLabor?: number,
    public otherLength?: number,
    public otherMaterialCost?: number,
    public pipeDiameter?: number,
    public imageUrl?: string,
    public portCubed?: boolean,
    public portDirection?: number,
    public portDiameter?: number,
    public rectangleLength?: number,
    public rectangleWidth?: number,
    public rectangleCutOutQty?: number,
    public rectangleAttachmentLength?: number,
    public rectangleCutoutLength?: number,
    public rectangleCutoutWidth?: number,
    public reducerCubed?: boolean,
    public reducerDiameterIn?: number,
    public reducerDiameterOut?: number,
    public reducerLength?: number,
    public straightDiameter?: number,
    public straightLength?: number,
    public teeAngleBranch1?: number,
    public teeAngleBranch2?: number,
    public teeCubed?: boolean,
    public teeDiameterBranch1?: number,
    public teeDiameterBranch2?: number,
    public teeDiameterIn?: number,
    public unistrutDepth?: number,
    public unistrutHeight?: number,
    public unistrutPipeDiameter?: number,
    public unistrutWidth?: number,
    public unistrutDirection?: number,
    public valveCubed?: boolean,
    public valveDiameterIn?: number,
    public valveDiameterOut?: number,
    public valveDouble?: boolean,
    public valveLength?: number,
    public valveSingle?: boolean,
    public weldmentPartNumber?: string,
    public weldmentLength?: number,
    public weldmentDiameter?: number,
    public vcr?: string,
    public removableLength?: number,
    public removableDiameter?: number,
    public removableVcrDiameterIn?: number,
    public removableVcrDiameterOut?: number,
    public removableVcrWidth?: number,
    public vcrOnlySection?: boolean,
    public removableVcrLength?: boolean,
    public teeWay?: string,
  ) {
    this.circleCutoutDiameter = null;
    this.circleDiameter = null;
    this.circleRadiusOfCurve = null;
    this.circleAngleOfCurve = null;
    this.circleCutOutQty = null;
    this.circleAttachmentLength = null;
    this.elbowAngle = null;
    this.elbowCubed = null;
    this.elbowDiameter = null;
    this.elbowLength = null;
    this.elbowDirection = null;
    this.elbowRadius = null;
    this.featureIndex = null;
    this.featuresId = null;
    this.featuresName = null;
    this.flangeDiameterIn = null;
    this.flangeDiameter = null;
    this.flangeDiameterOut = null;
    this.flangeWidth = null;
    this.id = null;
    this.jacketId = null;
    this.notes = null;
    this.otherDiameter = null;
    this.otherLabor = null;
    this.otherLength = null;
    this.otherMaterialCost = null;
    this.pipeDiameter = null;
    this.imageUrl = null;
    this.portCubed = null;
    this.portDirection = null;
    this.portDiameter = null;
    this.rectangleLength = null;
    this.rectangleWidth = null;
    this.rectangleCutOutQty = null;
    this.rectangleAttachmentLength = null;
    this.rectangleCutoutLength = null;
    this.rectangleCutoutWidth = null;
    this.reducerCubed = null;
    this.reducerDiameterIn = null;
    this.reducerDiameterOut = null;
    this.reducerLength = null;
    this.straightDiameter = null;
    this.straightLength = null;
    this.teeAngleBranch1 = null;
    this.teeAngleBranch2 = null;
    this.teeCubed = null;
    this.teeDiameterBranch1 = null;
    this.teeDiameterBranch2 = null;
    this.teeDiameterIn = null;
    this.unistrutDepth = null;
    this.unistrutHeight = null;
    this.unistrutPipeDiameter = null;
    this.unistrutWidth = null;
    this.unistrutDirection = null;
    this.valveCubed = null;
    this.valveDiameterIn = null;
    this.valveDiameterOut = null;
    this.valveDouble = null;
    this.valveLength = null;
    this.valveSingle = null;
    this.weldmentPartNumber = null;
    this.weldmentLength = null;
    this.weldmentDiameter = null;
    this.vcr = null;
    this.removableLength = null;
    this.removableDiameter = null;
    this.removableVcrDiameterIn = null;
    this.removableVcrDiameterOut = null;
    this.removableVcrWidth = null;
    this.vcrOnlySection = null;
    this.removableVcrLength = null;
    this.teeWay = null;

  }
}

export class Fets {
  constructor(public id?: number, public name?: string, public shortCut?: string) {
  }
}

export class AddJackets {
  constructor(
    public content?: AddNewJackets[],
    public totalElements?: number,
    public totalPages?: number,
    public number?: number,
    public size?: number
  ) {
  }
}

export class MoveJacketFeaturesDTO {
  constructor(
    public centerLine?: boolean,
    public discount?: number,
    public jacketFeatureIdList?: [],
    public jacketGroupId?: number,
    public jacketId?: number,
    public newJacket?: boolean,
    public reference?: string,
    public revisionId?: number
  ) {
  }
}

export interface ViewCCDCModalData {
  jacketGroupId: number;
}

export class AddNewJackets {
  constructor(
    public revisionId?: number,
    public dxfRequest?: boolean,
    public briskHeatPN?: string,
    public customerPN?: string,
    public gCode?: string,
    public jacketGroupChanged?: boolean,
    public id?: number,
    public oldId?: number,
    public jacketGroupId?: number,
    public jacketIndex?: number,
    public jacketType?: string,
    public length?: number,
    public mCode?: string,
    public name?: string,
    public jacketCount?: number,
    public quotationId?: number,
    public reference?: string,
    public jacketGroupName?: string,
    public jacketFeaturesList?: Features[],
    public discount?: number,
    public notes?: number,
    public imageUrl?: string,
    public partFilePath?: string,
    public watts?: number,
    public calcWatts?: string,
    public tempPartNumber?: string,
    public tempSequenceNumber?: string,
    public fieldWidth?: string,
    public showDash?: boolean,
    public dirty?: boolean,
    public repeat?: boolean,
    public facings?: string[],
    public insulations?: string[],
    public liners?: string[],
    public insulationThickness?: number,
    public partNumber?: string
  ) {
  }
}
