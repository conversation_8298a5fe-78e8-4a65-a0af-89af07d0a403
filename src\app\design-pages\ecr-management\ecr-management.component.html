<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
    class="sfl-global-spinner-loader"
    [mode]="mode"
    [color]="color"
    [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="sfl-ecr-management">
  <div class="filter less-peding">
    <mat-card>
      <div fxLayout="row wrap" fxLayoutGap="10px">
        <div class="sub-heder" fxLayoutAlign="start center" fxLayoutGap="10px" fxFlex>
          <span><strong>{{ title }}</strong></span>
        </div>
        <div fxLayoutAlign="end" fxLayoutGap="10px">
          <button mat-raised-button type="button" color="warn" [routerLink]="['/app-eng/dashboard']">App Engineering
            Dashboard
          </button>
          <button mat-raised-button type="button" color="warn" [routerLink]="['/design-eng/dashboard']">Design
            Engineering Dashboard
          </button>
          <button mat-raised-button type="button" color="warn" (click)="generateNewEcr()">New ECR</button>
          <button mat-raised-button type="button" color="warn" (click)="openEco()"
                  [disabled]="!ecrDto?.id">{{ btnSwitchView }}
          </button>

        </div>
      </div>
    </mat-card>
  </div>
  <div class="ecr-report" *ngIf="viewEcr">
    <div class="filter less-peding cust_fields">
      <mat-card class="mb-10">
        <form class="fields" #ecrFilterForm="ngForm">
          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20">
              <mat-label>ECR Number</mat-label>
              <input matInput placeholder="ECR #" [(ngModel)]="ecrSearchFilter.ecrNo" name="ecrNo" sflIsDecimal>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20">
              <mat-label>Status</mat-label>
              <mat-select placeholder="Status" [(ngModel)]="ecrSearchFilter.status" name="status">
                <mat-option *ngFor="let status of statuses" [value]="status.id">
                  {{ status?.status }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20">
              <mat-label>Requestor</mat-label>
              <mat-select placeholder="Requestor" [(ngModel)]="ecrSearchFilter.requestor" name="requestor">
                <mat-option *ngFor="let requestor of users" [value]="requestor?.id">
                  {{ requestor?.firstName }} {{ requestor?.lastName }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex.gt-lg="20" fxFlex.gt-md="20">
              <mat-label>Part Number</mat-label>
              <input matInput placeholder="Part Number" [(ngModel)]="ecrSearchFilter.partNumber" name="partNumber">
            </mat-form-field>
            <mat-actions-row fxLayoutAlign="end" fxLayoutGap="10px">
              <button mat-raised-button color="warn" (click)="addFilter()">Search</button>
              <button mat-raised-button color="warn" (click)="resetFilter()">Reset</button>
            </mat-actions-row>
          </div>
        </form>
      </mat-card>
    </div>
    <div class="less-peding">
      <mat-card class="cust_table" style="margin-top:-10px">
        <table
          class="w-auto"
          aria-hidden="true"
          mat-table
          matSort
          matSortDisableClear
          [dataSource]="ecrDataSource"
          (matSortChange)="getSorting($event)">
          <ng-container matColumnDef="ecrNo">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> ECR #</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20">
              <span class="mobile-label">ECR #:</span>
              <span>{{ element?.ecrNo }}</span>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="ecrStatusId">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Status</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10">
              <span class="mobile-label">Status:</span>
              <span *ngFor="let status of statuses; let i = index">
                  {{ status.id === element?.ecrStatusId ? status.status : '' }}
                </span>

            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="ecrDate">
            <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Date</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20">
              <span class="mobile-label">Date:</span>
              {{ element?.updatedDate | date }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="submitted">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Submitted</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10">
              <span class="mobile-label">Submitted:</span>
              {{ element?.ecrDate | date }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="requestor">
            <mat-header-cell *matHeaderCellDef fxFlex="20"> Requestor</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20">
              <span class="mobile-label">Requestor:</span>
              <span *ngFor="let requestor of users; let i = index">
                  {{ requestor.id === element?.requestorId ? requestor.firstName + ' ' + requestor.lastName : '' }}
                </span>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="review">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Review</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10">
              <span class="mobile-label">Review:</span>
              {{ element?.designReview }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="20"> Part Number(s)</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="20">
              <span class="mobile-label">Part Number(s):</span>
              <span class="text-ellipsis">{{ element?.ecrPartNumbers | arrayToCSV : 'partNumber' }} </span>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="description">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Description</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="15">
              <span class="mobile-label">Description:</span>
              <span class="text-ellipsis">{{ element?.description }}</span>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
            <mat-cell *matCellDef="let element" fxFlex="10">
              <span class="mobile-label">Action:</span>
              <mat-icon class="open-doc" matTooltip="Delete Operation" (click)="deleteOperation(element)">delete
              </mat-icon>
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns;" class="open-doc" (click)="openECR(row, myElem)"
                   [ngClass]="{ 'selected': selectedRow == row.id }"></mat-row>
        </table>
        <div *ngIf="ecrDataSource.data.length === 0 && !showLoader">
          <span class="no-records">No data found</span>
        </div>
        <mat-paginator
          [length]="length"
          [pageSizeOptions]="pageSizeOptions"
          [pageSize]="pageSize"
          [pageIndex]="pageIndex"
          (page)="getPagination($event)"
          showFirstLastButtons
        >
        </mat-paginator>
      </mat-card>
    </div>
    <div *ngIf="isEcrSelected" class="filter less-peding cust_fields">
      <mat-card>
        <div fxFlex fxLayout="row" fxLayoutGap="20px">
          <div fxFlex.gt-lg="60" fxFlex.gt-md="60" fxFlex.gt-xs="100">
            <div fxLayout="row wrap" class="mb-10 cust_fields">
              <div fxFlex fxLayoutAlign="space-between">
                <mat-form-field appearance="outline" fxFlex.gt-lg="45" fxFlex.gt-md="45" fxFlex.gt-sm="45"
                                fxFlex.gt-xs="50">
                  <mat-label>ECR Number</mat-label>
                  <input matInput placeholder="ECR #" [(ngModel)]="ecrDto.ecrNo" name="ecrNumber" (change)="updateEcr()"
                         sflIsDecimal>
                </mat-form-field>
                <mat-form-field appearance="outline" fxFlex.gt-lg="45" fxFlex.gt-md="45" fxFlex.gt-sm="45"
                                fxFlex.gt-xs="50">
                  <mat-label>Status</mat-label>
                  <mat-select placeholder="Status" [(ngModel)]="ecrDto.ecrStatusId" name="status"
                              (ngModelChange)="updateEcr(true)">
                    <mat-option *ngFor="let allstatus of statuses" [value]="allstatus.id">
                      {{ allstatus?.status }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            <div class="mb-10 cust_fields">
              <div fxFlex fxLayout="row wrap">
                <span fxFlex>Part Number(s)</span>
                <button class="add-new-icon-button" mat-icon-button fxFlexAlign="end" color="warn"
                        (click)="addPartNumber()" matTooltip="Add Part Number" matTooltipPosition="above">
                  <mat-icon>add</mat-icon>
                </button>
              </div>
            </div>
            <div class="mb-10 cust_fields sfl-mat-list-ecr mat-list-selected-pn">
              <table
                class="w-auto"
                aria-hidden="true"
                mat-table
                [dataSource]="ecrDto.ecrPartNumbers" style="margin: 0px">
                <ng-container matColumnDef="partNo">
                  <mat-header-cell style="padding-left: 10px" *matHeaderCellDef fxFlex="25">   Part Number   </mat-header-cell>
                  <mat-cell style="padding-left: 10px" *matCellDef="let element" fxFlex="25">
                    <span>{{ element?.partNumber }}</span>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="syncStatus">
                  <mat-header-cell *matHeaderCellDef fxFlex="20"> Sync Status </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="20">
                    <div *ngIf="element.syncStatus">
                    <span class="bom-sync-status"
                          matTooltip="{{element.syncStatus}}"
                          [ngClass]="(element.syncStatus.toLowerCase().includes('failed') ? 'sync-error' : 'bom-sync-status')">{{ element.syncStatus }}</span>
                    </div>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="refresh">
                  <mat-header-cell *matHeaderCellDef fxFlex="4">  </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="4">
                    <mat-icon (click)="getECRPartSyncStatus(element)">refresh</mat-icon>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="usSync">
                  <mat-header-cell *matHeaderCellDef fxFlex="4"> US</mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="4">
                    <mat-checkbox color="warn" class="checkbox-align"
                                  [ngClass]="{ 'mat-checkbox-inner-container': element.usOnHold == null }"
                                  matTooltip="Part number: {{ element?.partNumber }} , On-Hold Response Code: {{ element?.usHoldReasonCode }}, On-Hold Reason: {{ element?.usHoldComments }} "
                                  name="onhold" [(ngModel)]="element.usOnHold" readonly="true"
                                  [disabled]="true"></mat-checkbox>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="vietnamSync">
                  <mat-header-cell *matHeaderCellDef fxFlex="4"> VN</mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="4">
                    <mat-checkbox color="warn" class="checkbox-align"
                                  [ngClass]="{ 'mat-checkbox-inner-container': element.vietnamOnHold == null }"
                                  matTooltip="Part number: {{ element?.partNumber+'V' }} , On-Hold Response Code: {{ element?.vietnamHoldReasonCode }}, On-Hold Reason: {{ element?.vietnamHoldComments }} "
                                  name="onhold" [(ngModel)]="element.vietnamOnHold" readonly="true"
                                  [disabled]="true"></mat-checkbox>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="costaRicaSync">
                  <mat-header-cell *matHeaderCellDef fxFlex="4"> CR</mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="4">
                    <mat-checkbox color="warn"
                                  class="checkbox-align"
                                  [ngClass]="{ 'mat-checkbox-inner-container': element.costaRicaOnHold == null }"
                                  matTooltip="Part number: {{ element?.partNumber+'R' }} , On-Hold Response Code: {{ element?.costaRicaHoldReasonCode }}, On-Hold Reason: {{ element?.costaRicaHoldComments }} "
                                  name="onhold" [(ngModel)]="element.costaRicaOnHold" readonly="true"
                                  [disabled]="true"></mat-checkbox>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="usvSync">
                  <mat-header-cell *matHeaderCellDef fxFlex="4"> US-V</mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="4">
                    <mat-checkbox color="warn" class="checkbox-align"
                                  [ngClass]="{ 'mat-checkbox-inner-container': element.usVietnamOnHold == null }"
                                  matTooltip="Part number: {{ element?.partNumber+'V' }} , On-Hold Response Code: {{ element?.usVietnamHoldReasonCode }}, On-Hold Reason: {{ element?.usVietnamHoldComments }} "
                                  name="onhold" [(ngModel)]="element.usVietnamOnHold" readonly="true"
                                  [disabled]="true"></mat-checkbox>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="uscSync">
                  <mat-header-cell *matHeaderCellDef fxFlex="4"> US-R</mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="4">
                    <mat-checkbox color="warn" class="checkbox-align"
                                  [ngClass]="{ 'mat-checkbox-inner-container': element.usCostaRicaOnHold == null }"
                                  matTooltip="Part number: {{ element?.partNumber+'R' }} , On-Hold Response Code: {{ element?.usCostaRicaHoldReasonCode }}, On-Hold Reason: {{ element?.usCostaRicaHoldComments }} "
                                  name="onhold" [(ngModel)]="element.usCostaRicaOnHold" readonly="true"
                                  [disabled]="true"></mat-checkbox>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="setOnHold">
                  <mat-header-cell *matHeaderCellDef fxFlex="12">  </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="12">
                    <button class="custom-button" color="warn" (click)="syncOnhold(element,'OnHold')">Set
                      On-Hold
                    </button>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="clearHold">
                  <mat-header-cell *matHeaderCellDef fxFlex="12"></mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="12">
                    <button class="custom-button" color="warn" (click)="syncOnhold(element,'OnHoldClear')">
                      Remove Hold
                    </button>
                  </mat-cell>
                </ng-container>
                <ng-container matColumnDef="action">
                  <mat-header-cell *matHeaderCellDef fxFlex="6">  </mat-header-cell>
                  <mat-cell *matCellDef="let element" fxFlex="6">
                    <a (click)="confirmDelete(element.id, 'partNumber')">
                      <div class="open-doc" fxLayoutAlign="end">
                        <mat-icon>delete</mat-icon>
                      </div>
                    </a>
                  </mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="displayedECRPartColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedECRPartColumns;" class="open-doc"></mat-row>
              </table>
            </div>
            <div class="mb-10 cust_fields">
              <div fxFlex fxLayout="row wrap">
                <span fxFlex>Attachment(s)</span>
                <button class="add-new-icon-button" mat-icon-button fxFlexAlign="end" color="warn"
                        (click)="addAttachment(ecrDto.id)" matTooltip="Add Attachment" matTooltipPosition="above">
                  <mat-icon>add</mat-icon>
                </button>
              </div>
            </div>
            <div class="mb-10 cust_fields sfl-mat-list mat-list-selected-pn">
              <div fxLayout="row wrap" class="filename" *ngFor="let file of ecrDto.ecrAttachments;let i = index">
                <mat-icon>insert_drive_file</mat-icon>
                <h4 fxFlex class="open-doc" (click)="openDoc(file)">{{ file?.fileName }}</h4>
                <a (click)="confirmDelete(file.id, 'attachment')">
                  <div class="open-doc" fxLayoutAlign="end">
                    <mat-icon>delete</mat-icon>
                  </div>
                </a>
                <hr>
              </div>
            </div>
          </div>
          <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-xs="100">
            <div fxLayout="row wrap" class="mb-10 cust_fields">
              <mat-label>Description</mat-label>
              <textarea matInput placeholder="Description" [(ngModel)]="ecrDto.description" rows="16"
                        class="sfl-textarea" (change)="updateEcr()"></textarea>
            </div>
          </div>
          <div fxFlex.gt-lg="20" fxFlex.gt-md="20" fxFlex.gt-xs="100">
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>Review Level Required</mat-label>
                <mat-select placeholder="Review Level Required" [(ngModel)]="ecrDto.designReview" name="ecrRevLevel"
                            (ngModelChange)="updateEcr()">
                  <mat-option *ngFor="let level of reviewLevel" [value]="level">
                    {{ level }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>Designer</mat-label>
                <mat-select placeholder="Designer" [(ngModel)]="ecrDto.designer" name="ecrDesigner"
                            (ngModelChange)="updateEcr()">
                  <mat-option *ngFor="let user of dataUsers" [value]="user?.id">
                    {{ user?.firstName }} {{ user?.lastName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>Level 1 Reviewer</mat-label>
                <mat-select placeholder="Level 1 Reviewer" [(ngModel)]="ecrDto.level1Reviewer" name="ecrLvl1Rev"
                            (ngModelChange)="updateEcr()">
                  <mat-option *ngFor="let user of dataUsers" [value]="user?.id">
                    {{ user?.firstName }} {{ user?.lastName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>E&BOM</mat-label>
                <mat-select placeholder="E&BOM" [(ngModel)]="ecrDto.eBom" name="ecrEBom" (ngModelChange)="updateEcr()">
                  <mat-option *ngFor="let user of dataUsers" [value]="user?.id">
                    {{ user?.firstName }} {{ user?.lastName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>Final Reviewer</mat-label>
                <mat-select placeholder="Final Reviewer" [(ngModel)]="ecrDto.finalReviewer" name="ecrFinalRev"
                            (ngModelChange)="updateEcr()">
                  <mat-option *ngFor="let user of dataUsers" [value]="user?.id">
                    {{ user?.firstName }} {{ user?.lastName }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" class="mb-20 cust_fields">
              <mat-form-field appearance="outline">
                <mat-label>Color</mat-label>
                <mat-select placeholder="Color" [(ngModel)]="ecrDto.color" name="color" [ngStyle]="{color: color}"
                            (ngModelChange)="updateEcr()">
                  <mat-option *ngFor="let color of colors" [value]="color">{{ color }}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </div>
      </mat-card>
    </div>
  </div>
  <div class="eco-report" *ngIf="viewEco">
    <sfl-eco-plan [ecrDto]="ecrDto" [_users]="users"></sfl-eco-plan>
  </div>
</div>
