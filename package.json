{"name": "briskheat", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^7.2.16", "@angular/cdk": "^7.3.7", "@angular/common": "^7.2.16", "@angular/compiler": "^7.2.16", "@angular/core": "^7.2.16", "@angular/flex-layout": "^7.0.0-beta.24", "@angular/forms": "^7.2.16", "@angular/http": "^7.2.16", "@angular/material": "^7.3.7", "@angular/platform-browser": "^7.2.16", "@angular/platform-browser-dynamic": "^7.2.16", "@angular/router": "^7.2.16", "@ngx-loading-bar/router": "^2.1.0", "@sentry/browser": "^4.6.6", "bootstrap": "^4.6.0", "core-js": "^2.5.4", "dragula": "^3.7.2", "hammerjs": "^2.0.8", "intl": "^1.2.5", "jquery": "^3.6.0", "lodash": "^4.17.21", "ng2-dragula": "^2.1.1", "ng2-validation": "^4.2.0", "ngx-cookie": "^4.1.2", "ngx-cookie-service": "^1.0.10", "ngx-infinite-scroll": "^6.9.1", "ngx-perfect-scrollbar": "^6.3.1", "ngx-virtual-scroller": "^3.0.3", "ngx-webstorage": "^2.0.1", "rxjs": "6.6.7", "rxjs-compat": "6.6.7", "screenfull": "^3.3.3", "sweetalert2": "^7.33.1", "tslib": "^1.14.1", "zone.js": "^0.8.29"}, "devDependencies": {"@angular-devkit/build-angular": "~0.13.0", "@angular/cli": "~7.3.10", "@angular/compiler-cli": "^7.2.16", "@angular/language-service": "^7.2.16", "@types/jasmine": "~2.8.6", "@types/jasminewd2": "~2.0.3", "@types/lodash": "^4.14.117", "@types/node": "~8.9.4", "codelyzer": "~4.2.1", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~1.7.1", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.0", "karma-jasmine": "~1.1.1", "karma-jasmine-html-reporter": "^0.2.2", "protractor": "~5.3.0", "sass": "^1.56.1", "ts-node": "~5.0.1", "tslint": "~5.9.1", "typescript": "~3.2.4"}}