import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { ProductGroupMaster } from '../../masterdata-management.model';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'app-manage-product-group',
  templateUrl: './manage-product-group.component.html',
  styleUrls: ['./manage-product-group.component.css'],
})
export class ManageProductGroupComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  productGroups: ProductGroupMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageProductGroupComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
  ) {
    this.productGroups = data;
  }

  ngOnInit() {
    this.productGroups = this.productGroups.id ? Object.assign({}, this.productGroups) : new ProductGroupMaster();
    this.productGroups.id ? (this.title = 'Update Product Group') : (this.title = 'Add Product Group');
  }

  updateProductGroup() {
    this.showLoader = true;
    if (this.productGroups.id) {
      this.subscription.add(
        this.masterDataService.updateProductGroup(this.productGroups).subscribe(
          (res) => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          (error) => {
            this.showLoader = false;
          },
        ),
      );
    } else {
      this.subscription.add(
        this.masterDataService.addProductGroup(this.productGroups).subscribe(
          (res) => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          (error) => {
            this.showLoader = false;
          },
        ),
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
