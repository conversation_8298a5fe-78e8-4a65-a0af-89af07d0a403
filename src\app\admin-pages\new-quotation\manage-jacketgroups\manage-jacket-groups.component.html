<h2 mat-dialog-title>Manage Jacket Groups
  <hr>
</h2>
<div class="cust_table">
  <mat-table [dataSource]="dataSource">
    <ng-container matColumnDef="jacket">
      <mat-header-cell *matHeaderCellDef fxFlex="80"> Jacket Groups </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="80">{{element?.name}}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="action">
      <mat-header-cell *matHeaderCellDef fxFlex="20"> Action </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="20">
        <mat-icon class="open-doc" [matMenuTriggerFor]="menu">more_vert</mat-icon>
        <mat-menu class="menu" #menu="matMenu">
          <button mat-menu-item (click)="editJacketGroup(element)">
            <mat-icon>edit</mat-icon>
            <span>Rename</span>
          </button>
          <button mat-menu-item (click)="copyJacketGroup(element)">
            <mat-icon>file_copy</mat-icon>
            <span>Copy</span>
          </button>
          <button mat-menu-item (click)="deleteJacketGroup(element.id)">
            <mat-icon>delete</mat-icon>
            <span>Delete</span>
          </button>
        </mat-menu>
      </mat-cell>
    </ng-container>
    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
  </mat-table>
</div>
<div *ngIf="addNew || isCopy" class="mt-10">
  <mat-form-field>
    <input matInput placeholder="Jacket Group Name" name="jacketName" [(ngModel)]="jacketName" maxlength="200">
  </mat-form-field>
</div>
<hr>
<mat-dialog-actions>
  <button mat-raised-button type="submit" color="warn" *ngIf="!isSave && !isCopy" (click)="newJacket()">New</button>
  <button mat-raised-button type="submit" color="warn" *ngIf="isSave" (click)="saveJacket()">Save</button>
  <button mat-raised-button type="submit" color="warn" *ngIf="isCopy" (click)="copyJacket()">Save</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
