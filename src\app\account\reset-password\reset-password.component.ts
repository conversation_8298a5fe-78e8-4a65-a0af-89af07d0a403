import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ResetPasswordModel } from '../account.model';
import { AccountService } from '../account.service';
import { SnakbarService, Messages } from '../../shared';


@Component({
  selector: 'sfl-reset-password',
  templateUrl: './reset-password.component.html'
})
export class ResetPasswordComponent implements OnInit {
  confirmPassword = null;
  resetPasswordModel = new ResetPasswordModel();
  key = null;
  isError = false;
  isSuccess = false;
  message = '';

  constructor(
    private accontService: AccountService,
    private activatedRoute: ActivatedRoute,
    private snakBarService: SnakbarService,
    private router: Router,
  ) { }

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['key']) {
        this.key = params['key'];
      } else {
        this.isError = true;
        this.message = Messages.Login.key_missing_message;
      }
    });
  }

  resetPassword(resetPasswordForm: NgForm) {
    this.isError = false;
    this.isSuccess = false;
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['key']) {
        this.resetPasswordModel.key = params['key'];
        this.accontService.resetPassword(this.resetPasswordModel).subscribe(
          (success) => {
            this.isSuccess = true;
            this.snakBarService.success(Messages.Login.reset_Password_success);
            resetPasswordForm.reset();
            this.router.navigate(['/login']);
          },
          (error) => {
            this.isError = true;
            if (error.error.title === Messages.Login.invalid_resetkey) {
              this.message = Messages.Login.invalid_resetkey;
            } else {
              this.message = Messages.error.error_msg;
            }
          }
        );
      } else {
        this.isError = true;
        this.message = Messages.Login.key_missing_message;
      }
    });
  }
}

