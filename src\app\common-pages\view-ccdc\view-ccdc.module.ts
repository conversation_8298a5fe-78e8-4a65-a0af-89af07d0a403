import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { LayoutModule } from '@angular/cdk/layout';
import { ViewccdcComponent } from './view-ccdc.component';
import { ViewccdcRoutes } from './view-ccdc.route';
import { SensorService } from 'src/app/admin-pages/new-quotation/Add Sensors/add-sensors.service';
import { SelectJacketGroupsComponent } from './select-jacket-groups/select-jacket-groups.component';

@NgModule({
  imports: [RouterModule.forChild(ViewccdcRoutes), SharedModule, LayoutModule],
  declarations: [ViewccdcComponent, SelectJacketGroupsComponent],
  entryComponents: [SelectJacketGroupsComponent],
  providers: [SensorService],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ViewccdcModule {}
