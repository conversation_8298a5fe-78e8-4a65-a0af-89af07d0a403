import { Component, OnInit, Inject, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { LaborMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-thermostat-types',
  templateUrl: './manage-labor-master.component.html'
})
export class ManageLaborMasterComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  laborMaster: LaborMaster;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageLaborMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.laborMaster = data;
  }

  ngOnInit() {
    this.laborMaster = this.laborMaster.id ? Object.assign({}, this.laborMaster) : new LaborMaster();
    this.laborMaster.id ? (this.title = 'Update Burden & Labour') : (this.title = 'Add Burden & Labour');
  }

  updateLaborMaster() {
    this.showLoader = true;
    if (this.laborMaster.id) {
      this.subscription.add(
        this.masterDataService.updateLaborMaster(this.laborMaster).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addLaborMaster(this.laborMaster).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
