
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from '../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';


import { LapCalc } from './lap-calc.model';

@Injectable({ providedIn: 'root' })
export class LapCalcService {

    constructor(private http: HttpClient) { }

    lapCalc(lapcalc: LapCalc) {
        return this.http.post(AppConfig.LAP_CALC, lapcalc).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }
}
