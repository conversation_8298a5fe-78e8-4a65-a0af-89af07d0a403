import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { ECRStatusesMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-ecr-statuses',
  templateUrl: './manage-ecr-statuses.component.html'
})
export class ManageEcrStatusesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  ecrStatus: ECRStatusesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageEcrStatusesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.ecrStatus = data;
  }

  ngOnInit() {
    this.ecrStatus = this.ecrStatus.id ? Object.assign({}, this.ecrStatus) : new ECRStatusesMaster();
    this.ecrStatus.id ? (this.title = 'Update ECR Status') : (this.title = 'Add ECR Status');
  }

  updateDepartment() {
    this.showLoader = true;
    if (this.ecrStatus.id) {
      this.subscription.add(
        this.masterDataService.updateECRStatus(this.ecrStatus).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addECRStatus(this.ecrStatus).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
