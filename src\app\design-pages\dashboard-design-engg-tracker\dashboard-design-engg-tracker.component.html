<!-- So In Design Main Content for Grid -->
<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color"
                        [diameter]="spinnerDiameter"></mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center" fxLayoutGap="100px">
        <mat-card-title>{{ headingTitle }}</mat-card-title>
        <div class="cust_fields" *ngIf="getRole()==='ROLE_ADMIN'">
          <mat-form-field  appearance="outline" >
            <mat-label>Assigned Designer</mat-label>
            <mat-select placeholder="Design Engineer"  name="designEngineer" (selectionChange)="this.initializeTable()" [(ngModel)]="designerId">
              <mat-option *ngFor="let designengineer of salesassociates" [value]="designengineer?.id">
                {{ designengineer?.firstName }} {{ designengineer?.lastName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <mat-checkbox name="show-all-fields" color="warn" [(ngModel)]="filter.statusOutForApproval"
                      (change)="this.initializeTable()">
          Out for Approval
        </mat-checkbox>
      </div>
    </div>
    <div class="no-records" *ngIf="filteredList?.length<=0">No data found</div>
    <div *ngIf="filteredList?.length>0">
      <virtual-scroller class="dynamic-virtual-scroll" #scroll [items]="filteredList"
                        [enableUnequalChildrenSizes]="true" (scrollend)="fetchMore($event)">
        <table class="table" id="table-items">
          <thead #header class="sticky-table-header">
          <tr>
            <th>Action</th>
            <th>Design Location</th>
            <th>Customer</th>
            <th>SO Number</th>
            <th>Project Name</th>
            <th>Number of Designs</th>
            <th>Quotation Status</th>
            <th>Comments</th>
            <th>Submitted Date</th>
            <th>Estimated Engineering Release Date</th>
          </tr>
          </thead>
          <tbody class="text-center" #container>
          <tr class="overflow-text-css" *ngFor="let item of scroll.viewPortItems; let i = index"
              [ngStyle]="rowColorSelection(item?.designStatusId)">
            <td class="td_size_action">
              <button mat-icon-button [matMenuTriggerFor]="menu">
                <mat-icon>more_vert</mat-icon>
              </button>

              <mat-menu class="menu" #menu="matMenu">
                <button *ngIf="!isSales" mat-menu-item (click)="openTrackerField(item?.id)">
                  <mat-icon>visibility</mat-icon>
                  <span>Update tracker fields</span>
                </button>
                <button  mat-menu-item (click)="retrieveAllZipFiles(item.id)">
                  <mat-icon>download</mat-icon>
                  <span>Retrieve Zipped Files</span>
                </button>
                <button mat-menu-item *ngIf="isAuthorized; else other" [routerLink]="jacketLink"
                        [queryParams]="{ quotId: item?.id }">
                  <mat-icon>arrow_right_alt</mat-icon>
                  <span>Go To Order</span>

                </button>
              </mat-menu>

            </td>
            <td (click)="openColorSelectionColumn('designLocation',item?.id)"
                [ngStyle]="columnColorSelection('designLocation', item?.designEngQuoteTrackerColumnColorList)">{{ item?.designLocation | uppercase }}
            </td>
            <td class="td_size" (click)="openColorSelectionColumn('customerName',item?.id)"
                [ngStyle]="columnColorSelection('customerName', item?.designEngQuoteTrackerColumnColorList)"
                matTooltip="{{ item?.customerName }}" matTooltipClass="sfl-formula-tooltip">{{ item?.customerName }}
            </td>
            <td [ngStyle]="columnColorSelection('soNumber', item?.designEngQuoteTrackerColumnColorList)"
                matTooltip="{{ item?.soNumber }}" matTooltipClass="sfl-formula-tooltip">{{ item?.soNumber }}
            </td>
            <td class="td_size_projectTitle" (click)="openColorSelectionColumn('projectTitle',item?.id)"
                [ngStyle]="columnColorSelection('projectTitle', item?.designEngQuoteTrackerColumnColorList)"
                matTooltip="{{ item?.projectTitle }}" matTooltipClass="sfl-formula-tooltip">{{ item?.projectTitle }}
            </td>
            <td (click)="openColorSelectionColumn('noOfDesigns',item?.id)"
                [ngStyle]="columnColorSelection('noOfDesigns', item?.designEngQuoteTrackerColumnColorList)">{{ item?.noOfDesigns }}
            </td>
            <td class="td_size" (click)="openColorSelectionColumn('designStatusId',item?.id)"
                [ngStyle]="columnColorSelection('designStatusId', item?.designEngQuoteTrackerColumnColorList)"
                matTooltip="{{ item?.designStatusId | getQuoteStatusName: statuses }}"
                matTooltipClass="sfl-formula-tooltip">{{ item?.designStatusId | getQuoteStatusName: statuses }}
            </td>
            <td class="td_size_comments" (click)="openColorSelectionColumn('designComments',item?.id)"
                [ngStyle]="columnColorSelection('designComments', item?.designEngQuoteTrackerColumnColorList)"
                matTooltip="{{ item?.designComments }}" matTooltipClass="sfl-formula-tooltip">
              <b>{{ item?.designComments }}</b></td>
            <td (click)="openColorSelectionColumn('folderSubmittedDate',item?.id)"
                [ngStyle]="columnColorSelection('folderSubmittedDate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.folderSubmittedDate | date: 'MM-dd-yyyy' }}
            </td>
            <td class="td_size_shipDate" (click)="openColorSelectionColumn('engReleaseDate',item?.id)"
                [ngStyle]="columnColorSelection('engReleaseDate', item?.designEngQuoteTrackerColumnColorList)">{{ item?.engReleaseDate | date: 'MM-dd-yyyy' }}
            </td>
          </tr>
          </tbody>
        </table>
      </virtual-scroller>
      <div class="status">
        Showing <span>{{ scroll.viewPortInfo.startIndex + 1 }}</span> -
        <span>{{ scroll.viewPortInfo.endIndex + 1 }}</span> of
        <span>{{ filteredList?.length }}</span>
      </div>
    </div>
  </mat-card>
</div>
