import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { MatD<PERSON>ogRef, MatDialogConfig, MatDialog } from '@angular/material';
import { AddPartnumberComponent } from '../add-partnumber/add-partnumber.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { SnakbarService, SharedService, Messages } from 'src/app/shared';
import { AddAttachmentComponent } from '../add-attachment/add-attachment.component';
import { Subscription } from 'rxjs';
import { EcrManagementService } from '../ecr-management.service';
import { User } from 'src/app/common-pages/users/user.model';
import { EcrDto, Departments, EcrPartnumberDto } from '../ecr-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'sfl-generate-new-ecr',
  templateUrl: './generate-new-ecr.component.html'
})
export class GenerateNewEcrComponent implements OnInit, OnDestroy {

  description: string;
  partnumbers: string[];
  attachments: string[];
  users: User[];
  ecrDto: EcrDto = new EcrDto();
  ecrDate = new Date();
  ecrPartNumbers: EcrPartnumberDto[] = [];
  requestorId: number;
  departments: Departments[];
  reviewLevel = Variable.ecrReviewLevel;
  uploadedFiles: FormData[];
  formData = new FormData();
  formData2 = new FormData();
  subscription = new Subscription();
  uploadedAttachments = [];
  selectedPartNumber: EcrPartnumberDto;
  selectedAttachment: string;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(public dialogRef: MatDialogRef<GenerateNewEcrComponent>, private matDialog: MatDialog, private snakbarService: SnakbarService,
      private ecrManagementService: EcrManagementService, private sharedService: SharedService, private datePipe: DatePipe) {
  }

  ngOnInit() {
    this.ecrDto.requestorId = this.sharedService.getUserId();
    this.getAllUsers();
    this.getDepartments();

    this.partnumbers = [];
    this.attachments = [];
  }

  getAllUsers() {
    // getUsers
    this.ecrManagementService.getSalesAssociate(true).subscribe((res: User[]) => {
      if (res) {
        this.users = res;
        this.showLoader = false;
      }
    }, error => {
      this.showLoader = false;
    });
  }

  getDepartments() {
    // getUsers
    this.subscription.add(this.ecrManagementService.getDepartments().subscribe((res: Departments[]) => {
      if (res) {
        this.departments = res;
      }
    }));
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  addPartNumber() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_sm;
    matDataConfig.panelClass = 'sfl-add-partnumber-model';
    const dialogRef = this.matDialog.open(AddPartnumberComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((addedPartnumber: EcrPartnumberDto) => {
      if (addedPartnumber.partNumber) {
        if (!this.partNumberExist(this.ecrPartNumbers, addedPartnumber.partNumber)) {
          this.ecrPartNumbers.push(addedPartnumber);
        } else {
          this.snakbarService.error('Part number ' + addedPartnumber.partNumber + ' already exist!');
        }
      }
    });
  }

  partNumberExist(list, object): boolean {
    let flag = false;
    list.forEach(element => {
      if (element.partNumber === object) {
        flag = true;
      }
    });
    return flag;
  }

  addAttachment() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-add-partnumber-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      if (uploadedAttachment !== undefined) {
          this.formData2.append(uploadedAttachment.name, uploadedAttachment, uploadedAttachment.name);
          this.uploadedAttachments.push(uploadedAttachment);
      }
    });
  }

  selectPartnumber(partNumber) {
    this.selectedPartNumber = this.selectedPartNumber === partNumber ? new EcrPartnumberDto() : partNumber;
  }
  selectAttachment(attachment) {
    this.selectedAttachment = this.selectedAttachment === attachment ? '' : attachment;
  }

  removePartnumber(selectedPartNumber) {
    this.partnumbers = this.ecrManagementService.removeListItemByIndex(this.ecrPartNumbers, selectedPartNumber);
    this.selectedPartNumber = new EcrPartnumberDto();
  }
  removeAttachment(selectedAttachment) {
    this.attachments = this.ecrManagementService.removeListItemByIndex(this.uploadedAttachments, selectedAttachment);
    this.formData2.delete(selectedAttachment);
    this.selectedAttachment = '';
  }

  submitEcr() {
    this.showLoader = true;
    this.ecrDto.ecrPartNumbers = this.ecrPartNumbers;
    this.ecrDto.ecrDate = this.datePipe.transform(this.ecrDate, Values.dateFormat.formatHyphen);
    this.ecrDto.updatedDate = this.datePipe.transform(this.ecrDate, Values.dateFormat.formatHyphen);
    this.uploadedAttachments.forEach(x => {
      this.formData.append('file', this.formData2.get(x.name), x.name);
    });
    this.formData.append('ecrDTO', JSON.stringify(this.ecrDto));
    this.subscription.add(this.ecrManagementService.generateEcr(this.formData).subscribe(res => {
      if (res) {
        this.showLoader = false;
        this.snakbarService.success(Messages.ECR_MANAGEMENT.Ecr_Created_Success);
        this.dialogRef.close(true);
      }
    }, error => { this.showLoader = false; }));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
