import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'sfl-design-engg-master-data',
  templateUrl: './design-engg-master-data.component.html'
})
export class DesignEnggMasterDataComponent implements OnInit {
  constructor(private router: Router) {}

  ngOnInit() {}
  manageQuotStatus() {
    this.router.navigate(['master-data/management/quotation-status']);
  }
  manageFeatures() {
    this.router.navigate(['master-data/management/features']);
  }
  managePQPFamily() {
    this.router.navigate(['master-data/management/pqp-family']);
  }
  manageGoldStandardTapeWidth() {
    this.router.navigate(['master-data/management/gold-standard-tape-width']);
  }
  manageAlloys() {
    this.router.navigate(['master-data/management/alloys']);
  }
  manageGoldWireTapes() {
    this.router.navigate(['master-data/management/gold-wire-tapes']);
  }
  manageHeatingTapes() {
    this.router.navigate(['master-data/management/heating-tapes']);
  }
  manageBHXMaterial() {
    this.router.navigate(['master-data/management/bhx-material']);
  }
  manageDepartments() {
    this.router.navigate(['master-data/management/departments']);
  }
  manageECRStatuses() {
    this.router.navigate(['master-data/management/ecr-statuses']);
  }
  manageWarps() {
    this.router.navigate(['master-data/management/warps']);
  }
  manageLeadMaster() {
    this.router.navigate(['master-data/management/label-master']);
  }
  manageEcoLineRevisionMaster() {
    this.router.navigate(['master-data/management/eco-line-revision-master']);
  }
  manageSolidWorksMaster() {
    this.router.navigate(['master-data/management/solidworks-master']);
  }
  manageEstimatedEngRelDate() {
    this.router.navigate(['master-data/management/est-eng-rel-date-master']);
  }
  manageProductTypeMaster() {
    this.router.navigate(['master-data/management/product-group']);
  }
}
