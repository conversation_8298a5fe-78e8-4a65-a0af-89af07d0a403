export class TitleBlockSuccess {
  constructor(public success?: boolean, public title?: string, public message?: string, public data?: TitleBlock) {}
}

class HighlightedJacketList {
  constructor(
    public revision: boolean = false,
    public drawnBy: boolean = false, // linked to Pattern design
    public drawnDate: boolean = false, // linked to Pattern design
    public tapeBy: boolean = false, // linked to Element & BOM
    public tapeDate: boolean = false, // linked to Element & BOM
    public bomBy: boolean = false, // linked to Element & BOM
    public bomDate: boolean = false, // linked to Element & BOM
    public engineeringApprovalBy: boolean = false,
    public engAppDate: boolean = false
  ) {}
}

class HighlightedGeometry {
  constructor(public jacketDiams: boolean = false) {}
}

class HighlightedElement {
  constructor(
    public elementType: boolean = false, // (Design Type of TitleBlock will change on change of Tajima Wire and Element Type in element screen's element info. )
    public construct1: boolean = false, // (Element Screen's WireType under the Element Info)
    public number1: boolean = false, // (Element Screen's No of Tapes under the Element Info)
    public length1: boolean = false, // (Element Screen's TotalLength under the Element Info)
    public width1: boolean = false, // (Element Screen's Tape Width under the Element Info)
    public warp1: boolean = false, // (Will update on change Element Screen's Tape Width so update this too)
    public pick1: boolean = false, // (Element Screen's Picks under the Tape Info will update selecting a element for element choices)
    public ohmPerfT1: boolean = false, // (Element Screen's Ohms/FT under the Tape Info will update selecting a element for element choices)
    public wirePart1_1: boolean = false, // (Element Screen's partNumber under the Wire Info will update selecting a element for element choices)
    public wireOhmsfT1: boolean = false, // (Element Screen's Ohms/FT under the Wire Info will update selecting a element for element choices)
    public tgtOhms1: boolean = false, // (Element Screen's TGT OHMS will update selecting a element for element choices)
    public maxOhms1: boolean = false, // (Element Screen's MIN OHMS will update selecting a element for element choices)
    public minOhms1: boolean = false, // (Element Screen's MAX OHMS will update selecting a element for element choices)
    public totMaxOhms: boolean = false, // (will update selecting a element for element choices)
    public totMinOhms: boolean = false, // (will update selecting a element for element choices)
    public totTgtOhms: boolean = false // (will update selecting a element for element choices)
  ) {}
}

class HighlightedCommon {
  constructor(
    public watts: boolean = false, // (can be changed from geometry screen,element screen's basic info)
    public volts: boolean = false, // (can be changed from element screen's basic info)
    public amps: boolean = false // (dependent on watts and volts on change of watts or volts the amps will change)
  ) {}
}

export class TitleBlockHighlighterDTO {
  highlightedJacketList: HighlightedJacketList = new HighlightedJacketList();
  highlightedGeometry: HighlightedGeometry = new HighlightedGeometry();
  highlightedElement: HighlightedElement = new HighlightedElement();
  highlightedCommon: HighlightedCommon = new HighlightedCommon();
  jacketIds: number[];
}

export class TitleBlock {
  constructor(
    public id?: number,
    public partFilePath?: string,
    public volts?: number,
    public watts?: number,
    public amps?: number,
    public customerName?: string,
    public description1?: string,
    public description2?: string,
    public jacketDiams?: string,
    public salesOrderNumber?: string,
    public fahrenheit?: string,
    public celcius?: string,
    public fit?: string,
    public finish?: string,
    public drawnBy?: string,
    public drawnDate?: string,
    public tapeBy?: string,
    public tapeDate?: string,
    public bomBy?: string,
    public bomDate?: string,
    public engineeringApprovalBy?: string,
    public engAppDate?: string,
    public revisionId?: number,
    public layerTL5?: string,
    public layerTR5?: string,
    public layerTL4?: string,
    public layerTR4?: string,
    public layerTL3?: string,
    public layerTR3?: string,
    public layerTL2?: string,
    public layerTR2?: string,
    public layerTL1?: string,
    public layerTR1?: string,
    public layerML?: string,
    public tape?: string,
    public layerMR?: string,
    public layerBL1?: string,
    public layerBR1?: string,
    public layerBL2?: string,
    public layerBR2?: string,
    public layerBL3?: string,
    public layerBR3?: string,
    public layerBL4?: string,
    public layerBR4?: string,
    public layerBL5?: string,
    public layerBR5?: string,
    public partNumber?: string,
    public custPartNoLabel?: string,
    public filePath1?: string,
    public filePath2?: string,
    public filePath3?: string,
    public revision?: string,
    public oldRevision?: string,
    public revLetter1?: string,
    public revLetter2?: string,
    public revLetter3?: string,
    public revLetter4?: string,
    public revBy1?: string,
    public revBy2?: string,
    public revBy3?: string,
    public revBy4?: string,
    public revDate1?: string,
    public revDate2?: string,
    public revDate3?: string,
    public revDate4?: string,
    public revDescR1?: string,
    public revDescR2?: string,
    public revDescR3?: string,
    public revDescR4?: string,
    public tpi?: string,
    public strand?: string,
    public pick?: string,
    public warp?: string,
    public elementType?: string,
    public number1?: string,
    public number2?: string,
    public number3?: string,
    public wirePart1_1?: string,
    public wirePart1_2?: string,
    public wirePart2_1?: string,
    public wirePart2_2?: string,
    public wirePart3_1?: string,
    public wirePart3_2?: string,
    public wireOhmsfT1?: string,
    public wireOhmsfT2?: string,
    public wireOhmsfT3?: string,
    public length1?: string,
    public length2?: string,
    public length3?: string,
    public width1?: string,
    public width2?: string,
    public width3?: string,
    public tpi1?: string,
    public tpi2?: string,
    public tpi3?: string,
    public strand1?: string,
    public strand2?: string,
    public strand3?: string,
    public construct1?: string,
    public construct2?: string,
    public construct3?: string,
    public pick1?: string,
    public pick2?: string,
    public pick3?: string,
    public warp1?: string,
    public warp2?: string,
    public warp3?: string,
    public ohmPerfT1?: string,
    public ohmPerfT2?: string,
    public ohmPerfT3?: string,
    public maxOhms1?: string,
    public maxOhms2?: string,
    public maxOhms3?: string,
    public tgtOhms1?: string,
    public tgtOhms2?: string,
    public tgtOhms3?: string,
    public minOhms1?: string,
    public minOhms2?: string,
    public minOhms3?: string,
    public totMaxOhms?: string,
    public totTgtOhms?: string,
    public totMinOhms?: string,
    public jacketId?: number,
    public selectedPartFilePath?: string,
    public oldPartNumber?: string,
    public titleBlockHighlighterDTO: TitleBlockHighlighterDTO = new TitleBlockHighlighterDTO()
  ) {}
}
