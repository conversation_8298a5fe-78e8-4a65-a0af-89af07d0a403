<h2 mat-dialog-title>Lap Calc
  <hr>
</h2>
<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="start">
    <mat-form-field fxFlex.gt-lg="20" fxFlex.gt-md="30">
      <input matInput placeholder="Pipe Diameter ({{(lapCalc.unit) ? lapCalc.unit :''}})" sflIsDecimal [(ngModel)]="lapCalc.pipeDiameter"
        required autofocus name="pipeDiameter" autocomplete="off">
    </mat-form-field>
    <mat-form-field class="data" fxFlex.gt-lg="20" fxFlex.gt-md="30" >
      <mat-select placeholder="Unit" [(ngModel)]="lapCalc.unit" name="unit" required>
        <mat-option value="IN">IN</mat-option>
        <mat-option value="MM">MM</mat-option>
      </mat-select>
    </mat-form-field>
    <div class="loading-icon mr-10">
      <img *ngIf="loading" src="../../../assets/images/loader.gif" alt="loader" />
    </div>
  </div>
  <div class="highlight">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div class="mb-20" fxLayout="column wrap" fxFlex="15" fxLayoutAlign="space-between">
        <div class="col">
          <label class="lbl">Laps</label>
        </div>
        <div class="col">
          <label class="lbl">Tape Width</label>
        </div>
        <div class="col">
          <label class="lbl">Between Laps</label>
        </div>
      </div>
      <div class="lap-result" fxFlex="85" fxLayoutAlign="space-between">
        <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngFor="let lap of lapCalcRes">
          <div fxLayout="column wrap">
            <div class="col">
              <label class="cal"><span>{{lap.laps}}</span></label>
            </div>
            <div class="col">
              <label class="cal"><span>{{lap.tapeWidth}}</span></label>
            </div>
            <div class="col">
              <label class="cal"><span>{{lap.betweenLaps}}</span></label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-dialog-content>
<hr>
<mat-dialog-actions>
  <button mat-raised-button type="submit" color="warn" (click)="calculateLapList()" id="lapcalc">Calculate</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>