<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner
  class="sfl-global-spinner-loader"
  [mode]="mode"
  [color]="color"
  [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title fxFlex>
    Please set your initial name to continue
    <hr>
</h2>
<h5 class="initial-name">You need to set up your initial name here, this will be shown on Title Block screen.<br>Only after you set initial name you will be able to continue!</h5>
<form #initials="ngForm">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field appearance="outline">
          <input matInput placeholder="Initial Name" [(ngModel)]="userDto.initialName" name="initialName" #initialNameInput="ngModel" required>
        </mat-form-field>
        <div *ngIf="initialNameInput.touched && initialNameInput.invalid">
          <small class="mat-text-warn" *ngIf="initialNameInput?.errors.required">Initial name is required.</small>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  
  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="end">
        <button mat-raised-button type="submit" color="warn" (click)="setInitialName()" [disabled]="!initials.valid">Set Initial Name</button>
    </div>    
  </mat-dialog-actions>
</form>