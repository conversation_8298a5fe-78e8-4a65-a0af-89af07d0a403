import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { ClosureMaterialMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogConfig, MatDialog } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { AddAttachmentComponent } from 'src/app/design-pages/ecr-management/add-attachment/add-attachment.component';
import { Messages, SnakbarService } from 'src/app/shared';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'sfl-manage-closure-material',
  templateUrl: './manage-closure-material.component.html'
})

export class ManageClosureMaterialComponent implements OnInit, OnD<PERSON>roy {
  subscription = new Subscription();
  showLoader = false;
  closureMaterial: ClosureMaterialMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  fastnerCodeValues = Values.FastnerCode;
  title = '';
  imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  formData = new FormData();

  constructor(
    public dialogRef: MatDialogRef<ManageClosureMaterialComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) {
    this.closureMaterial = data;
  }

  ngOnInit() {
    this.closureMaterial = this.closureMaterial.id ? Object.assign({}, this.closureMaterial) : new ClosureMaterialMaster();
    this.closureMaterial.id ? (this.title = 'Update Closure Material') : (this.title = 'Add Closure Material');
    this.closureMaterial.imageUrl
      ? (this.imageUrl = environment.IMAGES_URL + this.closureMaterial.imageUrl)
      : (this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE);
  }

  updateMaterialProperty() {
    this.showLoader = true;
    this.formData.append('closureMaterialDTO', JSON.stringify(this.closureMaterial));
    this.subscription.add(
      this.masterDataService.addClosureMaterial(this.formData).subscribe(
        () => {
          this.showLoader = false;
          this.dialogRef.close(true);
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  addAttachment() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-upload-material-file-model';
    const dialogRef = this.matDialog.open(AddAttachmentComponent, matDataConfig);
    dialogRef.afterClosed().subscribe((uploadedAttachment: File) => {
      if (uploadedAttachment && uploadedAttachment.type.match(/image\/*/)) {
        const reader = new FileReader();
        reader.readAsDataURL(uploadedAttachment);
        reader.onload = _event => {
          this.imageUrl = reader.result.toString();
          this.closureMaterial.imageUrl = reader.result.toString();
        };
        this.formData.append('file', uploadedAttachment, uploadedAttachment.name);
      } else if (uploadedAttachment) {
        this.snakbarService.error(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Only_Image_Type_Allowed);
      }
    });
  }

  removeAttachment() {
    this.formData.delete('file');
    this.closureMaterial.imageUrl = '';
    this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  // takes temperature type and sends to master data service method for conversion based on the type [celcius <-> fahrenheit]
  convertTeperature(temperatureType: string): void {
    temperatureType === 'celcius'
      ? (this.closureMaterial.maxTempF = this.masterDataService.convertTemperature(this.closureMaterial.maxTemp, temperatureType))
      : (this.closureMaterial.maxTemp = this.masterDataService.convertTemperature(this.closureMaterial.maxTempF, temperatureType));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
