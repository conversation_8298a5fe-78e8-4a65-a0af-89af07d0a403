import { Component, OnInit, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { PlugMaster, PlugMasterPageable, PlugMasterFilter, GenericPageable } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManagePlugComponent } from './manage-plug/manage-plug.component';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-plug-master',
  templateUrl: './plug-master.component.html'
})
export class PlugMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Plug Master';
  plugMaster: PlugMaster;
  plugMasterPageable: GenericPageable<PlugMaster>;
  plugMasterDataSource = new MatTableDataSource<PlugMaster>();
  plugMasterMasterColumns = DisplayColumns.Cols.PlugMaster;
  plugFilter: PlugMasterFilter = new PlugMasterFilter();

  dataSource = new MatTableDataSource<PlugMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  showLoader = false;
  numberOfElements: number;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldpartNumber = Values.FilterFields.partNumber;
  filterFieldplugName = Values.FilterFields.plugName;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) {}

  ngOnInit() {
    this.getPlugMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new plug
  addPlug() {
    this.editPlug(new PlugMaster());
  }

  // used to add filter to plug listing
  async addFilter() {
    this.filter = [
      { key: this.filterFieldpartNumber, value: !this.plugFilter.partNumber ? '' : this.plugFilter.partNumber },
      { key: this.filterFieldplugName, value: !this.plugFilter.plugName ? '' : this.plugFilter.plugName }
    ];
    this.getPlugMasterData(this.initialPageIndex, this.pageSize);
  }

  // used to clear filter of plug listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldpartNumber,
        value: fieldToClear === this.filterFieldpartNumber ? (this.plugFilter.partNumber = '') : this.plugFilter.partNumber
      },
      {
        key: this.filterFieldplugName,
        value: fieldToClear === this.filterFieldplugName ? (this.plugFilter.plugName = '') : this.plugFilter.plugName
      }
    ];
    this.getPlugMasterData(this.initialPageIndex, this.pageSize);
  }

  getPlugMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getAllPlugs(this.filter, pageable).subscribe(
        (res: GenericPageable<PlugMaster>) => {
          this.plugMasterPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createPlugTable(this.plugMasterPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.plugMasterDataSource.data = [];
          if (error.applicationStatusCode === 1230) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  createPlugTable(serviceRequestList: GenericPageable<PlugMaster>) {
    this.plugMasterDataSource.data = serviceRequestList.content;
  }

  getPlugMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getPlugMasterData(this.pageIndex, this.pageSize);
  }

  getPlugMasterSorting(event) {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getPlugMasterData(this.pageIndex, this.pageSize);
  }

  async deletePlug(plugId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deletePlug(plugId).subscribe(
        () => {
          this.snakbarService.success('Plug' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getPlugMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }
  editPlug(plug: PlugMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = plug;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-plug-master-model';
    const dialogRef = this.matDialog.open(ManagePlugComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          plug.id
            ? 'Plug' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Plug' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getPlugMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
