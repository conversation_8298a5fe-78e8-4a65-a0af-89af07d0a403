import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { AccessoriesComponent } from 'src/app/admin-pages/accessories/accessories.component';
@Component({
  selector: 'app-view-image',
  templateUrl: './view-image.component.html',
  styleUrls: ['./view-image.component.css']
})
export class ViewImageComponent implements OnInit {
  imageUrl: string;

  constructor(
    public  readonly dialogRef: MatDialogRef<AccessoriesComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
    this.imageUrl = data.imageUrl;
   }

  ngOnInit() {
  }

  closeDialog(): void {
    this.dialogRef.close();
}

}
