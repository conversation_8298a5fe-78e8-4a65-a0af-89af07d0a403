import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { MaterialPropertiesMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-manage-material-property',
  templateUrl: './manage-material-property.component.html'
})
export class ManageMaterialPropertyComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  materialProperty: MaterialPropertiesMaster;
  _data: MaterialPropertiesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  materialPropertyTypes = Values.MaterialPropertyTypes;
  constructor(
    public dialogRef: MatDialogRef<ManageMaterialPropertyComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private masterDataService: MasterdataManagementService
  ) {
    this._data = data;
  }

  ngOnInit() {
    this.materialProperty = this._data.id ? Object.assign({}, this._data) : new MaterialPropertiesMaster();
    this._data.id ? (this.title = 'Update Material Property') : (this.title = 'Add Material Property');
  }

  updateMaterialProperty() {
    this.showLoader = true;
    if (this._data.id) {
      this.subscription.add(
        this.masterDataService.updateMaterialProperties(this.materialProperty).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addMaterialProperties(this.materialProperty).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
