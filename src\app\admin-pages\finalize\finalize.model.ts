export class AddLeadTime {
  constructor(
    public quotationId?: number,
    public leadTime?: number
  ) { }
}


export class ManufacturedInDTO {
  constructor(
    public quotationId?: number,
    public manufacturedIn?: string
  ) { }
}

export class CurrencyDTO {
  public abbreviation?: string;
  public id?: number;
}

export class SendToDesignConfigurationDTO {
  constructor(
    public expedite = false,
    public stimulationRequired = false,
    public manufacturedIn?: string,
    public quotationId?: number) { }
}
