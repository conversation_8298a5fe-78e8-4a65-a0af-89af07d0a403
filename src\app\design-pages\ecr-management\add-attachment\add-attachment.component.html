<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Upload Attachment
  <hr />
</h2>

<mat-dialog-content>
  <form class="fields" #addPartnumberForm="ngForm">
    <div class="file-upload-card">
      <mat-card fxFlex="100">
        <div class="sfl-upload form-group form_file">
          <input class="open-doc" type="file" (change)="readUrl($event)" />
          <em class="material-icons">cloud_upload</em>
          <p class="font-weight-bold">{{ value }}</p>
        </div>
        <br />
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
          <div fxLayoutAlign="start">
            <label class="link" *ngIf="isValidFileSize">*File must be less than 10MB</label>
            <span *ngIf="isUploading" class="loading-icon">
              <img src="../../../../assets/images/loader.gif" alt="loader" />
            </span>
          </div>
        </div>
      </mat-card>
    </div>
  </form>
</mat-dialog-content>

<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button color="warn" [disabled]="!filename" (click)="uploadFile()">Upload</button>
  </div>
</mat-dialog-actions>
