import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { User } from 'src/app/common-pages/users/user.model';
import { EcrManagementService } from 'src/app/design-pages/ecr-management/ecr-management.service';
import { Messages, SnakbarService } from 'src/app/shared';
import { CcdcTemplateExportDTO } from '../ccdc-model/ccdc.model';
import { JacketGroupService } from '../manage-jacketgroups/manage-jacket-groups.service';
import { SummarySalesOrderComponent } from '../summary-sales-order.component';

@Component({
  selector: 'app-export-ccdc-modal',
  templateUrl: './export-ccdc-modal.component.html',
  styleUrls: ['./export-ccdc-modal.component.css']
})
export class ExportCcdcModalComponent implements OnInit {
  subscription: Subscription = new Subscription();
  showLoader = false;
  ccdcTemplateExportDTO :CcdcTemplateExportDTO;
  users: User[];

  constructor(
    public dialogRef: MatDialogRef<SummarySalesOrderComponent>,
    public jacketGroupService: JacketGroupService,
    private ecrManagementService: EcrManagementService,
    private snakbarService: SnakbarService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.ccdcTemplateExportDTO = data;
  }

  ngOnInit() {
    this.getAllUsers();
  }

  exportCcdcData(){
    this.showLoader = true;
    this.subscription.add(
      this.jacketGroupService.exportCcdcTemplateForJacketGroup(this.ccdcTemplateExportDTO).subscribe(
        (res) => {
          this.showLoader = false;
          this.dialogRef.close();
            this.snakbarService.success(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.ccdc_export_success);
        },
        () => {
          this.showLoader = false;
        }
      )
    );
  }

  getAllUsers() {
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.getSalesAssociate(true).subscribe((res: User[]) => {
      if (res) {
        this.users = res;
        this.showLoader = false;
      }
    }, error => {
      this.showLoader = false;
    }));
  }

  closeDialog(success: boolean): void {
    this.dialogRef.close(success);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

