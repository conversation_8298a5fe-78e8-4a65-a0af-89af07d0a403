const Centipede1 = 'Centipede 1';
export const Values = {
  approvalLevels: [
    {name: 'Level 1', value: 'LEVEL1'},
    {name: 'Level 2', value: 'LEVEL2'},
    {name: 'Level 3', value: 'LEVEL3'}
  ],

  dateTimeZone: '+05:30',
  timeZone: '.000Z',

  approvalFormats: [
    {name: 'PDF', approvalLevel: 'PDF', selected: false},
    {name: 'STEP', approvalLevel: 'STEP', selected: false},
    {name: 'Jacket Model', approvalLevel: 'JM', selected: false},
    {name: 'Complex', approvalLevel: 'COMPLEX', selected: false}
  ],

  markings: [
    {name: 'CE', markings: 'CE', selected: false},
    {name: 'UL', markings: 'UL', selected: false},
    {name: 'Hot Label', markings: 'HL', selected: false},
    {name: 'Private Label', markings: 'PL', selected: false},
    {name: 'Hazardous Area', markings: 'HAZARDOUSAREA', selected: false},
    {name: 'ETL', markings: 'ETL', selected: false}
  ],

  dateFormat: {
    format: 'MM/dd/yyyy',
    formatHyphen: 'yyyy-MM-dd',
    formatHyphenTimeZone: 'yyyy-MM-ddT00:00:00',
    excelFormat: 'MM-dd-yyyy'
  },
  Other: 'Other',
  Archived: 'Archived',
  ApplicationEng: 'app',
  DesignEng: 'design',
  Both: 'both',
  Part_Regex: /^[A-Za-z0-9]+(?:\-\SC?\d+)?$/,
  UL_Sequence_Regex: /^\d*$|^[F|L]{1}[0-9]\d*$/,
  NON_UL_Sequence_Regex: /^\d*$|^[SC]{2}[0-9]\d*$/,
  ROOT_PN_Regex: /^[A-Za-z0-9]*$|^[A-Za-z0-9]+(-SC){1}$/,
  PartNumber_Regex: /^[A-Za-z0-9]+$/,
  SequenceNumber_Regex: /^\d*$|^[F|SC|L]+[0-9]\d*$/,
  Tape_Part_Number_Regex: /^$|^[A-Z]+-[\d]+$/,
  ECR_PartNumber_Regex: /^[a-zA-Z0-9-]+$/,

  highlightColors: [
    {
      name: 'Default',
      value: 'default'
    },
    {
      name: 'Yellow',
      value: 'lemonchiffon'
    },
    {
      name: 'Red',
      value: 'tomato'
    },
    {
      name: 'Blue',
      value: 'skyblue'
    },
    {
      name: 'Green',
      value: 'limegreen'
    },
    {
      name: 'Orange',
      value: 'orange'
    },
    {
      name: 'None',
      value: 'white'
    }
  ],

  highlightRowColors: [
    {
      name: 'Default',
      value: 'default'
    },
    {
      name: 'Yellow',
      value: 'lemonchiffon'
    },
    {
      name: 'Red',
      value: 'tomato'
    },
    {
      name: 'Blue',
      value: 'skyblue'
    },
    {
      name: 'Orange',
      value: 'orange'
    },
    {
      name: 'Green',
      value: 'limegreen'
    },
    {
      name: 'None',
      value: 'white'
    }
  ],

  designQuoteTrackerColumnList: [
    {
      name: 'SO Number',
      value: 'soNumber'
    },
    {
      name: 'Project Name',
      value: 'projectTitle'
    },
    {
      name: 'Customer',
      value: 'customerName'
    },
    {
      name: 'Design Location',
      value: 'designLocation'
    },
    {
      name: 'Ship Date',
      value: 'shipDate'
    },
    {
      name: 'Folder Submitted Date',
      value: 'folderSubmittedDate'
    },
    {
      name: 'Design Status',
      value: 'designStatusId'
    },
    {
      name: 'OFA1',
      value: 'ofa1Date'
    },
    {
      name: 'Approval 1',
      value: 'approval1Date'
    },
    {
      name: 'OFA2',
      value: 'ofa2Date'
    },
    {
      name: 'Approval 2',
      value: 'approval2Date'
    },
    {
      name: 'Sales Rep',
      value: 'salesRepId'
    },
    {
      name: 'Product Type',
      value: 'productType'
    },
    {
      name: 'Designer',
      value: 'designerId'
    },
    {
      name: 'Number Of Designs',
      value: 'noOfDesigns'
    },
    {
      name: 'Dollar Amount',
      value: 'dollarAmount'
    },
    {
      name: 'Comments',
      value: 'designComments'
    },
    {
      name: 'Number Of Revisions',
      value: 'noOfRevisions'
    },
    {
      name: 'OFA-1 Days',
      value: 'noOfDaysOFA1'
    },
    {
      name: 'OFA-2 Days',
      value: 'noOfDaysOFA2'
    },
    {
      name: 'Total Days of OFA',
      value: 'totalDaysOFA'
    },
    {
      name: 'Assigned App Engg',
      value: 'appEnggAssigned'
    }
  ],

  appQuoteTrackerColumnList: [
    {
      name: 'Quote Number',
      value: 'quotationNumber'
    },
    {
      name: 'Customer Name',
      value: 'customerName'
    },
    {
      name: 'Project Title',
      value: 'projectTitle'
    },
    {
      name: 'Submitted To App On',
      value: 'dateSubmittedToApp'
    },
    {
      name: 'App Started On',
      value: 'appStartedDate'
    },
    {
      name: 'Completed On',
      value: 'appCompletedDate'
    },
    {
      name: 'Major Product Type',
      value: 'productType'
    },
    {
      name: 'Ext. Quote Reqd',
      value: 'extQuoteRequired'
    },
    {
      name: 'App Engineer Assigned',
      value: 'assignedAppEngineerId'
    },
    {
      name: 'Account Manager',
      value: 'accountManagerId'
    },
    {
      name: '# of Custom Designs',
      value: 'noOfDesigns'
    },
    {
      name: 'Cust. Clarification Req	',
      value: 'customerClarificationRequired'
    },
    {
      name: 'Notes',
      value: 'notes'
    },
    {
      name: 'SQT Link',
      value: 'sqtLink'
    },
    {
      name: 'Current Status',
      value: 'currentStatusComment'
    }
  ],

  JacketTypeConst: [
    {
      id: 'NONGROUNDED',
      value: 'NonGrounded'
    },
    {
      id: 'GROUNDED',
      value: 'Grounded'
    },
    {
      id: 'SOFT_MICA',
      value: 'Soft MICA'
    },
    {
      id: 'INSULATOR',
      value: 'Insulator'
    },
    {
      id: 'NONGROUNDED_SAMOX',
      value: 'Non-Grounded Samox'
    }
  ],

  PhaseTypeConst: [
    {
      id: 'AC_SINGLE_PHASE',
      value: 'VAC Single Phase 50-60Hz'
    },
    {
      id: 'AC_3_WYE',
      value: 'VAC 3-Phase Wye 50-60Hz'
    },
    {
      id: 'AC_3_DELTA',
      value: 'VAC 3-Phase Delta 50-60Hz'
    },
    {
      id: 'DC',
      value: 'DC'
    },
    {
      id: 'NONE',
      value: 'None(Insulator)'
    }
  ],

  elementPhaseTypeConst: [
    {
      data: 'V AC Single Phase',
      value: 'AC Single Phase'
    },
    {
      data: 'V AC 3-Phase Wye',
      value: 'AC 3-Phase Wye'
    },
    {
      data: 'V AC 3-Phase Delta',
      value: 'AC 3-Phase Delta'
    },
    {
      data: 'DC',
      value: 'DC'
    },
    {
      data: 'NONE',
      value: 'None(Insulator)'
    },
    {
      data: 'DUAL_PHASE',
      value: 'Dual Phase'
    }
  ],

  SensorControlType: [
    {
      id: Centipede1,
      value: Centipede1
    },
    {
      id: 'Cent 2 Stack',
      value: 'Cent 2 Stack'
    },
    {
      id: 'T/S Control',
      value: 'T/S Control'
    },
    {
      id: 'MPC',
      value: 'MPC'
    },
    {
      id: 'BriskONE',
      value: 'BriskONE'
    },
    {
      id: 'Freestyle',
      value: 'Freestyle'
    },
    {
      id: 'Supplied by Customer',
      value: 'Supplied by Customer'
    },
    {
      id: 'None',
      value: 'None'
    }
  ],

  SensorType: [
    {
      id: 'RTD',
      value: 'RTD'
    },
    {
      id: 'J-Type T/C',
      value: 'J-Type T/C'
    },
    {
      id: 'K-Type T/C',
      value: 'K-Type T/C'
    },
    {
      id: 'Other',
      value: 'Other'
    }
  ],

  SensorLocation: [
    {
      id: 'Inside Jacket Through Liner',
      value: 'Inside Jacket Through Liner'
    },
    {
      id: 'Other',
      value: 'Other'
    }
  ],

  SensorConneector: [
    {
      id: 'Centipede 2',
      value: 'Centipede 2'
    },
    {
      id: Centipede1,
      value: Centipede1
    },
    {
      id: 'Freestyle',
      value: 'Freestyle'
    },
    {
      id: 'Standard T/C',
      value: 'Standard T/C'
    },
    {
      id: 'Mini T/C',
      value: 'MIni T/C'
    },
    {
      id: 'Bare Wire',
      value: 'Bare Wire'
    },
    {
      id: 'LYNX Dock',
      value: 'LYNX Dock'
    },
    {
      id: 'Other',
      value: 'Other'
    }
  ],
  ContentMotionsConst: [
    {
      id: 'STATIC',
      value: 'Static'
    },
    {
      id: 'DYNAMIC',
      value: 'Dynamic'
    }
  ],
  ThermostatTypesConst: [
    {
      id: 'High Limit'
    },
    {
      id: 'Control'
    },
    {
      id: 'Low Limit'
    }
  ],
  ProductGroupConst: [
    {
      id: 'CLOTH',
      value: 'Cloth'
    },
    {
      id: 'SILICONE',
      value: 'Silicone'
    },
    {
      id: 'INSEPARABLE',
      value: 'Inseparable'
    }
  ],
  ProductTypeConst: [ // Legacy alias
    {
      id: 'CLOTH',
      value: 'Cloth'
    },
    {
      id: 'SILICONE',
      value: 'Silicone'
    },
    {
      id: 'INSEPARABLE',
      value: 'Inseparable'
    }
  ],
  valueByProductGroupSilicone: 'Silicone',
  valueByProductGroupCloth: 'Cloth',
  valueByProductGroupInseparable: 'Inseparable',
  valueByProductGroupINSEPARABLE: 'INSEPARABLE',
  // Legacy aliases
  valueByProductTypeSilicone: 'Silicone',
  valueByProductTypeCloth: 'Cloth',
  valueByProductTypeInseparable: 'Inseparable',
  valueByProductTypeINSEPARABLE: 'INSEPARABLE',
  FitTypeConst: [
    {
      id: 'FORM_FITTING',
      value: 'Form Fitting'
    },
    {
      id: 'LOOSE_FITTING',
      value: 'Loose Fitting'
    },
    {
      id: 'NOT_APPLICABLE',
      value: 'Not Applicable'
    }
  ],
  CCDC_WorkFlow_ProductGroupConst: [
    {
      id: 'CLOTH',
      value: 'Cloth'
    },
    {
      id: 'SILICONE',
      value: 'Silicone'
    },
    {
      id: 'COMPOSITES',
      value: 'Composites'
    },
    {
      id: 'TAPE',
      value: 'Tape'
    },
    {
      id: 'CONTROL',
      value: 'Control'
    },
    {
      id: 'CABLE',
      value: 'Cable'
    },
    {
      id: 'INSEPARABLE',
      value: 'Inseparable'
    },
    {
      id: 'OTHER',
      value: 'Other'
    }
  ],
  CCDC_WorkFlow_ProductTypeConst: [ // Legacy alias
    {
      id: 'CLOTH',
      value: 'Cloth'
    },
    {
      id: 'SILICONE',
      value: 'Silicone'
    },
    {
      id: 'COMPOSITES',
      value: 'Composites'
    },
    {
      id: 'TAPE',
      value: 'Tape'
    },
    {
      id: 'CONTROL',
      value: 'Control'
    },
    {
      id: 'CABLE',
      value: 'Cable'
    },
    {
      id: 'INSEPARABLE',
      value: 'Inseparable'
    },
    {
      id: 'OTHER',
      value: 'Other'
    }
  ],

  TRACKER_FIELDS_PRODUCT_GROUPS: [
    {
      id: 'Cloth-Ind-Us'
    },
    {
      id: 'Cloth-Ind-V'
    },
    {
      id: 'Cloth-EndUser-US'
    },
    {
      id: 'Cloth-EndUser-V'
    },
    {
      id: 'Cloth-OEM-US'
    },
    {
      id: 'Cloth-OEM-V'
    },
    {
      id: 'Control'
    },
    {
      id: 'Cable'
    },
    {
      id: 'Composites'
    },
    {
      id: 'Fabroc'
    },
    {
      id: 'Silicone'
    },
    {
      id: 'Tape'
    },
    {
      id: 'Vacuum Table'
    },
    {
      id: 'Wire'
    },
    {
      id: 'Manual'
    },
    {
      id: 'Power Cords'
    },
    {
      id: 'Other'
    }
  ],
  TRACKER_FIELDS_PRODUCT_TYPES: [ // Legacy alias
    {
      id: 'Cloth-Ind-Us'
    },
    {
      id: 'Cloth-Ind-V'
    },
    {
      id: 'Cloth-EndUser-US'
    },
    {
      id: 'Cloth-EndUser-V'
    },
    {
      id: 'Cloth-OEM-US'
    },
    {
      id: 'Cloth-OEM-V'
    },
    {
      id: 'Control'
    },
    {
      id: 'Cable'
    },
    {
      id: 'Composites'
    },
    {
      id: 'Fabroc'
    },
    {
      id: 'Silicone'
    },
    {
      id: 'Tape'
    },
    {
      id: 'Vacuum Table'
    },
    {
      id: 'Wire'
    },
    {
      id: 'Manual'
    },
    {
      id: 'Power Cords'
    },
    {
      id: 'Other'
    }
  ],
  PowerCordMaterialConstant: [
    {
      id: 'A',
      value: 'Fiberglass Leadwire',
      price: 0.8
    },
    {
      id: 'B',
      value: 'SO Cord',
      price: 0.5
    }
  ],
  PowerCordVoltageConstant: [
    {
      id: '1',
      value: '120VAC'
    },
    {
      id: '2',
      value: '208VAC'
    },
    {
      id: '3',
      value: '220VAC'
    },
    {
      id: '4',
      value: '240VAC'
    },
    {
      id: '5',
      value: '277VAC'
    },
    {
      id: '6',
      value: '480VAC'
    },
    {
      id: '7',
      value: 'Up to 600VAC'
    },
    {
      id: '8',
      value: 'Up to 300VAC'
    },
    {
      id: 'S',
      value: 'Special'
    }
  ],
  PowerCordAMPSConstant: [
    {
      id: '1',
      value: '10A'
    },
    {
      id: '2',
      value: '15A'
    },
    {
      id: '3',
      value: '20A'
    },
    {
      id: '4',
      value: '30A'
    },
    {
      id: '5',
      value: '3A'
    },
    {
      id: 'S',
      value: 'Special'
    }
  ],
  PowerCordOptionsConstant: [
    {
      id: 'A',
      value: 'SS Braiding'
    },
    {
      id: 'B',
      value: 'Tin/Copper Braiding'
    },
    {
      id: 'C',
      value: '2 Connectors to 1 Plug'
    },
    {
      id: 'D',
      value: '3 Connectors to 1 Plug'
    },
    {
      id: 'F',
      value: '4 Connectors to 1 Plug'
    },
    {
      id: 'E',
      value: '1 Connector to 2 Plugs'
    }
  ],
  AnticipatedValuesConstant: [
    {
      id: 'Less than 5',
      value: 'Less than 5'
    },
    {
      id: '6 to 50',
      value: '6 to 50'
    },
    {
      id: 'More than 50',
      value: 'More than 50'
    }
  ],
  TestingDepartmentValuesConstant: [
    {
      id: 'Engineering Quality',
      value: 'Engineering Quality'
    },
    {
      id: 'Manufacturing',
      value: 'Manufacturing'
    },
    {
      id: 'Sales/Marketing',
      value: 'Sales/Marketing'
    }
  ],
  ElementType: [
    {
      id: 'WIRE',
      value: 'Wire'
    },
    {
      id: 'TAPE',
      value: 'Tape'
    },
    {
      id: 'DUALWIRE',
      value: 'Dual Wire Tape'
    }
  ],
  BHXMaterialElementType: [
    {
      id: 'WIRE',
      value: 'Wire'
    },
    {
      id: 'TAPE',
      value: 'Tape'
    },
    {
      id: 'DUALWIRE',
      value: 'Dual Wire Tape'
    },
    {
      id: 'TAJIMA',
      value: 'Tajima'
    }
  ],
  DEFAULT_PRODUCT_IMAGE: '..//..//..//assets//images//ft-logo.png',
  relOp: [5, 30, 45, 47, 50, 60],
  pageSize: 25,

  wireTypeConst: [
    {
      id: 'Grounded',
      value: 'Grounded'
    },
    {
      id: 'Samox',
      value: 'Samox'
    },
    {
      id: 'Fiberglass',
      value: 'Fiberglass'
    }
  ],
  wireTypeSiliconeConst: [
    {
      id: 'Silicone Grounded',
      value: 'Silicone Grounded'
    },
    {
      id: 'Silicone Non Grounded',
      value: 'Silicone Non Grounded'
    }
  ],
  wireTypesBHXMaterialMaster: [
    {
      id: 'Grounded',
      value: 'Grounded'
    },
    {
      id: 'Samox',
      value: 'Samox'
    },
    {
      id: 'Fiberglass',
      value: 'Fiberglass'
    },
    {
      id: 'Silicone Grounded',
      value: 'Silicone Grounded'
    },
    {
      id: 'Silicone Non Grounded',
      value: 'Silicone Non Grounded'
    }
  ],
  productTypeSilicone: 'SILICONE',
  ResultTypeValue: 'value',
  ResultTypeES: 'es',
  ResultTypeElement: 'elementOnly',
  ResultTypeSensors: 'sensorsOnly',
  ResultTypeLabels: 'labels',
  ResultTypeflc: 'flc',
  ResultTypewp: 'wp',
  proprtyNamePartNumber: 'partNumber',
  proprtyNameQuantity: 'qty',
  propertyNameRelOp: 'relOperation',
  propertyNameRelDesc: 'description',
  propertyNameRelUOM: 'uom',
  jacketTypeNonGrounded: 'Non-Grounded',
  jacketTypeNonGroundedSamox: 'Non-Grounded Samox',
  wireTypeSamox: 'Samox',
  wireTypeFiberglass: 'Fiberglass',
  EcoChangeTypes: ['Rolling Change', 'Immediate change'],
  QuotStatusTypes: [
    {id: 'design', name: 'Design Engineering'},
    {id: 'both', name: 'Both'},
    {id: 'app', name: 'App Engineering'}
  ],
  MaterialPropertyTypes: ['MATERIAL', 'CONTENT'],
  PlugLightsTypes: ['RED', 'GREEN'],
  GoldStandardTapeWidthTypes: [
    {id: 'grounded', value: 'Grounded'},
    {id: 'fiberglass', value: 'Fiberglass'}
  ],
  BHXGoldWireTapeType: ['str', 'tpi'],
  ManufacturingCountries: ['Usa', 'Vietnam', 'Costa Rica'],
  MadeInCountryUSA: 'Usa',
  MadeInCountryVietnam: 'Vietnam',
  MadeInCountryCostaRica: 'Costa Rica',
  BHXGoldWireTapeWireType: ['grounded', 'fiberglass'],
  wiringPluggingDisplayedColumnsValues: [
    'partNumber',
    'description',
    'qty',
    'uom',
    'relOperation',
    'update',
    'delete'],
  elementsSensorsDisplayedColumnsValues: [
    'partNumber',
    'description',
    'qty',
    'uom',
    'relOperation',
    'update',
    'delete'
  ],
  sensorsDisplayedColumnsValues: [
    'partNumber',
    'description',
    'qty',
    'uom',
    'relOperation',
    'update',
    'delete'
  ],
  facingLinerClosureDisplayedColumnsValues: [
    'partNumber',
    'description',
    'qty',
    'uom',
    'relOperation',
    'update',
    'delete'
  ],
  labelsDisplayedColumnsValues: [
    'partNumber',
    'description',
    'qty',
    'uom',
    'relOperation',
    'update',
    'delete'
  ],
  operationsdisplayedColumnsValues: [
    'sequence',
    'opNumber',
    'operationName',
    'prodHrs',
    'setupHrs',
    'update',
    'delete'
  ],
  ElementDualWireTape: 'Dual Wire Tape',
  ElementTape: 'Tape',
  HeatingTapesTypes: [
    {id: 'fiberglass', value: 'Fiberglass'},
    {id: 'grounded', value: 'Grounded'},
    {id: 'samox', value: 'Samox'},
    {id: 'HTC', value: 'HTC'},
    {id: 'HTE', value: 'HTE'},
    {id: 'HTCG', value: 'HTCG'},
    {id: 'silicone grounded', value: 'Silicone Grounded'},
    {id: 'silicone non grounded', value: 'Silicone Non Grounded'}
  ],
  HeatingTapesWireTypes: [
    {id: 'Wire', value: 'Wire'},
    {id: 'tpi', value: 'TPI'},
    {id: 'Strands', value: 'Strands'}
  ],
  GroupingsBHXMaterial: [
    {id: 1, name: 'Label'},
    {id: 6, name: 'Element'},
    {id: 2, name: 'Sensor'},
    {id: 3, name: 'Facing Liner Closure'},
    {id: 4, name: 'Wire Plugging'},
    {id: 5, name: 'Operation'}
  ],
  UOMBHXMaterial: ['CG', 'CM', 'EA', 'SQ. CM', 'ML'],
  TargetCountryTypes: ['USA', 'VIETNAM', 'Costa Rica'],
  Connectors: ['2 Pos Mate-N-Lok', '3 Pos Mate-N-Lok', '3 Pos Mate-N-Lok Black', '4 Pos CPC', '4 Pos Mate-N-Lok', '5 Pos Mate-N-Lok'],
  GreenLights: ['In Plug', 'None', 'NULL'],
  Operations: [
    'Cloth Patterns',
    'Flex & Lab Braiders',
    'Inspection',
    'KNITTERS',
    'Manual Cutting Table',
    'Standard Cloth Line 1',
    'Stockroom',
    'Tajima Machine',
    'Vietnam Assembly',
    'Vietnam Kitting'
  ],
  BHXMaterialFormulaConst: {
    A: 'CLOSURE LENGTH',
    B: 'LEAD LENGTH',
    C: 'SMALL BAR',
    D: 'THERMOSTAT COUNT',
    E: 'JUMPER QTY',
    F: 'BIG BAR',
    G: 'JACKET LENGTH',
    H: 'JUMPER LEAD LENGTH',
    I: 'DOUBLE BAR',
    Z: 'TAPE THREAD',
    J: 'WELDMENT LENGTH',
    K: 'WELDMENT DIAMETER',
    L: 'QTR VCR',
    M: 'HALF VCR',
    N: 'Silicone Width',
    REMOVABLE_LAYER: 'REMOVABLE LAYER',
    P: 'REMOVABLE QTY',
    LAYER: 'LAYER',
    ROUND_UP: 'ROUND_UP',
    ROUND_DOWN:'ROUND_DOWN',
  },
  BHXMaterialFormulaFullName: {
    'CLOSURE LENGTH': 'A',
    'LEAD LENGTH': 'B',
    'SMALL BAR': 'C',
    'THERMOSTAT COUNT': 'D',
    'JUMPER QTY': 'E',
    'BIG BAR': 'F',
    'JACKET LENGTH': 'G',
    'JUMPER LEAD LENGTH': 'H',
    'DOUBLE BAR': 'I',
    'TAPE THREAD': 'Z',
    'WELDMENT LENGTH': 'J',
    'WELDMENT DIAMETER': 'K',
    'QTR VCR': 'L',
    'HALF VCR': 'M',
    'Silicone Width': 'N',
    'REMOVABLE LAYER': 'REMOVABLE_LAYER',
    'REMOVABLE QTY': 'P',
    'ROUND_UP': 'ROUND_UP',
    'ROUND_DOWN':'ROUND_DOWN',
    LAYER: 'LAYER'
  },
  ThermostatTypesMasterConst: [
    {id: '1/2 Disc'},
    {id: '1/2" Disc'},
    {id: '1/2"W X 1"L X 1/2"H'},
    {id: '3/4" Disc'},
    {id: 'Block'},
    {id: 'FUSE'},
    {id: 'PEPI'},
    {id: 'PEPI (B)'},
    {id: 'Thin'},
    {id: 'UP62'},
    {id: 'Wet Area'},
    {id: 'ADJ'}
  ],
  sectionList: [
    {id: 'OPERATIONS', value: 'Operations', title: 'Operations'},
    {id: 'LABELS', value: 'Labels', title: 'Labels'},
    {id: 'ELEMENTS', value: 'Elements', title: 'Elements'},
    {id: 'SENSORS', value: 'Sensors', title: 'Sensors'},
    {id: 'FACING_LINER_CLOSURE', value: 'FacingLinerClosure', title: 'Facing / Liner / Closure'},
    {id: 'WIRE_PLUGGING', value: 'WirePlugging', title: 'Wire / Plugging'},
  ],
  GeometryFeaturesShortCuts: ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 's', 'g', 'h'],
  ListTypes: {
    element: 'element',
    geometry: 'geometry',
    titleblockeditor: 'titleblockeditor',
    bomeditor: 'bomeditor',
    finalReview: 'finalreview'
  },

  Countries: {
    countryUsa: 'Usa',
    countryVietnam: 'Vietnam'
  },

  geometryVCR: [
    {id: '0.25', name: '1/4'},
    {id: '0.5', name: '1/2'},
    {id: '0', name: 'None'}
  ],

  geometryWay: [
    {id: '3way', name: '3 way'},
    {id: '4way', name: '4 way'},
    {id: '5way', name: '5+ way'}
  ],

  FastnerCode: [
    {id: '1'},
    {id: '2'},
    {id: '3'},
    {id: '4'},
    {id: '5'},
    {id: '6'},
    {id: '7'},
    {id: '8'},
    {id: 'N'},
    {id: 'N/A'}
  ],
  FilterFields: {
    partNumber: 'partNumber',
    name: 'name',
    description: 'description',
    userId: 'userId',
    templateName: 'name',
    id: 'id',
    controllerName: 'controllerName',
    gndPartNumber: 'gndPartNumber',
    alloyName: 'alloyName',
    partNumber1: 'partNumber1',
    status: 'status',
    tapeType: 'tapeType',
    tapePartNumber: 'tapePartNumber',
    leadName: 'leadName',
    material: 'material',
    plugName: 'plugName',
    value: 'value',
    tapeWidth: 'tapeWidth',
    newTapeWidth: 'width',
    maxTapeWidth: 'maxTapeWidth',
    minTapeWidth: 'minTapeWidth',
    filterDataTypeFloat: 'Float',
    filterLessThanEqualsTo: 'LtEq',
    filterGreaterThanEqualsTo: 'GtEq'
  },
  // bhx-material checkbox labels
  CheckboxLabels: {
    YES: 'Yes',
    NO: 'No',
    NONE: 'None',
    NULL: 'NULL'
  },
  ElementTypes: {
    WireType: 'Wire',
    Tape: 'Tape',
    DualWireTape: 'Dual Wire Tape'
  },
  OutdatedViewError: 'Please reload the page, your view seems to be outdated',
  BHXMaterial_CheckBox_Titles: {
    ClothCE: 'ClothCE',
    ClothUL: 'ClothUL',
    Hazardous: 'Hazardous',
    Thermostat: 'Thermostat',
    ManualReset: 'Manual Reset T/S',
    PrivateLabel: 'Private Label'
  },

  SolidWorksBlock_CheckBox_Titles: {
    RedLight: 'RedLight',
    GreenLight: 'GreenLight',
    AnySensor: 'AnySensor',
    AnyThermostat: 'AnyThermostat',
    OpenOnRise: 'OpenOnRise',
    ManualReset: 'ManualReset'
  },

  BHXMaterial_DualState_CheckBoxes_Value: {
    Blocked: 'blocked',
    Deleted: 'deleted'
  },

  QuoteStatus_Customer_Clarification_Required: 'Quote in Process - Customer Clarification Required',
  QuoteStatus_External_Quote_Required: 'External Quote Required',
};

export const finalizeInformationChangedField = {
  QUANTITY: 'QUANTITY',
  LABORHOUR: 'LABORHOUR',
  MATERIALCOST: 'MATERIALCOST',
  COST: 'COST',
  LISTPRICE: 'LISTPRICE',
  DISCOUNT: 'DISCOUNT',
  NONE: 'NONE'
};

export const LabelConfigVariables = {
  LABEL_PART_NUMBER: 'Label Part Number',
  VOLTAGE: 'Voltage',
  WATTAGE: 'Wattage',
  AMPERAGE: 'Amperage',
  PHASE: 'Phase',
  DIMENSIONS: 'Dimensions',
  LENGTH: 'Length',
  WIDTH: 'Width',
  JACKET_PART_NUMBER: 'Jacket Part Number',
  DESC_2: 'DESC 2',
  CUSTOMER_PART_NUMBER: 'Customer Part Number'
};
