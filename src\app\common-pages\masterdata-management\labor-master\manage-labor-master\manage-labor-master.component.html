<div class="sfl-loading" *ngIf="showLoader">
    <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
    </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
    {{ title }}
    <hr/>
</h2>
<form class="forms_form" #laborMasterForm="ngForm" (ngSubmit)="updateLaborMaster()">
    <mat-dialog-content>
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-form-field>
                    <input
                            matInput
                            placeholder="Labor"
                            [(ngModel)]="laborMaster.labor"
                            name="labor"
                            #laborInput="ngModel"
                            required
                            sflNoWhiteSpaces
                    />
                </mat-form-field>
                <div *ngIf="laborInput.touched && laborInput.invalid">
                    <small class="mat-text-warn" *ngIf="laborInput?.errors?.required">Labor is required.</small>
                    <small class="mat-text-warn" *ngIf="laborInput?.errors?.whitespace && !laborInput?.errors?.required"
                    >Invalid Labor.
                    </small>
                </div>
            </div>
            <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-form-field>
                    <input
                            matInput
                            placeholder="Burden"
                            [(ngModel)]="laborMaster.burden"
                            name="burden"
                            #burdenInput="ngModel"
                            required
                            sflNoWhiteSpaces
                    />
                </mat-form-field>
                <div *ngIf="burdenInput.touched && burdenInput.invalid">
                    <small class="mat-text-warn" *ngIf="burdenInput?.errors?.required">Burden is required.</small>
                    <small class="mat-text-warn"
                           *ngIf="burdenInput?.errors?.whitespace && !burdenInput?.errors?.required"
                    >Invalid Burden.
                    </small>
                </div>
            </div>
          <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
            <mat-form-field>
              <input
                matInput
                placeholder="Material OH"
                [(ngModel)]="laborMaster.materialOH"
                name="materialOH"
                #materialOHInput="ngModel"
                required
                sflNoWhiteSpaces
              />
            </mat-form-field>
            <div *ngIf="materialOHInput.touched && materialOHInput.invalid">
              <small class="mat-text-warn" *ngIf="materialOHInput?.errors?.required">Material OH is required.</small>
              <small class="mat-text-warn"
                     *ngIf="materialOHInput?.errors?.whitespace && !materialOHInput?.errors?.required"
              >Invalid Burden.
              </small>
            </div>
          </div>
            <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-form-field>
                    <input
                            matInput
                            placeholder="country"
                            [(ngModel)]="laborMaster.country"
                            name="country"
                            #countryInput="ngModel"
                            required
                            sflNoWhiteSpaces
                    />
                </mat-form-field>
                <div *ngIf="countryInput.touched && countryInput.invalid">
                    <small class="mat-text-warn" *ngIf="countryInput?.errors?.required">Country is required.</small>
                    <small class="mat-text-warn"
                           *ngIf="countryInput?.errors?.whitespace && !countryInput?.errors?.required"
                    >Invalid Country.
                    </small>
                </div>
            </div>
            <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="laborMaster.isObsolete">Is Obsolete
                </mat-checkbox>
            </div>
        </div>
    </mat-dialog-content>

    <mat-dialog-actions fxLayoutAlign="space-between">
        <div fxLayoutAlign="start">
            <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
        </div>
        <div fxLayoutAlign="end">
            <button mat-raised-button type="submit" color="warn" [disabled]="!laborMasterForm.valid">Submit</button>
        </div>
    </mat-dialog-actions>
</form>
