
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../shared/helpers/app.helper';


import { Observable } from 'rxjs';
import { QuotationSearchFilter } from 'src/app/admin-pages/dashboard/dashboard.model';
import { QuotationList } from './topbar.model';

@Injectable({ providedIn: 'root' })
export class TopbarService {

    constructor(private http: HttpClient) { }

    getQuotation(quotso: string) {
        return <Observable<QuotationList[]>>this.http.get(AppConfig.QUOT_LIST + '/' + quotso).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

    getQuotationDesignSide(quotso: string) {
        return <Observable<QuotationSearchFilter[]>>this.http.get(AppConfig.QUOT_LIST_DESIGN + '/' + quotso).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

}
