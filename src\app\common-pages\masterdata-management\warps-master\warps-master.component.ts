import { Component, OnInit, <PERSON>Child, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { WarpsMaster, GenericPageable, WarpsFilter } from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SnakbarService, SweetAlertService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageWarpsMasterComponent } from './manage-warps-master/manage-warps-master.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-warps-master',
  templateUrl: './warps-master.component.html'
})
export class WarpsMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Warp Master';
  warpMaster: WarpsMaster;
  warpPageable: GenericPageable<WarpsMaster>;
  warpDataSource = new MatTableDataSource<WarpsMaster>();
  warpColumns = DisplayColumns.Cols.WarpsMasterCols;
  warpFilter: WarpsFilter = new WarpsFilter();

  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  showLoader = false;
  numberOfElements: number;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  maxTapeWidth = Values.FilterFields.maxTapeWidth;
  minTapeWidth = Values.FilterFields.minTapeWidth;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) {}

  ngOnInit() {
    this.getWarpMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new warp
  addWarp() {
    this.editWarp(new WarpsMaster());
  }

  // used to add filter to warp listing
  async addFilter() {
    this.filter = [];
    if (!this.warpFilter.maxTapeWidth && !this.warpFilter.minTapeWidth) {
      this.filter = [];
    } else {
      if (this.warpFilter.maxTapeWidth) {
        this.filter.push({
          key: Values.FilterFields.tapeWidth,
          value: Number(this.warpFilter.maxTapeWidth),
          dataType: Values.FilterFields.filterDataTypeFloat,
          operator: Values.FilterFields.filterLessThanEqualsTo
        });
      }
      if (this.warpFilter.minTapeWidth) {
        this.filter.push({
          key: Values.FilterFields.tapeWidth,
          value: Number(this.warpFilter.minTapeWidth),
          dataType: Values.FilterFields.filterDataTypeFloat,
          operator: Values.FilterFields.filterGreaterThanEqualsTo
        });
      }
    }

    this.getWarpMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter to warp listing
  clearFilter(fieldToClear: string) {
    this.filter = [];
    if (fieldToClear === Values.FilterFields.maxTapeWidth) {
      this.warpFilter.maxTapeWidth = null;
      this.filter.push({
        key: Values.FilterFields.tapeWidth,
        value: Number(this.warpFilter.minTapeWidth ? this.warpFilter.minTapeWidth : null),
        dataType: Values.FilterFields.filterDataTypeFloat,
        operator: Values.FilterFields.filterGreaterThanEqualsTo
      });
    }
    if (fieldToClear === Values.FilterFields.minTapeWidth) {
      this.warpFilter.minTapeWidth = null;
      this.filter.push({
        key: Values.FilterFields.tapeWidth,
        value: Number(this.warpFilter.maxTapeWidth ? this.warpFilter.maxTapeWidth : null),
        dataType: Values.FilterFields.filterDataTypeFloat,
        operator: Values.FilterFields.filterLessThanEqualsTo
      });
    }
    if (!this.warpFilter.maxTapeWidth && !this.warpFilter.minTapeWidth) {
      this.filter = [];
    }
    this.getWarpMasterData(this.initialPageIndex, this.pageSize);
  }

  getWarpMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getWarpsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<WarpsMaster>) => {
          this.warpPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createWarpTable(this.warpPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.warpDataSource.data = [];
        }
      )
    );
  }

  createWarpTable(serviceRequestList: GenericPageable<WarpsMaster>) {
    this.warpDataSource.data = serviceRequestList.content;
  }

  getWarpMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getWarpMasterData(this.pageIndex, this.pageSize);
  }

  getWarpMasterSorting(event) {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getWarpMasterData(this.pageIndex, this.pageSize);
  }

  async deleteWarp(WarpId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteWarps(WarpId).subscribe(
        () => {
          this.snakbarService.success('Warp' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getWarpMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }
  editWarp(Warp: WarpsMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = Warp;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-warp-master-model';
    const dialogRef = this.matDialog.open(ManageWarpsMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          Warp.id
            ? 'Warp' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Warp' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getWarpMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
