import {catchError, map} from 'rxjs/operators';
import {
  CcdcWorkflow,
  MaterialInfoReq,
  Notes,
  PluggingInformation,
  Quotation,
  ThermostatInfo,
  ThermostatList
} from './ccdc-model/ccdc.model';
import {AppConfig} from '../../app.config';
import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {utils} from '../../shared/helpers/app.helper';
import {SharedService} from '../../shared';

@Injectable()
export class SalesOrderSummaryService {
  constructor(private http: HttpClient, private readonly sharedService: SharedService) {}

  getQuotation(quoteNumber: string) {
    return this.http
      .get(AppConfig.GET_QUOTATION + '/' + quoteNumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }


  saveQuotation(quotation: Quotation) {
    return this.http.post(AppConfig.BHX_SAVE_QUOTATION, quotation).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getQUotById(quotID: number) {
    return this.http
      .get(AppConfig.GET_QUOT_BY_ID + '/' + quotID).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  saveOrUpdateQuotation(quotation: Quotation, quoteId: number) {
    if (quoteId) {
      return this.http.put(AppConfig.BHX_SAVE_QUOTATION, quotation).pipe(map(utils.extractData),catchError(utils.handleError),);
    } else {
      return this.http.post(AppConfig.BHX_SAVE_QUOTATION, quotation).pipe(map(utils.extractData),catchError(utils.handleError),);
    }
  }

  getMaterialLayer() {
    return this.http.get(AppConfig.GET_MATERIAL_LAYER).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getMaterialLayerIdentifiers() {
    return this.http.get(AppConfig.GET_MATERIAL_LAYER_IDENTIFIERS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getMaterialLayerById(id) {
    return this.http
      .get(AppConfig.GET_MATERIAL_LAYER + '/' + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  openFileInExplorer(filePath) {
    return this.http.post(AppConfig.OPEN_FILE_LOCATION, {
      path: filePath,
      ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations()
    });
  }

  saveMaterial(materialInfo: MaterialInfoReq, jgId: number) {
    return this.http
      .post(AppConfig.SAVE_MATERIAL + jgId, materialInfo).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getAllMaterial(id: number) {
    return this.http
      .get(AppConfig.GET_ALL_MATERIAL_BY_JACKETGROUP_ID + '/' + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteMaterial(id: number) {
    return this.http
      .delete(AppConfig.DELETE_MATERIAL + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  saveOrUpdateWorkFlow(workFlow: CcdcWorkflow, workFlowId: number) {
    if (workFlowId) {
      return this.http.put(AppConfig.WORKFLOW_API, workFlow).pipe(map(utils.extractData),catchError(utils.handleError),);
    } else {
      return this.http.post(AppConfig.WORKFLOW_API, workFlow).pipe(map(utils.extractData),catchError(utils.handleError),);
    }
  }

  getWorkFlowByJacketGroupId(id: number) {
    return this.http
      .get(AppConfig.WORKFLOW_BY_JACKETGROUP_API + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  saveNote(note: Notes, noteId: number) {
    if (noteId) {
      return this.http.put(AppConfig.SAVE_NOTES, note).pipe(map(utils.extractData),catchError(utils.handleError),);
    } else {
      return this.http.post(AppConfig.SAVE_NOTES, note).pipe(map(utils.extractData),catchError(utils.handleError),);
    }
  }

  getNotesByJacketGroupId(id: number) {
    return this.http
      .get(AppConfig.GET_NOTES_JACKETGROUP_ID + '/' + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
  getAllPlugs() {
    return this.http.get(AppConfig.PLUGS_API).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getPlugsByJacketType(jacketType: string) {
    return this.http
      .get(AppConfig.GET_PLUGS_BY_JACKET_TYPE + jacketType).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getThermostatsList() {
    return this.http.get(AppConfig.GET_THERMOSATS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  saveThermostatsInfo(thermostatInfo: ThermostatInfo, jgid: number) {
    return this.http
      .post(AppConfig.SAVE_THERMOSATS_INFO + jgid, thermostatInfo).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getThermostatByJacketGroupId(id: number) {
    return this.http
      .get(AppConfig.GET_THERMOSTAT_BY_JGID + '/' + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
  savePluggingInformation(pluggingInformation: PluggingInformation, pgId: number) {
    if (pgId) {
      return this.http.put(AppConfig.PLUGGINGINFORMATION_API, pluggingInformation).pipe(map(utils.extractData),catchError(utils.handleError),);
    } else {
      return this.http.post(AppConfig.PLUGGINGINFORMATION_API, pluggingInformation).pipe(map(utils.extractData),catchError(utils.handleError),);
    }
  }

  getPluggingInformationByJacketGroupId(jgId: number) {
    return this.http
      .get(AppConfig.PLUGGING_BY_JACKETGROUP_API + jgId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  searchThermostate(thermostatList: ThermostatList) {
    return this.http.post(AppConfig.SEARCH_THERMOSTATE, thermostatList).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  deleteThermostate(id: number) {
    return this.http
      .delete(AppConfig.DELETE_THERMOSTAT + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  uploadDocument(formData: FormData) {
    return this.http.post(AppConfig.DOCUMENT_API, formData).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getDocumentListyByQuoteId(id) {
    return this.http
      .get(AppConfig.DOCUMENT_API + 'quotationId/' + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getDocument(id) {
    return this.http
      .get(AppConfig.DOCUMENT_API + id, { responseType: 'blob' }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteDocument(id) {
    return this.http
      .delete(AppConfig.DOCUMENT_API + +id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getPluginMasterData(): any {
    return this.http.get(AppConfig.GET_PLUGIN_MASTER_DATA).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  changeEntryMethod(quotationId: number, entryMethod: string) {
    return this.http
      .put(AppConfig.ENTRY_METHOD + quotationId + '/' + entryMethod, {}).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getLeadTypeMaster() {
    return this.http.get(AppConfig.LEAD_TYPE_MASTER).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getThermostatTypeList() {
    return this.http.get(AppConfig.THERMOSTAT_TYPES).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
  getInstallationMethod() {
    return this.http.get(AppConfig.THERMOSTAT_INSTALLATION_METHOD).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  // used to get the available sales associates
  getSalesAssociate(isGlobalSearch: boolean) {
    return this.http
      .get(AppConfig.SalesAssociate + '/' + `${isGlobalSearch}`).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  // used to get the available account managers
  getAccountManagers() {
    return this.http.get(AppConfig.ACCOUNT_MANAGERS).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  // used to get the default values for quote by quote id
  getQuoteDefaultValues(quoteId: number) {
    return this.http
      .get(AppConfig.QUOTE_DEFAULTS + `${quoteId}`).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
