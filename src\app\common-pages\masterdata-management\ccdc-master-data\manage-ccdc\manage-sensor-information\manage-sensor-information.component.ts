import { Component, Input, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { MatTableDataSource } from '@angular/material';
import { Subscription } from 'rxjs';
import { SensorService } from 'src/app/admin-pages/new-quotation/Add Sensors/add-sensors.service';
import { CcdcTemplateDTO, SensorConnector, ThermostatTypesMaster } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { Messages, SnakbarService, SweetAlertService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SensorConnectorsMaster, SensorTypesMaster } from '../../../masterdata-management.model';
import { SensorInformationObject, SensorsInformationTemplateDTO } from '../manage-ccdc.model';


@Component({
  selector: 'app-manage-sensor-information',
  templateUrl: './manage-sensor-information.component.html',
  styleUrls: ['./manage-sensor-information.component.css']
})
export class ManageSensorInformationComponent implements OnInit {
  @Input("ccdcMasterData")
  ccdcMasterData: CcdcTemplateDTO;
  @Input("sensorInfoDto")
  sensorInfoDto: Array<SensorsInformationTemplateDTO>;
  sensorInfo: SensorsInformationTemplateDTO;
  sensorInfoObject: SensorInformationObject;
  sensorTypes: SensorTypesMaster[];
  sensorLocations: object = Values.SensorLocation;
  sensorConnectors: SensorConnectorsMaster[];
  subscription: Subscription = new Subscription();
  sensorsType: ThermostatTypesMaster[];
  SensorsdisplayedColumns = ['sensorType', 'sensorLocation', 'sensorConnector', 'sensorLeadLength', 'sensorTempType', 'action'];
  sensorsDataSource = new MatTableDataSource<SensorsInformationTemplateDTO>();
  isOtherSensorType = false;
  isOtherSensorLocation = false;
  isOtherSensorConnector = false;
  controlType = '';
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  constructor(
    private snakbarService: SnakbarService,
    public sensorService: SensorService,
    private sweetAlertService: SweetAlertService
  ) { }

  ngOnInit() {
    this.sensorInfo = new SensorsInformationTemplateDTO();
    this.setSensorsDataSource();
    this.getSensorsConnectors();
    this.getSensorsTypes();
    this.sensorInfo.sensorLocation = 'Inside Jacket Through Liner';
    this.getThermostatTypes();
  }

  setSensorsDataSource() {
    this.sensorsDataSource.data = this.sensorInfoDto;
  }

  // used to get the sensors conntectors
  getSensorsConnectors() {
    this.showLoader = true;
    this.subscription.add(
      this.sensorService.getSensorConnectorsList().subscribe(
        (sensorConnector: SensorConnectorsMaster[]) => {
          if (sensorConnector) {
            this.sensorConnectors = sensorConnector;
          }
          this.showLoader = false;
        },
        error => {
          this.sensorConnectors = [];
          this.showLoader = false;
        }
      )
    );
  }

  // used to get listing of sensors types
  getSensorsTypes() {
    this.showLoader = true;
    this.subscription.add(
      this.sensorService.getSensorTypesList().subscribe(
        (sensorTypes: SensorTypesMaster[]) => {
          if (sensorTypes) {
            this.sensorTypes = sensorTypes;
          }
          this.showLoader = false;
        },
        error => {
          this.sensorConnectors = [];
          this.showLoader = false;
        }
      )
    );
  }

  // used to get listing of thermostat types
  getThermostatTypes() {
    this.showLoader = true;
    this.subscription.add(
      this.sensorService.getThermostatTypeList().subscribe(
        (res: ThermostatTypesMaster[]) => {
          this.sensorsType = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to handle the sensor type change event
  onSensorTypeChanged(sensorType) {
    this.isOtherSensorType = false;
    if (sensorType === Values.Other) {
      this.isOtherSensorType = true;
    } else {
      this.sensorInfo.otherSensorType = null;
    }
  }

  // used to get listing of sensors types
  onSensorLocationChanged(sensorLocation) {
    this.isOtherSensorLocation = false;
    if (sensorLocation === Values.Other) {
      this.isOtherSensorLocation = true;
    } else {
      this.sensorInfo.otherSensorLocation = null;
    }
  }

  // used to handle the sensor connector change event
  onSensorConnectorChanged(sensorConnector) {
    this.isOtherSensorConnector = false;
    if (sensorConnector === Values.Other) {
      this.isOtherSensorConnector = true;
    } else {
      this.sensorInfo.otherSensorConnector = null;
    }
  }

  // used to add selected senser to the listing
  addToSensor(form: NgForm) {
    let tempTypeCount = false;
    this.sensorsDataSource.data.forEach(sensor => {
      if (sensor.sensorTempType.toLowerCase() === 'control') {
        tempTypeCount = true;
      }
    });
    if (this.controlType.toLowerCase() === 'lynx' && this.sensorInfo.sensorTempType.toLowerCase() === 'control' && tempTypeCount) {
      this.snakbarService.error(Messages.SENSOR_INFO.Sensor_Temp_Type, 3000);
    } else {
      this.sensorInfoDto.push(this.sensorInfo);
      this.sensorsDataSource.data = this.sensorInfoDto;
      this.sensorInfo = new SensorsInformationTemplateDTO();
      form.resetForm();
    }
  }

  // used to handle the updation of the sensor
  editFromSensor(sensorInfo: SensorsInformationTemplateDTO) {
    this.isOtherSensorType = false;
    this.isOtherSensorLocation = false;
    this.isOtherSensorConnector = false;
    this.sensorsDataSource.data.splice(this.sensorsDataSource.data.indexOf(sensorInfo), 1);
    this.sensorsDataSource._updateChangeSubscription();
    this.sensorInfo = sensorInfo;
    if (this.sensorInfo.sensorType === Values.Other) {
      this.isOtherSensorType = true;
    }
    if (this.sensorInfo.sensorLocation === Values.Other) {
      this.isOtherSensorLocation = true;
    }
    if (this.sensorInfo.sensorConnector && this.sensorInfo.sensorConnector.id === Values.Other) {
      this.isOtherSensorConnector = true;
    }
    if (!this.sensorInfo.sensorConnector) {
      this.sensorInfo.sensorConnector = new SensorConnector();
    }
  }

  // used to handle
  async removeFromSensor(ele) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.sensorsDataSource.data.splice(this.sensorsDataSource.data.indexOf(ele), 1);
      this.sensorsDataSource._updateChangeSubscription();
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
