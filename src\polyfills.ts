/**
 * This file includes polyfills needed by <PERSON><PERSON> and is loaded before the app.
 * You can add your own extra polyfills to this file.
 *
 * This file is divided into 2 sections:
 *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.
 *   2. Application imports. Files imported after ZoneJS that should be loaded before your main
 *      file.
 *
 * The current setup is for "evergreen" browsers; the last versions of browsers that
 * automatically update themselves, including Chrome.
 *
 * Learn more in https://angular.io/guide/browser-support
 */

/***************************************************************************************************
 * BROWSER POLYFILLS
 */

// No need for core-js imports for ES6/ES7 features, as Chrome supports these natively

/** Evergreen browsers require these. **/
// No need for reflect-metadata polyfills
// import 'core-js/es6/reflect';
// import 'core-js/es7/reflect';

/** Remove polyfills for IE, Firefox, and other non-Chrome browsers */
// import 'classlist.js';
// import 'web-animations-js';

/** Evergreen browsers require these. **/
// No need for reflect-metadata polyfills
// import 'core-js/es6/reflect';
// import 'core-js/es7/reflect';
/** Remove polyfills for IE, Firefox, and other non-Chrome browsers */
// import 'classlist.js';
// import 'web-animations-js';
/***************************************************************************************************
 * Zone JS is required by Angular itself.
 */
import 'zone.js/dist/zone'; // Included with Angular CLI

/***************************************************************************************************
 * APPLICATION IMPORTS
 */

// If you are using Angular Material or other libraries that need additional polyfills, include them here
// Remove 'intl' as Chrome natively supports Intl API
// import 'intl';

// Remove 'hammerjs' unless you are using it for gesture support in Angular Material
// import 'hammerjs';
