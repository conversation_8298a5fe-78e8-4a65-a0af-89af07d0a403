import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { ThermostatMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-manage-thermostat-list',
  templateUrl: './manage-thermostat-list.component.html'
})
export class ManageThermostatListComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  thermostatList: ThermostatMaster;
  _data: ThermostatMaster;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  thermostatTypes = Values.ThermostatTypesMasterConst;
  constructor(
    public readonly dialogRef: MatDialogRef<ManageThermostatListComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this._data = data;
  }

  ngOnInit() {
    this.thermostatList = this._data.thermostatListId ? Object.assign({}, this._data) : new ThermostatMaster();
    this._data.thermostatListId ? (this.title = 'Update Thermostat') : (this.title = 'Add Thermostat');
  }

  updateThermostatList() {
    this.showLoader = true;
    if (this._data.thermostatListId) {
      this.subscription.add(
        this.masterDataService.updateThermostatsList(this.thermostatList).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addThermostatsList(this.thermostatList).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  // takes temperature type and propertyName which is property of our thermostatList DTO like [closeTemp, closeTolC, openTemp or tolerance] and sends to master data service method for conversion based on the type [celcius <-> fahrenheit]
  convertTemperature(temperatureType: string, propertyName: string): void {
    switch (propertyName) {
      case 'closeTemp':
        temperatureType === 'celcius'
          ? (this.thermostatList.closeTempF = this.masterDataService.convertTemperature(this.thermostatList.closeTemp, temperatureType))
          : (this.thermostatList.closeTemp = this.masterDataService.convertTemperature(this.thermostatList.closeTempF, temperatureType));
        break;
      case 'closeTolC':
        // takes tolerance temperature type and sends to master data service method for conversion based on the type [celcius <-> fahrenheit]
        temperatureType === 'celcius'
          ? (this.thermostatList.closeTolF = this.masterDataService.convertTemperatureTolerance(this.thermostatList.closeTolC, temperatureType))
          : (this.thermostatList.closeTolC = this.masterDataService.convertTemperatureTolerance(this.thermostatList.closeTolF, temperatureType));
        break;
      case 'tolerance':
        // takes tolerance type and sends to master data service method for conversion based on the type [celcius <-> fahrenheit]
        temperatureType === 'celcius'
          ? (this.thermostatList.toleranceF = this.masterDataService.convertTemperatureTolerance(this.thermostatList.tolerance, temperatureType))
          : (this.thermostatList.tolerance = this.masterDataService.convertTemperatureTolerance(this.thermostatList.toleranceF, temperatureType));
        break;
      case 'openTemp':
        // takes tolerance type and sends to master data service method for conversion based on the type [celcius <-> fahrenheit]
        temperatureType === 'celcius'
          ? (this.thermostatList.openTempF = this.masterDataService.convertTemperature(this.thermostatList.openTemp, temperatureType))
          : (this.thermostatList.openTemp = this.masterDataService.convertTemperature(this.thermostatList.openTempF, temperatureType));
        break;
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
