export class Simulation {
  id: number | null;
  name: string | null;
  initialName: string | null;
  checked: boolean | null;
  simulationDate: any | null;
  slitElementReview: boolean | null;
  buttedRequirement: boolean | null;
  criticalRange: boolean | null;
  sensorSpecified: boolean | null;
  dutyCycleCal: boolean | null;
  wattageDensity: boolean | null;
  comments: string | null;
  jacketId: number | null;

  constructor(
    id: number | null = null,
    name: string | null = null,
    initialName: string | null = null,
    checked: boolean | null = null,
    simulationDate: any | null = null,
    slitElementReview: boolean | null = null,
    buttedRequirement: boolean | null = null,
    criticalRange: boolean | null = null,
    sensorSpecified: boolean | null = null,
    dutyCycleCal: boolean | null = null,
    wattageDensity: boolean | null = null,
    comments: string | null = null,
    jacketId: number | null = null
  ) {
  }
}

export class ChecklistSimulationDTO {
  public simulationDTOs: Simulation[] = [];
  public quotationID: number;
}
