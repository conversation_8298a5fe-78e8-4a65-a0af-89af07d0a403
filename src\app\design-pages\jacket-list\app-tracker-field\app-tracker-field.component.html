<h2 mat-dialog-title>App Tracker Field
  <hr>
</h2>

<form [formGroup]="trackerFieldsForm" role="form" (ngSubmit)="saveTrackerFields()">
  <div class="container-fluid">
  <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
    <mat-form-field>
      <mat-select placeholder="Quotation Status" name="quoteStatus" formControlName="quotationStatusId">
        <mat-option *ngFor="let status of statuses | orderBy:'orderNumber'" [value]="status.id">
          {{ status?.status }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
</div>
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="submit" color="warn">Save</button>
  </div>
</mat-dialog-actions> <br><br><br><br>
</form>
<div class="container-fluid">
  <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
    <mat-select placeholder="Select Colour" name ="colorSelect" [(ngModel)]="trackerFields.primaryColorValue" #selectedColour>
      <mat-option *ngFor="let color of highlightedColors" [value]="color?.value" [ngStyle]="{'background': color?.value}">
        {{ color?.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>
</div>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" [disabled]="!selectedColour?.value" (click)="selectRow(selectedColour?.value)">Select Colour</button>
    <button mat-raised-button type="submit" (click)="closeDialog(false)">Cancel</button>
  </mat-dialog-actions>



