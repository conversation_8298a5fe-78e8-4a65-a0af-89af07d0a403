<div fxLayoutAlign="end">
  <div class="geo-header cust_fields">
    <mat-form-field appearance="outline">
      <mat-label>Jacket Discount</mat-label>
      <input matInput sflIsDecimal (focusout)="applyJacketDiscount()" [(ngModel)]="jacketDiscount" autocomplete="off">
    </mat-form-field>
  </div>
</div>
<mat-card>
  <div class="cust_table">
    <table aria-describedby="jacket_table">
      <tr>
        <th class="mat-header-cell" scope="col">
          Jacket Group
        </th>
        <th class="mat-header-cell" scope="col">
          Jacket
        </th>
        <th class="mat-header-cell" scope="col">
          System Length
        </th>
        <th class="mat-header-cell" scope="col">
          Control Type
        </th>
        <th class="mat-header-cell" scope="col">
          Watts
        </th>
        <th class="mat-header-cell" scope="col">
          Amps
        </th>
        <th class="mat-header-cell" scope="col">
          Labor Hours
        </th>
        <th class="mat-header-cell" scope="col">
          Material Cost
        </th>
        <th class="mat-header-cell" scope="col">
          Factory Cost
        </th>
        <th class="mat-header-cell" scope="col">
          List Price
        </th>
        <th class="mat-header-cell" scope="col">
          Net Price
        </th>
        <th class="mat-header-cell" scope="col">
          Ext. Net Price
        </th>
        <th class="mat-header-cell" scope="col">
          Discount
        </th>
        <th class="mat-header-cell" scope="col">
          Margin
        </th>
      </tr>
      <tbody *ngFor="let data of jacketList; let i = index" class="text-center">
        <tr (click)="hideme[i] = !hideme[i]" class="open-doc">
          <td class="mat-cell">
            {{data?.jacketGroupName}}
          </td>
          <td class="mat-cell">
            {{data?.totalJacketCount}}
          </td>
          <td class="mat-cell">
            {{data?.totalSystemLength | number: "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{(data?.jacketControlType != 'Other') ? data?.jacketControlType : data?.otherJacketControlType}}
          </td>
          <td class="mat-cell">
            {{data?.totalWatts | number: "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{data?.totalAmps}}
          </td>
          <td class="mat-cell">
            {{data?.totalLaborHours | number: "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{data?.totalMaterialCost | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{data?.totalFactoryCost | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{data?.totalListPrice | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{data?.totalNetPrice | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{data?.totalExtendedNetPrice | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{data?.totalDiscount}}%
          </td>
          <td class="mat-cell">
            {{data?.totalMargin | number: "1.1-2"}}%
          </td>
        </tr>
        <tr *ngFor="let value of data.jacketAccessoryList" [hidden]="hideme[i]">
          <td class="mat-cell">

          </td>
          <td class="mat-cell">
            {{value?.name}}
          </td>
          <td class="mat-cell">
            {{value?.systemLength | number: "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.controlType != 'Other' ? value?.controlType : value?.otherControlType}}
          </td>
          <td class="mat-cell">
            {{value?.watts | number: "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.amps}}
          </td>
          <td class="mat-cell">
            {{value?.laborHours | number: "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.materialCost | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.usCost | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.listPrice | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.netPrice | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.extendedNetPrice | currency: 'USD': 'symbol': "1.1-2"}}
          </td>
          <td class="mat-cell">
            {{value?.discount}}%
          </td>
          <td class="mat-cell">
            {{value?.margin | number: "1.1-2"}}%
          </td>
        </tr>
      </tbody>
      <tfoot class="text-center font-sm">
        <tr>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td><strong>Total</strong></td>
          <td><strong>{{materialCostTotal | currency: 'USD': 'symbol': "1.1-2"}}</strong></td>
          <td><strong>{{factoryCostTotal | currency: 'USD': 'symbol': "1.1-2"}}</strong></td>
          <td><strong>{{listPriceTotal | currency: 'USD': 'symbol': "1.1-2"}}</strong></td>
          <td><strong>{{netPriceTotal | currency: 'USD': 'symbol': "1.1-2"}}</strong></td>
          <td><strong>{{extNetPriceTotal | currency: 'USD': 'symbol': "1.1-2"}}</strong></td>
        </tr>
      </tfoot>
    </table>
  </div>
</mat-card>
<div fxLayoutAlign="end center">
  <div fxLayout="row wrap" class="geo-header cust_fields">
    <mat-form-field appearance="outline" fxFlex.gt-lg="60" fxFlex.gt-md="60">
      <mat-label>Control Discount</mat-label>
      <input matInput sflIsDecimal (focusout)="applyDiscount()" [(ngModel)]="discount" autocomplete="off">
    </mat-form-field>&nbsp;&nbsp;
    <div fxFlex.gt-lg="33" fxFlex.gt-md="33">
      <button *ngIf="isRevisionActive" mat-raised-button color="warn" type="submit" (click)="addAccessories()">Add
        Accessories</button><br />
    </div>
  </div>
</div>
<mat-card>
  <mat-table [dataSource]="accdataSource">
    <ng-container matColumnDef="qty">
      <mat-header-cell *matHeaderCellDef fxFlex="5"> Qty </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="5">
        <input class="effect quantity" matInput sflIsNumber [(ngModel)]="element.qty" (change)="saveElement(element)"
          type="number">
      </mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="5"></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="pn">
      <mat-header-cell *matHeaderCellDef fxFlex="10"> Part Number </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">
        <input class="effect" matInput [(ngModel)]="element.partNumber" (change)="saveElement(element)"
          autocomplete="off">
      </mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="10"></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="descripation">
      <mat-header-cell *matHeaderCellDef fxFlex="50"> Description </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="50">
        <input class="effect" matInput [(ngModel)]="element.description" (change)="saveElement(element)">
      </mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="50"></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="listprice">
      <mat-header-cell *matHeaderCellDef fxFlex="7"> List Price </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="7">
        <input class="effect quantity" matInput [(ngModel)]="element.listPrice" sflIsDecimal
          (change)="saveElement(element)">
      </mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="7"></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="discount">
      <mat-header-cell *matHeaderCellDef fxFlex="7"> Discount </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="7">{{element?.discount}}%</mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="7"></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="netprice">
      <mat-header-cell *matHeaderCellDef fxFlex="7"> Net Price </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="7">{{element?.netPrice | number: '1.1-2'}}</mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="7"><strong>Total</strong></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="extnetprice">
      <mat-header-cell *matHeaderCellDef fxFlex="7"> Ext. Net Price </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="7">{{element?.extendedNetPrice | number: '1.1-2'}}</mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="7"><strong>${{totalNetPrice | number: '1.1-2'}}</strong>
      </mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="margin">
      <mat-header-cell *matHeaderCellDef fxFlex="7"> Margin </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="7">{{element?.margin ? element?.margin + '%' : ''}} </mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="7"></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="action">
      <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
      <mat-cell *matCellDef="let element" (click)="confirmDelete(element.id)" fxFlex="5">
        <mat-icon class="open-doc">delete</mat-icon>
      </mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="5"></mat-footer-cell>
    </ng-container>
    <ng-container matColumnDef="order">
      <mat-header-cell *matHeaderCellDef fxFlex="10" fxLayoutAlign="center center"> Order </mat-header-cell>
      <mat-cell *matCellDef="let element; let i = index" fxFlex="10" fxLayoutAlign="center center">
        <mat-icon class="open-doc" (click)="up(i)" matTooltip="Move Up">keyboard_arrow_up</mat-icon>
        <mat-icon class="open-doc" (click)="down(i)" matTooltip="Move Down">keyboard_arrow_down</mat-icon>
      </mat-cell>
      <mat-footer-cell *matFooterCellDef fxFlex="10"></mat-footer-cell>
    </ng-container>
    <mat-header-row *matHeaderRowDef="accdisplayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: accdisplayedColumns;"></mat-row>
    <mat-footer-row *matFooterRowDef="accdisplayedColumns; sticky: true"
      [ngClass]="{'hide':accdataSource.data.length === 0}"></mat-footer-row>
  </mat-table>
  <div class="no-records" *ngIf="isNoDataFound | async">
    No data found
  </div>
  <mat-action-row fxLayoutAlign="end" class='mt-10'>
    <button mat-raised-button color="warn" type="submit" (click)="submitChange()"
      [disabled]="accdataSource.data.length <= 0">Save Changes</button>
  </mat-action-row>
</mat-card>
