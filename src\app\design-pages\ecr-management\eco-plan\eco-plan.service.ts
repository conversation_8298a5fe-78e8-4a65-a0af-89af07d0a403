import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { AppConfig } from 'src/app/app.config';
import { utils } from 'src/app/shared/helpers';
import 'rxjs/add/operator/map'
import 'rxjs/add/operator/catch';
import { PqpDTOS, EcoDto, FinalReviewDto, EcoWorkFlowDto, WorkFlowMasterDataDto, DesignActivityMasterDataDto } from './eco-plan.model';

@Injectable({
  providedIn: 'root'
})
export class EcoPlanService {

  constructor(private http: HttpClient) { }

  createEco(ecoDto: EcoDto) {
    return this.http.post(AppConfig.ECO_OPERATIONS, ecoDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  updateEco(ecoDto: EcoDto) {
    return this.http.put(AppConfig.ECO_OPERATIONS, ecoDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  updatePqp(pqpDto: PqpDTOS) {
    return this.http.put(AppConfig.PQP_OPERATIONS, pqpDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getEcos(ecrId) {
    return this.http.get(AppConfig.GET_ECO + ecrId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getPqpDetails(ecoId) {
    return this.http.get(AppConfig.GET_PQP_DETAILS + ecoId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getPqpByEcoId(ecoId: number) {
    return this.http.get(AppConfig.GET_PQP_BY_ECO_ID + ecoId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  createEcoPqp(ecoPqpDTO: PqpDTOS) {
    return this.http.post(AppConfig.PQP_OPERATIONS, ecoPqpDTO )
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getPqpList() {
    return this.http.get(AppConfig.GET_PQP_LIST)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  addPqpItem(ecoDto: PqpDTOS) {
    return this.http.put(AppConfig.PQP_OPERATIONS, ecoDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  deletePqpItem(pqpObject) {
    const options = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      }),
      body: pqpObject
    };
    return this.http.delete(AppConfig.PQP_OPERATIONS, options)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getFinalReviewByEcoId(ecoId: number) {
    return this.http.get(AppConfig.GET_FINAL_REVIEW_BY_ECO_ID + ecoId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  createFinalReviewForEco(finalReviewDto: FinalReviewDto) {
    return this.http.post(AppConfig.ECO_FINAL_REVIEW, finalReviewDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  updateFinalReviewForEco(finalReviewDto: FinalReviewDto) {
    return this.http.put(AppConfig.ECO_FINAL_REVIEW, finalReviewDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getWorkFlowMasterData() {
    return this.http.get(AppConfig.GET_WORK_FLOW_MASTER_DATA)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getWorkFlowByEcoId(ecoId: number) {
    return this.http.get(AppConfig.GET_WORK_FLOW_BY_ECO_ID + ecoId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  createWorkFlowForEco(workFlowDto: WorkFlowMasterDataDto) {
    return this.http.post(AppConfig.ECO_WORKFLOW, workFlowDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  updateWorkFlowForEco(workFlowDto: WorkFlowMasterDataDto) {
    return this.http.put(AppConfig.ECO_WORKFLOW, workFlowDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  deleteWorkFlowEco(ecoWorkFlowId: number) {
    return this.http.delete(AppConfig.ECO_WORKFLOW + '/' + ecoWorkFlowId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  addStakeHolder(stakeHolder) {
    return this.http.post(AppConfig.ADD_STAKE_HOLDER, stakeHolder)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  removeStakeHolder(stakeHolderId) {
    return this.http.delete(AppConfig.REMOVE_STAKE_HOLDER + stakeHolderId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  // design activity
  getDesignActivityMasterData() {
    return this.http.get(AppConfig.GET_DESIGN_ACTIVITY_MASTER_DATA)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  getDesignActivityByEcoId(ecoId: number) {
    return this.http.get(AppConfig.GET_DESIGN_ACTIVITY_BY_ECO_ID + ecoId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  createDesignActivityForEco(designActivityDto: DesignActivityMasterDataDto) {
    return this.http.post(AppConfig.ECO_DESIGN_ACTIVITY, designActivityDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  updateDesignActivityEco(designActivityDto: DesignActivityMasterDataDto) {
    return this.http.put(AppConfig.ECO_DESIGN_ACTIVITY, designActivityDto)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  deleteDesignActivityEco(ecoDesignActivityId: number) {
    return this.http.delete(AppConfig.ECO_DESIGN_ACTIVITY + '/' + ecoDesignActivityId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  // ECO Costing
  getEcoCostingByEcrId(ecrId: number) {
    return this.http.get(AppConfig.GET_ECO_COSTING + ecrId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }

  regenerateEcoCostingByEcrId(ecrId: number) {
    return this.http.get(AppConfig.REGENERATE_ECO_COSTING + ecrId)
    .map(utils.extractData)
    .catch(utils.handleError);
  }
}
