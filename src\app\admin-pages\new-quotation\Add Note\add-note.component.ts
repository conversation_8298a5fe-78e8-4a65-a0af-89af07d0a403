import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { SummarySalesOrderComponent } from '../summary-sales-order.component';
import { Notes } from '../ccdc-model/ccdc.model';
import { SalesOrderSummaryService } from '../summary-sales-order.service';
import { NgForm } from '@angular/forms';
import { Subscription } from 'rxjs';
import { Variable } from 'src/app/shared/constants/Variable.constants';

@Component({
  selector: 'sfl-add-note',
  templateUrl: './add-note.component.html'
})
export class AddNoteComponent implements OnInit, OnDestroy {
  note: Notes;
  jacketGroupId: number;
  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public dialogRef: MatDialogRef<SummarySalesOrderComponent>,
    private salesOrderSummaryService: SalesOrderSummaryService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketGroupId = data.jacketGroupId;
  }

  ngOnInit() {
    this.note = new Notes();
    this.getNotesByJacketGroupId();
  }

  // used to save the note info
  saveNote(notesForm: NgForm) {
    this.showLoader = true;
    this.note.jacketGroupId = this.jacketGroupId;
    this.subscription.add(
      this.salesOrderSummaryService.saveNote(this.note, this.note.id).subscribe(
        success => {
          this.showLoader = false;
          this.dialogRef.close(success);
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to get the notes
  getNotesByJacketGroupId() {
    this.showLoader = true;
    this.subscription.add(
      this.salesOrderSummaryService.getNotesByJacketGroupId(this.jacketGroupId).subscribe(
        (res: Notes) => {
          if (res !== null) {
            this.note = res;
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog() {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
