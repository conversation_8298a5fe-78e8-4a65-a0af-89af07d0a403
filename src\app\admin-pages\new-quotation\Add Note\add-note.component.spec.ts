import { SalesOrderSummaryService } from '../summary-sales-order.service';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { Notes } from '../ccdc-model/ccdc.model';
import { inject, TestBed, async } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/catch';


describe('ADD NOTES TEST CASE', () => {

    const noteId = null;
    const jgId = 3008;

    const dummyNote = {
        'id': null,
        'jacketGroupId': 3008,
        'notes': 'Demo notes'
    };

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                HttpClientModule,
                HttpClientTestingModule
            ]
        });
    });

    it('Should add a note when saveNotes() call', async(inject([HttpClient], (http: HttpClient) => {
        const service = new SalesOrderSummaryService(http);
        service.saveNote(dummyNote, noteId);

        expect(service.saveNote(dummyNote, noteId).subscribe((res: Notes) => {
            expect(res).toEqual(dummyNote);
        }));
    })));

    it('Should get notes when getNotesByJacketId() call', async(inject([HttpClient], (http: HttpClient) => {
        const service = new SalesOrderSummaryService(http);

        expect(service.getNotesByJacketGroupId(jgId).subscribe((res: Notes) => {
            expect(res).toBe(dummyNote);
        }));
    })));
});
