import { Component, EventEmitter, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import * as screenfull from 'screenfull';
import { Role, SharedService } from 'src/app/shared';
import { Route } from 'src/app/shared/constants/router.constants';
import { RoleAuthorisationServiceService } from 'src/app/shared/service/role-authorisation-service.service';

@Component({
  selector: 'sfl-quote-tracker-topbar',
  templateUrl: './quote-tracker-topbar.component.html'
})
export class QuoteTrackerTopbarComponent implements OnInit, OnDestroy {
  @Output() toggleSidenav = new EventEmitter<void>();

  subscription = new Subscription();

  filter: boolean;
  isAuthorized = false;
  isDesignAdmin: boolean;
  isAppAdmin: boolean;
  isFullScreen: boolean;
  authorizedRole = [Role.ADMIN_ROLE, Role.DESIGN_ROLE, Role.ENG_ROLE];


  constructor(private readonly router: Router, private readonly sharedService: SharedService, private readonly roleAuthorisationServiceService: RoleAuthorisationServiceService
  ) { }

  async ngOnInit() {
    this.checkIsAuthorized();
  }

  checkIsAuthorized() {
    this.isAuthorized = this.roleAuthorisationServiceService.isAuthorised(this.authorizedRole);
  }

  fullScreenToggle(): void {
    if (screenfull.enabled) {
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    }
  }

  // used to switch to the Quote tracker dashboard
  goToDashboard() {
    this.router.navigate([Route.QUOTE_TRACKER.dashboard]);
  }

  goToSalesDashboard() {
    this.router.navigate([Route.SALES.dashboard]);
  }

  // used to switch to the design engg. screen
  switchToDesign() {
    this.router.navigate([Route.DESIGN_ENGG.dashboard]);
  }

  // used to switch to the super admin screen
  switchToSuperAdmin() {
    this.router.navigate([Route.SUPER_ADMIN.user_list]);
  }

  // used to switch to the app. engg. screen
  switchToAppAdmin() {
    this.router.navigate([Route.APP_ENGG.dashboard]);
  }

  // used to switch to the master data management screen
  switchToMasterdataManagement() {
    this.router.navigate([Route.MASTERDATA.management]);
  }

  // used to logged out the user
  logout() {
    this.sharedService.logout(null);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
