import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { AccessoryMaster, AccessoryControllerMaster } from '../../masterdata-management.model';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Router } from '@angular/router';
import { SnakbarService } from 'src/app/shared';

@Component({
  selector: 'sfl-manage-accessory',
  templateUrl: './manage-accessory.component.html'
})
export class ManageAccessoryComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  accessory: AccessoryMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  accessoryControllers: AccessoryControllerMaster[];
  constructor(
    public readonly dialogRef: MatDialogRef<ManageAccessoryComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private snakbarService: SnakbarService,
    private readonly router: Router
  ) {
    this.accessory = data;
  }

  ngOnInit() {
    this.accessory = this.accessory.id ? Object.assign({}, this.accessory) : new AccessoryMaster();
    this.accessory.id ? (this.title = 'Update Accessory') : (this.title = 'Add Accessory');
    this.getAccessoryController();
  }
  getAccessoryController() {
    this.subscription.add(
      this.masterDataService.getAccessoryControllersList().subscribe(
        (accessoryController: AccessoryControllerMaster[]) => {
          this.accessoryControllers = accessoryController;
          this.showLoader = false;
        },
        error => {
          this.accessoryControllers = [];
          this.showLoader = false;
        }
      )
    );
  }
  manageAccessoryControllers() {
    this.dialogRef.close(false);
    this.router.navigate(['master-data/management/accessory-controller']);
  }
  updateAccessory() {
    this.showLoader = true;
    if (this.accessory.id) {
      this.subscription.add(
        this.masterDataService.updateAccessory(this.accessory).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
              if (error.applicationStatusCode === 1232) {
                this.snakbarService.error(error.message);
                }
              
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addAccessory(this.accessory).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
