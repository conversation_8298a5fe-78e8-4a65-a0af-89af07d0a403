<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="filter less-peding">
  <mat-card class="matcard-no-padding">
    <div fxLayout="column">
      <div fxLayout="row wrap">
        <div class="sub-heder" fxLayoutAlign="start center" fxLayoutGap="10px" fxFlex>
          <span><strong>Customer:</strong> {{ quotation?.customerDTO?.name }}</span>
          <span> <strong>Quotation # :</strong> {{ quotation?.quotationNumber }}</span>
          <span> <strong>SO # :</strong>{{ quotation?.salesOrderNumber }} </span>
        </div>
        <div fxFlex.gt-lg="10" fxFlex.gt-md="15" fxFlex.gt-sm="15" fxFlex.gt-xs="100" *ngIf="onlyComparison"
             fxLayoutAlign="end center">
          <button
            mat-raised-button
            color="warn"
            type="submit"
            [routerLink]="['/app-eng/quotation-comparison']"
            [queryParams]="{ quotId: quotID }"
          >
            Comparison Tool
          </button>
        </div>
        &nbsp;&nbsp;&nbsp;
        <div class="cust_fields" fxLayoutAlign="end center" fxLayoutGap="10px">
          <mat-form-field appearance="outline" *ngIf="menuChange">
            <mat-label>Jacket Group</mat-label>
            <mat-select placeholder="Jacket Group" [(ngModel)]="jacketGroupId"
                        (selectionChange)="onJacketGroupChanged($event.value)">
              <mat-option *ngFor="let jacket of jacketGroup" [value]="jacket.id">
                {{ jacket.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" *ngIf="onlyrevision">
            <mat-label>Revision</mat-label>
            <mat-select placeholder="Revision" [(ngModel)]="revisionId"
                        (selectionChange)="onRevisionChanged($event.value)">
              <mat-option *ngFor="let rev of revisionList" [value]="rev.id">
                {{ rev.revisionName }}
                <span
                  [ngClass]="{ 'lbl-red': rev.activeRevision === true }">{{ rev.activeRevision === true ? 'Active' : '' }}</span>
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div fxLayoutAlign="end center">
          <button *ngIf="onlyrevision" [matMenuTriggerFor]="menu" mat-icon-button class="ml-xs" x-position="before">
            <mat-icon>settings</mat-icon>
          </button>
          <button *ngIf="listType === 'ccdc'" mat-icon-button class="ml-xs" x-position="before"
                  (click)="manageImportCcdc()">
            <mat-icon matTooltip="Import CCDC">arrow_downward</mat-icon>
          </button>
          <button *ngIf="listType === 'ccdc'" mat-icon-button class="ml-xs" x-position="before"
                  (click)="manageExportCcdc()">
            <mat-icon matTooltip="Export CCDC">arrow_upward</mat-icon>
          </button>
          <a
            mat-icon-button
            target="_blank"
            [routerLink]="['/app-eng/ccdc/print']"
            [queryParams]="{ revisionId: revisionId, quotId: quotID }"
            [disabled]="!revisionId || !quotID || !jacketGroupId"
          >
            <mat-icon>print</mat-icon>
          </a
          >&nbsp;
          <button mat-icon-button class="ml-xs" (click)="openDoclist()" class="ml-xs overflow-visible">
            <mat-icon>folder</mat-icon>
          </button>
        </div>
        <mat-menu #menu="matMenu" x-position="before">
          <button [ngClass]="{ revDisable: !isRevisionActive }" mat-menu-item (click)="manageJacketGroup()">Manage
            Jacket Groups
          </button>
          <button mat-menu-item (click)="manageRevision()">Manage Revision</button>
        </mat-menu>
      </div>
    </div>
  </mat-card>
</div>
<br/>
<div class="demo" fxLayout="row wrap" style="margin-top: -20px">
  <div class="lft-float-menu" fxFlex.gt-lg="10" fxFlex.gt-md="15" fxFlex.gt-sm="19" fxFlex.gt-xs="100" fxFlex>
    <div class="list">
      <ol class="steps">
        <li class="pending">
          <a class="{{ listType === 'customer' ? 'active' : '' }}" (click)="changeList('customer')" id="customer info">Customer
            Info</a>
        </li>
        <li class="pending">
          <a class="{{ listType === 'ccdc' ? 'active' : 'no-class' }}" (click)="changeList('ccdc'); expandSubmenu()"
             id="ccdc">CCDC</a>
          <ul [ngClass]="{ revDisable: !isRevisionActive }" *ngIf="submenu">
            <li class="section">
              <strong>2.1</strong>
              <a (click)="manageUnits()" id=" manageunits"> Manage Units</a>
            </li>
            <li class="section">
              <strong>2.2</strong>
              <a (click)="addWorkflow()" id="workflow">Workflow</a>
            </li>
            <li class="section">
              <strong>2.3</strong>
              <a (click)="addApplicationInfo()" id="application">Application</a>
            </li>
            <li class="section">
              <strong>2.4</strong>
              <a (click)="addPlugin()" id="material"> Plugging</a>
            </li>
            <li class="section">
              <strong>2.5</strong>
              <a (click)="addMaterial()" id="closure"> Material</a>
            </li>
            <li class="section">
              <strong>2.6</strong>
              <a (click)="addClosure()" id="plugging">Closure</a>
            </li>
            <li class="section">
              <strong>2.7</strong>
              <a (click)="addSensors()" id="sensors">Sensors</a>
            </li>
            <li class="section">
              <strong>2.8</strong>
              <a (click)="addThermostat()" id="thermostat">Thermostat</a>
            </li>
            <li class="section">
              <strong>2.9</strong>
              <a (click)="addNote()" id="notes">Notes</a>
            </li>
            <li class="section">
              <strong>2.10</strong>
              <a (click)="openDoclist()" id="documents">Documents</a>
            </li>
          </ul>
        </li>
        <li class="pending">
          <a class="{{ listType === 'geometry' ? 'active' : '' }}" (click)="changeList('geometry')" id="geometry">Geometry</a>
        </li>
        <li class="pending">
          <a class="{{ listType === 'accessories' ? 'active' : '' }}" (click)="changeList('accessories')"
             id="accessories">Accessories</a>
        </li>
        <li class="pending no-border">
          <a class="{{ listType === 'finalize' ? 'active' : '' }}" (click)="changeList('finalize')" id="finalize">Finalize</a>
        </li>
      </ol>
    </div>
  </div>
  <div
    class="right-jacket-list"
    fxFlex.gt-lg="{{ sizeLG }}"
    fxFlex.gt-md="{{ sizeMD }}"
    fxFlex.gt-sm="{{ sizeSM }}"
    fxFlex.gt-xs="{{ sizeXS }}"
    fxFlex
  >
    <form #orderInfoForm="ngForm" role="form">
      <div *ngIf="listType === 'customer'" class="right-customer-panel">
        <div fxLayout="row wrap" class="less-peding mt-10">
          <div fxFlex.gt-sm="24" fxFlex.gt-xs="50" fxFlex="100">
            <mat-card class="sfl-cust-info-cust-height">
              <div fxLayout="row">
                <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Quote Number"
                    name="quotationNumber"
                    [(ngModel)]="quotation.quotationNumber"
                    #quotationNumberInput="ngModel"
                    autocomplete="off"
                    required
                  />
                  <mat-error>This field is required</mat-error>
                </mat-form-field>
              </div>
              <div fxLayout="row">
                <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Sales Order Number"
                    name="salesOrderNumber"
                    [(ngModel)]="quotation.salesOrderNumber"
                    #salesOrderNumberInput="ngModel"
                    autocomplete="off"
                  />
                </mat-form-field>
              </div>
              <div fxLayout="row">
                <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <mat-select
                    placeholder="Quotation Status"
                    [(ngModel)]="quotation.quotationStatusId"
                    name="status"
                    required
                    (selectionChange)="updateQuoteStatus(quotation.quotationStatusId)"
                  >
                    <mat-option *ngFor="let status of quotationStatuses" [value]="status.id">
                      {{ status?.status }}
                    </mat-option>
                  </mat-select>
                  <mat-error>This field is required</mat-error>
                </mat-form-field>
              </div>
            </mat-card>
          </div>
          <div fxFlex.gt-sm="76" fxFlex.gt-xs="50" fxFlex="100">
            <mat-card>
              <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Customer Name"
                    name="name"
                    [(ngModel)]="quotation.customerDTO.name"
                    #nameInput="ngModel"
                    required
                  />
                  <mat-error>This field is required</mat-error>
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
                  <input matInput placeholder="Customer Code" name="code" [(ngModel)]="quotation.customerDTO.code"
                         #codeInput="ngModel"/>
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Eng. Cust Abrev"
                    name="engCustAbrev"
                    [(ngModel)]="quotation.customerDTO.engCustAbrev"
                    #engCustAbrevInput="ngModel"
                    required
                  />
                  <mat-error>This field is required</mat-error>
                </mat-form-field>
              </div>
              <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Contact"
                    name="contact"
                    [(ngModel)]="quotation.customerDTO.contact"
                    #contactInput="ngModel"
                  />
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Phone Number"
                    name="phoneNumber"
                    (keypress)="isNumberKey($event)"
                    class="quantity"
                    [(ngModel)]="quotation.customerDTO.phoneNumber"
                    #phoneNumberInput="ngModel"
                  />
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="30" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Fax Number"
                    name="faxNumber"
                    (keypress)="isNumberKey($event)"
                    class="quantity"
                    [(ngModel)]="quotation.customerDTO.faxNumber"
                    #faxNumberInput="ngModel"
                  />
                </mat-form-field>
              </div>
              <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <mat-form-field fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="48" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Address 1"
                    name="address1"
                    [(ngModel)]="quotation.customerDTO.addressLine1"
                    #address1Input="ngModel"
                  />
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="50" fxFlex.gt-md="48" fxFlex.gt-sm="48" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Address 2"
                    name="address2"
                    [(ngModel)]="quotation.customerDTO.addressLine2"
                    #address2Input="ngModel"
                  />
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <mat-chip-list #chipList aria-label="Email Address">
                    <mat-chip *ngFor="let email of emailChips" [selectable]="selectable" [removable]="removable"
                              (removed)="remove(email)">
                      {{ email }}
                      <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
                    </mat-chip>
                    <input
                      placeholder="Email Address"
                      [matChipInputFor]="chipList"
                      [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                      [matChipInputAddOnBlur]="addOnBlur"
                      (matChipInputTokenEnd)="add($event)"
                    />
                  </mat-chip-list>
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="95" fxFlex.gt-md="95" fxFlex.gt-sm="95" fxFlex.gt-xs="95">
                  <input
                    matInput
                    placeholder="SQT Link"
                    name="sqtLink"
                    [(ngModel)]="quotation.customerDTO.sqtLink"
                    #address2Input="ngModel"
                  />
                </mat-form-field>
                <div fxFlex.gt-lg="5" fxFlex.gt-md="5" fxFlex.gt-sm="5" fxFlex.gt-xs="5">
                  <mat-icon role="img" class="mat-icon notranslate material-icons mat-ligature-font mat-icon-no-color"
                            (click)="openFileInExplorer(quotation.customerDTO.sqtLink)" style="cursor: pointer"
                            aria-hidden="true" data-mat-icon-type="font">open_in_new
                  </mat-icon>
                </div>
                <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <input
                    matInput
                    placeholder="Notes"
                    name="notes"
                    [(ngModel)]="quotation.customerDTO.notes"
                    #address2Input="ngModel"
                  />
                </mat-form-field>
              </div>
            </mat-card>
          </div>
        </div>
        <div fxLayoutAlign="end" class="bottom-btn-left">
          <button mat-raised-button color="warn" type="submit" (click)="saveOrderInfo()"
                  [disabled]="orderInfoForm.invalid">Update
          </button>
        </div>
      </div>
    </form>
    <div [ngClass]="{ revDisable: !isRevisionActive }" class="pointer less-peding cust_table right-ccdc-panel"
         *ngIf="listType === 'ccdc'">
      <div fxLayout="row wrap">
        <mat-card fxFlex.gt-lg="29" fxFlex.gt-md="28" fxFlex.gt-xs="100">
          <div fxLayout="row wrap">
            <mat-card-title fxFlex>Manage Measurement Units</mat-card-title>
            <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="manageUnits()">edit</mat-icon>
          </div>
          <hr/>
          <div class="mb-10"></div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Measurement Unit</label>
              <label>{{ measureUnit }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Temperature Unit</label>
              <label>{{ tempUnit }}</label>
            </div>
          </div>
          <div *ngIf="quotation?.entryMethod">
            <div fxLayout="row wrap">
              <mat-card-title fxFlex>Entry Method</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addEntryMethod()">edit</mat-icon>
            </div>
            <hr/>
            <div class="mb-10"></div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <label
                  class="lbl">{{ quotation?.entryMethod === 'true' ? 'True Dimensions' : 'Centerline Dimensions' }}</label>
              </div>
            </div>
          </div>
          <div *ngIf="!quotation?.entryMethod">
            <div
              class="details-box text-center open-doc"
              fxFlex.gt-lg="90"
              fxFlex.gt-md="90"
              fxFlex.gt-sm="90"
              fxFlex.gt-xs="100"
              (click)="addEntryMethod()"
            >
              <h4>Add Entry Method</h4>
            </div>
          </div>
        </mat-card>
        <mat-card fxFlex.gt-lg="{{ documentslist ? 68 : 69 }}" fxFlex.gt-md="{{ documentslist ? 68 : 69 }}"
                  fxFlex.gt-xs="100">
          <div *ngIf="workflow">
            <div fxLayout="row">
              <mat-card-title fxFlex>Workflow Information</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addWorkflow()">edit</mat-icon>
            </div>
            <hr/>
            <div class="mb-10"></div>
            <div class="mb-10" fxLayout="column">
              <div fxLayout="row" class="mb-10">
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">Approval Level</label>
                  <p *ngIf="workflow?.approvalLevel">{{ workflow?.approvalLevel | approvalLevel }}</p>
                </div>
                <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="48" fxFlex.gt-xs="100">
                  <label class="lbl">Entry Date</label>
                  <p>{{ workflow?.entryDate }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">App Engineer Started</label>
                  <p>{{ workflow?.dateAppStarted }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">App Engineer Completed</label>
                  <p>{{ workflow?.dateAppCompleted }}</p>
                </div>
              </div>
              <div fxLayout="row" class="mb-10">
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">App Engineer Assigned</label>
                  <p>{{ workflow?.assignedAppEngineerId | getFirstNameLastName: salesassociates }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">Account Manager</label>
                  <p>{{ workflow?.accountMgrId | getFirstNameLastName: salesassociates }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">Product Type</label>
                  <p>{{ workflow?.productType | productType }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">External Quote Required</label>
                  <p>{{ workflow?.externalQuoteRequired | convertToYesNo }}</p>
                </div>
              </div>

              <div fxLayout="row" class="mb-10">
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">Project Name</label>
                  <p>{{ workflow?.projectName }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">Approval Format</label>
                  <p *ngIf="workflow?.approvalFormatList">{{ workflow?.approvalFormatList | approvalFormats }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="33" fxFlex.gt-sm="34" fxFlex.gt-xs="100">
                  <label class="lbl">Markings</label>
                  <p *ngIf="workflow?.markingList">{{ workflow?.markingList | markings }}</p>
                </div>
                <div fxFlex.gt-lg="34" fxFlex.gt-md="38" fxFlex.gt-sm="38" fxFlex.gt-xs="100">
                  <label class="lbl">Customer Clarification Required</label>
                  <p>{{ workflow?.customerClarificationRequired | convertToYesNo }}</p>
                </div>
              </div>
              <div fxLayout="row" class="mb-10">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Notes</label>
                  <p>
                    {{ workflow?.notes }}
                  </p>
                </div>
                <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Current Status</label>
                  <p>
                    {{ workflow?.currentStatusComment }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="!workflow">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addWorkflow()"
            >
              <h1>Create Workflow</h1>
            </div>
          </div>
        </mat-card>
      </div>
      <div fxLayout="row wrap">
        <mat-card class="details details-srooll" fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-xs="100">
          <div class="display-details" *ngIf="appInfo">
            <div fxLayout="row wrap">
              <mat-card-title fxFlex>Application Information</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addApplicationInfo()">edit</mat-icon>
            </div>
            <hr/>
            <div class="mb-10"></div>
            <div class="mb-10" fxLayout="column">
              <div fxLayout="row" class="mb-10" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Element Type</label>
                  <p>{{ appInfo?.jacketType }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Voltage</label>
                  <p>{{ appInfo?.voltage }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Phase</label>
                  <p>{{ appInfo?.phase }}</p>
                </div>
              </div>
              <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Operating Temp ({{ tempUnit ? tempUnit : '' }})</label>
                  <p>{{ appInfo?.operatingTemp }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Max Exposure Temp ({{ tempUnit ? tempUnit : '' }})</label>
                  <p>{{ appInfo?.maxExposureTemp }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Min Ambient Temp ({{ tempUnit ? tempUnit : '' }})</label>
                  <p>{{ appInfo?.minAmbientTemp }}</p>
                </div>
              </div>
              <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Heat up From</label>
                  <p>{{ appInfo?.heatupFrom }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Heat up To</label>
                  <p>{{ appInfo?.heatupTo }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">In (Hours)</label>
                  <p>{{ appInfo?.heatupIn }}</p>
                </div>
              </div>
              <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Pipe Thickness ({{ measureUnit ? measureUnit : '' }})</label>
                  <p>{{ appInfo?.pipeThickness }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Content Motion</label>
                  <p>{{ appInfo?.contentMotion }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Flowing Rate</label>
                  <p>{{ appInfo?.contentMotionFlowingRate }}</p>
                </div>
              </div>
              <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Pipe Material</label>
                  <p>{{ appInfo?.materialName }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})</label
                  >
                  <p>{{ appInfo?.otherMaterialHeat !== null ? appInfo?.otherMaterialHeat : appInfo?.materialSpecificHeat }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Density (lb/ft3)</label
                  >
                  <p>{{ appInfo?.otherMaterialDensity !== null ? appInfo?.otherMaterialDensity : appInfo?.materialDensity }}</p>
                </div>
              </div>
              <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Pipe Contents</label>
                  <p>{{ appInfo?.contentName }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Specific Heat (BTU/lb {{ tempUnit ? tempUnit : '' }})</label
                  >
                  <p>{{ appInfo?.otherContentHeat !== null ? appInfo?.otherContentHeat : appInfo?.contentSpecificHeat }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Density (lb/ft3)</label
                  >
                  <p>{{ appInfo?.otherContentDensity !== null ? appInfo?.otherContentDensity : appInfo?.contentDensity }}</p>
                </div>
              </div>
              <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Control Type</label
                  >
                  <p>{{ appInfo?.controlType != 'Other' ? appInfo?.controlType : appInfo?.otherControlType }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Watt Density</label>
                  <p>{{ appInfo?.wattDensity }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                </div>
              </div>
              <div class="mb-10" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Duty Cycle Min</label>
                  <p>{{ appInfo?.minDutyCycle }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Duty Cycle Max</label>
                  <p>{{ appInfo?.maxDutyCycle }}</p>
                </div>
                <div fxFlex.gt-lg="32" fxFlex.gt-md="32" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                </div>
              </div>
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <label class="lbl">Notes</label>
                  <p>{{ appInfo?.notes }}</p>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="!appInfo">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addApplicationInfo()"
            >
              <h1>Create Application Information</h1>
            </div>
          </div>
        </mat-card>
        <mat-card
          class="details details-srooll"
          fxFlex.gt-lg="{{ documentslist ? 48 : 49 }}"
          fxFlex.gt-md="{{ documentslist ? 48 : 49 }}"
          fxFlex.gt-xs="100"
        >
          <div *ngIf="pluggingInformation" class="display-details">
            <div fxLayout="row wrap">
              <mat-card-title fxFlex>Plugging Information</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addPlugin()">edit</mat-icon>
            </div>
            <hr/>
            <div class="mb-10"></div>
            <div class="mb-20" fxLayout="column">
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Sleeving Type</label
                  ><label>{{
                    pluggingInformation?.sleevingTypeName !== 'Other'
                      ? pluggingInformation?.sleevingTypeName
                      : pluggingInformation?.otherSleevingType
                  }}</label>
                </div>


                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Strain Relief</label
                  ><label>{{
                    pluggingInformation?.strainReliefName !== 'Other'
                      ? pluggingInformation?.strainReliefName
                      : pluggingInformation?.otherStrainRelief
                  }}</label>
                </div>
              </div>

              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Green Light</label
                  ><label>{{
                    pluggingInformation?.greenLightName !== 'Other'
                      ? pluggingInformation?.greenLightName
                      : pluggingInformation?.otherGreenLight
                  }}</label>
                </div>
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Red Light</label
                  ><label>{{
                    pluggingInformation?.redLightName !== 'Other' ? pluggingInformation?.redLightName : pluggingInformation?.otherRedLight
                  }}</label>
                </div>
              </div>
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Plug</label
                  ><label>{{
                    pluggingInformation?.leadPlugDTO?.plugName !== 'Other'
                      ? pluggingInformation?.leadPlugDTO?.plugName
                      : pluggingInformation?.otherPlug
                  }}</label>
                </div>
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Connector</label
                  ><label>{{
                    pluggingInformation?.jumperPlugDTO?.plugName !== 'Other'
                      ? pluggingInformation?.jumperPlugDTO?.plugName
                      : pluggingInformation?.otherConnector
                  }}</label>
                </div>
              </div>
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Lead Length ({{ measureUnit ? measureUnit : '' }})</label
                  ><label>{{ pluggingInformation?.leadPlugDTO?.leadLength }} </label>
                </div>
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Jumper Length ({{ measureUnit ? measureUnit : '' }})</label
                  ><label>{{ pluggingInformation?.jumperPlugDTO?.jumperLength }} </label>
                </div>
              </div>
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Jumper Qty</label><label>{{ pluggingInformation?.jumperPlugDTO?.quantity }}</label>
                </div>
              </div>
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Lead Type</label><label>{{ pluggingInformation?.leadTypeDTO?.leadName }}</label>
                </div>
              </div>
              <div fxLayout="row" fxLayoutAlign="space-between">
                <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Lead Part
                    Number</label><label>{{ pluggingInformation?.leadTypeDTO?.partNumber }}</label>
                </div>
                <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Max Temp (°C)</label><label>{{ pluggingInformation?.leadTypeDTO?.maxTemp }}</label>
                </div>
              </div>
              <div fxLayout="row" fxLayoutAlign="space-between">
                <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label
                    class="lbl">Cost/ft.</label><label>{{ pluggingInformation?.leadTypeDTO?.costPerFoot | currency }}</label>
                </div>
                <div class="mb-20" fxFlex.gt-lg="25" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
                  <label class="lbl">Max Volts</label><label>{{ pluggingInformation?.leadTypeDTO?.maxVolts }}</label>
                </div>
              </div>
              <div class="mb-20" fxFlex="100">
                <label class="lbl">Notes</label>
                <p>{{ pluggingInformation?.notes }}</p>
              </div>
            </div>
          </div>
          <div *ngIf="!pluggingInformation">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addPlugin()"
            >
              <h1>Create Plugging</h1>
            </div>
          </div>
        </mat-card>
      </div>
      <div fxLayout="row wrap">
        <mat-card class="details" fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-xs="100">
          <div *ngIf="materialData">
            <div fxLayout="row wrap">
              <mat-card-title fxFlex>Material Information</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addMaterial()">edit</mat-icon>
            </div>
            <hr/>
            <div class="mb-10"></div>
            <div class="mb-10" fxLayout="column">
              <div class="highlight-mat-table">
                <mat-table [dataSource]="materialDataSource" fxFlex="100">
                  <ng-container matColumnDef="layerName">
                    <mat-header-cell *matHeaderCellDef fxFlex="30"> Layer</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="30"> {{ element.layerName }}</mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="material">
                    <mat-header-cell *matHeaderCellDef fxFlex="40"> Material</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="40">{{
                        element?.material === 'Other'
                          ? element?.layerName === 'Facing'
                            ? element?.otherFacing
                            : element?.layerName === 'Insulation'
                              ? element?.otherInsulation
                              : element?.otherLiner
                          : element?.material
                      }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="partNumber">
                    <mat-header-cell *matHeaderCellDef fxFlex="20"> Part Number</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="20">
                      {{
                        element?.otherFacingPartNumber
                          ? element?.otherFacingPartNumber
                          : element?.otherInsulationPartNumber
                            ? element?.otherInsulationPartNumber
                            : element?.otherLinerPartNumber
                              ? element?.otherLinerPartNumber
                              : element?.partNumber
                      }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="maxTemp">
                    <mat-header-cell *matHeaderCellDef fxFlex="17"> Max Temp ({{ tempUnit ? tempUnit : '' }})
                    </mat-header-cell>
                    <mat-cell *matCellDef="let element"
                              fxFlex="17"> {{ tempUnit === '°F' ? element?.maxTempF : element?.maxTemp }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="costPerSq">
                    <mat-header-cell *matHeaderCellDef fxFlex="17"> Cost / Sq. Ft.</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="17"
                    >${{
                        element?.otherFacingCost
                          ? element?.otherFacingCost
                          : element?.otherInsulationCost
                            ? element?.otherInsulationCost
                            : element?.otherLinerCost
                              ? element?.otherLinerCost
                              : element?.costPerSq
                      }}
                    </mat-cell
                    >
                  </ng-container>
                  <mat-header-row *matHeaderRowDef="materialdisplayedColumns"></mat-header-row>
                  <mat-row *matRowDef="let row; columns: materialdisplayedColumns"></mat-row>
                </mat-table>
              </div>
              <div fxFlex="100" class="col mt-10">
                <label class="lbl">Notes</label>
                <p>{{ materialData?.notes }}</p>
              </div>
            </div>
          </div>
          <div *ngIf="!materialData">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addMaterial()"
            >
              <h1>Create Material Information</h1>
            </div>
          </div>
        </mat-card>
        <mat-card
          class="details"
          fxFlex.gt-lg="{{ documentslist ? 48 : 49 }}"
          fxFlex.gt-md="{{ documentslist ? 48 : 49 }}"
          fxFlex.gt-xs="100"
        >
          <div *ngIf="closureInfo">
            <div fxLayout="row wrap" class="mb-10">
              <mat-card-title fxFlex>Closure Information</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addClosure()">edit</mat-icon>
            </div>
            <hr/>
            <div fxLayout="column" class="mb-20">
              <div class="mb-10"></div>
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100"
                     *ngIf="closureInfo?.otherClosure === null">
                  <label class="lbl">Closure</label><label>{{ closureMaterial?.name }}</label>
                </div>
                <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100"
                     *ngIf="closureInfo?.otherClosure !== null">
                  <label class="lbl">Closure</label><label>{{ closureInfo?.otherClosure }}</label>
                </div>
                <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                  <label class="lbl">Extended
                    Flap</label><label>{{ closureInfo?.extendedFlap == true | convertToYesNo }}</label>
                </div>
                <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                  <label class="lbl">Cat 5
                    Tunnel</label><label>{{ closureInfo?.cat5Tunnel == true | convertToYesNo }}</label>
                </div>
              </div>
              <div class="mb-20" fxLayout="row" fxLayoutAlign="space-between">
                <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                  <label class="lbl">Est Surface Temp ({{ tempUnit ? tempUnit : '' }}
                    )</label><label>{{ closureInfo?.estSurfaceTemp }}</label>
                </div>
                <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                  <label class="lbl">Max Temp ({{ tempUnit ? tempUnit : '' }})</label
                  ><label>{{ tempUnit === '°F' ? closureMaterial?.maxTempF : closureMaterial?.maxTemp }}</label>
                </div>
                <div fxFlex.gt-lg="33" fxFlex.gt-md="33" fxFlex.gt-sm="33" fxFlex.gt-xs="100">
                  <label class="lbl">Cost per foot</label
                  ><label>${{ closureInfo.otherClosureCost ? closureInfo.otherClosureCost : closureMaterial?.costPerSq }}</label>
                </div>
              </div>
              <div fxFlex="100" class="col mt-10">
                <label class="lbl">Notes</label>
                <p>{{ closureInfo?.notes }}</p>
              </div>
            </div>
          </div>
          <div *ngIf="!closureInfo">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addClosure()"
            >
              <h1>Create Closure Information</h1>
            </div>
          </div>
        </mat-card>
      </div>
      <div fxLayout="row wrap">
        <mat-card class="details" fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-xs="100">
          <div *ngIf="sensorInfoObject">
            <div fxLayout="row wrap">
              <mat-card-title fxFlex>Sensor Information</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addSensors()">edit</mat-icon>
            </div>
            <hr/>
            <div class="mb-10"></div>
            <div fxLayout="column" class="mb-10">
              <div class="highlight-mat-table">
                <mat-table [dataSource]="sensorsDataSource">
                  <ng-container matColumnDef="sensorType">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Sensor Type</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{
                        element?.sensorType !== 'Other' ? element?.sensorType?.id : element?.otherSensorType
                      }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="sensorLocation">
                    <mat-header-cell *matHeaderCellDef fxFlex="30"> Location</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="30">{{
                        element?.sensorLocation !== 'Other' ? element?.sensorLocation : element?.otherSensorLocation
                      }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="sensorConnector">
                    <mat-header-cell *matHeaderCellDef fxFlex="20"> Connector</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="20">{{
                        element?.sensorConnector !== 'Other' ? element?.sensorConnector?.id : element?.otherSensorConnector
                      }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="sensorLeadLength">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Lead ({{ measureUnit ? measureUnit : '' }})
                    </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.sensorLeadLength }}</mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="sensorTempType">
                    <mat-header-cell *matHeaderCellDef fxFlex="25">Sensor Temp Type</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.sensorTempType }}</mat-cell>
                  </ng-container>
                  <mat-header-row *matHeaderRowDef="SensorsdisplayedColumns"></mat-header-row>
                  <mat-row *matRowDef="let row; columns: SensorsdisplayedColumns"></mat-row>
                </mat-table>
              </div>
              <div fxFlex="100" class="col mt-10">
                <label class="lbl">Notes</label>
                <p>{{ sensorInfoObject?.notes }}</p>
              </div>
            </div>
          </div>
          <div *ngIf="!sensorInfoObject">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addSensors()"
            >
              <h1>Create Sensor Information</h1>
            </div>
          </div>
        </mat-card>
        <mat-card
          class="details"
          fxFlex.gt-lg="{{ documentslist ? 48 : 49 }}"
          fxFlex.gt-md="{{ documentslist ? 48 : 49 }}"
          fxFlex.gt-xs="100"
        >
          <div *ngIf="thermostatInfo">
            <div class="highlight-mat-table">
              <div fxLayout="row wrap">
                <mat-card-title fxFlex>Thermostat Information</mat-card-title>
                <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addThermostat()">edit</mat-icon>
              </div>
              <hr/>
              <div class="mb-10"></div>
              <div fxLayout="column" class="mb-10">
                <mat-table [dataSource]="thermostatdataSource">
                  <ng-container matColumnDef="thermostatType">
                    <mat-header-cell *matHeaderCellDef fxFlex="12"> Type</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{
                        element?.thermostatType?.id ? element?.thermostatType?.id : element.otherThermostatType?.id
                      }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="installationMethod">
                    <mat-header-cell *matHeaderCellDef fxFlex="13"> Installation Method</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="15">{{ element?.installationMethodDTO?.methodName }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="openTemp">
                    <mat-header-cell *matHeaderCellDef fxFlex="14"> Open Temp ({{ tempUnit ? tempUnit : '' }})
                    </mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="13">
                      {{ tempUnit === '°F' ? element?.openTempF : element?.openTemp }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="partNumber">
                    <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="11">{{
                        element?.partNumber ? element?.partNumber : element?.otherThermostatPartNumber
                      }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="cost">
                    <mat-header-cell *matHeaderCellDef fxFlex="8"> Cost</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="8"
                    >${{ element?.cost ? element?.cost : element?.otherThermostatCost }}
                    </mat-cell
                    >
                  </ng-container>
                  <ng-container matColumnDef="manualReset">
                    <mat-header-cell *matHeaderCellDef fxFlex="10">Manual Reset</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.manualReset | convertToYesNo }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="openOnRise">
                    <mat-header-cell *matHeaderCellDef fxFlex="10">Open On Rise</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.openOnRise | convertToYesNo }}
                    </mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="usaStock">
                    <mat-header-cell *matHeaderCellDef fxFlex="11">USA Stock</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="11">{{ element?.usaStock }}</mat-cell>
                  </ng-container>
                  <ng-container matColumnDef="vietnamStock">
                    <mat-header-cell *matHeaderCellDef fxFlex="11">Vietnam Stock</mat-header-cell>
                    <mat-cell *matCellDef="let element" fxFlex="11">{{ element?.vietnamStock }}</mat-cell>
                  </ng-container>
                  <mat-header-row *matHeaderRowDef="thermostatdisplayedColumns"></mat-header-row>
                  <mat-row *matRowDef="let row; columns: thermostatdisplayedColumns"></mat-row>
                </mat-table>

                <div fxFlex="100" class="mt-10">
                  <label class="lbl">Notes</label>
                  <p>{{ thermostatInfo?.notes }}</p>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="!thermostatInfo">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addThermostat()"
            >
              <h1>Create Thermostat Information</h1>
            </div>
          </div>
        </mat-card>
      </div>
      <div fxLayout="row wrap">
        <mat-card class="details" fxFlex.gt-lg="{{ documentslist ? 98 : 99 }}" fxFlex.gt-md="97" fxFlex.gt-xs="100">
          <div *ngIf="noteInfo?.notes">
            <div fxLayout="row wrap">
              <mat-card-title fxFlex>Note</mat-card-title>
              <mat-icon *ngIf="isRevisionActive" class="open-doc" (click)="addNote()">edit</mat-icon>
            </div>
            <hr/>
            <div class="mb-10"></div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <p>{{ noteInfo?.notes }}</p>
              </div>
            </div>
          </div>
          <div *ngIf="!noteInfo?.notes">
            <div
              class="details-box text-center"
              fxFlex.gt-lg="98"
              fxFlex.gt-md="98"
              fxFlex.gt-sm="98"
              fxFlex.gt-xs="100"
              (click)="addNote()"
            >
              <h1>Create Note</h1>
            </div>
          </div>
        </mat-card>
      </div>
      <br/>
    </div>
    <div [ngClass]="{ revDisable: !isRevisionActive }" *ngIf="listType === 'geometry'" class="right-geometry-panel">
      <sfl-geometry
        [quotId]="quotID"
        [revisionId]="revisionId"
        [jacketGroups]="jacketGroup"
        [isDocumentsListOpen]="documentslist"
        [isEnable]="isRevisionActive"
        [quotEntryMethod]="quotation?.entryMethod"
      ></sfl-geometry>
    </div>
    <div [ngClass]="{ revDisable: !isRevisionActive }" *ngIf="listType === 'accessories'"
         class="right-accessories-panel">
      <div class="less-peding cust_table">
        <sfl-accessories [quotId]="quotID" [revisionId]="revisionId"></sfl-accessories>
      </div>
    </div>
    <div [ngClass]="{ revDisable: !isRevisionActive }" class="less-peding cust_table right-finalize-panel"
         *ngIf="listType === 'finalize'">
      <sfl-finalize
        [quotId]="quotID"
        [quoteNumber]="quotation?.quotationNumber"
        [revisionId]="revisionId"
        [soNumber]="quotation?.salesOrderNumber"
      ></sfl-finalize>
    </div>
  </div>
  <div fxLayout="column">
    <div fxLayout="row">
      <div
        class="file-upload-card"
        *ngIf="documentslist"
        fxFlex.gt-lg="{{ 100 - sizeLG - 10 }}"
        fxFlex.gt-md="{{ 100 - sizeMD - 35 }}"
        fxFlex.gt-sm="{{ 100 - sizeSM }}"
        fxFlex.gt-xs="{{ 100 - sizeXS }}"
      >
        <mat-card fxFlex="100">
          <div fxLayout="row wrap" class="filename" *ngFor="let file of documentDataSource.data; let i = index">
            <mat-icon>insert_drive_file</mat-icon>
            <h4 fxFlex class="open-doc" (click)="openDoc(file.id)">{{ file?.name }}</h4>
            <a (click)="confirmDelete(file.id)">
              <div class="open-doc" fxLayoutAlign="end">
                <mat-icon>delete</mat-icon>
              </div>
            </a>
            <hr/>
          </div>
          <br/>
          <div fxLayout="row">
            <div class="form-group form_file">
              <input type="file" (change)="readUrl($event)"/>
              <p class="font-weight-bold">{{ value }}</p>
            </div>
          </div>
          <br/>
          <div fxLayout="row wrap" fxLayoutAlign="space-between">
            <div fxLayoutAlign="start">
              <label class="link" *ngIf="isValidFileSize">*File must be less than 10MB</label>
              <span *ngIf="isUploading" class="loading-icon">
            <img src="../../../../assets/images/loader.gif" alt="loader"/>
          </span>
            </div>
            <div fxLayoutAlign="end">
              <button mat-raised-button color="warn" [disabled]="!filename" (click)="uploadFile()">Upload</button>
            </div>
          </div>
        </mat-card>
      </div>
    </div>
  </div>
</div>
