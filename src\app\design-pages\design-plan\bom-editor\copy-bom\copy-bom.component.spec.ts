import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CopyBomComponent } from './copy-bom.component';
import { DesignPlanModule } from '../../design-plan.component.module';
import { RouterTestingModule } from '@angular/router/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

let component: CopyBomComponent;
let fixture: ComponentFixture<CopyBomComponent>;
describe('CopyBomComponent', () => {
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [DesignPlanModule, RouterTestingModule, BrowserAnimationsModule],
      declarations: [CopyBomComponent]
    })
      .compileComponents()
      .then(() => {
        fixture = TestBed.createComponent(CopyBomComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
      });
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  testCopyBOM();
});

function testCopyBOM() {
  it('should have a title on ngInit', () => {
    expect(component.title).toBe('Copy this BOM to different Jacket', 'should have title on Initialization');
  });
}
