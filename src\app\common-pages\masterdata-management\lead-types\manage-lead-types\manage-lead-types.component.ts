import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { LeadTypesMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog, MatDialogConfig } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-lead-types',
  templateUrl: './manage-lead-types.component.html'
})
export class ManageLeadTypesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  leadType: LeadTypesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  formData = new FormData();
  constructor(
    public dialogRef: MatDialogRef<ManageLeadTypesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.leadType = data;
  }

  ngOnInit() {
    this.leadType = this.leadType.id ? Object.assign({}, this.leadType) : new LeadTypesMaster();
    this.leadType.id ? (this.title = 'Update Lead Type') : (this.title = 'Add Lead Type');
  }

  updateLeadType() {
    this.showLoader = true;
    if (this.leadType.id) {
      this.subscription.add(
        this.masterDataService.updateLeadType(this.leadType).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addLeadType(this.leadType).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
