import {Injectable} from '@angular/core';
import Swal from 'sweetalert2';
import {Messages} from './../constants/messages.constants';

declare var jQuery: any;

@Injectable()
export class SweetAlertService {
  constructor() { }

  public async deleteAlert() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title,
      text: Messages.sweet_error.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async deleteAllFeaturesAlert() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title,
      text: Messages.sweet_error.confirm_msg_text_features,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }


  public async deleteAlertFeature() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title,
      text: Messages.sweet_error.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }


  public async confirmSync() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title,
      text: Messages.sweet_error.confirm_sync_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.continue_sync_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async confirmLabelEntrySync() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title,
      text: Messages.sweet_error.consirm_label_entry_sync,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.continue_sync_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async confirmNonStandardPartNumber() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title_generate_PN,
      text: Messages.sweet_error.confirm_non_stdandard_PN,
      type: 'question',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async checkPartNumberExist(message) {
    return Swal({
      title: Messages.sweet_error.part_number_found,
      text: message,
      type: 'warning',
      showCancelButton: false,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text_part_number
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async checkInEpicor() {
    return Swal({
      title: Messages.sweet_error.in_epicor_unchecked,
      text: Messages.sweet_error.in_epicor_unchecked_message,
      type: 'warning',
      showCancelButton: false,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text_part_number
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async checkPartNumberDontExist(message) {
    return Swal({
      title: Messages.sweet_error.part_number_not_associated,
      text: message,
      type: 'warning',
      showCancelButton: false,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text_part_number
    }).then(result => {
      return this.resultReturn(result);
    });
  }


  public async archiveAlert() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title,
      text: Messages.sweet_error.confirm_archive_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async createQuotationAlert(alertMsgObj) {
    return Swal({
      title: alertMsgObj.confirm_msg_title,
      text: alertMsgObj.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.quotation_sweet_error.confirm_btn_color,
      confirmButtonText: Messages.quotation_sweet_error.confirm_btn_text,
      customClass: 'sfl-create-quotation-alert sfl-create-quotation-alert h2'
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async warningWhenChangeUnit() {
    return Swal({
      title: Messages.warning_unitchange.confirm_msg_title,
      text: Messages.warning_unitchange.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_unitchange.confirm_btn_color,
      confirmButtonText: Messages.warning_unitchange.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async warningWhenUndoSignature() {
    return Swal({
      title: Messages.warning_FinalReview.confirm_msg_title,
      text: Messages.warning_FinalReview.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_unitchange.confirm_btn_color,
      confirmButtonText: Messages.warning_unitchange.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async warningWhenEntryMethodChange() {
    return Swal({
      title: Messages.warning_entryMethod_change.confirm_msg_title,
      text: Messages.warning_entryMethod_change.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_entryMethod_change.confirm_btn_color,
      confirmButtonText: Messages.warning_entryMethod_change.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async warningGeometryWattsUpdate() {
    return Swal({
      title: Messages.sweet_error.confirm_msg_title,
      text: Messages.sweet_error.confirm_update_watts_msg,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.sweet_error.confirm_btn_color,
      confirmButtonText: Messages.sweet_error.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async folderDeleteWarning(folderName: string) {
    return Swal({
      title: Messages.warning_delete_folder.confirm_msg_title,
      text: Messages.warning_delete_folder.confirm_msg_text + ' ' + folderName,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_delete_folder.confirm_btn_color,
      confirmButtonText: Messages.warning_delete_folder.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async rootPartNumberResetWarning() {
    return Swal({
      title: Messages.root_part_reset.confirm_msg_title,
      text: Messages.root_part_reset.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_delete_folder.confirm_btn_color,
      confirmButtonText: Messages.warning_delete_folder.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async confirmSyncElementToEpicor() {
    return Swal({
      text: Messages.Bom_Element.confirm_sync_msg_titile,
      type: 'question',
      showCancelButton: true,
      cancelButtonText: 'No',
      confirmButtonColor: Messages.warning_unitchange.confirm_btn_color,
      confirmButtonText: Messages.warning_unitchange.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async sequenceDuplicationWarning() {
    return Swal({
      title: Messages.warning_sequence_duplication.confirm_msg_title,
      type: 'warning',
      showCancelButton: false,
      confirmButtonColor: Messages.warning_sequence_duplication.confirm_btn_color,
      confirmButtonText: Messages.warning_sequence_duplication.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async partNumberNotExistsWarning() {
    return Swal({
      title: Messages.warning_part_number_not_exist.confirm_msg_title,
      type: 'warning',
      showCancelButton: false,
      confirmButtonColor: Messages.warning_part_number_not_exist.confirm_btn_color,
      confirmButtonText: Messages.warning_part_number_not_exist.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async partNumberNotExistWarning() {
    return Swal({
      title: Messages.warning_part_number_not_exist.confirm_msg_title,
      text: Messages.warning_part_number_not_exist.confirm_msg_text,
      type: 'warning',
      showCancelButton: false,
      confirmButtonColor: Messages.warning_part_number_not_exist.confirm_btn_color,
      confirmButtonText: Messages.warning_part_number_not_exist.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async confirmResetFilePathWarning() {
    return Swal({
      title: Messages.warning_reset_file_paths.confirm_msg_title,
      text: Messages.warning_reset_file_paths.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_reset_file_paths.confirm_btn_color,
      confirmButtonText: Messages.warning_reset_file_paths.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  public async confirmResetTitleBlock() {
    return Swal({
      title: Messages.warning_reset_title_block.confirm_msg_title,
      text: Messages.warning_reset_title_block.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_reset_title_block.confirm_btn_color,
      confirmButtonText: Messages.warning_reset_title_block.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }


  public async confirmResetBOM() {
    return Swal({
      title: Messages.warning_reset_bom.confirm_msg_title,
      text: Messages.warning_reset_bom.confirm_msg_text,
      type: 'warning',
      showCancelButton: true,
      confirmButtonColor: Messages.warning_reset_bom.confirm_btn_color,
      confirmButtonText: Messages.warning_reset_bom.confirm_btn_text
    }).then(result => {
      return this.resultReturn(result);
    });
  }

  private resultReturn(result): boolean {
    if (result.value) {
      return true;
    } else {
      return false;
    }
  }
}
