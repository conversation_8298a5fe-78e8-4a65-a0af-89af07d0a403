<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Name</mat-label>
          <input matInput [(ngModel)]="plugLightsFilter.name" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldplugName)"
            *ngIf="plugLightsFilter.name"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Part Number</mat-label>
          <input matInput [(ngModel)]="plugLightsFilter.partNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldpartNumber)"
            *ngIf="plugLightsFilter.partNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addPlugLights()">Add New Plug Light</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="plugLightsDataSource" (matSortChange)="getPlugLightsSorting($event)">
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.name }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="25"> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="25"> {{ element?.partNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="cost">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cost </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.cost }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="type">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.type }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clothCE">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cloth CE </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.clothCE | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="clothUL">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cloth UL </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.clothUL | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">          
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editPlugLights(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deletePlugLight(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="plugLightsColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: plugLightsColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!plugLightsDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPlugLightsPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
