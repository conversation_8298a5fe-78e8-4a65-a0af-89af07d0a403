import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Variable } from '../shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { QuoteTrackerService } from './quote-tracker.service';
import { QuoteStatusCount } from './quote-tracker.model';

@Component({
  selector: 'sfl-quote-tracker',
  templateUrl: './quote-tracker.component.html'
})
export class QuoteTrackerComponent implements OnInit, OnDestroy {
  pageTitle = 'Quatation Tracking';
  viewing = 'so-in-design';

  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  quoteStatusCounts: QuoteStatusCount;
  animationDuration = 1000;

  constructor(private readonly quoteTrackerService: QuoteTrackerService) {}

  ngOnInit() {
    this.quoteStatusCounts = new QuoteStatusCount();
    this.getQuoteStatusCounters();
  }

  getQuoteStatusCounters() {
    this.showLoader = true;
    this.quoteTrackerService.getQuoteStatusCounts().subscribe(
      (res: QuoteStatusCount) => {
        this.quoteStatusCounts = res;
        this.showLoader = false;
      },
      () => (this.showLoader = false)
    );
  }
  // used to handle the click action on the top counters of SO In Design
  switchToSOInDesign() {
    this.viewing = 'so-in-design';
  }
  // used to handle the click action on the top counters of Quote Completed
  switchToQuoteCompleted() {
    this.viewing = 'quote-completed';
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
