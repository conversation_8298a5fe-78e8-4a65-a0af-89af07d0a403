
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from '../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';


import { TitleBlock } from './title-block-editor.model';
import { SharedService } from 'src/app/shared';

@Injectable({ providedIn: 'root' })
export class TitleBlockService {
  constructor(private readonly http: HttpClient, private readonly sharedService: SharedService) {}

  updateTitleBlock(titleBlock: TitleBlock) {
    return this.http
      .post(AppConfig.UpdateTitleBlock, {
        TitleBlock: titleBlock,
        ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations(),
        CustomPath: titleBlock.selectedPartFilePath ? true : false
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  refreshTitleBlock(jacketId) {
    return this.http.get(AppConfig.RefreshTitleBlock + jacketId);
  }

  getTitleBlock(jacketId) {
    return this.http
      .get(AppConfig.GET_TITLE_BLOCK_BY_JACKETID + jacketId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  updateTitleBlockBHX(titleBlock: TitleBlock) {
    return this.http.put(AppConfig.UPDATE_TITLE_BLOCK, titleBlock).pipe(map(utils.extractData),catchError(utils.handleError),);
  }
}
