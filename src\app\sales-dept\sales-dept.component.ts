import { Component, OnInit } from '@angular/core';
import { Variable } from '../shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'sfl-sales-dept',
  templateUrl: './sales-dept.component.html'
})
export class SalesDeptComponent implements OnInit {
  pageTitle = 'Sales Department';

  subscription = new Subscription();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(private readonly titleService: Title) {}

  ngOnInit() {
    this.titleService.setTitle('Sales Department Dashboard');
  }
}
