import {Component, Inject, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {FormControl} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialog, MatDialogConfig, MatDialogRef} from '@angular/material';
import {Observable, Subscription} from 'rxjs';
import {map, startWith} from 'rxjs/operators';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {SharedService, SnakbarService} from '../../../shared';
import {AccessoriesService} from '../../accessories/accessories.service';
import {AccessoryControllers} from '../../accessories/Add Accessories/add-accessories.model';
import {Values} from './../../../shared/constants/values.constants';
import {ApplicationInfo, Material, Units} from './../ccdc-model/ccdc.model';
import {AddApplicationService} from './add-application.service';
import {
  UpdatePluggingInfoWarningComponent
} from './update-plugging-info-warning/update-plugging-info-warning.component';
import {ManageUnitsService} from '../manage-units/manage-units.service';

@Component({
  selector: 'sfl-add-application-info',
  templateUrl: './add-application.component.html'
})
export class AddApplicationComponent implements OnInit, OnDestroy {
  appInfo: ApplicationInfo;
  pipeMaterial: Material[] = [];
  pipeMaterialObservable$: Observable<Material[]>;
  pipeContent: Material[] = [];
  pipeContentObservable$: Observable<Material[]>;
  accessoryControllers: AccessoryControllers[];
  accessoryObservable$: Observable<AccessoryControllers[]>;
  appMaterial: Material;
  appContent: Material;
  jacketTypes: object = Values.JacketTypeConst;
  phaseTypes: object = Values.PhaseTypeConst;
  contentMotions: object = Values.ContentMotionsConst;
  productTypes: object = Values.ProductTypeConst;
  fitTypes: object = Values.FitTypeConst;
  subscription: Subscription = new Subscription();
  measureUnit = '';
  tempUnit = '';
  jacketGroupId: number;
  isOtherContent = false;
  isOtherMaterial = false;
  isOther = false;
  selectedJacketType: string;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  showReloadButton = false;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  materialControl = new FormControl();
  pipeContentControl = new FormControl();
  pipeMaterialControl = new FormControl();
  lastFilter = '';
  selection: string;
  accessoryControllersDetails: AccessoryControllers;
  pipeContentDetails: Material;
  pipeMaterialDetails: Material;

  constructor(
    public appinfoDialogRef: MatDialogRef<AddApplicationComponent>,
    private addApplicationService: AddApplicationService,
    private sharedService: SharedService,
    private snakbarService: SnakbarService,
    private accessoriesService: AccessoriesService,
    private manageUnitService: ManageUnitsService,
    @Inject(MAT_DIALOG_DATA) data,
    private matDialog: MatDialog
  ) {
    this.jacketGroupId = data.jacketGroupId;
  }

  ngOnInit() {
    this.getMeasurementUnit();
    this.appInfo = new ApplicationInfo();
    this.appMaterial = new Material();
    this.appContent = new Material();
    this.getMaterialList();
    this.getControlerTypeMasterData();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.measureUnit = res.measurementUnit;
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  // used to get the controller types master data
  getControlerTypeMasterData() {
    this.showLoader = true;
    this.subscription.add(
      this.accessoriesService.getAccessoryControllers().subscribe(
        (res: AccessoryControllers[]) => {
          this.accessoryControllers = res;
          this.accessoryObservable$ = this.materialControl.valueChanges.pipe(
            startWith<string | AccessoryControllers[]>(''),
            map(value => (typeof value === 'string' ? value : this.lastFilter)),
            map(filter => this.filter(filter)),
            map(values => values.sort(function (a, b) {
              return a.name > b.name ? 1 : -1;
            }))
          );
          this.showLoader = false;
        },
        (error) => {
          if (error.applicationStatusCode === 1209) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false;
        }
      )
    );
  }

  filter(filter: string): AccessoryControllers[] {
    this.lastFilter = filter;
    if (filter) {
      return this.accessoryControllers.filter(option => {
        if (option.id !== null && option.name !== null) {
          return (
            option.name.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.accessoryControllers ? this.accessoryControllers.slice() : [];
    }
  }

  filterPipeControl(filter: string): Material[] {
    this.lastFilter = filter;
    if (filter) {
      return this.pipeContent.filter(option => {
        if (option.name !== null) {
          return (
            option.name.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.pipeContent ? this.pipeContent.slice() : [];
    }
  }


  filterPipeMaterial(filter: string): Material[] {
    this.lastFilter = filter;
    if (filter) {
      return this.pipeMaterial.filter(option => {
        if (option.name !== null) {
          return (
            option.name.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.pipeMaterial ? this.pipeMaterial.slice() : [];
    }
  }


  // used to get the material listings
  getMaterialList() {
    this.showLoader = true;
    this.subscription.add(
      this.addApplicationService.getMaterialList().subscribe(
        (res: Material[]) => {
          res.forEach(element => {
            if (element.materialType.toLowerCase() === 'material') {
              this.pipeMaterial.push(element);
              this.pipeMaterialObservable$ = this.pipeMaterialControl.valueChanges.pipe(
                startWith<string | Material[]>(''),
                map(value => (typeof value === 'string' ? value : this.lastFilter)),
                map(filter => this.filterPipeMaterial(filter)),
                map(values => values.sort(function (a, b) {
                  return a.name > b.name ? 1 : -1;
                }))
              );
            } else if (element.materialType.toLowerCase() === 'content') {
              this.pipeContent.push(element);
              this.pipeContentObservable$ = this.pipeContentControl.valueChanges.pipe(
                startWith<string | Material[]>(''),
                map(value => (typeof value === 'string' ? value : this.lastFilter)),
                map(filter => this.filterPipeControl(filter)),
                map(values => values.sort(function (a, b) {
                  return a.name > b.name ? 1 : -1;
                }))
              );
            }
          });
          this.getApplicationInfo();
          this.showLoader = false;
        },
        (error) => {
          this.showLoader = false;
          if (error.applicationStatusCode === 1225) {
            this.snakbarService.error(error.message);
          }
        }
      )
    );
  }

  // used to set the defaults values
  setDefualtValue() {
    this.appInfo.phase = 'AC_SINGLE_PHASE';
    this.appInfo.heatupFrom = 20;
    this.appInfo.minAmbientTemp = 20;
    this.appInfo.heatupIn = 1;
    this.appInfo.pipeThickness = 0.125;
    this.appInfo.contentMotionFlowingRate = 0;
    this.appInfo.contentMotion = 'STATIC';
    this.appInfo.materialId = this.pipeMaterial.find(x => x.name.toLowerCase() === 'steel. 304').id;
    this.setMaterialValues(this.appInfo.materialId);
    this.appInfo.contentId = this.pipeContent.find(x => x.name.toLowerCase() === 'air').id;
    this.setContentValues(this.appInfo.contentId);
    this.selectedJacketType = '';
  }

  // used to get the application info
  getApplicationInfo() {
    this.showLoader = true;
    this.subscription.add(
      this.addApplicationService.getApplicationInfoByJacketGroup(this.jacketGroupId).subscribe(
        (res: ApplicationInfo) => {
          if (res) {
            this.appInfo = res;
            if (res.materialId) {
              this.setMaterialValues(res.materialId);
            }
            if (res.contentName) {
              this.setContentValues(res.contentId);
            }
            if (res.controlType === Values.Other) {
              this.isOther = true;
            }
            this.selectedJacketType = this.appInfo.jacketType;
            this.showLoader = false;
          } else {
            this.setDefualtValue();
            this.showLoader = false;
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  onPipeContentChange(value) {
    if (value) {
      this.pipeContentControl.setValue(this.appInfo.contentName);
      this.setContentValues(value);
    }
  }

  onFitTypeChange(value) {
    if (value) {
      this.appInfo.fitTypeEnum = value;
    }
  }

  onPipeMaterialChange(value) {
    if (value) {
      this.appInfo.materialId = value;
      this.setMaterialValues(value);
    }
  }

  setMaterialValues(value) {
    this.isOtherMaterial = false;

    this.appMaterial = this.pipeMaterial.find(x => x.id === value);

    if (this.appMaterial) {
      if (this.appMaterial.name === Values.Other) {
        this.isOtherMaterial = true;
      } else {
        this.appInfo.materialHeat = this.appMaterial.specificHeat;
        this.appInfo.materialDensity = this.appMaterial.density;
      }
    }
  }

  setContentValues(value) {
    this.isOtherContent = false;
    this.appContent = this.pipeContent.find(x => x.id === value);
    if (this.appContent) {
      if (this.appContent.name === Values.Other) {
        this.isOtherContent = true;
      } else {
        this.appInfo.contentHeat = this.appContent.specificHeat;
        this.appInfo.contentDensity = this.appContent.density;
      }
    }
  }

  onControlTypeChanged(value) {
    this.isOther = false;
    if (value === Values.Other) {
      this.isOther = true;
    }
  }

  onSelectionChanges(material: Material) {
    if (material) {
      this.accessoryControllersDetails = material;
      this.onControlTypeChanged(material);
    }
  }

  onSelectionChangesPipeContent(material: Material) {
    if (material) {
      this.appInfo.contentId = material.id;
      this.appInfo.contentName = material.name;

      this.onPipeContentChange(material);
    }
  }

  onSelectionChangesPipeMaterial(material: Material) {
    if (material) {
      this.appInfo.id = material.id;
      this.appInfo.materialName = material.name;
      this.setMaterialValues(material.name);
    }
  }

  displayFn(value: AccessoryControllers[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          material.name;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  displayPipeContents(value: Material[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.name;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  displayPipeMaterial(value: Material[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.name;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  // used to save the application info modal values
  saveApplicationInfo(value) {
    this.showLoader = true;
    this.appInfo.jacketGroupId = this.jacketGroupId;
    if (this.appContent) {
      if (this.appContent.name && this.appContent.name !== Values.Other) {
        this.appInfo.otherContentDensity = null;
        this.appInfo.otherContentHeat = null;
      }
    }
    if (this.appMaterial) {
      if (this.appMaterial.name && this.appMaterial.name !== Values.Other) {
        this.appInfo.otherMaterialDensity = null;
        this.appInfo.otherMaterialHeat = null;
      }
    }
    if (!this.isOther) {
      this.appInfo.otherControlType = null;
    }
    this.subscription.add(
      this.addApplicationService.saveOrupdateApplicationInfo(this.appInfo, this.appInfo.id).subscribe(
        (res: ApplicationInfo) => {
          if (res) {
            const obj = {data: res, mode: value === 'save' ? 0 : 1};
            this.appinfoDialogRef.close(obj);
          }
          this.showLoader = false;
        },
        () => {
          this.showReloadButton = true;
          this.showLoader = false;
        }
      )
    );
  }

  checkIfPluggingInfoNeedsUpdate() {
    if (this.selectedJacketType) {
      const matDataConfig = new MatDialogConfig();
      matDataConfig.width = PopupSize.size.popup_lg;
      this.matDialog
        .open(UpdatePluggingInfoWarningComponent, matDataConfig)
        .afterClosed()
        .subscribe((selection: boolean) => {
          if (selection) {
            this.saveApplicationInfo('saveandnext');
          }
        });
    }
  }

  closeDialog() {
    this.appinfoDialogRef.close();
  }

  // used to reload the page
  reloadPage(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
