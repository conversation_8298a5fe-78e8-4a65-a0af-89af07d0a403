<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex.gt-lg="30" fxFlex.gt-md="30">
          <mat-label>Search</mat-label>
          <input matInput [(ngModel)]="productFilter.name" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldName)"
            *ngIf="productFilter.name"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addProductType()">Add New Product Type</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="productTypeDataSource" (matSortChange)="getProductTypeSorting($event)">
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef fxFlex="80"> Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="80"> {{ element?.name }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> isObsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.isObsolete ? element?.isObsolete : false | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editProductType(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteProductType(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="productTypeColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: productTypeColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!productTypeDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getProductTypePagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
