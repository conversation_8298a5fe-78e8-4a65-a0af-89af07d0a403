import { Component, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { GenericPageable } from '../masterdata-management.model';
import { MasterdataManagementService } from '../masterdata-management.service';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { ApplicationInfoTemplateDTO, CcdcTemplateDTO, ClosureTemplateDTO,ImportCcdcFilter,PluggingInformationTemplateDTO } from './manage-ccdc/manage-ccdc.model';
import { Values } from 'src/app/shared/constants/values.constants';
import { SweetAlertService } from 'src/app/shared/service/sweet-alert.service';
import { Messages, SnakbarService } from 'src/app/shared';
import { EcrManagementService } from 'src/app/design-pages/ecr-management/ecr-management.service';
import { User } from '../../users/user.model';

@Component({
  selector: 'app-ccdc-master-data',
  templateUrl: './ccdc-master-data.component.html',
  styleUrls: ['./ccdc-master-data.component.css']
})
export class CcdcMasterDataComponent implements OnInit {
  pageTitle = 'CCDC Master';
  length: number;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  CcdcMaterialPageable: GenericPageable<CcdcTemplateDTO>;
  sortOrder = Variable.defaultSortOrderDescending;
  numberOfElements: number;
  filter = [];
  users: User[];
  ccdcMaterialMasterDataSource = new MatTableDataSource<CcdcTemplateDTO>();
  ccdcMaterialMasterMasterColumns = DisplayColumns.Cols.CcdcMasterDataColumn;
  showLoader = false;
  pageSizeOptions = Variable.pageSizeOptions;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  subscription = new Subscription();
  filterFieldValue = Values.FilterFields.value;
  importFilter: ImportCcdcFilter = new ImportCcdcFilter();

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
    private router: Router,
    private ecrManagementService: EcrManagementService
  ) { }

  ngOnInit() {
    this.getCCDCMasterData(this.initialPageIndex, this.initialPageSize);
    this.getAllUsers();
  }

  getCcdcMaterialMasterSorting(event) {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getCCDCMasterData(this.pageIndex, this.pageSize);
  }

  getCcdcPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getCCDCMasterData(this.pageIndex, this.pageSize);
  }

  createCcdcMaterialsTable(serviceRequestList: GenericPageable<CcdcTemplateDTO>) {
    this.ccdcMaterialMasterDataSource.data = serviceRequestList.content;
  }

  addFilter() {
    for (const property of Object.entries(this.importFilter)) {
      if (property[1].value) {
        this.filter.push(property[1]);
      }
    }
     this.getCCDCMasterData(this.pageIndex, this.pageSize);
  }

  getAllUsers() {
    // getUsers
    this.showLoader = true;
    this.subscription.add(this.ecrManagementService.getSalesAssociate(true).subscribe((res: User[]) => {
      if (res) {
        this.users = res;
        this.showLoader = false;
      }
    }, error => {
      this.showLoader = false;
    }));
  }

  getCCDCMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getCCDCMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<CcdcTemplateDTO>) => {
          this.CcdcMaterialPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createCcdcMaterialsTable(this.CcdcMaterialPageable);
          this.filter = [];
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  resetFilter() {
    this.importFilter = new ImportCcdcFilter();
    this.getCCDCMasterData(this.pageIndex, this.pageSize);
  }

  async deleteCcdcTemplateData(ccdcApplicationInfoDTO: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteCcdcTemplate(ccdcApplicationInfoDTO).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getCCDCMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  editCCDC(element: CcdcTemplateDTO) {
    if(!element.applicationInfoTemplateDTO){
      element.applicationInfoTemplateDTO = new ApplicationInfoTemplateDTO();
    }
    if(!element.pluggingInformationTemplateDTO){
      element.pluggingInformationTemplateDTO = new PluggingInformationTemplateDTO();
    }
    if(!element.closureTemplateDTO){
      element.closureTemplateDTO = new ClosureTemplateDTO();
    }
    this.masterDataService.setCcdcTemplateObs(element);
    this.router.navigate(['master-data/management/manage-ccdc']);
  }
}

