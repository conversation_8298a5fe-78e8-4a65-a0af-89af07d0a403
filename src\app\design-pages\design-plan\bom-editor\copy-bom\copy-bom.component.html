<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #copyBOMForm="ngForm" (ngSubmit)="copyJacketBOM()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
        <h3>
          Source Jacket
        </h3>
      </div>
      <div fxFlex.gt-lg="13" fxFlex.gt-md="13" fxFlex.gt-sm="16" fxFlex.gt-xs="100">
        <h3>
          Type
        </h3>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="22" fxFlex.gt-sm="23" fxFlex.gt-xs="100">
        <h3>Select Target Jacket</h3>
      </div>
      <div fxFlex.gt-lg="25" fxFlex.gt-md="25" fxFlex.gt-sm="23" fxFlex.gt-xs="100">
        <h3>Select a Section To Copy</h3>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
        <h3>
          <strong>{{ sourceJacket?.partNumber ? sourceJacket?.partNumber + ' - ' : '' }} {{sourceJacket?.name}}</strong>
        </h3>
      </div>
      <div fxFlex.gt-lg="5" fxFlex.gt-md="5" fxFlex.gt-sm="8" fxFlex.gt-xs="100">
        <h3>
          <strong>{{ type | uppercase }}</strong>
        </h3>
      </div>
      <div fxFlex.gt-lg="28" fxFlex.gt-md="28" fxFlex.gt-sm="25" fxFlex.gt-xs="100">
        <div *ngIf="!noOtherJacketsAvailable; else noJacketOption">
          <mat-form-field>
            <mat-select placeholder="Target Jacket" name="targetJacket" [(ngModel)]="targetJacket" #targetJacketSelect="ngModel" required>
              <mat-option *ngFor="let jacket of jacketListInfo" [value]="jacket.id">
                {{ jacket?.partNumber ? jacket?.partNumber + ' - ' : '' }} {{jacket?.name}}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <div *ngIf="targetJacketSelect.touched && targetJacketSelect.invalid">
            <small class="mat-text-warn" *ngIf="targetJacketSelect?.errors?.required">Target Jacket is required.</small>
          </div>
        </div>
        <ng-template #noJacketOption>
          <div>
            <label class="mat-text-warn">
              <h3>
                <strong>There are no other jackets available to copy BOM to.</strong>
              </h3>
            </label>
          </div>
        </ng-template>
      </div>
      <div fxFlex.gt-lg="28" fxFlex.gt-md="28" fxFlex.gt-sm="25" fxFlex.gt-xs="100">
      <mat-form-field>
        <mat-select placeholder="Copy Section" name="Section" #targetSection="ngModel" [(ngModel)]="sections" #select required multiple>
          <div class="select-all">
            <mat-checkbox [(ngModel)]="allSelected" #targetJacketSelect="ngModel" color="warn" [ngModelOptions]="{standalone: true}" (change)="toggleAllSelection()"></mat-checkbox>
          </div>
          <mat-option *ngFor="let sections of sectionListArray" [value]="sections.value">
            {{ sections?.title }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <div *ngIf="targetSection.touched && targetSection.invalid">
        <small class="mat-text-warn" *ngIf="targetSection?.errors?.required">Section is required.</small>
      </div>
    </div>
  </div>
  </mat-dialog-content>


  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!copyBOMForm.valid || noOtherJacketsAvailable">Copy BOM</button>
    </div>
  </mat-dialog-actions>
</form>
