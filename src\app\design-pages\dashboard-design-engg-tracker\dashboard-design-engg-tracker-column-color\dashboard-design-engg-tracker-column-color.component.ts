import {Component, Inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {Subscription} from 'rxjs';
import {Values} from 'src/app/shared/constants/values.constants';
import {DesignEngineeringQuoteTracker} from '../../../quote-tracker/quote-tracker.model';
import {QuoteTrackerService} from '../../../quote-tracker/quote-tracker.service';
import {DashboardDesignEnggTrackerComponent} from '../dashboard-design-engg-tracker.component';
import {Statuses} from '../../ecr-management/ecr-management.model';
import {SharedService} from '../../../shared';
import {SalesAssociate} from '../../dashboard/dashboard.model';
import {JacketListService} from '../../jacket-list/jacket-list.service';
import {Variable} from '../../../shared/constants/Variable.constants';
import {EditableTrackerFields} from '../../jacket-list/jacket-list.model';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import {DatePipe} from '@angular/common';

@Component({
  selector: 'app-design-engg-quote-tracker-column-color',
  templateUrl: './dashboard-design-engg-tracker-column-color.component.html',
  styleUrls: ['./dashboard-design-engg-tracker-column-color.component.css']
})
export class DashboardDesignEnggTrackerColumnColorComponent implements OnInit {
  highlightedColors = Values.highlightColors;
  quoteId: number;
  subscription: Subscription = new Subscription();
  showLoader = false;
  columnList = Values.designQuoteTrackerColumnList;
  value: string;
  statuses: Statuses[];
  designers: SalesAssociate[] = [];
  countries: object = Values.ManufacturingCountries;
  type = Variable.typeDesign;
  trackerFields: EditableTrackerFields = new EditableTrackerFields();
  trackerFieldsForm: FormGroup;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    private readonly quoteTrackerService: QuoteTrackerService,
    private readonly sharedService: SharedService,
    private readonly jacketListService: JacketListService,
    private readonly formBuilder: FormBuilder,
    private readonly datePipe: DatePipe,
    public readonly dialogRef: MatDialogRef<DashboardDesignEnggTrackerComponent>,
  ) {
    this.quoteId = data.quoteId;
    this.value = data.value;
  }

  ngOnInit() {
    this.getDesignQuoteTrackerDefaultValues();
    this.createEditableTrackerFieldForm();
    if (this.value === 'designStatusId') {
      this.getQuoteStatuses();
    } else if (this.value === 'designerId') {
      this.getAllDesigners();
    }
  }

  selectColumn(colorValue: string) {
    this.showLoader = true;
    if (colorValue !== undefined) {
      this.subscription.add(
        this.quoteTrackerService.designColumnHighlighting(this.quoteId, this.value, colorValue).subscribe(
          (res: DesignEngineeringQuoteTracker) => {
            if (res) {
              this.closeDialog(true);
            }
          },
          () => (this.showLoader = false)
        )
      );
    }
    this.saveTrackerFields();
  }

  getDesignQuoteTrackerDefaultValues() {
    this.showLoader = true;
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService
          .getDesignQuoteTrackerDefaultValues(this.type, this.quoteId)
          .subscribe(
            (defaultValues: EditableTrackerFields) => {
              this.trackerFields = defaultValues;
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            },
            () => {
              this.showLoader = false;
              this.setDefaultValuesForForm();
              resolve();
            }
          )
      );
    });
  }

  createEditableTrackerFieldForm() {
    this.trackerFieldsForm = this.formBuilder.group({
      quotationId: new FormControl(),
      dollarAmount: new FormControl(),
      designLocation: new FormControl(),
      quotationStatusId: new FormControl(),
      ofa1Date: new FormControl(),
      approval1Date: new FormControl(),
      assignedDesignerId: new FormControl(),
      designComments: new FormControl(),
      customNoOfDesigns: new FormControl()
    });

  }

  getQuoteStatuses() {
    this.subscription.add(
      this.sharedService
        .getStatuesByTypeOrderByOrderNumber()
        .subscribe((res: Array<Statuses>) => {
          if (res) {
            this.statuses = [];
            for (const statusType of res) {
              if (statusType.type === 'both' || statusType.type === 'design') {
                this.statuses.push(statusType);
              }
            }
            res = this.statuses;
          }
        })
    );
  }

  getAllDesigners() {
    this.showLoader = true;
    return new Promise<void>((resolve) => {
      this.subscription.add(
        this.jacketListService.getAllDesigners().subscribe(
          (res: Array<SalesAssociate>) => {
            this.designers = res;
            this.showLoader = false;
            resolve();
          },
          () => {
            resolve();
            this.showLoader = false;
          }
        )
      );
    });
  }

  setDefaultValuesForForm() {
    this.trackerFieldsForm.controls.quotationId.patchValue(this.quoteId);
    this.trackerFieldsForm.controls.designLocation.patchValue(
      this.trackerFields.designLocation
    );
    this.trackerFieldsForm.controls.quotationStatusId.patchValue(
      this.trackerFields.quotationStatusId
    );
    this.trackerFieldsForm.controls.dollarAmount.patchValue(
      this.trackerFields.dollarAmount
    );
    this.trackerFieldsForm.controls.ofa1Date.patchValue(
      this.trackerFields.ofa1Date ? new Date(this.trackerFields.ofa1Date) : ''
    );
    this.trackerFieldsForm.controls.approval1Date.patchValue(
      this.trackerFields.approval1Date
        ? new Date(this.trackerFields.approval1Date)
        : ''
    );
    this.trackerFieldsForm.controls.assignedDesignerId.patchValue(
      this.trackerFields.assignedDesignerId
    );
    this.trackerFieldsForm.controls.designComments.patchValue(
      this.trackerFields.designComments
    );
    this.trackerFieldsForm.controls.customNoOfDesigns.patchValue(
      this.trackerFields.customNoOfDesigns
    );
  }

  saveTrackerFields() {
    this.showLoader = true;
    this.trackerFieldsForm.controls.ofa1Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.ofa1Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.trackerFieldsForm.controls.approval1Date.setValue(
      this.datePipe.transform(
        this.trackerFieldsForm.controls.approval1Date.value,
        Values.dateFormat.formatHyphen
      )
    );
    this.subscription.add(
      this.jacketListService
        .saveDesignQuoteTrackerFields(this.trackerFieldsForm.value, this.value)
        .subscribe(
          (res: EditableTrackerFields) => {
            this.trackerFields = res;
            this.showLoader = false;
            this.dialogRef.close();
          },
          () => {
            this.showLoader = false;
          }
        )
    );
  }

  closeDialog(flag = false): void {
    this.dialogRef.close(flag);
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
