import { SharedService } from '../../../shared/service/shared.service';
import { ManageRevisionsService } from './manage-revisions.service';
import { On<PERSON><PERSON>roy, Inject, ViewChild } from '@angular/core';
import { OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { Revision } from '../ccdc-model/ccdc.model';
import { Component } from '@angular/core';
import { MatDialogRef, MatTableDataSource, MAT_DIALOG_DATA, MatPaginator } from '@angular/material';
import { SnakbarService } from '../../../shared';
import { SummarySalesOrderComponent } from '../summary-sales-order.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';

@Component({
    selector: 'sfl-manage-revision',
    templateUrl: './manage-revisions.component.html'
})

export class ManageRevisionsComponent implements OnInit, OnDestroy {

    displayedColumns = ['revision', 'editby', 'time', 'active'];
    dataSource = new MatTableDataSource<Revision>();

    subscription: Subscription = new Subscription();
    quotationId: number;
    revisionList: Revision[];
    revision: Revision;
    pageSize = PopupSize.size.pageSize;
    showLoader: boolean;
    color = Variable.warnRed;
    mode = Variable.spinnerMode;
    spinnerDiameter = Variable.smallSpinnerDiameter75;

    @ViewChild(MatPaginator) paginator: MatPaginator;

    constructor(
        private snakbarService: SnakbarService,
        private dialogRef: MatDialogRef<SummarySalesOrderComponent>,
        private manageRevisionsService: ManageRevisionsService,
        private sharedService: SharedService,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {
        this.quotationId = data.quotationId;
    }

    ngOnInit() {
        this.revision = new Revision();
        this.getRevisionsListByQuotationId(this.quotationId);
    }

    getRevisionsListByQuotationId(quotation: number) {
        this.subscription.add(this.manageRevisionsService.getRevisionsByQuotationId(this.quotationId).subscribe((res: Revision[]) => {
            this.revisionList = res;
            this.dataSource.data = this.revisionList;
            this.dataSource.paginator = this.paginator;
        }));
    }

    createRevision() {
        this.showLoader = true;
        this.revision.quotationId = this.quotationId;
        this.revision.createdByUser = this.sharedService.getUserName();
        this.subscription.add(this.manageRevisionsService.createRevision(this.revision).subscribe((res: Revision) => {
            this.getRevisionsListByQuotationId(this.quotationId);
            this.showLoader = false;
            this.closeDialog();
        }, error => {
            this.showLoader = false;
        }));
    }

    closeDialog(): void {
        this.dialogRef.close();
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
