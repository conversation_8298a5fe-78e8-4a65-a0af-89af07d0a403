
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../app.config';
import { QuotationSearchFilter } from './dashboard.model';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../shared/helpers/app.helper';


import { createRequestOption, PageableQuery } from 'src/app/shared';
import { Quotation } from '../new-quotation/ccdc-model/ccdc.model';

@Injectable({ providedIn: 'root' })
export class DashboardService {
  constructor(private http: HttpClient) { }

  getQuotation() {
    return this.http.get(AppConfig.QUOTATION).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getPageableQuotation(pageableObject: PageableQuery) {
    return this.http.get(AppConfig.QUOTATION, {
      params: createRequestOption(pageableObject)
    }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getSalesAssociate(isGlobalSearch: boolean) {
    return this.http.get(AppConfig.SalesAssociate + '/' + isGlobalSearch).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  searchQuotations(quotationSearchFilter: QuotationSearchFilter, pageableObject) {
    return this.http.post(AppConfig.SEARCH_QUOTATION_API, quotationSearchFilter, {
      params: createRequestOption(pageableObject)
    }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  copyQuotations(quotationDTO: Quotation, quotationId: number) {
    return this.http.post(AppConfig.COPY_QUOTATIONS + quotationId, quotationDTO).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  deleteQuotations(id: number) {
    return this.http.delete(
      AppConfig.DELETE_QUOTATION + '/' + id
    ).pipe(map(utils.extractData),
      catchError(utils.handleError),);
  }

  countStatus() {
    return this.http.get(
      AppConfig.STATUS_COUNT
    ).pipe(map(utils.extractData),
      catchError(utils.handleError),);
  }

  importMobileData() {
    return this.http.get(
      AppConfig.ANDROID_DATA_IMPORT
    ).pipe(map(utils.extractData),
      catchError(utils.handleError),);
  }
}
