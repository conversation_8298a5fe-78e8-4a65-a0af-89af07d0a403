import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import {
  AccessoryControllerMaster,
  AccessoryControllerMasterPageable,
  AccessoryControllerFilter,
  GenericPageable
} from '../masterdata-management.model';
import { MatTableDataSource, MatPaginator, MatSort, MatDialogConfig, MatDialog } from '@angular/material';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageAccessoryControllersComponent } from './manage-accessory-controllers/manage-accessory-controllers.component';
import { SnakbarService, SweetAlertService, Messages } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-accessory-controllers',
  templateUrl: './accessory-controllers.component.html'
})
export class AccessoryControllersComponent implements OnInit, OnDestroy {
  pageTitle = 'Accessory Controllers Master';
  accessoryController: AccessoryControllerMaster;
  accessoryControllerPageable: GenericPageable<AccessoryControllerMaster>;
  accessoryControllerDataSource = new MatTableDataSource<AccessoryControllerMaster>();
  accessoryControllerColumns = DisplayColumns.Cols.AccessoryController;
  accessoryControllerFilter: AccessoryControllerFilter = new AccessoryControllerFilter();
  dataSource = new MatTableDataSource<AccessoryControllerMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  showLoader = false;
  numberOfElements: number;
  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldName = Values.FilterFields.name;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getAccessoryControllerMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new accessory controller
  addAccessoryController() {
    this.editAccessoryController(new AccessoryControllerMaster());
  }

  // used to add filter for accessories listing
  async addFilter() {
    this.filter =
      this.accessoryControllerFilter.name === '' ? [] : [{ key: this.filterFieldName, value: this.accessoryControllerFilter.name }];
    this.getAccessoryControllerMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear accessories controller listing filter
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldName,
        value: fieldToClear === this.filterFieldName ? (this.accessoryControllerFilter.name = '') : this.accessoryControllerFilter.name
      }
    ];
    this.getAccessoryControllerMasterData(this.initialPageIndex, this.pageSize);
  }

  getAccessoryControllerMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getAccessoryControllers(this.filter, pageable).subscribe(
        (res: GenericPageable<AccessoryControllerMaster>) => {
          this.accessoryControllerPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createAccessoryControllerTable(this.accessoryControllerPageable);
          this.showLoader = false;
        },
        error => {
          if (error.applicationStatusCode === 1225) {
            this.snakbarService.error(error.message);
          }
          this.showLoader = false;
          this.accessoryControllerDataSource.data = [];
        }
      )
    );
  }

  createAccessoryControllerTable(serviceRequestList: GenericPageable<AccessoryControllerMaster>) {
    this.accessoryControllerDataSource.data = serviceRequestList.content;
  }

  getAccessoryControllerPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getAccessoryControllerMasterData(this.pageIndex, this.pageSize);
  }

  getAccessoryControllerSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getAccessoryControllerMasterData(this.pageIndex, this.pageSize);
  }

  editAccessoryController(accessoryController: AccessoryControllerMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = accessoryController;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-accessory-controller-master-model';
    const dialogRef = this.matDialog.open(ManageAccessoryControllersComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          accessoryController.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getAccessoryControllerMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  async deleteAccessoryController(accessoryControllerId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteAccessoryControllers(accessoryControllerId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getAccessoryControllerMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
