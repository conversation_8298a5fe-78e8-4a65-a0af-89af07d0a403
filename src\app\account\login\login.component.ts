import { AuthenticationService } from './login.service';
import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { SharedService } from '../../shared';
import { Role } from '../../shared/constants/user-roles.constants';
import { MatDialogConfig, MatDialog } from '@angular/material';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { SolidworksDownloadComponent } from 'src/app/shared/component/solidworks-download/solidworks-download.component';
import { Title } from '@angular/platform-browser';
import { SetInitialNameComponent } from './set-initial-name/set-initial-name.component';
import { Route } from 'src/app/shared/constants/router.constants';

@Component({
  selector: 'sfl-login',
  templateUrl: './login.component.html'
})
export class LoginComponent implements OnInit {
  email = '';
  password = '';
  isError = false;
  authority: string;
  returnUrl: string;
  setInitial = true;

  constructor(
    private matDialog: MatDialog,
    private router: Router,
    private sharedService: SharedService,
    private authenticationService: AuthenticationService,
    private titleService: Title,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.titleService.setTitle('Login - BriskHeat');
    this.route.queryParams.subscribe(params => {
      this.returnUrl = params.returnUrl || '';
    });
  }

  login() {
    this.isError = false;
    this.authenticationService.login(this.email, this.password).subscribe((response: any) => {
      this.sharedService.setUserToken(response.access_token);
      if (this.sharedService.getUserToken() !== null) {
        this.authenticationService.getAccountData().subscribe(async (res: any) => {
          this.authority = res.authorities[0];
          this.sharedService.setUserRole(this.authority);
          this.sharedService.setUserId(res.id);
          this.sharedService.setUsersCountry(res.country);
          this.sharedService.setUserName(res.firstName + ' ' + res.lastName);
          if (this.returnUrl) {
            this.router.navigateByUrl('/' + this.returnUrl);
          } else if (this.authority === Role.ADMIN_ROLE) {
            this.router.navigate([Route.QUOTE_TRACKER.dashboard]);
          } else if (this.authority === Role.ENG_ROLE) {
            this.router.navigate([Route.APP_ENGG.dashboard]);
          } else if (this.authority === Role.DESIGN_ROLE) {
            this.router.navigate([Route.DESIGN_ENGG.dashboard]);
          } else if (this.sharedService.getRole() === Role.SALES_ROLE) {
            this.router.navigate([Route.SALES.rfqForm]);
          }
          if (res.initial === false) {
            await this.setInitialName(res.id);
          }
        });
      } else {
        this.isError = true;
      }
    });
  }

  openSolidworkDownloadPage() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_md;
    this.matDialog.open(SolidworksDownloadComponent, matDataConfig);
  }

  setInitialName(loginUserId) {
    return new Promise(resolve => {
      this.setInitial = false;
      const userId = loginUserId;
      const matDataConfig = new MatDialogConfig();
      matDataConfig.data = { userId: userId };
      matDataConfig.width = PopupSize.size.popup_md;
      matDataConfig.panelClass = 'sfl-initial-name-model';
      matDataConfig.disableClose = true;
      const dialogRef = this.matDialog.open(SetInitialNameComponent, matDataConfig);
      dialogRef.afterClosed().subscribe(res => {
        if (res) {
          this.setInitial = true;
          resolve();
        }
      });
    });
  }
}
