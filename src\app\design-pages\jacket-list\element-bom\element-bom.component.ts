import { Values } from './../../../shared/constants/values.constants';
import { ElementBomService } from './element-bom.service';
import { DatePipe } from '@angular/common';
import { ElementBomModel, ChecklistElementBomModelDTO } from './element-bom.model';
import { Subscription } from 'rxjs';
import { MatDialogRef, MatTableDataSource, MAT_DIALOG_DATA } from '@angular/material';
import { Component, Inject, OnInit, OnDestroy } from '@angular/core';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { UndoResponse } from '../pattern/pattern.model';
import { SweetAlertService } from 'src/app/shared/service/sweet-alert.service';
import { JacketStatus } from '../jacket-list.model';

@Component({
  selector: 'sfl-element-bom',
  templateUrl: './element-bom.component.html'
})
export class ElementBomComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  elementBom = new ElementBomModel();
  checklistElementBomModelDTO: ChecklistElementBomModelDTO = new ChecklistElementBomModelDTO();
  jacketList = new Array<number>();
  jacketId: number;
  quotationId: number;
  showLoader: boolean;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  undoResponseDTO: UndoResponse = new UndoResponse();
  JacketStatuses: JacketStatus = new JacketStatus();

  constructor(
    public readonly dialogRef: MatDialogRef<ElementBomComponent>,
    private readonly sweetAlertService: SweetAlertService,
    private readonly datePipe: DatePipe,
    private readonly elementBomService: ElementBomService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketList = data.jacketList;
    this.jacketId = data.jacketId;
    this.quotationId = data.quotationId;
  }

  ngOnInit() {
    this.elementBom.elementBOMDate = new Date();
    if (this.jacketId) {
      this.getElementBomByJacketId(this.jacketId);
    }
    this.checklistElementBomModelDTO.quotationID = this.quotationId;
  }

  getElementBomByJacketId(jacketId) {
    this.subscription.add(
      this.elementBomService.getElementBomByJacketId(jacketId).subscribe((res: ElementBomModel) => {
        if (res) {
          this.elementBom = res;
        }
      })
    );
  }

  saveElementBom() {
    this.showLoader = true;
    this.elementBom.elementBOMDate = this.datePipe.transform(this.elementBom.elementBOMDate, Values.dateFormat.format);
    if (this.jacketList && this.jacketList.length > 0) {
      this.jacketList.forEach(ele => {
        const elementBomCost = Object.assign({}, this.elementBom);
        elementBomCost.jacketId = ele;
        elementBomCost.checked = true;
        this.checklistElementBomModelDTO.elementBOMDTOs.push(elementBomCost);
      });
    } else {
      if (this.jacketId) {
        this.elementBom.jacketId = this.jacketId;
        this.checklistElementBomModelDTO.elementBOMDTOs.push(this.elementBom);
      }
    }

    if (this.checklistElementBomModelDTO.elementBOMDTOs.length > 0) {
      this.subscription.add(
        this.elementBomService.saveElementBOM(this.checklistElementBomModelDTO).subscribe(
          res => {
            this.checklistElementBomModelDTO = new ChecklistElementBomModelDTO();
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.SAVE, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    } else {
      this.showLoader = false;
    }
  }

  async undoElementBOM() {
    if (await this.sweetAlertService.warningWhenUndoSignature()) {
      this.subscription.add(
        this.elementBomService.updateElementBOM(this.checklistElementBomModelDTO, this.jacketId).subscribe(
          res => {
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.UNDO, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  closeDialog(updated?: string, jacketId?: number): void {
    this.undoResponseDTO.status = updated;
    this.undoResponseDTO.id = jacketId;
    this.dialogRef.close(this.undoResponseDTO);
  }


  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
