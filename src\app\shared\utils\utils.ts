import { formatDate } from '@angular/common';
import { HttpParams } from '@angular/common/http';
export class Utils {
  public static getCopyrightYear() {
    const startYear = 2018;
    const currentYear = new Date().getFullYear();
    if (startYear === currentYear) {
      return currentYear;
    } else {
      return startYear + '-' + currentYear;
    }
  }

  public static filterDropDownList(array, value) {
    if (value) {
      const filterValue = value.toLowerCase();
      return array.filter(state => state.name.toLowerCase().startsWith(filterValue));
    }
    return array;
  }

  public static converStringToDate(date: string) {
    return (date !== null) ? new Date(date) : null;
  }

  public static isDecimalNumber(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode === 46) {
      if (event.target.value.includes('.')) {
        return false;
      } else {
        return true;
      }
    } else {
      return !(charCode > 31 && (charCode < 48 || charCode > 57));
    }
  }

  public static isNumberKey(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    return !(charCode > 31 && (charCode < 48 || charCode > 57));
  }

  public static getToDate() {
    const today = Date.now();
    const toData = formatDate(today, 'MM/dd/yyyy', 'en');
    return toData;
  }

}

export const createRequestOption = (req?: any): HttpParams => {
  let options: HttpParams = new HttpParams();
  if (req) {
    Object.keys(req).forEach(key => {
      options = options.set(key, req[key]);
    });
  }
  return options;
};

export class PageableQuery {
  size: number;
  page: number;
  sort: string;
  direction: string;
}

//used for deep copying objects.
export const deepCopyFunction = (inObject) => {
  let outObject, value, key

  if (typeof inObject !== "object" || inObject === null) {
    return inObject // Return the value if inObject is not an object
  }

  // Create an array or object to hold the values
  outObject = Array.isArray(inObject) ? [] : {}

  for (key in inObject) {
    value = inObject[key]

    // Recursively (deep) copy for nested objects, including arrays
    outObject[key] = deepCopyFunction(value)
  }

  return outObject;
}
