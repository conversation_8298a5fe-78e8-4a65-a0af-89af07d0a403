import {AddClosureService} from './add-closure.service';
import {ClosureInformation, ClosureMaterial, Units} from './../ccdc-model/ccdc.model';
import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material';
import {SharedService, SnakbarService} from '../../../shared';
import {Observable, Subscription} from 'rxjs';
import {Values} from '../../../shared/constants/values.constants';
import {environment} from 'src/environments/environment';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {map, startWith} from 'rxjs/operators';
import {FormControl} from '@angular/forms';
import {ManageUnitsService} from '../manage-units/manage-units.service';

@Component({
  selector: 'sfl-add-closure',
  templateUrl: './add-closure.component.html'
})
export class AddClosureComponent implements OnIni<PERSON>, OnD<PERSON>roy {
  closureInfo: ClosureInformation;
  closureMaterial: ClosureMaterial;
  materials: ClosureMaterial[];
  closureDetails: ClosureMaterial;
  closureObservable$: Observable<ClosureMaterial[]>;
  closureMaterialControl = new FormControl();
  material: ClosureMaterial;
  subscription: Subscription = new Subscription();
  tempUnit = '';
  jacketGroupId: number;
  isOther = false;
  imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
  outDatedViewErrorMessage = Values.OutdatedViewError;
  showReloadButton = false;
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  lastFilter = ''
  selection: string;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  constructor(
    public closureDialogRef: MatDialogRef<AddClosureComponent>,
    public matDialog: MatDialog,
    public addClosureService: AddClosureService,
    private readonly snakbarService: SnakbarService,
    public sharedService: SharedService,
    private manageUnitService: ManageUnitsService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketGroupId = data.jacketGroupId;
  }

  ngOnInit() {
    this.getMeasurementUnit();
    this.closureMaterial = new ClosureMaterial();
    this.closureInfo = new ClosureInformation();
    this.material = new ClosureMaterial();
    this.getMaterialList();
  }

  getMeasurementUnit(){
    this.subscription.add(
        this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
            (res: Units) => {
              if (res) {
                this.tempUnit  = res.tempUnit;
              }
            }
        )
    );
  }

  // used to get the closure materials
  getClosure() {
    this.showLoader = true;
    this.subscription.add(
      this.addClosureService.getClosureInfoByJacketGroup(this.jacketGroupId).subscribe(
        (res: ClosureInformation) => {
          if (res) {
            this.closureInfo = res;

            if (this.closureInfo.closureMaterialId) {
              this.material = this.materials.find(x => x.id === this.closureInfo.closureMaterialId);
              if (this.closureInfo.otherClosure) {
                this.isOther = true;
              }
              if (this.closureInfo.closureMaterialImageUrl) {
                this.imageUrl = environment.IMAGES_URL + this.closureInfo.closureMaterialImageUrl;
              }
            }
          }
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  filterClosure(filter: string): ClosureMaterial[] {
    this.lastFilter = filter;
    if (filter) {
      return this.materials.filter(option => {
        if (option.id !== null && option.name !== null) {
          return (
            option.name.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.materials ? this.materials.slice() : [];
    }
  }

  displayMaterials(value: ClosureInformation[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.closureMaterialName;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }


  onSelectionClosure(data: ClosureMaterial) {
    if (data) {
      this.closureInfo.closureMaterialId = data.id;
      this.closureInfo.closureMaterialName = data.name
      this.onMaterialChange(data.id);
    }
  }

  // used to get the materials listing
  getMaterialList() {
    this.showLoader = true;
    this.subscription.add(
      this.addClosureService.getClosureMaterialList().subscribe(
        (res: ClosureMaterial[]) => {
          this.materials = res;
          this.closureObservable$ = this.closureMaterialControl.valueChanges.pipe(
            startWith<string | ClosureMaterial[]>(''),
            map(value => (typeof value === 'string' ? value : this.lastFilter)),
            map(filter => this.filterClosure(filter)),
            map(values => values.sort(function(a,b) { return a.name > b.name ? 1 : -1}))
          );
          this.showLoader = false;
          this.getClosure();
        },
        () => (this.showLoader = false)
      )
    );
  }

  // used to handle the material change
  onMaterialChange(id) {
    this.isOther = false;
    this.material = this.materials.find(x => x.id === id);
    this.closureMaterialControl.setValue(this.closureInfo.closureMaterialName);
    if (this.material.name === Values.Other) {
      this.isOther = true;
    }
    this.closureInfo.closureMaterialId = id;
    if (this.material.imageUrl) {
      this.imageUrl = environment.IMAGES_URL + this.material.imageUrl;
    } else {
      this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
    }
  }

  // used to save the closure material modal details
  saveClosure(value) {
    this.showLoader = true;
    this.closureInfo.jacketGroupId = this.jacketGroupId;
    if (!this.isOther) {
      this.closureInfo.otherClosure = null;
      this.closureInfo.otherClosurePartNumber = null;
      this.closureInfo.otherClosureCost = null;
    }
    this.subscription.add(
      this.addClosureService.saveClosureInfo(this.closureInfo).subscribe(
        (res: ClosureMaterial) => {
          if (res) {
            this.imageUrl = Values.DEFAULT_PRODUCT_IMAGE;
            const obj = { data: res, mode: value === 'save' ? 0 : 1 };
            this.closureDialogRef.close(obj);
          }
          this.showLoader = false;
        },
        (error) => {
          if (error.applicationStatusCode === 3001) {
            this.snakbarService.error(error.message);
          }
          this.showReloadButton = true;
          this.showLoader = false;
        }
      )
    );
  }

  closeDialog() {
    this.closureDialogRef.close();
  }

  // used to reload the page
  reloadPage(): void {
    window.location.reload();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
