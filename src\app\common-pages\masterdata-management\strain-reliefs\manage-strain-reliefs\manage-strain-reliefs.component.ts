import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { SleevingTypesAndStrainReliefsMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-strain-reliefs',
  templateUrl: './manage-strain-reliefs.component.html'
})
export class ManageStrainReliefsComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  strainReliefs: SleevingTypesAndStrainReliefsMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageStrainReliefsComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.strainReliefs = Object.assign({}, data);
    data.clothCE === 'true' || data.clothCE === true ? (this.strainReliefs.clothCE = true) : (this.strainReliefs.clothCE = false);
    data.clothUL === 'true' || data.clothUL === true ? (this.strainReliefs.clothUL = true) : (this.strainReliefs.clothUL = false);
  }

  ngOnInit() {
    this.strainReliefs = this.strainReliefs.id ? Object.assign({}, this.strainReliefs) : new SleevingTypesAndStrainReliefsMaster();
    this.strainReliefs.id ? (this.title = 'Update Stran Relief') : (this.title = 'Add Stran Relief');
  }

  updateStrainReliefs() {
    this.showLoader = true;
    if (this.strainReliefs.id) {
      this.subscription.add(
        this.masterDataService.updateStrainReliefs(this.strainReliefs).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addStrainReliefs(this.strainReliefs).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
