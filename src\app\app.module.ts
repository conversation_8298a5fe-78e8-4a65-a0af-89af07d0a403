import { HttpClientModule } from '@angular/common/http';
import { NgModule, Error<PERSON>and<PERSON> } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { Ng2Webstorage } from 'ngx-webstorage';
import { SharedService } from 'src/app/shared/service/shared.service';
import { AddApplicationService } from './admin-pages/new-quotation/Add Applicaton/add-application.service';
import { AddClosureService } from './admin-pages/new-quotation/Add Closure/add-closure.service';
import { JacketGroupService } from './admin-pages/new-quotation/manage-jacketgroups/manage-jacket-groups.service';
import { ManageRevisionsService } from './admin-pages/new-quotation/manage-revisions/manage-revisions.service';
import { SalesOrderSummaryService } from './admin-pages/new-quotation/summary-sales-order.service';
import { AppRoutes } from './app.route';
import { MainComponent } from './layouts';
import { LayoutsModule } from './layouts/layouts.module';
import { SharedModule } from './shared/shared.module';
import { SflErrorHandler } from './shared/error-handler/sfl-error-handler';
import { SensorService } from './admin-pages/new-quotation/Add Sensors/add-sensors.service';
import { FinalizeService } from './admin-pages/finalize/finalize.service';
import { FormsModule } from '@angular/forms';
import {ManageUnitsService} from './admin-pages/new-quotation/manage-units/manage-units.service';


@NgModule({
  declarations: [MainComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    RouterModule.forRoot(AppRoutes, { useHash: true }),
    Ng2Webstorage.forRoot({ prefix: 'sfl-briskheat', separator: '-' }),
    ReactiveFormsModule,
    HttpClientModule,
    SharedModule,
    LayoutsModule,
    FormsModule
  ],
  exports: [],
  providers: [
    CookieService,
    SalesOrderSummaryService,
    AddApplicationService,
    AddClosureService,
    JacketGroupService,
    ManageRevisionsService,
    SensorService,
    FinalizeService,
    SharedService,
    ManageUnitsService,
    { provide: ErrorHandler, useClass: SflErrorHandler }
  ],
  bootstrap: [MainComponent]
})
export class AppModule {}
