import { Overlay } from '@angular/cdk/overlay';
import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { Revision } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { JacketGroupService } from 'src/app/admin-pages/new-quotation/manage-jacketgroups/manage-jacket-groups.service';
import { Messages, SharedService, SnakbarService } from 'src/app/shared';
import { ViewCcdcDesignComponent } from 'src/app/shared/component/geometry/view-ccdc-design/view-ccdc-design.component';
import { PartNumberComponent } from 'src/app/shared/component/Part Number/part-number.component';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { APIRESULT, FilePath, JacketGroup, JacketListInfo, JacketProjection, JacketRevisionIdDTO } from '../jacket-list/jacket-list.model';
import { JacketListService } from '../jacket-list/jacket-list.service';
import { DesignPlanService } from './design-plan.service';
import { LapCalcComponent } from './lap-calc/lap-calc.component';
import { ViewJacketReferenceComponent } from './view-jacket-reference/view-jacket-reference.component';

@Component({
  selector: 'sfl-design-plan',
  templateUrl: './design-plan.component.html',
  styleUrls: ['./design-plan.component.css']
})
export class DesignPlanComponent implements OnInit {
  listType = Values.ListTypes.geometry;
  jacketID: number;
  quotationId: number;
  jacketListInfo: JacketListInfo;
  newJacketList: JacketProjection[] = [];
  jacketlist: boolean;
  status: string;
  documentslist: boolean;
  jacketGroup: JacketGroup[] = [];
  subscription: Subscription = new Subscription();
  fileName: string;
  showLoader: boolean;
  filePath: FilePath;
  isDesignEng: boolean;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  newJackGroupName: JacketRevisionIdDTO[] = [];
  updateUserInfo: JacketProjection;
  jacketGroupId: number;
  revisionList: Revision[];
  revisionId: number;
  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly matDialog: MatDialog,
    private readonly sharedService: SharedService,
    private overlay: Overlay,
    private readonly snakbarService: SnakbarService,
    private readonly designPlanService: DesignPlanService,
    private readonly jacketListService: JacketListService,
    private readonly location: Location,
    private readonly titleService: Title,
    private readonly jacketGroupService: JacketGroupService
  ) { }

  sizeLG = 90;
  sizeMD = 85;
  sizeSM = 49;
  sizeXS = 100;
  listTypes = Values.ListTypes;

  ngOnInit() {
    this.titleService.setTitle('Design Plan - Design Eng');
    this.filePath = new FilePath();
    this.activatedRoute.queryParams.subscribe(params => {
      this.isDesignEng = true;
      this.jacketID = +params['jacketId'];
      this.quotationId = +params['quotId'];
      this.jacketGroupId = +params['jgId'];
    });
    this.getJacketGroupByRevisionId(this.quotationId);
  }

  goBack() {
    this.location.back();
  }

  onRevisionChanged(revisionId) {
    this.showLoader = true;
    this.subscription.add(
      this.jacketGroupService.getJacketGroupsByRevisionId(revisionId).subscribe(
        (res: JacketGroup[]) => {
          this.jacketGroup = res;
          this.showLoader = false;
        },
        () => (this.showLoader = false)
      )
    );
  }

  getJacketGroupByRevisionId(revisionId) {
    this.jacketListService.getJacketGroupByRevId(revisionId).subscribe((response: JacketRevisionIdDTO[]) => {
      this.newJackGroupName = response;
      this.getJacketListByQuotationId(response[0].revisionId);
      this.onRevisionChanged(response[0].revisionId);
    })
  }

  getJacketListByQuotationId(revisionId) {
    this.subscription.add(
      this.jacketListService.getJacketListDropdownByRevId(revisionId).subscribe(res => {
        if (res) {
          this.newJacketList = res;
          this.fileName = this.jacketListInfo.jacketList.find(e => e.id === this.jacketID).partNumber;
        }
      },
      error => {
        if (error.applicationStatusCode === 1208) {
          this.snakbarService.error(error.message);
        }
      }
     ));
  }



  OnJacketChange(value) {
    this.jacketID = value;
    this.fileName = this.jacketListInfo.jacketList.find(e => e.id === this.jacketID).partNumber;
  }

  openFile(fileName) {
    this.showLoader = true;
    const filePath = this.jacketListInfo.jacketList.find(e => e.partNumber === fileName).partFilePath;
    const revision = this.jacketListInfo.jacketList.find(e => e.partNumber === fileName).designRevision;
    const partNumber = this.jacketListInfo.jacketList.find(e => e.partNumber === fileName).partNumber;
    let selectedPartFilePath = false;
    this.jacketListInfo.jacketList.forEach(e => {
      if (e.partNumber === fileName) {
        if (e.selectedPartFilePath) {
          selectedPartFilePath = true;
        } else {
          selectedPartFilePath = false;
        }
      }
    });
    this.filePath.path = filePath;
    this.filePath.rev = revision;
    this.filePath.partNumber = partNumber;
    if (filePath && revision && partNumber) {
      this.subscription.add(
        this.jacketListService.openFileInSLD(this.filePath, selectedPartFilePath).subscribe(
          (res: APIRESULT) => {
            if (res.success) {
              this.showLoader = false;
            } else {
              this.snakbarService.error(Messages.Title_Block.error_msg);
              this.showLoader = false;
            }
          },
          error => {
            this.showLoader = false;
            this.snakbarService.error(Messages.Solid_Work.not_install);
          }
        )
      );
    } else {
      this.snakbarService.error(Messages.Solid_Work.no_file);
    }
  }

  viewCCDC() {
    const matDataConfig = new MatDialogConfig();
    const scrollStrategy = this.overlay.scrollStrategies.reposition();
    matDataConfig.width = PopupSize.size.popup_lg;
    matDataConfig.data = { jacketGroupId: this.jacketGroupId};
    scrollStrategy
    this.matDialog.open(ViewCcdcDesignComponent, matDataConfig)
  }

  PartNumber() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { quotationId: this.quotationId };
    matDataConfig.width = PopupSize.size.popup_xxlg;
    this.matDialog
      .open(PartNumberComponent, matDataConfig)
      .afterClosed()
      .subscribe(result => {
        this.getJacketGroupByRevisionId(this.quotationId);
      });
  }

  lapCalc() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.width = PopupSize.size.popup_lg;
    this.matDialog
      .open(LapCalcComponent, matDataConfig)
      .afterClosed()
      .subscribe(result => { });
  }

  openJacketlist() {
    this.jacketlist = !this.jacketlist;
    this.documentslist = false;
    if (!this.jacketlist) {
      this.sizeLG = 90;
      this.sizeMD = 85;
      this.sizeSM = 49;
      this.sizeXS = 100;
    } else {
      this.sizeLG = 70;
      this.sizeMD = 65;
      this.sizeSM = 50;
      this.sizeXS = 100;
    }
  }

  openDesignlist() {
    this.documentslist = !this.documentslist;
    this.jacketlist = false;
    if (!this.documentslist) {
      this.sizeLG = 90;
      this.sizeMD = 85;
      this.sizeSM = 49;
      this.sizeXS = 100;
    } else {
      this.sizeLG = 70;
      this.sizeMD = 65;
      this.sizeSM = 50;
      this.sizeXS = 100;
    }
  }

  changeList(listType) {
    this.listType = listType;
  }
}
