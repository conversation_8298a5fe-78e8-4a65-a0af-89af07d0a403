import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {EstEngRelDateMaster, GenericPageable} from '../masterdata-management.model';
import {MatDialog, MatDialogConfig, MatPaginator, MatSort, MatTableDataSource} from '@angular/material';
import {DisplayColumns} from 'src/app/shared/constants/displayColName.constants';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {Subscription} from 'rxjs';
import {MasterdataManagementService} from '../masterdata-management.service';
import {Messages, SnakbarService, SweetAlertService} from 'src/app/shared';
import {PopupSize} from 'src/app/shared/constants/popupsize.constants';
import {
  ManageEstimatedEngReleaseMasterComponent
} from './manage-estimated-eng-release-master/manage-estimated-eng-release-master.component';

@Component({
  selector: 'sfl-est-eng-rel-date-master',
  templateUrl: './estimated-eng-release-master.component.html'
})
export class EstimatedEngReleaseMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'Estimated Engineering Release Date';
  estRelDatePageable: GenericPageable<EstEngRelDateMaster>;
  estRelDateDataSource = new MatTableDataSource<EstEngRelDateMaster>();
  estRelDateColumns = DisplayColumns.Cols.estEngRelDateMasterCols;

  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  showLoader = false;
  numberOfElements: number;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  constructor(
    private masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService
  ) {}

  ngOnInit() {
    this.getEstEngRelMasterData(this.initialPageIndex, this.initialPageSize);
  }

  addEstEngRelDate() {
    this.editEstEngRelDate(new EstEngRelDateMaster());
  }

  getEstEngRelMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getEstEngRelDate(this.filter, pageable).subscribe(
        (res: GenericPageable<EstEngRelDateMaster>) => {
          this.estRelDatePageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createEstEngRelDateTable(this.estRelDatePageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.estRelDateDataSource.data = [];
        }
      )
    );
  }

  createEstEngRelDateTable(serviceRequestList: GenericPageable<EstEngRelDateMaster>) {
    this.estRelDateDataSource.data = serviceRequestList.content;
  }

  getEstEngRelMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getEstEngRelMasterData(this.pageIndex, this.pageSize);
  }

  getEstEngRelMasterSorting(event) {
    this.sortOrder = event.direction;
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getEstEngRelMasterData(this.pageIndex, this.pageSize);
  }

  async deleteEstEngRel(id: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteEstEngRelDate(id).subscribe(
        () => {
          this.snakbarService.success('Estimated Engineering Release Date' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getEstEngRelMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  editEstEngRelDate(engRelDate: EstEngRelDateMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = engRelDate;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-est-eng-rel-master-model';
    const dialogRef = this.matDialog.open(ManageEstimatedEngReleaseMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          engRelDate.id
            ? 'Estimated Engineering Release Date' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : 'Estimated Engineering Release Date' + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getEstEngRelMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
