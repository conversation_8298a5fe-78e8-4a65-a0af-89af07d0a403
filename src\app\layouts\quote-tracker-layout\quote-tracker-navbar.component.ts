import { Component } from '@angular/core';
import { QuoteTrackerLayoutNavbarService } from './quote-tracker-layout-navbar.service';
import { Router } from '@angular/router';

@Component({
  selector: 'sfl-quote-tracker-navbar',
  templateUrl: './quote-tracker-navbar.component.html'
})
export class QuoteTrackerNavbarComponent {
  constructor(public quoteTrackerNavbarService: QuoteTrackerLayoutNavbarService, private router: Router) {}

  addMenuItem(): void {
    this.quoteTrackerNavbarService.add({
      state: 'menu',
      name: 'MENU',
      type: 'sub',
      icon: 'trending_flat',
      children: [
        { state: 'menu', name: 'MENU' },
        { state: 'timeline', name: 'MENU' }
      ]
    });
  }
}
