import {Component, Inject, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Observable, Subscription} from 'rxjs';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material';
import {SnakbarService} from 'src/app/shared';
import {FinalEcrReviewService} from '../final-ecr-review.service';
import {MaterialDTO} from '../final-ecr-review.model';
import {FormControl} from '@angular/forms';
import {map, startWith} from 'rxjs/operators';
import {JacketProductType} from '../../../../design-plan/bom-editor/bom-editor.model';
import {BomEditorService} from '../../../../design-plan/bom-editor/bom-editor.service';

@Component({
  selector: 'sfl-final-ecr-add-material',
  templateUrl: './final-ecr-add-material.component.html'
})
export class FinalEcrAddMaterialComponent implements OnInit, OnDestroy {
  subscription: Subscription = new Subscription();
  jacketId: number;
  groupingId: number;
  selection: string;
  type: string;
  masterDataList: MaterialDTO[];
  masterDataDetails: MaterialDTO;
  materialObservable$: Observable<MaterialDTO[]>;
  materialControl = new FormControl();
  lastFilter = '';
  productType: string;

  constructor(
    public dialogRef: MatDialogRef<FinalEcrAddMaterialComponent>,
    private snakbarService: SnakbarService,
    private finalReviewService: FinalEcrReviewService,
    private bomeditorService: BomEditorService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketId = data.jacketId;
    this.groupingId = data.groupingId;
    this.type = data.type;
  }

  async ngOnInit() {
    this.masterDataDetails = new MaterialDTO();
    this.getProductType();
  }

  filter(filter: string): MaterialDTO[] {
    this.lastFilter = filter;
    if (filter) {
      return this.masterDataList.filter(option => {
        if (option.partNumber != null && option.description != null) {
          return (
            option.partNumber.toLowerCase().indexOf(filter.toLowerCase()) >= 0 ||
            option.description.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        } else if (option.partNumber !== null && option.description == null) {
          return (
            option.partNumber.toLowerCase().indexOf(filter.toLowerCase()) >= 0
          );
        }
      });
    } else {
      return this.masterDataList ? this.masterDataList.slice() : [];
    }
  }

  // used to get the product type of a jacket by jacket id
  getProductType() {
    this.subscription.add(
      this.bomeditorService.getProductTypeByJacketId(this.jacketId).subscribe((typeObject: JacketProductType) => {
        this.productType = typeObject.productType;
        this.getAllMaterials();
      })
    );
  }

  getAllMaterials() {
    this.subscription.add(
      this.finalReviewService.getAllMaterials(this.productType, this.type).subscribe((res: MaterialDTO[]) => {
        if (res) {
          this.masterDataList = res;
          this.materialObservable$ = this.materialControl.valueChanges.pipe(
            startWith<string | MaterialDTO[]>(''),
            map(value => (typeof value === 'string' ? value : this.lastFilter)),
            map(filter => this.filter(filter))
          );
        }
      })
    );
  }

  displayFn(value: MaterialDTO[] | string): string | undefined {
    let displayValue: string;
    if (Array.isArray(value)) {
      value.forEach((material, index) => {
        if (index === 0) {
          displayValue = material.partNumber + ', ' + material.description;
        } else {
          displayValue += ', ' + material.partNumber + ', ' + material.description;
        }
      });
    } else {
      displayValue = value;
    }
    return displayValue;
  }

  onSelectionChanges(material) {
    if (material !== 'other') {
      this.selection = '';
      this.masterDataDetails = material;
      this.materialControl.setValue(this.masterDataDetails.partNumber + ', ' + this.masterDataDetails.description);
    } else {
      this.selection = 'other';
      this.masterDataDetails = {};
    }
  }

  saveData() {
    this.masterDataDetails.relOp = this.masterDataDetails.relOp;
    this.dialogRef.close(this.masterDataDetails);
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
