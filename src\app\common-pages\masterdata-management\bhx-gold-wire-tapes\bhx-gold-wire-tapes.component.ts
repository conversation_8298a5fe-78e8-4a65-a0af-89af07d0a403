import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { BHXGoldWireTapesMaster, BHXGoldWireTapesFilter, GenericPageable } from '../masterdata-management.model';
import { Subscription } from 'rxjs';
import { MatPaginator, MatSort, MatTableDataSource, MatDialog, MatDialogConfig } from '@angular/material';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageBhxGoldWireTapeComponent } from './manage-bhx-gold-wire-tape/manage-bhx-gold-wire-tape.component';
import { Values } from 'src/app/shared/constants/values.constants';

@Component({
  selector: 'sfl-bhx-gold-wire-tapes',
  templateUrl: './bhx-gold-wire-tapes.component.html'
})
export class BhxGoldWireTapesComponent implements OnInit, OnDestroy {
  pageTitle = 'Gold Wire Tapes Master';
  goldWireTapes: BHXGoldWireTapesMaster;
  goldWireTapesPageable: GenericPageable<BHXGoldWireTapesMaster>;
  goldWireTapesDataSource = new MatTableDataSource<BHXGoldWireTapesMaster>();
  goldWireTapesColumns = DisplayColumns.Cols.BHXGoldWireTapesMasterCols;

  dataSource = new MatTableDataSource<BHXGoldWireTapesMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;
  showLoader = false;
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  goldWireTapesFilter: BHXGoldWireTapesFilter = new BHXGoldWireTapesFilter();

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  filterFieldAlloyName = Values.FilterFields.alloyName;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService
  ) {}

  ngOnInit() {
    this.getBHXGoldWireTapesMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new gold wire tapes
  addGoldWireTapes() {
    this.editGoldWireTapes(new BHXGoldWireTapesMaster());
  }

  // used to filter gold wire tapes listing
  async addFilter() {
    this.filter =
      this.goldWireTapesFilter.alloyName === '' ? [] : [{ key: this.filterFieldAlloyName, value: this.goldWireTapesFilter.alloyName }];
    this.getBHXGoldWireTapesMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter for gold wire tapes listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldAlloyName,
        value: fieldToClear === this.filterFieldAlloyName ? (this.goldWireTapesFilter.alloyName = '') : this.goldWireTapesFilter.alloyName
      }
    ];
    this.getBHXGoldWireTapesMasterData(this.initialPageIndex, this.pageSize);
  }

  getBHXGoldWireTapesMasterData(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.showLoader = true;
    this.subscription.add(
      this.masterDataService.getGoldWireTapesMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<BHXGoldWireTapesMaster>) => {
          this.goldWireTapesPageable = res;
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createGoldWireTapesTable(this.goldWireTapesPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
        }
      )
    );
  }

  createGoldWireTapesTable(serviceRequestList: GenericPageable<BHXGoldWireTapesMaster>) {
    this.goldWireTapesDataSource.data = serviceRequestList.content;
  }

  getGoldWireTapesPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getBHXGoldWireTapesMasterData(this.pageIndex, this.pageSize);
  }

  getGoldWireTapesSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getBHXGoldWireTapesMasterData(this.pageIndex, this.pageSize);
  }

  editGoldWireTapes(goldWireTape: BHXGoldWireTapesMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = goldWireTape;
    matDataConfig.width = PopupSize.size.popup_md;
    matDataConfig.panelClass = 'sfl-bhx-gold-wire-tapes-master-model';
    const dialogRef = this.matDialog.open(ManageBhxGoldWireTapeComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          goldWireTape.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getBHXGoldWireTapesMasterData(this.pageIndex, this.pageSize);
      }
    });
  }
  async deleteGoldWireTapes(goldWireTapeId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteGoldWireTapes(goldWireTapeId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getBHXGoldWireTapesMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
