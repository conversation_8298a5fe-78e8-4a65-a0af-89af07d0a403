import {Observable, Subscription} from 'rxjs';
import {TopbarService} from './../topbar/topbar.service';
import {DesignQuotationList} from './../../design-pages/dashboard/dashboard.model';
import {DashboardService} from './../../admin-pages/dashboard/dashboard.service';
import {QuotationSearchFilter, SalesAssociate, Status} from './../../admin-pages/dashboard/dashboard.model';
import {Component, EventEmitter, OnDestroy, OnInit, Output} from '@angular/core';
import {Router} from '@angular/router';
import {FormControl} from '@angular/forms';

import * as screenfull from 'screenfull';

import {SharedService} from '../../shared';
import {Route} from '../../shared/constants/router.constants';
import {QuotationList} from '../topbar/topbar.model';

@Component({
  selector: 'sfl-design-topbar',
  templateUrl: './design-topbar.component.html'
})
export class DesignTopbarComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
  @Output() toggleSidenav = new EventEmitter<void>();

  myControl = new FormControl();
  filteredOptions: Observable<Quotations[]>;
  filter: boolean;
  isDesignAdmin: boolean;
  isAppAdmin: boolean;
  quotation: string;
  status: Status[];
  salesassociate: SalesAssociate[];
  quotationSearchFilter = new QuotationSearchFilter();
  quotationsList: DesignQuotationList[];
  quotationSearchResultList: QuotationList[];
  isFullScreen: boolean;
  quotationObservable$ = new Observable<QuotationSearchFilter[]>();
  userRole: string;
  subscription = new Subscription();

  constructor(
    private router: Router,
    private sharedService: SharedService,
    private dashboardService: DashboardService,
    private topbarService: TopbarService
  ) {}

  async ngOnInit() {
    this.filter = false;
    this.status = await this.sharedService.getAllStatus();
    this.userRole = this.sharedService.getRole();
    this.getSaleAssociate();
    this.filter = false;
  }

  getSaleAssociate() {
    this.subscription.add(
      this.dashboardService.getSalesAssociate(true).subscribe((res: Array<SalesAssociate>) => {
        this.salesassociate = res;
      })
    );
  }

  showFilter() {
    this.filter = !this.filter;
  }

  quotationFilter(event) {
    if (this.quotation.length >= 3) {
      this.quotationObservable$ = this.topbarService.getQuotationDesignSide(event);
    }
  }

  displayFn(company?: Quotations): string | undefined {
    return company ? company.name : undefined;
  }

  gotolist() {
    this.router.navigate([this.sharedService.getRole() + '/jacket-list']);
  }

  fullScreenToggle(): void {
    if (screenfull.enabled) {
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    }
  }

  switchToAppAdmin() {
    this.router.navigate([Route.APP_ENGG.dashboard]);
  }

  switchToSuperAdmin() {
    this.router.navigate([Route.SUPER_ADMIN.user_list]);
  }

  logout() {
    this.sharedService.logout(null);
  }

  gotDashboard() {
    this.router.navigate([Route.DESIGN_ENGG.dashboard]);
  }

  search() {
    this.sharedService.setDesignQuotationSearchObject(this.quotationSearchFilter);
    this.router.navigate([Route.DESIGN_ENGG.dashboard]);
    this.filter = !this.filter;
  }

  reset() {
    this.quotationSearchFilter = new QuotationSearchFilter();
    this.sharedService.setDesignQuotationSearchObject(this.quotationSearchFilter);
    this.router.navigate([Route.DESIGN_ENGG.dashboard]);
    this.filter = !this.filter;
  }

  selectQuotation(quotOption) {
    this.router.navigate(['/design-eng/jacket-list'], { queryParams: { quotId: quotOption.id } });
  }
  getQuotNumberFn(quot) {
    return quot ? quot.quotationNumber : quot;
  }

  switchToMasterdataManagement() {
    this.router.navigate([Route.MASTERDATA.management]);
  }

  // used to switch to the Quote tracker screen
  switchToQuoteTracker() {
    this.router.navigate([Route.QUOTE_TRACKER.dashboard]);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}

export interface Quotations {
  name: string;
  value: string;
}
