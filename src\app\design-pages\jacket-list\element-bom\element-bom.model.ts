export class ElementBomModel {
  constructor(
    public comments?: string,
    public customerSpecificLabel?: boolean,
    public efNoConcerns?: boolean,
    public elementBOMDate?: any,
    public elementDetails?: string,
    public id?: number,
    public jacketId?: number,
    public labelEntered?: boolean,
    public laborHours?: boolean,
    public materialBill?: boolean,
    public checked?: boolean,
    public name?: string,
    public newLabelCreated?: boolean,
    public operationBill?: boolean,
    public tempRatingNoConcerns?: boolean,
    public tsNotInterfere?: boolean,
    public wIn2NoConcerns?: boolean,
    public wInNoConcerns?: boolean
  ) {}
}

export class ChecklistElementBomModelDTO {
  public elementBOMDTOs: ElementBomModel[] = [];
  public quotationID: number;
}
