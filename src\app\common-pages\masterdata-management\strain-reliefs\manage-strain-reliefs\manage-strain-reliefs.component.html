<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #strainReliefForm="ngForm" (ngSubmit)="updateStrainReliefs()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Strain Relief Name" [(ngModel)]="strainReliefs.name" name="name" #nameInput="ngModel" required />
        </mat-form-field>
        <div *ngIf="nameInput.touched && nameInput.invalid">
          <small class="mat-text-warn" *ngIf="nameInput?.errors?.required">Strain relief name is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Part Number" [(ngModel)]="strainReliefs.partNumber" name="partNumber" #partNumberInput="ngModel" />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Cost" [(ngModel)]="strainReliefs.cost" name="cost" #costInput="ngModel" sflIsDecimal />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="20" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothCE" color="warn" [(ngModel)]="strainReliefs.clothCE">Cloth CE</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="21" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothUL" color="warn" [(ngModel)]="strainReliefs.clothUL">Cloth UL</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="22" fxFlex.gt-md="21" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="strainReliefs.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!strainReliefForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
