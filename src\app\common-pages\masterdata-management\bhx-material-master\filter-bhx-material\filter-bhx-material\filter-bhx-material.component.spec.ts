import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FilterBhxMaterialComponent } from './filter-bhx-material.component';

describe('FilterBhxMaterialComponent', () => {
  let component: FilterBhxMaterialComponent;
  let fixture: ComponentFixture<FilterBhxMaterialComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FilterBhxMaterialComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FilterBhxMaterialComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
