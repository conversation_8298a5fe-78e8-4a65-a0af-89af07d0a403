import { <PERSON>mpo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { BHXMaterialMaster, BHXMaterialFilter, GenericPageable, SensorConnectorsAndTypesMaster, BhxMatetrialFilter } from '../masterdata-management.model';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogConfig } from '@angular/material';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { Subscription } from 'rxjs';
import { MasterdataManagementService } from '../masterdata-management.service';
import { SweetAlertService, SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ManageBhxMaterialComponent } from './manage-bhx-material/manage-bhx-material.component';
import { Values } from 'src/app/shared/constants/values.constants';
import { FilterBhxMaterialComponent } from './filter-bhx-material/filter-bhx-material/filter-bhx-material.component';
import { DuplicateBhxMaterialMasterComponent } from './manage-bhx-material/duplicate-bhx-material-master/duplicate-bhx-material-master.component';

@Component({
  selector: 'sfl-bhx-material-master',
  templateUrl: './bhx-material-master.component.html',
  styleUrls: ['./bhx-material-master.component.css']
})
export class BhxMaterialMasterComponent implements OnInit, OnDestroy {
  pageTitle = 'BHX Material Master';
  bhxMaterial: BHXMaterialMaster;
  bhxMaterialPageable: GenericPageable<BHXMaterialMaster>;
  bhxMaterialMasterDataSource = new MatTableDataSource<BHXMaterialMaster>();
  bhxMaterialMasterColumns = DisplayColumns.Cols.BHXMaterialMasterCols;
  groupings = Values.GroupingsBHXMaterial;

  bhxMaterialDataSource = new MatTableDataSource<BHXMaterialMaster>();
  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  length: number;
  pageIndex = Variable.activePage;
  pageSize = Variable.tenItemsPerPage;
  sortOrder = Variable.defaultSortOrderDescending;

  subscription = new Subscription();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('BHXMaterialForm')
  table: BhxMatetrialFilter;
  bhxMaterialMaster = new BhxMatetrialFilter();
  showLoader = false;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  bhxMaterialFilter: BHXMaterialFilter = new BHXMaterialFilter();
  sortField = Variable.defaultSortById;
  ascSort = Variable.sortAscending;
  filter = [];
  numberOfElements: number;
  phaseTypes = Values.PhaseTypeConst;
  filterFieldPartNumber = Values.FilterFields.partNumber;
  checkBoxYesLabel = Values.CheckboxLabels.YES;
  checkBoxNoLabel = Values.CheckboxLabels.NO;
  checkBoxNullLabel = Values.CheckboxLabels.NULL;

  constructor(
    private readonly masterDataService: MasterdataManagementService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly snakbarService: SnakbarService,
    private readonly matDialog: MatDialog
  ) {}

  ngOnInit() {
    this.getBHXMaterialMasterData(this.initialPageIndex, this.initialPageSize);
  }

  // used to add new BHX Material
  addBHXMaterial() {
    this.editBHXMaterialMaster(new BHXMaterialMaster());
  }

  duplicateBHXMaterial() {
    this.duplicateBHXMaterialMaster
  }

  filterBHXMaterial() {
    this.filterBHXMaterialMaster(new BHXMaterialMaster());
  }

  // used to add filter to new BHX Material listing
  async addFilter() {
    this.filter =
      this.bhxMaterialFilter.partNumber === '' ? [] : [{ key: this.filterFieldPartNumber, value: this.bhxMaterialFilter.partNumber }];
    this.getBHXMaterialMasterData(this.initialPageIndex, this.pageSize);
  }
  // used to clear filter for new BHX Material listing
  clearFilter(fieldToClear: string) {
    this.filter = [
      {
        key: this.filterFieldPartNumber,
        value: fieldToClear === this.filterFieldPartNumber ? (this.bhxMaterialFilter.partNumber = '') : this.bhxMaterialFilter.partNumber
      }
    ];
    this.getBHXMaterialMasterData(this.initialPageIndex, this.pageSize);
  }

  getBHXMaterialMasterData(pageIndex, pageSize) {
    this.showLoader = true;
    const pageable = { page: pageIndex, size: pageSize, asc: this.ascSort, orderBy: this.sortField };
    this.subscription.add(
      this.masterDataService.getBHXMaterialsMaster(this.filter, pageable).subscribe(
        (res: GenericPageable<BHXMaterialMaster>) => {
          this.bhxMaterialPageable = res;
          this.formulaConverter();
          this.length = res.totalElements;
          this.pageIndex = res.number;
          this.numberOfElements = res.numberOfElements;
          this.createBHXMaterialTable(this.bhxMaterialPageable);
          this.showLoader = false;
        },
        error => {
          this.showLoader = false;
          this.bhxMaterialMasterDataSource.data = [];
        }
      )
    );
  }

  resetBHXMaterial() {
    this.filter = [];
    this.getBHXMaterialMasterData(this.initialPageIndex, this.initialPageSize);
    this.snakbarService.success(Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Reset_success);
  }

  formulaConverter() {
    return new Promise<void>(resolve => {
      this.bhxMaterialPageable.content.forEach(async material => {
        if (material.formula) {
          material.formula = this.masterDataService.equationFormatterFromConstToFullName(material.formula);
        }
        resolve();
      });
    });
  }

  createBHXMaterialTable(serviceRequestList: GenericPageable<BHXMaterialMaster>) {
    this.bhxMaterialMasterDataSource.data = serviceRequestList.content;
    this.bhxMaterialMasterDataSource.data.forEach(materials => {
      if (materials.phase) {
        const phaseValues = materials.phase.split(',');
        const arrayOfPhaseValues = [];
        phaseValues.forEach(phaseValue => {
          if(phaseValue !== "DUAL_PHASE") {
          const phase = this.phaseTypes.find(e => e.id === phaseValue).value;
          if (phase) {
            arrayOfPhaseValues.push(phase);
          }
        }
        });
        materials.phaseValue = arrayOfPhaseValues.join(',');
      }
    });
  }

  getBHXMaterialMasterPagination(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.getBHXMaterialMasterData(this.pageIndex, this.pageSize);
  }

  getBHXMaterialMasterSorting(event) {
    this.pageIndex = this.initialPageIndex;
    event.direction === Variable.defaultSortOrder ? (this.ascSort = true) : (this.ascSort = false);
    this.sortField = event.active;
    this.getBHXMaterialMasterData(this.pageIndex, this.pageSize);
  }

  editBHXMaterialMaster(bhxMaterial: BHXMaterialMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = bhxMaterial;
    matDataConfig.width = PopupSize.size.popup_xxlg;
    matDataConfig.panelClass = 'sfl-bhx-material-model';
    const dialogRef = this.matDialog.open(ManageBhxMaterialComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
          bhxMaterial.id
            ? this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Updated_Success
            : this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Added_Success
        );
        this.getBHXMaterialMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  duplicateBHXMaterialMaster(bhxMaterial: BHXMaterialMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = bhxMaterial;
    matDataConfig.width = PopupSize.size.popup_xxlg;
    matDataConfig.panelClass = 'sfl-bhx-material-model';
    const dialogRef = this.matDialog.open(DuplicateBhxMaterialMasterComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.snakbarService.success(
         this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Duplicate_Success
        );
        this.getBHXMaterialMasterData(this.pageIndex, this.pageSize);
      }
    });
  }

  filterBHXMaterialMaster(bhxMaterial: BHXMaterialMaster) {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = bhxMaterial;
    matDataConfig.width = PopupSize.size.popup_xxlg;
    matDataConfig.panelClass = 'sfl-app-reset-bhx-material';
    const dialogRef = this.matDialog.open(FilterBhxMaterialComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(filterArr => {
      if (filterArr) {
        this.filter = filterArr;
        this.getBHXMaterialMasterData(this.initialPageIndex, this.pageSize);
        }
        });
  }

  async deleteBHXMaterialMaster(bhxMaterialId: number) {
    if (await this.sweetAlertService.deleteAlert()) {
      this.showLoader = true;
      this.masterDataService.deleteBHXMaterial(bhxMaterialId).subscribe(
        () => {
          this.snakbarService.success(this.pageTitle + Messages.MASTER_DATA_MANAGEMENT_MESSAGES.Deleted_Success);
          this.getBHXMaterialMasterData(this.numberOfElements === 1 ? this.initialPageIndex : this.pageIndex, this.pageSize);
          this.showLoader = false;
        },
        () => {
          this.showLoader = false;
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
