import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material';

@Component({
  selector: 'sfl-update-plugging-info-warning',
  templateUrl: './update-plugging-info-warning.component.html'
})
export class UpdatePluggingInfoWarningComponent {
  title = 'Updating existing Element Type';
  constructor(public updatePluggingWarningDialogRef: MatDialogRef<UpdatePluggingInfoWarningComponent>) {}

  openPlugginInfoScreen() {
    this.updatePluggingWarningDialogRef.close(true);
  }

  closeDialog() {
    this.updatePluggingWarningDialogRef.close(false);
  }
}
