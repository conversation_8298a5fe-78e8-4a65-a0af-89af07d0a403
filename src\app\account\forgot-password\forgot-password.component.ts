import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccountService } from '../account.service';
import { Messages, SnakbarService } from '../../shared';
import { Title } from '@angular/platform-browser';

@Component({
    selector: 'sfl-forgot-password',
    templateUrl: './forgot-password.component.html'
})
export class ForgotPasswordComponent implements OnInit {
    email: string;
    isError = false;
    errorEmailNotExists = false;
    success = false;
    message = '';
    constructor(
        private accountService: AccountService,
        private snakBarService: SnakbarService,
        private titleService: Title
    ) { }

    ngOnInit() {
        this.titleService.setTitle('Forgot Password - BriskHeat');
    }

    sendForgotPasswordEmail(forgotpasswordForm: NgForm) {
        this.isError = false;
        this.errorEmailNotExists = false;
        this.accountService.sendForgotPasswordEmail(this.email).subscribe(
            (response) => {
                this.success = true;
                this.snakBarService.success(Messages.Login.fogot_Password_success);
                forgotpasswordForm.reset();
            },
            (error) => {
                this.success = false;
                    this.errorEmailNotExists = true;
                    this.message = Messages.Login.email_not_registered;
                    this.snakBarService.error(Messages.Login.email_not_exists);
            }
        );
    }
}
