import { Component, OnInit, On<PERSON><PERSON><PERSON>, Inject, ViewChild, QueryList } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatDialogRef, MAT_DIALOG_DATA, MatTableDataSource, MatSort, MatPaginator, MatDialogConfig, MatDialog } from '@angular/material';
import { GoldReportQuotations, GoldReport, GoldReportQuotationPageable, GoldReportPageable, AssemblyResponseDTO, GoldReportList } from './gold-report.model';
import { GoldReportService } from './gold-report.service';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { DisplayColumns } from 'src/app/shared/constants/displayColName.constants';
import { map } from 'rxjs/operators';
import { SnakbarService, Messages } from 'src/app/shared';
import { PopupSize } from 'src/app/shared/constants/popupsize.constants';
import { ResetToolTipComponent } from './reset-tool-tip/reset-tool-tip.component';

@Component({
  selector: 'sfl-gold-report',
  templateUrl: './gold-report.component.html'
})
export class GoldReportComponent implements OnInit, OnDestroy {

  showLoader = false;
  readingFromAssembly = false;
  readingAssemblyMessage = Variable.readingAssemblyMessage;
  revisionId: string;
  subscription = new Subscription();
  goldReportList: GoldReportQuotations;
  goldReport: GoldReport;
  goldReportQuotationDataSource = new MatTableDataSource<GoldReportQuotations>();
  goldReportDataSource = new MatTableDataSource<GoldReportList>();
  soNumber: string;
  selectedSoNumber: string;
  soId: number;

  goldReportQuotationPageable: GoldReportQuotationPageable = new GoldReportQuotationPageable();
  goldReportPageable: GoldReportPageable = new GoldReportPageable();
  assemblyResponseDTO: AssemblyResponseDTO = new AssemblyResponseDTO();
  assemblyResponse: AssemblyResponseDTO = new AssemblyResponseDTO();

  goldReportQuotationsColumns = DisplayColumns.Cols.GoldReportQuotationColumn;
  goldReportColumns = DisplayColumns.Cols.GoldReportColumn;

  pageSizeOptions = Variable.pageSizeOptions;
  initialPageIndex = Variable.activePage;
  initialPageSize = Variable.tenItemsPerPage;

  pageIndexGoldReport = Variable.activePage;
  pageSizeGoldReport = Variable.tenItemsPerPage;
  pageIndexGoldReportQuotation = Variable.activePage;
  pageSizeGoldReportQuotation = Variable.tenItemsPerPage;

  sortOrderGoldReport = Variable.defaultSortOrderDescending;
  sortOrderGoldReportQuotation = Variable.defaultSortOrderDescending;

  lengthGoldReportQuotation: number;
  lengthGoldReport: number;
  isNoGoldReportQuotations = this.goldReportQuotationDataSource.connect().pipe(map(data => data.length === 0));
  isNoGoldReport = this.goldReportDataSource.connect().pipe(map(data => data.length === 0));

  @ViewChild(MatPaginator) paginator = new QueryList<MatPaginator>();
  @ViewChild(MatSort) sort = new QueryList<MatSort>();

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter;
  isError = false;
  isDatePresent: boolean;
  rootDirectory = Variable.rootPAthForAssemblyFile;

  constructor(public dialogRef: MatDialogRef<GoldReportComponent>, private goldReportService: GoldReportService, private snakbarService: SnakbarService, private matDialog: MatDialog, @Inject(MAT_DIALOG_DATA) data) {
    this.soNumber = data.soNumber;
  }

  ngOnInit() {
    this.getGoldReportQuotations(this.initialPageIndex, this.initialPageSize);
  }

  getGoldReportQuotations(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, sort: '', direction: this.sortOrderGoldReportQuotation };
    this.showLoader = true;
    this.subscription.add(this.goldReportService.getGoldReportQuotations(this.soNumber, pageable).subscribe(async (res: GoldReportQuotationPageable) => {
      if (res.content.length) {
        this.goldReportQuotationPageable = res;
        this.selectedSoNumber = this.soNumber;
        const retrieveSoId = this.goldReportQuotationPageable.content.filter(ele => (ele.soNumber === this.selectedSoNumber));
        if (retrieveSoId.length > 0) {
          this.soId = retrieveSoId[0].id;
        }
        const row: any = this.goldReportQuotationPageable.content[0];
        this.selectedRow = row.soNumber;
        this.lengthGoldReportQuotation = res.totalElements;
        this.pageIndexGoldReportQuotation = res.number;
        this.createQuotationTable(this.goldReportQuotationPageable);
        if (this.soId !== undefined) {
          // check if SO number is complete and gold report is not yet generated for this so number
          if (this.selectedSoNumber === this.soNumber && this.goldReportQuotationPageable.content[0].reportDate === null) {
            // call to our .net service for assembly file reading
            const data = {soNumber: this.soNumber, assemblyFileLocation: this.rootDirectory};
            await this.getAssemblyFileContent(data);
            this.isDatePresent = false;
            this.assemblyResponse = this.assemblyResponseDTO;
            if (this.assemblyResponse.data !== null && this.assemblyResponse.success === true) {
              this.generateGoldReport(this.initialPageIndex, this.initialPageSize, this.soId);
            } else if (this.assemblyResponse.success === false) {
              this.snakbarService.error(this.assemblyResponse.message);
              this.showLoader = false;
            }
          } else if (this.selectedSoNumber === this.soNumber && this.goldReportQuotationPageable.content[0].reportDate !== null) {
            // we alredy have generated gold report for this so number
            this.isDatePresent = true;
            this.assemblyResponse.data = [];
            this.getGeneratedGoldReport(this.initialPageIndex, this.initialPageSize, this.soId);
          }
        } else {
          this.snakbarService.error(Messages.goldReport.quotation_incomplete + this.soNumber);
          this.selectedRow = null;
          this.showLoader = false;
        }
      } else {
        this.goldReportQuotationDataSource.data = [];
        this.showLoader = false;
      }
    }, error => {
      this.goldReportQuotationDataSource.data = [];
      this.showLoader = false;
    }));
  }

  getGoldReportQuotationsPageable(pageIndex, pageSize) {
    const pageable = { page: pageIndex, size: pageSize, sort: '', direction: this.sortOrderGoldReportQuotation };
    this.showLoader = true;
    this.subscription.add(this.goldReportService.getGoldReportQuotations(this.soNumber, pageable).subscribe((res: GoldReportQuotationPageable) => {
      if (res) {
        this.goldReportQuotationPageable = res;
        this.lengthGoldReportQuotation = res.totalElements;
        this.pageIndexGoldReportQuotation = res.number;
        this.createQuotationTable(this.goldReportQuotationPageable);
        this.showLoader = false;
      }
    }, error => {
      this.goldReportQuotationDataSource.data = [];
      this.showLoader = false;
    }));
  }
  // After we have asembly file content we can start generating gold report
  generateGoldReport(pageIndex, pageSize, soId) {
    const pageable = { page: pageIndex, size: pageSize, sort: '', direction: this.sortOrderGoldReport };
    this.showLoader = true;

    this.subscription.add(this.goldReportService.generateGoldReport(this.assemblyResponse.data,  this.isDatePresent, soId, pageable).subscribe((res: GoldReportPageable) => {
      if (res) {
        this.goldReportPageable = res;
        // setting the reportDate for currently selected quotation
        const index = this.goldReportQuotationDataSource.data.findIndex(quotes => quotes.soNumber === this.soNumber);
        this.goldReportQuotationDataSource.data[index].reportDate = this.goldReportPageable.reportDate;

        this.lengthGoldReport = res.soGoldReportList.totalElements;
        this.pageIndexGoldReport = res.soGoldReportList.number;
        this.createGoldReportTable(this.goldReportPageable.soGoldReportList);
        this.showLoader = false;
      } else {
        this.goldReportDataSource.data = [];
        this.showLoader = false;
      }
    }, error => {
      this.goldReportDataSource.data = [];
      this.showLoader = false;
    }));
  }

  // @Param:: data { soNumber, assemblyFileLocation} call .Net service
  getAssemblyFileContent(data) {
    this.showLoader = true;
    this.readingFromAssembly = true;
    return new Promise((resolve, reject) => {
      this.subscription.add(this.goldReportService.readAssemblyForSONumber(data).subscribe((res: AssemblyResponseDTO) => {
        if (res) {
          this.assemblyResponseDTO = res;
          resolve();
          this.showLoader = false;
          this.readingFromAssembly = false;
        }
      }, (error) => {
        this.showLoader = false;
        this.readingFromAssembly = false;
        this.snakbarService.error(Messages.Solid_Work.not_install);
      }));
    });
  }
  // if we have already generated gold report
  getGeneratedGoldReport(pageIndex, pageSize, soId) {
    const pageable = { page: pageIndex, size: pageSize, sort: '', direction: this.sortOrderGoldReport };
    this.showLoader = true;
    this.isDatePresent = true;
    this.subscription.add(this.goldReportService.getGeneratedGoldReport(this.assemblyResponse.data, soId, this.isDatePresent, pageable).subscribe((res: GoldReportPageable) => {
      if (res) {
        this.goldReportPageable = res;
        this.lengthGoldReport = res.soGoldReportList.totalElements;
        this.pageIndexGoldReport = res.soGoldReportList.number;
        this.createGoldReportTable(this.goldReportPageable.soGoldReportList);
        this.showLoader = false;
      } else {
        this.goldReportDataSource.data = [];
        this.showLoader = false;
      }
    }, error => {
      this.goldReportDataSource.data = [];
      this.showLoader = false;
    }));
  }

  // when user clicks on available completed list of quotations
  async openGoldReport(quotation) {
    this.showLoader = true;
    this.selectedRow = quotation.soNumber;
    this.soNumber = quotation.soNumber;
    this.soId = quotation.id;
    if (quotation.reportDate === null) {
      // if the current quot is complete make a call to our .net service for assembly file reading
      const data = {soNumber: quotation.soNumber, assemblyFileLocation: this.rootDirectory};
      await this.getAssemblyFileContent(data);
      this.assemblyResponse = this.assemblyResponseDTO;
      this.isDatePresent = false;
      if (this.assemblyResponse.data !== null && this.assemblyResponse.success === true) {
        this.generateGoldReport(this.initialPageIndex, this.pageSizeGoldReport, this.soId);
      } else if (this.assemblyResponse.success === false) {
        this.showLoader = false;
        this.snakbarService.error(this.assemblyResponse.message);
        this.goldReportDataSource.data = [];
      }
    } else if (quotation.reportDate !== null) {
      this.isDatePresent = true;
      this.assemblyResponse.data = [];
      this.getGeneratedGoldReport(this.initialPageIndex, this.pageSizeGoldReport, this.soId);
    }
  }

  async resetGoldReport(selectedSoNumber) {
    const data = {soNumber: selectedSoNumber, assemblyFileLocation: this.rootDirectory};
      await this.getAssemblyFileContent(data);
      this.assemblyResponse = this.assemblyResponseDTO;
      this.isDatePresent = false;
      if (this.assemblyResponse.data !== null && this.assemblyResponse.success === true) {
        this.reGenerateGoldReport(this.initialPageIndex, this.pageSizeGoldReport, this.soId);
      } else if (this.assemblyResponse.success === false) {
        this.snakbarService.error(this.assemblyResponse.message);
        this.goldReportDataSource.data = [];
      }
  }

  reGenerateGoldReport(pageIndex, pageSize, soId) {
    const pageable = { page: pageIndex, size: pageSize, sort: '', direction: this.sortOrderGoldReportQuotation };
    this.showLoader = true;

    this.subscription.add(this.goldReportService.resetGoldReport(soId, this.assemblyResponse.data, pageable).subscribe((res: GoldReportPageable) => {
      if (res) {
        this.goldReportPageable = res;
        this.lengthGoldReport = res.soGoldReportList.totalElements;
        this.pageIndexGoldReport = res.soGoldReportList.number;
        this.createGoldReportTable(this.goldReportPageable.soGoldReportList);
        this.showLoader = false;
      } else {
        this.goldReportDataSource.data = [];
        this.showLoader = false;
      }
    }, error => {
      this.goldReportDataSource.data = [];
      this.showLoader = false;
    }));
  }

  createQuotationTable(serviceRequestList: GoldReportQuotationPageable) {
    this.goldReportQuotationDataSource.data = serviceRequestList.content;
  }

  createGoldReportTable(serviceRequestList: GoldReport) {
    this.goldReportDataSource.data = serviceRequestList.content;
  }

  downloadGoldReportExcel(soId) {
    this.showLoader = true;
    this.subscription.add(this.goldReportService.downloadGRExcel(soId).subscribe((success) => {
      this.downloadExcel(success, 'GoldReport-Sheet-' + this.soNumber + '.xlsx');
      this.showLoader = false;
      this.snakbarService.success('GoldReport-Sheet-' + this.soNumber + '.xlsx download complete.');
    }));
  }

  downloadExcel(response, sheetName: string) {
    const dwldLink = document.createElement('a');
    const url = URL.createObjectURL(response);
    dwldLink.setAttribute('href', url);
    dwldLink.setAttribute('target', '_blank');
    dwldLink.setAttribute('download', sheetName);
    dwldLink.style.visibility = 'hidden';
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  getGoldReportQuotationPagination(event) {
    this.pageIndexGoldReportQuotation = event.pageIndex;
    this.pageSizeGoldReportQuotation = event.pageSize;
    this.getGoldReportQuotationsPageable(this.pageIndexGoldReportQuotation, this.pageSizeGoldReportQuotation);
  }

  getGoldReportQuotationSorting(event) {
    this.sortOrderGoldReportQuotation = event.direction;
    this.pageIndexGoldReportQuotation = this.initialPageIndex;
  }

  getGoldReportPagination(event) {
    this.pageIndexGoldReport = event.pageIndex;
    this.pageSizeGoldReport = event.pageSize;
    this.getGeneratedGoldReport(this.pageIndexGoldReport, this.pageSizeGoldReport, this.soId);
  }

  getGoldReportSorting(event) {
    this.sortOrderGoldReport = event.direction;
    this.pageIndexGoldReport = this.initialPageIndex;
  }

  selectedRow(row) {
    this.selectedRow = row.soNumber;
  }

  closeGoldReport() {
    this.goldReportDataSource = new MatTableDataSource<GoldReportList>();
    this.selectedRow = null;
  }

  closeDialog() {
    this.dialogRef.close();
  }

  resetToolTip() {
    const matDataConfig = new MatDialogConfig();
    matDataConfig.data = { soNumber: this.soNumber };
    matDataConfig.width = PopupSize.size.popup_xmd;
    matDataConfig.panelClass = 'sfl-gold-report-tool-tip-model';
    const dialogRef = this.matDialog.open(ResetToolTipComponent, matDataConfig);
    dialogRef.afterClosed().subscribe(res => { });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
