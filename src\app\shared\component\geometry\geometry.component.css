.feature-class{
  margin-top: auto;
}

.view-ccdc {
  left: 50px;
}

 .dark-gray {
  margin-left: 50px;
}

.empty-zone {
  margin-left: 50px;
}

.light-gray {
  margin-left: 0px;
}

.full-width {
  width: 100px; /* or a specific width like 250px */
}

.full-width-notes {
  width: 100px; /* or a specific width like 250px */
  margin-left: 80%;
}

.full-width-cube{
  width: 100px; /* or a specific width like 250px */
  margin-left: 10%;
}

.featlist{
  margin-left: 50px;
}

.features-list{
  overflow-y: scroll;
  height: 350px;
}

.notes-field{
  margin-left: -160px;
}

.open-doc-action{
  cursor: pointer;
  margin-top: -9px;
  position: absolute;
  left: 150px;
}

.open-doc-action-design{
  margin-left: -307px;
  cursor: pointer;
  margin-top: 11px;
}

.expand-action{
  cursor: pointer;
  margin-top: -9px;
  position: absolute;
  left: 208px;
}

.expand-action-design{
  cursor: pointer;
  margin-top: -9px;
  position: absolute;
  left: 308px;
}


.image-jacket{
  margin-left: -225px;
  margin-top: 7px;
}
