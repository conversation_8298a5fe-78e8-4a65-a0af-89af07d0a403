import { DatePipe } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { User } from 'src/app/common-pages/users/user.model';
import { SweetAlertService } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SelectedStakeHolders, StakeHolders } from '../../ecr-management.model';
import { EcoDto, EcoWorkFlowDto, WorkFlowMasterDataDto } from '../eco-plan.model';
import { EcoPlanService } from '../eco-plan.service';

@Component({
  selector: 'sfl-eco-workflow',
  templateUrl: './eco-workflow.component.html',
  styleUrls: ['./eco-workflow.component.css']
})
export class EcoWorkflowComponent implements OnInit, OnDestroy {

  workFlowDto: EcoWorkFlowDto[];
  ecoWorkFlowData: EcoWorkFlowDto[];
  workFlowMasterData: WorkFlowMasterDataDto[];
  users: User[];
  dropDownStakeHolder;

  subscription: Subscription = new Subscription();
  showLoader = false;
  isDataAvailable = false;
  _ecoDto: EcoDto = new EcoDto;

  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;

  selectedStakeHolders: SelectedStakeHolders = new SelectedStakeHolders();
  holdStakeHolders = [];
  stakeHolders = [];
  maxDate = new Date();
  ecoWorkFlowDataChild: any;
  ecoWorkFlowDataChildNewParts: any;

  get ecoDto() {
    return this._ecoDto;
  }

  @Input()
  set ecoDto(val) {
    this._ecoDto = val;
  }
  get _users() {
    return this.users;
  }

  @Input()
  set _users(users) {
    this.users = users;
  }
  constructor(private ecoPlanService: EcoPlanService, private datePipe: DatePipe, private sweetAlertService: SweetAlertService) { }

  async ngOnInit() {
    await this.getWorkFlowMasterData();
    await this.getEcoWorkFlow();
    this.setUpStakeHolders(this._ecoDto.ecoStakeholderDTOS);
  }

  setUpStakeHolders(stakHoldersList) {
    this.holdStakeHolders = stakHoldersList;
    this.holdStakeHolders.filter((stake, i) => {
      this._users.filter(user => {
        if (stake.userId === user.id) {
          this.stakeHolders[i] = {userName: user.firstName + ' ' + user.lastName, userId: user.id, stakeHolderId: stake.id, id: stake.id, ecoId: this._ecoDto.id};
        }
      });
    });
  }

  async confirmDelete(itemValue) {
    if (await this.sweetAlertService.deleteAlert()) {
        this.removeStakeholder(itemValue);
    }
  }

  removeStakeholder(stakeHolderId: StakeHolders) {
    this.showLoader = true;
    this.subscription.add(this.ecoPlanService.removeStakeHolder(stakeHolderId).subscribe(res => {
      this.stakeHolders.splice(this.stakeHolders.map(x => x.stakeHolderId).indexOf(stakeHolderId), 1);
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }
  onStakeHolderChange(stakeHolder) {
    const data = {
      userId: stakeHolder,
      id: null,
      ecoId: this.ecoDto.id
    };
    this.subscription.add(this.ecoPlanService.addStakeHolder(data).subscribe(res => {
      this.stakeHolders.push(res);
      this.setUpStakeHolders(this.stakeHolders);
      this.dropDownStakeHolder = '';
      this.showLoader = false;
    }, error => {
      this.dropDownStakeHolder = '';
      this.showLoader = false;
    }));
  }

  // Group by is a custom function which generates new array which is grouped by provided key of the array
  groupBy = (array, key) => {
    return array.reduce((result, currentValue) => {
      // If an array already present for key, push it to the array. Else create an array and push the object
      (result[currentValue[key]] = result[currentValue[key]] || []).push(currentValue);
      // Return the current iteration `result` value, this will be taken as next iteration `result` value and accumulate
      return result;
    }, {});
  }

    // checks if workflow exist for eco otherwise creates new
    getEcoWorkFlow() {
      return new Promise<void>((resolve, reject) => {
        this.subscription.add(this.ecoPlanService.getWorkFlowByEcoId(this._ecoDto.id).subscribe((response: EcoWorkFlowDto[]) => {
            this.workFlowDto = response;
            this.createWorkflowActivity();
            resolve();
        }, error => { reject(); }));
      });
    }

  // gets work flow master data
  getWorkFlowMasterData() {
      return  new Promise<void>((resolve, reject) => {
        this.subscription.add(this.ecoPlanService.getWorkFlowMasterData().subscribe((workFlowMasterData: WorkFlowMasterDataDto[]) => {
          if (workFlowMasterData) {
            this.workFlowMasterData = workFlowMasterData;
            this.ecoWorkFlowData = this.groupBy(this.workFlowMasterData, 'title');
            this.ecoWorkFlowDataChild = this.groupBy(this.workFlowMasterData[5].childWorkFlows, 'title');
            this.ecoWorkFlowDataChildNewParts = this.groupBy(this.workFlowMasterData[10].childWorkFlows, 'title');
            setTimeout(() => {
              this.isDataAvailable = true;
            }, 500);
            resolve();
          }
        }, error => {
          reject();
        }));
      });
  }




  createWorkflowActivity() {
    if (this.ecoWorkFlowData) {
      this.ecoWorkFlowData['Notifications Required'].filter((x) => {
        if (this.workFlowDto) {
          this.workFlowDto.filter((appWork) => {
            if (appWork.workflowId === x.id) {
              x.completedBy = appWork.completedBy;
              x.completedDate = new Date(appWork.completedDate + 'T06:00:00.000Z');
              x.ecoId = appWork.ecoId;
              x.ecoWorkflowId = appWork.id;
              x.workflowId = appWork.workflowId;
              x.status = true;
            }
          });
        }
      });
      this.ecoWorkFlowDataChild['Notifications Required'].filter((x) => {
        if (this.workFlowDto) {
          this.workFlowDto.filter((appWork) => {
            if (appWork.workflowId === x.id) {
              x.completedBy = appWork.completedBy;
              x.completedDate = new Date(appWork.completedDate + 'T06:00:00.000Z');
              x.ecoId = appWork.ecoId;
              x.ecoWorkflowId = appWork.id;
              x.workflowId = appWork.workflowId;
              x.status = true;
            }
          });
        }
      });
      this.ecoWorkFlowDataChildNewParts['Notifications Required'].filter((x) => {
        if (this.workFlowDto) {
          this.workFlowDto.filter((appWork) => {
            if (appWork.workflowId === x.id) {
              x.completedBy = appWork.completedBy;
              x.completedDate = new Date(appWork.completedDate + 'T06:00:00.000Z');
              x.ecoId = appWork.ecoId;
              x.ecoWorkflowId = appWork.id;
              x.workflowId = appWork.workflowId;
              x.status = true;
            }
          });
        }
      });
      // this.ecoWorkFlowData['Notifications Required'].filter((x) => {
      //   if (this.workFlowDto) {
      //     this.workFlowDto.filter((appWork) => {
      //       if (appWork.workflowId === x.id) {
      //         x.completedBy = appWork.completedBy;
      //         // x.completedDate = appWork.completedDate;
      //         x.completedDate = new Date(appWork.completedDate.replace(Values.dateTimeZone, ""));
      //         x.ecoId = appWork.ecoId;
      //         x.ecoWorkflowId = appWork.id;
      //         x.workflowId = appWork.workflowId;
      //         x.status = true;
      //       }
      //     });
      //   }
      // });
      this.ecoWorkFlowData['Closure'].filter((x) => {
        if (this.workFlowDto) {
          this.workFlowDto.filter((actWorkFlow) => {
            if (actWorkFlow.workflowId === x.id) {
              x.completedBy = actWorkFlow.completedBy;
              x.completedDate = new Date(actWorkFlow.completedDate + 'T06:00:00.000Z');
              x.ecoId = actWorkFlow.ecoId;
              x.ecoWorkflowId = actWorkFlow.id;
              x.workflowId = actWorkFlow.workflowId;
              x.status = true;
            }
          });
        }
      });
    }
  }

  // getChildName(childNameId) {
  //   let childData =  this.workFlowDto.filter(newId => newId.workflowId === childNameId)
  //   if(childData.length > 0) {
  //     return childData[0].completedBy;
  //   }
  // }

  // getChildDate(childId) {
  //   let childData =  this.workFlowDto.filter(newId => newId.workflowId === childId)
  //    if(childData.length > 0) {
  //      return childData[0].completedDate;
  //    }
  //  }

  updateEcoWorkFlow(workFlowData: WorkFlowMasterDataDto, index: number) {
    this.showLoader = true;
    return new Promise<void>((resolve, reject) => {
      workFlowData.ecoId = this._ecoDto.id;
      workFlowData.workflowId = workFlowData.id;
      if(workFlowData.completedBy) {
        if (!workFlowData.completedDate) {
          workFlowData.completedDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        } else {
          workFlowData.completedDate = this.datePipe.transform(workFlowData.completedDate, Values.dateFormat.formatHyphenTimeZone);
        }
      }
      if (workFlowData.status) {
        let obj;
        obj = Object.assign({}, workFlowData);
        obj.id = null;
        // create Work flow, nullyfying the id as this id belongs to the master data id
        this.subscription.add(this.ecoPlanService.createWorkFlowForEco(obj).subscribe(async (createResponse: any) => {
          this.workFlowMasterData[index].newEcrId = createResponse.id;
          this.showLoader = false;
          resolve();
        }, error => { this.showLoader = false; obj.status = false; reject(); }));
      } else {
        // remove work flow
        this.subscription.add(this.ecoPlanService.deleteWorkFlowEco(workFlowData.id).subscribe(async () => {
          workFlowData.completedDate = null;
          workFlowData.completedBy = null;
          resolve();
          this.showLoader = false;
        }, error => { this.showLoader = false; workFlowData.status = true; reject(); }));
      }
    });
  }

  updateEcoWorkFlowChild(workFlowData: WorkFlowMasterDataDto, index) {
    this.showLoader = true;
    return new Promise<void>((resolve, reject) => {
      workFlowData.ecoId = this._ecoDto.id;
      workFlowData.workflowId = workFlowData.id;
      if(workFlowData.completedBy) {
        if (!workFlowData.completedDate) {
          workFlowData.completedDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        } else {
          workFlowData.completedDate = this.datePipe.transform(workFlowData.completedDate, Values.dateFormat.formatHyphenTimeZone);
        }
      }
      if (workFlowData.status) {
        let obj;
        obj = Object.assign({}, workFlowData);
        obj.id = null;
        // create Work flow, nullyfying the id as this id belongs to the master data id
        this.subscription.add(this.ecoPlanService.createWorkFlowForEco(obj).subscribe(async (createResponse: any) => {
          obj.ecoWorkflowId = createResponse.id;
          this.workFlowMasterData[index].newEcrId = createResponse.id;
          obj.id = createResponse.workflowId;
          this.showLoader = false;
          resolve();
        }, error => { this.showLoader = false; obj.status = false; reject(); }));
      } else {
        // remove work flow
        this.subscription.add(this.ecoPlanService.deleteWorkFlowEco(workFlowData.id).subscribe(async () => {
          this.workFlowDto = this.workFlowDto.filter(e => e.workflowId !== workFlowData.id)
          workFlowData.completedDate = null;
          workFlowData.completedBy = null;
          resolve();
          this.showLoader = false;
        }, error => { this.showLoader = false; workFlowData.status = true; reject(); }));
      }
    });
  }

  updateWorkFlow(workFlowData: WorkFlowMasterDataDto, index) {
    this.showLoader = true;
    return new Promise<void>((resolve, reject) => {
      if(workFlowData.completedBy) {
        if (!workFlowData.completedDate) {
          workFlowData.completedDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        } else {
          workFlowData.completedDate = this.datePipe.transform(workFlowData.completedDate, Values.dateFormat.formatHyphenTimeZone);
        }
      }

      if (workFlowData.status) {
        let obj;
        obj = Object.assign({}, workFlowData);

        obj.id = workFlowData.workflowId;
          this.subscription.add(this.ecoPlanService.updateWorkFlowForEco(obj).subscribe(async (createResponse: any) => {
            this.workFlowDto.push(createResponse);
          this.showLoader = false;
          resolve();
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
          reject();
        }));
      }
    });
  }

  updateWorkFlowChild(workFlowData: WorkFlowMasterDataDto, index) {
    this.showLoader = true;
    return new Promise<void>((resolve, reject) => {
      if(workFlowData.completedBy) {
        if (!workFlowData.completedDate) {
          workFlowData.completedDate = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphenTimeZone);
        } else {
          workFlowData.completedDate = this.datePipe.transform(workFlowData.completedDate, Values.dateFormat.formatHyphenTimeZone);
        }
      }
      if (workFlowData.status) {
        let obj;
        obj = Object.assign({}, workFlowData);
        obj.id = workFlowData.workflowId;
        obj.designedBy = workFlowData.completedBy;
        this.subscription.add(this.ecoPlanService.updateWorkFlowForEco(obj).subscribe(async (createResponse: any) => {
          this.workFlowDto.push(createResponse);
          this.showLoader = false;
          resolve();
          this.showLoader = false;
        }, error => {
          this.showLoader = false;
          reject();
        }));
      }


    });
  }

  updateEco(changeFinalNoticeSentBy = false) {
    this.showLoader = true;
    if (!this.ecoDto.finalNoticeSentOn && changeFinalNoticeSentBy) {
      this.ecoDto.finalNoticeSentOn = this.datePipe.transform(new Date(), Values.dateFormat.formatHyphen);
    } else {
      this.ecoDto.finalNoticeSentOn = this.datePipe.transform(this.ecoDto.finalNoticeSentOn, Values.dateFormat.formatHyphen);
    }
    this.subscription.add(this.ecoPlanService.updateEco(this.ecoDto).subscribe((response: EcoDto) => {
      this.ecoDto = response;
      this.showLoader = false;
    }, error => {
      this.showLoader = false;
    }));
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
