<div fxLayout="column" fxLayoutAlign="space-between">

  <div class="sfl-loading" *ngIf="showLoader">
    <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
    </mat-progress-spinner>
  </div>
  <h2 mat-dialog-title>
    Plugging Information
    <mat-icon
      *ngIf="showReloadButton"
      class="open-doc sfl-pull-right"
      [matTooltip]="outDatedViewErrorMessage"
      color="warn"
      matTooltipClass="sfl-formula-tooltip"
      (click)="reloadPage()"
      id="refresh"
    >
      cached
    </mat-icon>
    <hr />
  </h2>
  <form #pluggingForm="ngForm">


    <mat-dialog-content>
      <div fxLayout="column" fxLayoutAlign="space-between">
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <input type="text" placeholder="Sleeving Type" aria-label="Sleeving Type" matInput [(ngModel)]="pluggingInformation.sleevingTypeName" [matAutocomplete]="autoSleevingType"
            [formControl]="sleevingTypeControl"  />
          </mat-form-field>
          <mat-autocomplete #autoSleevingType="matAutocomplete" [displayWith]="displaySleevingTypes" (optionSelected)="onSelectionChangesSleevingTypes($event.option.value)">
            <mat-option *ngFor="let data of sleevingTypesObservable$ | async; let i = index" [value]="data">
              {{ data?.name }}
            </mat-option>
          </mat-autocomplete>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Strain Relief"
              name="strainRelief"
              (selectionChange)="onStrainReliefChanged($event)"
              [(ngModel)]="pluggingInformation.strainReliefId"
            >
              <mat-option *ngFor="let strainRelief of strainReliefs | orderBy:'name'" [value]="strainRelief.id">{{ strainRelief.name }}</mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Green Light"
              name="greenLight"
              (selectionChange)="onGreenLightChanged($event)"
              [(ngModel)]="pluggingInformation.greenLightId"
            >
              <mat-option *ngFor="let greenLight of greenLights | orderBy:'name'" [value]="greenLight?.id">{{ greenLight?.name }}</mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100">
            <mat-select
              placeholder="Red Light"
              name="redLight"
              (selectionChange)="onRedLightChanged($event)"
              [(ngModel)]="pluggingInformation.redLightId"
            >
              <mat-option *ngFor="let redLight of redLights | orderBy:'name'" [value]="redLight?.id">{{ redLight?.name }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherSleevingType">
            <input matInput placeholder="Other Sleeving Type" name="otherSleevingType" [(ngModel)]="pluggingInformation.otherSleevingType" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherSleevingType">
            <input
              matInput
              placeholder="Other Sleeving Type Part Nnumber"
              name="otherSleevingTypePartNumber"
              [(ngModel)]="pluggingInformation.otherSleevingTypePartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherSleevingType">
            <input
              matInput
              placeholder="Other Sleeving Type Cost"
              name="otherSleevingTypeCost"
              [(ngModel)]="pluggingInformation.otherSleevingTypeCost"
            />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherStrainRelief">
            <input matInput placeholder="Other Strain Relief" name="otherStrainRelief" [(ngModel)]="pluggingInformation.otherStrainRelief" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherStrainRelief">
            <input
              matInput
              placeholder="Other Strain Relief Part Number"
              name="otherStrainReliefPartNumber"
              [(ngModel)]="pluggingInformation.otherStrainReliefPartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherStrainRelief">
            <input
              matInput
              placeholder="Other Strain Relief Cost"
              name="otherStrainReliefCost"
              [(ngModel)]="pluggingInformation.otherStrainReliefCost"
            />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherGreenLight">
            <input matInput placeholder="Other Green Light " name="otherGreenLight" [(ngModel)]="pluggingInformation.otherGreenLight" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherGreenLight">
            <input
              matInput
              placeholder="Other Green Light Part Number "
              name="otherGreenLightPartNumber"
              [(ngModel)]="pluggingInformation.otherGreenLightPartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherGreenLight">
            <input
              matInput
              placeholder="Other Green Light Cost "
              name="otherGreenLightCost"
              [(ngModel)]="pluggingInformation.otherGreenLightCost"
            />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="row wrap" fxLayoutAlign="space-between" fxFlex="100">
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherRedLight">
            <input matInput placeholder="Other Red Light" name="otherRedLight" [(ngModel)]="pluggingInformation.otherRedLight" />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherRedLight">
            <input
              matInput
              placeholder="Other Red Light Part Number"
              name="otherRedLightPartNumber"
              [(ngModel)]="pluggingInformation.otherRedLightPartNumber"
            />
          </mat-form-field>
          <mat-form-field fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100" *ngIf="isOtherRedLight">
            <input matInput placeholder="Other Red Light Cost" name="otherRedLightCost" [(ngModel)]="pluggingInformation.otherRedLightCost" />
          </mat-form-field>
          <div fxFlex.gt-lg="24" fxFlex.gt-md="24" fxFlex.gt-sm="24" fxFlex.gt-xs="100"></div>
        </div>
        <div fxLayout="column wrap" fxFlex="49">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="69" fxFlex.gt-md="69" fxFlex.gt-sm="69" fxFlex.gt-xs="100">
                <input type="text" placeholder="Plug" aria-label="Plug" matInput [(ngModel)]="pluggingInformation.leadPlugDTO.plugName" [matAutocomplete]="autoPlugs"
                [formControl]="plugControl" />
              </mat-form-field>
              <mat-autocomplete #autoPlugs="matAutocomplete" [displayWith]="displayPlugTypes | orderBy:'plugName'" (optionSelected)="onSelectionChangesPlugTypes($event.option.value)">
                <mat-option *ngFor="let data of plugsTypesObservable$ | async; let i = index" [value]="data">
                  {{ data?.plugName }}
                </mat-option>
              </mat-autocomplete>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Lead Length ({{ measureUnit ? measureUnit : '' }})"
                  name="leadLength"
                  [(ngModel)]="pluggingInformation.leadPlugDTO.leadLength"
                />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="isOtherLeadPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Plug" name="otherLeadPlug" [(ngModel)]="pluggingInformation.otherPlug" />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Other Plug Part Number"
                  name="otherLeadPlugPartNumber"
                  [(ngModel)]="pluggingInformation.otherPlugPartNumber"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Plug Cost" name="otherLeadPlugCost" [(ngModel)]="pluggingInformation.otherPlugCost" />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherLeadPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Part Number" name="leadPartNumber" [(ngModel)]="leadPlug.partNumber" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Cost" name="leadPlugCost" [(ngModel)]="leadPlug.plugCost" readonly />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherLeadPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Amps" name="leadMaxAmps" [(ngModel)]="leadPlug.maxAmps" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Volts" name="leadMaxVolts" [(ngModel)]="leadPlug.maxVolts" readonly />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="69" fxFlex.gt-md="69" fxFlex.gt-sm="69" fxFlex.gt-xs="100">
                <input type="text" placeholder="Lead Type" aria-label="Lead Type" matInput [(ngModel)]="pluggingInformation.leadTypeDTO.leadName" [matAutocomplete]="autoLeadType"
                [formControl]="leadTypeControl" />
              </mat-form-field>
              <mat-autocomplete #autoLeadType="matAutocomplete" [displayWith]="displayLeadTypes" (optionSelected)="onSelectionChangesLeadTypes($event.option.value)">
                <mat-option *ngFor="let data of leadTypeObservable$ | async; let i = index" [value]="data">
                  {{ data?.leadName }}
                </mat-option>
              </mat-autocomplete>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Part Number" name="partnumber" [(ngModel)]="leadType.partNumber" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Temp (°C)" name="maxtemp" [(ngModel)]="leadType.maxTemp" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Cost/ft."
                  name="costft"
                  [ngModel]="leadType.costPerFoot | currency"
                  (ngModelChange)="leadType.costPerFoot = $event"
                  readonly
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Volts" name="maxvolts" [(ngModel)]="leadType.maxVolts" readonly />
              </mat-form-field>
            </div>
          </div>
        </div>
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <div>
              <label>Lead</label>
            </div>
            <img src="{{ leadPlugImageUrl }}" class="jumper-product-img" alt="No Image" />
          </div>
        </div>
      </div>
      <h2 mat-dialog-title>
        Jumper Information
        <hr />
      </h2>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxLayout="column wrap" fxFlex="49">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <mat-form-field>
              <input type="text" placeholder="Connector" aria-label="Connector" matInput [(ngModel)]="pluggingInformation.jumperPlugDTO.plugName" [matAutocomplete]="autoConnectorName"
              [formControl]="connectorControl" />
            </mat-form-field>
            <mat-autocomplete #autoConnectorName="matAutocomplete" [displayWith]="displayPlugTypes" (optionSelected)="onSelectionChangesJmpPlugTypes($event.option.value)">
              <mat-option *ngFor="let data of jmpPlugsTypesObservable$ | async; let i = index" [value]="data">
                {{ data?.plugName }}
              </mat-option>
            </mat-autocomplete>
            <div fxLayout="row wrap" fxLayoutAlign="space-between">
              <mat-form-field fxFlex.gt-lg="69" fxFlex.gt-md="69" fxFlex.gt-sm="69" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Jumper Length ({{ measureUnit ? measureUnit : '' }})"
                  name="jumperLength"
                  [(ngModel)]="pluggingInformation.jumperPlugDTO.jumperLength"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="29" fxFlex.gt-md="29" fxFlex.gt-sm="29" fxFlex.gt-xs="100">
                <input matInput placeholder="Qty" name="quantity" [(ngModel)]="pluggingInformation.jumperPlugDTO.quantity" />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="isOtherJumperPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Other Connector" name="otherConnector" [(ngModel)]="pluggingInformation.otherConnector" />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Other Connector Part Number"
                  name="otherConnectorPartNumber"
                  [(ngModel)]="pluggingInformation.otherConnectorPartNumber"
                />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input
                  matInput
                  placeholder="Other Connector Cost"
                  name="otherConnectorCost"
                  [(ngModel)]="pluggingInformation.otherConnectorCost"
                />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherJumperPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Part Number" name="jumperPartNumber" [(ngModel)]="jumperPlug.jumperPartNumber" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Cost" name="jumperPlugCost" [(ngModel)]="jumperPlug.plugCost" readonly />
              </mat-form-field>
            </div>
            <div fxLayout="row wrap" fxLayoutAlign="space-between" *ngIf="!isOtherJumperPlug">
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Amps" name="jumperMaxAmps" [(ngModel)]="jumperPlug.maxAmps" readonly />
              </mat-form-field>
              <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Max Volts" name="jumperMaxVolts" [(ngModel)]="jumperPlug.maxVolts" readonly />
              </mat-form-field>
            </div>
          </div>
        </div>
        <div fxLayout="column wrap" fxFlex="49" class="mat-img">
          <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
            <div>
              <label>Jumper</label>
            </div>
            <img src="{{ jumperConnectorImageUrl }}" class="jumper-product-img" alt="No Image" />
          </div>
        </div>
      </div>
      <div fxFlex="100">
        <mat-form-field>
          <textarea matInput placeholder="Notes" name="notes" [(ngModel)]="pluggingInformation.notes" rows="5"></textarea>
        </mat-form-field>
      </div>
    </div>
    </mat-dialog-content>
    <hr />
    <mat-dialog-actions fxLayoutAlign="space-between">
      <div fxLayoutAlign="start">
        <button mat-raised-button color="warn" type="submit" (click)="savePluggingInformation(pluggingForm, 'save')" name="save">Save</button>
        <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
      </div>
      <div fxLayoutAlign="end">
        <button mat-raised-button type="submit" (click)="savePluggingInformation(pluggingForm, 'saveandnext')" name="saveandnext">
          Save And Next
        </button>
      </div>
    </mat-dialog-actions>
  </form>
  </div>
