<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex.gt-sm="33.33%" fxFlex="100">
    <mat-card class="mat-card-yellow card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">description</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <h2 mat-card-widget-title>{{ statusCount?.patternDesign }}</h2>
          <p>Pattern Design</p>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxFlex.gt-sm="33.33%" fxFlex="100">
    <mat-card class="mat-card-purple card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">description</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <h2 mat-card-widget-title>{{ statusCount?.levelOneReview }}</h2>
          <p>Level One Review</p>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxFlex.gt-sm="33.33%" fxFlex="100">
    <mat-card class="mat-card-red card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">exit_to_app</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <h2 mat-card-widget-title>{{ statusCount?.outForApproval }}</h2>
          <p>Out For Approval</p>
        </div>
      </div>
    </mat-card>
  </div>
</div>
<div class="less-peding" [ngClass]="{'disable-section' : userRole==='ROLE_SALES'}">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center" fxLayoutGap="10px">
        <div>
          <button mat-raised-button type="button" id="ecr-management" color="warn"
                  [routerLink]="['/quote-tracker/dashboard/design-engineering-quote-tracker']">
            SO Tracker
          </button>
        </div>
        <div>
          <button mat-raised-button type="button" id="ecr-management" color="warn"
                  [routerLink]="['/design-eng/ecr-management']">
            ECR Management
          </button>
        </div>
        <div>
          <button mat-raised-button type="button" id="search-ecr" (click)="searchEcr()" color="warn">
            Search BH Part Number
          </button>
        </div>
        <a href="/view-jacket-reference" routerLink="/view-jacket-reference" target="_blank">
          <button mat-raised-button color="warn" type="button" id="switchtojacketreference">
            BOM Reference
          </button>
        </a>
        <div>
          <button mat-raised-button color="warn" (click)="searchMfgPart()">Search Mfg Part
          </button>
        </div>
      </div>
    </div>
  </mat-card>
</div>
<div class="less-peding" [ngClass]="{'disable-section' : userRole==='ROLE_SALES'}">
  <sfl-dashboard-design-engg-tracker></sfl-dashboard-design-engg-tracker>
</div>
<div class="less-peding">
  <div class="filter less-peding cust_fields">
    <mat-card class="mb-10">
      <form class="fields" #quotationFilterForm="ngForm">
        <div fxLayout="row wrap" fxLayoutAlign="space-between">
          <mat-form-field appearance="outline" fxFlex.gt-lg="18" fxFlex.gt-md="18">
            <mat-label>Search for Project Name</mat-label>
            <input matInput [(ngModel)]="quotationSearchFilter.projectTitle" #filterProjectTitle/>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="18" fxFlex.gt-xs="50">
            <input type="text" placeholder="Status" aria-label="Status" matInput
                   [(ngModel)]="quotationSearchFilter.status" [matAutocomplete]="autoStatus"
                   [formControl]="statusControl"/>
          </mat-form-field>
          <mat-autocomplete #autoStatus="matAutocomplete" [displayWith]="displayStatus">
            <mat-option *ngFor="let statuses of statusObservable$ | async; let i = index" [value]="statuses?.status">
              {{ statuses?.status }}
            </mat-option>
          </mat-autocomplete>
          <mat-form-field appearance="outline" fxFlex.gt-lg="18" fxFlex.gt-md="18" fxFlex.gt-sm="18" fxFlex.gt-xs="50">
            <mat-label>Sales Associate</mat-label>
            <mat-select placeholder="Sales Associate" [(ngModel)]="quotationSearchFilter.salesAssociateName"
                        name="salesAssociateName">
              <mat-option *ngFor="let salesAssociates of salesassociate"
                          [value]="salesAssociates?.firstName +' '+ salesAssociates?.lastName">{{ salesAssociates?.firstName }}
                {{ salesAssociates?.lastName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field appearance="outline" fxFlex.gt-lg="29" fxFlex.gt-md="27">
            <mat-label>Customer</mat-label>
            <input matInput placeholder="Customer" [(ngModel)]="quotationSearchFilter.customerName" name="customerName">
          </mat-form-field>
          <mat-actions-row fxFlex.gt-lg="13" fxFlex.gt-md="15" fxLayoutAlign="center center">
            <button mat-raised-button color="warn" (click)="addFilter()">Search</button>&nbsp;
            <button mat-raised-button color="warn" (click)="resetFilter()">Reset</button>
          </mat-actions-row>
        </div>
      </form>
    </mat-card>
  </div>
  <mat-card class="cust_table" [ngClass]="{'disable-section' : userRole==='ROLE_SALES'}">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>Quotation List</mat-card-title>
      </div>

    </div>
    <mat-table class="w-auto" matSort matSortDisableClear [dataSource]="dataSource"
               (matSortChange)="getSorting($event)">
      <ng-container matColumnDef="quotationNumber">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="8" fxFlex.gt-md="12"> Quote Number
        </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="12">
          <span class="mobile-label">Quote Number:</span>
          <a class="link" [routerLink]="['/design-eng/jacket-list']" [queryParams]="{ quotId: element?.id }">{{
              element?.quotationNumber
            }}</a>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="salesOrderNumber">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="7" fxFlex.gt-md="10"> SO Number
        </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="7" fxFlex.gt-md="10">
          <span class="mobile-label">SO Number:</span>
          <a class="link" [routerLink]="['/design-eng/jacket-list']" [queryParams]="{ quotId: element?.id }">{{
              element?.salesOrderNumber
            }}</a>
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="projectTitle">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="20" fxFlex.gt-md="17"> Project Name
        </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="20" fxFlex.gt-md="17">
          <span class="mobile-label">Project Name:</span> {{ element?.projectTitle }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="quotationStatus">
        <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex.gt-lg="10" fxFlex.gt-md="12"> Status</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="10" fxFlex.gt-md="12">
          <span class="mobile-label">Status:</span>
          {{ element?.quotationStatusName }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="activeRevision">
        <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="6"> Revision</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="5" fxFlex.gt-md="6">
          <span class="mobile-label">Rev:</span>
          {{ element?.revisionName }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="salesAssociate">
        <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="10" fxFlex.gt-md="10"> Sales Associate</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="10" fxFlex.gt-md="10">
          <span class="mobile-label">Sales Associate:</span>
          {{ element?.salesAssociate }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="customerName">
        <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="22" fxFlex.gt-md="12"> Customer</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="22" fxFlex.gt-md="12">
          <span class="mobile-label">Customer:</span>
          {{ element?.customerName }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="design">
        <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="5"> Design</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="5" fxFlex.gt-md="5">
          <span class="mobile-label">Design:</span>
          {{ element?.design }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="shipDate">
        <mat-header-cell mat-sort-header *matHeaderCellDef fxFlex.gt-lg="5" fxFlex.gt-md="8"> Ship Date
        </mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="5" fxFlex.gt-md="8">
          <span class="mobile-label">Ship Date:</span>
          {{ element?.shipDate | date: 'MM-dd-yyyy' }}
        </mat-cell>
      </ng-container>
      <ng-container matColumnDef="dollarAmount">
        <mat-header-cell *matHeaderCellDef fxFlex.gt-lg="8" fxFlex.gt-md="8"> Dollar Amount</mat-header-cell>
        <mat-cell *matCellDef="let element" fxFlex.gt-lg="8" fxFlex.gt-md="8">
          <span class="mobile-label">Dollar Amount:</span>
          {{ element?.dollarAmount | currency: 'USD':'symbol' }}
        </mat-cell>
      </ng-container>
      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
    </mat-table>
    <div *ngIf="isNoDataFound | async">
      <ng-template class="no-records" *ngIf="!showLoader">No data found</ng-template>
    </div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
