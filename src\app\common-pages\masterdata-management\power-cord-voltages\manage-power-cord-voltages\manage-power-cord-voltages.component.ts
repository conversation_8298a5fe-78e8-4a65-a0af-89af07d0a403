import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { PowerCordVoltagesMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-power-cord-voltages',
  templateUrl: './manage-power-cord-voltages.component.html'
})
export class ManagePowerCordVoltagesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  powerCordVoltage: PowerCordVoltagesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManagePowerCordVoltagesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.powerCordVoltage = data;
  }

  ngOnInit() {
    this.powerCordVoltage = this.powerCordVoltage.id ? Object.assign({}, this.powerCordVoltage) : new PowerCordVoltagesMaster();
    this.powerCordVoltage.id ? (this.title = 'Update Power Cord Voltage') : (this.title = 'Add Power Cord Voltage');
  }

  updatePowerCordVoltage() {
    this.showLoader = true;
    if (this.powerCordVoltage.id) {
      this.subscription.add(
        this.masterDataService.updatePowerCordVoltage(this.powerCordVoltage).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addPowerCordVoltage(this.powerCordVoltage).subscribe(
          res => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
