<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Quotation Status</mat-label>
          <input matInput [(ngModel)]="quotStatusFilter.status" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldStatus)"
            *ngIf="quotStatusFilter.status"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addQuotStatus()">Add New Quotation Status</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="quotationStatusDataSource"
        (matSortChange)="getQuotStateSorting($event)"
      >
        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="60"> Status </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="60"> {{ element?.status }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="defaultHidden">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Default Hidden </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.defaultHidden | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="type">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Type </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20">
            <span *ngFor="let quotType of quotTypes">{{ quotType.id === element?.type ? quotType.name : '' }}</span>
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="rowColor">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Design Eng Row Color </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">
           {{ element?.rowColor === 'None' ? 'White'
           : element?.rowColor === 'tomato' ? 'Red'
           : element?.rowColor === 'skyblue' ? 'Blue'
           : element?.rowColor === 'lemonchiffon' ? 'Yellow'
           : element?.rowColor === 'limegreen' ? 'Green'
           : element?.rowColor === 'white' ? 'White'
           : element?.rowColor }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="appEngRowColor">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> App Eng Row Color </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10">
            {{ element?.appEngRowColor === 'None' ? 'White'
            : element?.appEngRowColor === 'tomato' ? 'Red'
              : element?.appEngRowColor === 'skyblue' ? 'Blue'
                : element?.appEngRowColor === 'lemonchiffon' ? 'Yellow'
                  : element?.appEngRowColor === 'limegreen' ? 'Green'
                    : element?.appEngRowColor === 'white' ? 'White'
                      : element?.appEngRowColor }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="forApproval">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> For Approval </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.forApproval | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="orderNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Order Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.orderNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editQuotStatus(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteQuotStatus(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="quotStatusColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: quotStatusColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!quotationStatusDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getQuotStatePagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
