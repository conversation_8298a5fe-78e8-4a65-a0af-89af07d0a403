import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DuplicateBhxMaterialMasterComponent } from './duplicate-bhx-material-master.component';

describe('DuplicateBhxMaterialMasterComponent', () => {
  let component: DuplicateBhxMaterialMasterComponent;
  let fixture: ComponentFixture<DuplicateBhxMaterialMasterComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DuplicateBhxMaterialMasterComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DuplicateBhxMaterialMasterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
