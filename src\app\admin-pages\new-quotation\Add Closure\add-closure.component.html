<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Closure Information
  <mat-icon
    *ngIf="showReloadButton"
    class="open-doc sfl-pull-right"
    [matTooltip]="outDatedViewErrorMessage"
    color="warn"
    matTooltipClass="sfl-formula-tooltip"
    (click)="reloadPage()"
    id="refresh"
  >
    cached
  </mat-icon>
  <hr />
</h2>
<mat-dialog-content>
  <form class="cust_table" #closureInfoForm="ngForm">
    <div fxLayout="column">
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxLayout="column wrap" fxFlex="49">
        <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
          <div>
            <mat-checkbox color="warn" name="extendedFlap" [(ngModel)]="closureInfo.extendedFlap" #extendedFlapInput="ngModel"
              >Extended Flap</mat-checkbox
            >&nbsp;&nbsp;
            <mat-checkbox color="warn" name="cat5Tunnel" [(ngModel)]="closureInfo.cat5Tunnel" #cat5TunnelCheck="ngModel"
              >Cat5 Tunnel</mat-checkbox
            >
          </div>
          <mat-form-field>
            <input type="text" placeholder="Select an option" aria-label="Select an option" matInput [(ngModel)]="closureInfo.closureMaterialName" [matAutocomplete]="autoClosureMaterial"
            [formControl]="closureMaterialControl"  />
          </mat-form-field>
          <mat-autocomplete #autoClosureMaterial="matAutocomplete" [displayWith]="displayMaterials" (optionSelected)="onSelectionClosure($event.option.value)">
            <mat-option *ngFor="let data of closureObservable$ | async; let i = index" [value]="data">
              {{ data?.name }}
            </mat-option>
            <mat-option value="none">None</mat-option>
          </mat-autocomplete>
          <mat-form-field *ngIf="isOther">
            <input
              matInput
              placeholder="Other Closure"
              name="other"
              [(ngModel)]="closureInfo.otherClosure"
              #otherInput="ngModel"
              type="text"
            />
          </mat-form-field>
          <mat-form-field *ngIf="isOther">
            <input
              matInput
              placeholder="Other Closure Part Number"
              name="otherPartnumber"
              [(ngModel)]="closureInfo.otherClosurePartNumber"
              #otherInput="ngModel"
              type="text"
            />
          </mat-form-field>
          <mat-form-field *ngIf="isOther">
            <input
              matInput
              placeholder="Other Closure Cost"
              name="otherCost"
              [(ngModel)]="closureInfo.otherClosureCost"
              #otherInput="ngModel"
              type="text"
            />
          </mat-form-field>
          <mat-form-field>
            <input
              matInput
              placeholder="Est. Surf. Temp ({{ tempUnit ? tempUnit : '' }})"
              name="estSurfaceTemp"
              [(ngModel)]="closureInfo.estSurfaceTemp"
              #estSurfaceTempInput="ngModel"
              type="number"
              readonly
            />
          </mat-form-field>
          <mat-form-field *ngIf="!isOther">
            <input
              *ngIf="tempUnit === '°F'"
              matInput
              placeholder="Maximum Temp ({{ tempUnit ? tempUnit : '' }})"
              name="maxTempF"
              [(ngModel)]="material.maxTempF"
              type="number"
              #maxTempFInput="ngModel"
              readonly
            />
            <input
              *ngIf="tempUnit !== '°F'"
              matInput
              placeholder="Maximum Temp ({{ tempUnit ? tempUnit : '' }})"
              name="maxTemp"
              (ngModelChange)="material.maxTemp = $event"
              [ngModel]="material?.maxTemp"
              type="number"
              #maxTempInput="ngModel"
              readonly
            />
          </mat-form-field>
          <mat-form-field *ngIf="!isOther">
            <input
              matInput
              placeholder="Cost Per Foot"
              name="costPerSq"
              (ngModelChange)="material.costPerSq = $event"
              [ngModel]="material?.costPerSq"
              type="number"
              #costPerSqInput="ngModel"
              readonly
            />
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="column wrap" fxLayoutAlign="center center" fxFlex="49" class="mat-img">
        <img class="product-img" src="{{ imageUrl }}" alt="" />
      </div>
    </div>
    <div fxFlex="100">
      <mat-form-field>
        <textarea matInput placeholder="Notes" rows="5" name="notes" [(ngModel)]="closureInfo.notes"></textarea>
      </mat-form-field>
    </div>
  </div>
  </form>
</mat-dialog-content>
<hr />
<mat-dialog-actions fxLayoutAlign="space-between">
  <div fxLayoutAlign="start">
    <button mat-raised-button color="warn" type="submit" (click)="saveClosure('save')" name="save">Save</button>
    <button mat-raised-button type="submit" (click)="closeDialog()" name="cancel">Cancel</button>
  </div>
  <div fxLayoutAlign="end">
    <button mat-raised-button type="submit" (click)="saveClosure('saveandnext')" name="saveandnext">Save And Next</button>
  </div>
</mat-dialog-actions>
