import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { AppConfig } from '../app.config';
import { ResetPasswordModel } from './account.model';

@Injectable()
export class AccountService {
    constructor(private httpClient: HttpClient) { }

    sendForgotPasswordEmail(email: String) {
        return this.httpClient.post(
            AppConfig.RESET_PASSWORD_INIT_API,
            email
        );
    }

    resetPassword(resetPasswordModel: ResetPasswordModel) {
        return this.httpClient.post(
            AppConfig.RESET_PASSWORD_FINISH_API,
            resetPasswordModel
        );
    }
}
