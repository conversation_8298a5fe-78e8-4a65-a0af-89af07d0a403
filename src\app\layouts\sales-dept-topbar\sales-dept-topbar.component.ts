import {Component, EventEmitter, OnDestroy, OnInit, Output} from '@angular/core';
import {Router} from '@angular/router';
import {Subscription} from 'rxjs';
import * as screenfull from 'screenfull';
import {SharedService} from 'src/app/shared';
import {Route} from 'src/app/shared/constants/router.constants';

@Component({
  selector: 'sfl-sales-dept-topbar',
  templateUrl: './sales-dept-topbar.component.html'
})
export class SalesDeptTopbarComponent implements OnInit, OnDestroy {
  @Output() toggleSidenav = new EventEmitter<void>();

  subscription = new Subscription();

  filter: boolean;
  isDesignAdmin: boolean;
  isAppAdmin: boolean;
  isFullScreen: boolean;

  constructor(private readonly router: Router, private readonly sharedService: SharedService) { }

  async ngOnInit() { }

  fullScreenToggle(): void {
    if (screenfull.enabled) {
      screenfull.toggle();
      this.isFullScreen = !this.isFullScreen;
    }
  }

  // used to switch to the Sales dashboard
  goToDashboard() {
    this.router.navigate([Route.SALES.dashboard]);
  }

  switchToQuoteTracker() {
    this.router.navigate([Route.QUOTE_TRACKER.dashboard]);
  }

  switchToDesign() {
    this.router.navigate([Route.DESIGN_ENGG.dashboard]);
  }

  switchToAppAdmin() {
    this.router.navigate(['/app-eng/dashboard']);
  }

  // used to logged out the user
  logout() {
    this.sharedService.logout(null);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
