<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Part Number</mat-label>
          <input matInput [(ngModel)]="materialFilter.partNumber" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldPartNumber)"
            *ngIf="materialFilter.partNumber"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Material</mat-label>
          <input matInput [(ngModel)]="materialFilter.material" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldMaterial)"
            *ngIf="materialFilter.material"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addMaterial()">Add New Material</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="materialMasterDataSource"
        (matSortChange)="getMaterialMasterSorting($event)"
      >
        <ng-container matColumnDef="material">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Material </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.material }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.partNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="costPerSq">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cost / Sq. Ft. </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.costPerSq }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="imageUrl">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Image URL </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.imageUrl }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="inventory">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Inventory </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.inventory }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxTemp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Max Temprature </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.maxTemp }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxTempF">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Max Temp Fahrenheit </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.maxTempF }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="identifier">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Identifier </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.identifier }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef fxFlex="10"> Is Obselete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editMaterialMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteMaterialMaster(element.materialId)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="materialMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: materialMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!materialMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getMaterialMasterPagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
