<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #sleevingTypeForm="ngForm" (ngSubmit)="updatePlugLight()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Plug Light Name"
            [(ngModel)]="plugLights.name"
            name="plugLightsName"
            #plugLightsNameInput="ngModel"
            required
          />
        </mat-form-field>
        <div *ngIf="plugLightsNameInput.touched && plugLightsNameInput.invalid">
          <small class="mat-text-warn" *ngIf="plugLightsNameInput?.errors?.required">Plug light name is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Part Number" [(ngModel)]="plugLights.partNumber" name="partNumber" #partNumberInput="ngModel" />
        </mat-form-field>
      </div>
    </div>

    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothCE" color="warn" [(ngModel)]="plugLights.clothCE">Cloth CE</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="clothUL" color="warn" [(ngModel)]="plugLights.clothUL">Cloth UL</mat-checkbox>
      </div>
      <div fxFlex.gt-lg="31" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="plugLights.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <mat-select placeholder="Type" [(ngModel)]="plugLights.type" name="plugLightType" #plugLightTypeInut="ngModel">
            <mat-option *ngFor="let type of plugLightType" [value]="type">
              {{ type }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="48" fxFlex.gt-md="47" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Cost" [(ngModel)]="plugLights.cost" name="cost" #costInput="ngModel" sflIsDecimal />
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!sleevingTypeForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
