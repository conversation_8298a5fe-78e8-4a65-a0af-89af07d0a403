<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Add Part Number
  <hr />
</h2>
<form class="fields" #addPartnumberForm="ngForm">
  <mat-dialog-content>
    <div fxFlex fxLayout="row" fxLayoutGap="20px">
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-xs="100">
        <div fxLayout="row wrap" class="mb-10 cust_fields">
          <mat-form-field>
            <mat-label>Part Number</mat-label>
            <input matInput placeholder="Part Number" [(ngModel)]="partnumberDto.partNumber" name="partnumber" required />
          </mat-form-field>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" [disabled]="addPartnumberForm.invalid" color="warn" (click)="checkExistsingPartNumber()">Add</button>
    </div>
  </mat-dialog-actions>
</form>
