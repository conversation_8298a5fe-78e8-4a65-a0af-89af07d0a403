<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Part Number 1</mat-label>
          <input matInput [(ngModel)]="closureMaterialFilter.partNumber1" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldPartNumber1)"
            *ngIf="closureMaterialFilter.partNumber1"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-auto">
          <mat-label>Search Closure Material</mat-label>
          <input matInput [(ngModel)]="closureMaterialFilter.name" (change)="addFilter()" />
          <button
            mat-button
            mat-icon-button
            matSuffix
            class="sfl-clear-filter-icon-btn"
            (click)="clearFilter(filterFieldName)"
            *ngIf="closureMaterialFilter.name"
            tabindex="-1"
          >
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addClosureMaterial()">Add New Closure Material</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="closureMaterialMasterDataSource"
        (matSortChange)="getClosureMaterialMasterSorting($event)"
      >
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Closure Material Name </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.name }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber1">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="20"> Part Number 1 </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="20"> {{ element?.partNumber1 }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="partNumber2">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="15"> Part Number 2 </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="15"> {{ element?.partNumber2 }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="ul p/n">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> UL Part Number </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.fastenerCode }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="costPerSq">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Cost Per Sq </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.costPerSq }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="imageUrl">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Image Url </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.imageUrl }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxTemp">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Max Temperature </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.maxTemp }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="maxTempF">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Max Temp Fahrenheit </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.maxTempF }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editClosureMaterial(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteClosureMaterial(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="closureMaterialMasterMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: closureMaterialMasterMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!closureMaterialMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getClosurePagination($event)"
      showFirstLastButtons
    >
    </mat-paginator>
  </mat-card>
</div>
