import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Values } from 'src/app/shared/constants/values.constants';
import { AppEngineeringQuoteTracker, AppRowColorDialog } from '../../quote-tracker.model';
import { QuoteTrackerService } from '../../quote-tracker.service';
import { AppEnggQuoteTrackerComponent } from '../app-engg-quote-tracker.component';
import { Subscription } from 'rxjs';
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';

@Component({
  selector: 'app-app-engg-quote-tracker-row-colour',
  templateUrl: './app-engg-quote-tracker-row-colour.component.html',
  styleUrls: ['./app-engg-quote-tracker-row-colour.component.css']
})
export class AppEnggQuoteTrackerRowColourComponent implements OnInit {
  highlightedColors = Values.highlightColors;
  quoteId: number;
  showLoader = false;
  subscription: Subscription = new Subscription();
  quoteStatusId: number;

  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    public dialogRef: MatDialogRef<AppEnggQuoteTrackerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AppRowColorDialog,
  ) {
    this.quoteId = data.quoteId;
    this.quoteStatusId = data.quoteStatusId;
  }

  ngOnInit() {
  }

  selectRow(colorValue: string) {
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.appRowHighlighting(this.quoteId, colorValue, this.quoteStatusId).subscribe(
        (res: AppEngineeringQuoteTracker) => {
          if (res) {
            this.closeDialog(true);
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog(flag = false): void {
    this.dialogRef.close(flag);
  }

}
