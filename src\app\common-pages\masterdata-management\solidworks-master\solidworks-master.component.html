<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<div class="less-peding" [ngStyle]="{ overflow: 'visible !important' }">
  <mat-card class="cust_table bhx-material-tbl-header">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
      <div fxFlex fxLayoutAlign="end center" fxLayoutGap="5px">
        <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addSolidWorksMaster()">Add New SolidWorks Master</button>
      </div>
    </div>
    <div class="cust_table">
      <mat-table mat-table matSort matSortDisableClear [dataSource]="solidworksMasterDataSource">
        <ng-container matColumnDef="usaFilePath">
          <mat-header-cell *matHeaderCellDef fxFlex="33"> USA File Path </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="33">
            {{ element.usaFilePath }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="vietnamFilePath">
          <mat-header-cell *matHeaderCellDef fxFlex="33"> Vietnam File Path </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="33">
            {{ element.vietnamFilePath }}
          </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="20"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="30">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editSolidWorksMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteSolidWorksBlockMaster(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="solidworksMasterColumn; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: solidworksMasterColumn"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!solidworksMasterDataSource?.data?.length">No data found</div>
    <mat-paginator
      [length]="length"
      [pageSizeOptions]="pageSizeOptions"
      [pageSize]="pageSize"
      [pageIndex]="pageIndex"
      (page)="getSWBlockMastersPagination($event)"
      showFirstLastButtons
      class="bhx-material-paginator"
    >
    </mat-paginator>
  </mat-card>
</div>
