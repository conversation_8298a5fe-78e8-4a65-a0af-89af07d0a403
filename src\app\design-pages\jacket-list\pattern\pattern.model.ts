export class PatternDesign {
  constructor(
    public id?: number,
    public name?: string,
    public checked?: boolean,
    public patternDate?: any,
    public geometryPattern?: boolean,
    public layeredConstruction?: boolean,
    public wattageCalculation?: boolean,
    public wirringDiagram?: boolean,
    public stepJacketModels?: boolean,
    public checkedEngineering?: boolean,
    public verifiedUL?: boolean,
    public comments?: string,
    public jacketId?: number
  ) {}
}

export class ChecklistPatternDesignDTO {
  public patternDesignDTOs: PatternDesign[] = [];
  public quotationID: number;
}

export class UndoResponse {
  public status?: string;
  public id?: number;
}
