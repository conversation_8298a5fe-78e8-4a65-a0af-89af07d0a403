<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>

<!-- Quote status counters -->
<div fxLayout="row wrap" class="mb-20">
  <div fxFlex.gt-sm="25" fxFlex="100">
    <mat-card class="mat-card-yellow card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">batch_prediction</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <sfl-animate-counter [digit]="quoteStatusCounts.rfqInQueueStatus" [duration]="animationDuration"></sfl-animate-counter>
          <p>RFQ In Queue</p>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxFlex.gt-sm="25" fxFlex="100">
    <mat-card class="mat-card-indigo card-widget">
      <div mat-card-widget>
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">account_tree</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <sfl-animate-counter [digit]="quoteStatusCounts.quotingInProcessStatus" [duration]="animationDuration">1200</sfl-animate-counter>
          <p>Quoting In Process</p>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxFlex.gt-sm="25" fxFlex="100">
    <mat-card class="mat-card-purple card-widget" [ngClass]="viewing === 'quote-completed' ? 'active-card' : ''">
      <div mat-card-widget class="open-doc" (click)="switchToQuoteCompleted()">
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">playlist_add_check</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <sfl-animate-counter [digit]="quoteStatusCounts.quoteCompleteStatus" [duration]="animationDuration">1800</sfl-animate-counter>
          <p>Quote Completed</p>
        </div>
      </div>
    </mat-card>
  </div>
  <div fxFlex.gt-sm="25" fxFlex="100">
    <mat-card class="mat-card-green card-widget" [ngClass]="viewing === 'so-in-design' ? 'active-card' : ''">
      <div mat-card-widget class="open-doc" (click)="switchToSOInDesign()">
        <div mat-card-float-icon>
          <mat-icon class="material-icons_md-48">engineering</mat-icon>
        </div>
        <div class="pl-0" fxFlex.gt-sm="40" fxFlex.gt-xs="50" fxFlex="100">
          <sfl-animate-counter [digit]="quoteStatusCounts.soInDesign" [duration]="animationDuration">11000</sfl-animate-counter>
          <p>SO in Design</p>
        </div>
      </div>
    </mat-card>
  </div>
</div>

<!-- Switching to app engineering or design engineering quote trackers -->
<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <div fxFlex.gt-lg="16" fxFlex.gt-md="24" fxFlex.gt-xs="32" fxFlex="100">
          <button
            mat-raised-button
            type="button"
            id="app-engg-quote-tracker"
            color="warn"
            [routerLink]="['/quote-tracker/dashboard/app-engineering-quote-tracker']"
          >
            Application Engineering Quote Tracker
          </button>
        </div>
        <div fxFlex.gt-lg="15" fxFlex.gt-md="22" fxFlex.gt-xs="28" fxFlex="100">
          <button
            mat-raised-button
            type="button"
            id="design-engg-quote-tracker"
            color="warn"
            [routerLink]="['/quote-tracker/dashboard/design-engineering-quote-tracker']"
          >
            Design Engineering SO Tracker
          </button>
        </div>
      </div>
    </div>
  </mat-card>
</div>

<!-- Main Content -->

<!-- SO In Design Grid Starts -->
<ng-container *ngIf="viewing === 'so-in-design'">
  <sfl-so-in-design-grid></sfl-so-in-design-grid>
</ng-container>
<!-- Ends SO In Design Grid -->

<!-- Quote Completed Starts -->
<ng-container *ngIf="viewing === 'quote-completed'">
  <sfl-quote-completed></sfl-quote-completed>
</ng-container>
<!-- End of Quote Completed -->
