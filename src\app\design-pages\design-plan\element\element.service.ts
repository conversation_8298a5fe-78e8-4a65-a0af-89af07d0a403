
import {catchError, map} from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';


import { AppConfig } from '../../../app.config';
import { utils } from '../../../shared/helpers/app.helper';
import { Element, WireSelector } from './element.model';

@Injectable({ providedIn: 'root' })
export class ElementService {
  constructor(private readonly http: HttpClient) { }

  getBasicInfo(jacketId: number) {
    return this.http
      .get(AppConfig.GET_BASIC_INFO + jacketId).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  saveElement(element: Element) {
    return this.http.post(AppConfig.SAVE_JACKET_ELEMENT, element).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getJacketElementJacket(jacketId: number, elementNumber: number) {
    return this.http
      .get(AppConfig.SAVE_JACKET_ELEMENT + '/jacket/' + jacketId + '/elementNumber/' + elementNumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  searchWire(wireSelector: WireSelector) {
    return this.http.post(AppConfig.SEARCH_WIRE, wireSelector).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  getAMPS(volts: number, watts: number, phase: string) {
    return this.http
      .get(AppConfig.GETAMPS + '?volts=' + volts + '&watts=' + watts + '&phase=' + phase).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  checkElementLastSyncStatusByJacketId(jacketId: number, elementNumber: number) {
    return this.http
      .get(AppConfig.CHECK_LAST_SYNC_STATUS_BY_JACKET_ID + 'jacket/' + jacketId + '/elementNumber/' + elementNumber).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getElementDMTLogResponseByJacketId(id: number) {
    return this.http
      .get(AppConfig.GET_DMT_RESPONSE + id).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  retrySync(elementNumber: number) {
    return this.http
      .get(AppConfig.RETRY_SYNC_TO_EPICORE + elementNumber).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  // used to get the DMT logs for final review screen once the sync is completed
  getDMTLogsFinalReview(jacketId: number, type: string) {
    return this.http.get(`${AppConfig.GET_FINAL_REVIEW_DMT_LOGS}${jacketId}/${type}`).pipe(map(utils.extractData),catchError(utils.handleError),);
  }

  isAnyElementSelectedForJacket(jacketId: number) {
    return this.http.get(AppConfig.SAVE_JACKET_ELEMENT + '/selected/jacket/' + jacketId).pipe(
      map(utils.extractData),catchError(utils.handleError),);
  }

  deleteSelectedElementsByJacketId(jacketId: number) {
    return this.http.delete(AppConfig.SAVE_JACKET_ELEMENT + '/delete/jacket/' + jacketId).pipe(
      map(utils.extractData),catchError(utils.handleError),);
  }

  deleteElementById(jacketId: number, elementNumber: number) {
    return this.http.delete(AppConfig.SAVE_JACKET_ELEMENT + '/jacket/' + jacketId + '/elementNumber/' + elementNumber).pipe(
      map(utils.extractData),catchError(utils.handleError),);
  }

}
