<div class="less-peding">
  <mat-card class="cust_table">
    <div fxLayout="row wrap" class="mb-10 cust_fields">
      <div fxFlex fxLayoutAlign="start center">
        <mat-card-title>{{ pageTitle }}</mat-card-title>
      </div>
    </div>
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="laborMasterDataSource"
      >
        <ng-container matColumnDef="labor">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="85"> Labor</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="85"> {{ element?.labor }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="burden">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="85"> Burden</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="85"> {{ element?.burden }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="materialOH">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="85"> Material OH</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="85"> {{ element?.materialOH }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="country">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="85"> Country</mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="85"> {{ element?.country }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="isObsolete">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="10"> Is Obsolete </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="10"> {{ element?.isObsolete | convertToYesNo }} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="5"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="5">            
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item (click)="editLaborMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="laborMasterColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: laborMasterColumns"></mat-row>
      </mat-table>
    </div>
    <div class="no-records" *ngIf="!laborMasterDataSource.data?.length">No data found</div>
  </mat-card>
</div>
