import { NgModule } from '@angular/core';
import { SalesDeptComponent } from './sales-dept.component';
import { RouterModule } from '@angular/router';
import { SalesDeptRoutes } from './sales-dept.route';
import { SharedModule } from '../shared/shared.module';
import { RfqSubmissionFormComponent } from './rfq-submission-form/rfq-submission-form.component';
import { OrderSubmissionFormComponent } from './order-submission-form/order-submission-form.component';

@NgModule({
  imports: [RouterModule.forChild(SalesDeptRoutes), SharedModule],
  declarations: [SalesDeptComponent, RfqSubmissionFormComponent, OrderSubmissionFormComponent]
})
export class SalesDeptModule {}
