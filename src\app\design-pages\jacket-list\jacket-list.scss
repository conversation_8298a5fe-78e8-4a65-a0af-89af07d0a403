
.customerPN-input {
  border: 1px solid black;
  width: 40px auto;
}

.save-button {
  position: absolute;
  left: 0px;
  top: 15px;
  display: flex;
  align-items: center;
}

.on-hold-row {
  background-color: #f5f5f5 !important;
  opacity: 0.6;
  pointer-events: none;

  mat-cell {
    color: #999 !important;
  }

  .customerPN-input {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    pointer-events: none;
  }

  mat-checkbox {
    pointer-events: none;
    opacity: 0.5;
  }

  button {
    pointer-events: none;
    opacity: 0.5;
  }

  a {
    pointer-events: none;
    color: #999 !important;
    text-decoration: none;
  }

  // Allow the action menu button to still work
  .mat-icon-button {
    pointer-events: auto;
    opacity: 1;
  }
}
