
.customerPN-input {
  border: 1px solid black;
  width: 40px auto;
}

.save-button {
  position: absolute;
  left: 0px;
  top: 15px;
  display: flex;
  align-items: center;
}

.on-hold-row {
  background-color: #f5f5f5 !important;
  opacity: 0.5;
  pointer-events: none;

  mat-cell {
    color: #999 !important;
  }

  .customerPN-input {
    background-color: #f5f5f5 !important;
    color: #999 !important;
    pointer-events: none;
  }

  mat-checkbox {
    pointer-events: none;
    opacity: 0.5;
  }

  button {
    pointer-events: none;
    opacity: 0.5;
  }

  a {
    pointer-events: none;
    color: #999 !important;
    text-decoration: none;
  }

  // Allow the action menu button to still work
  .mat-icon-button {
    pointer-events: auto;
    opacity: 1;
  }
}

/* Sync Header Styling */
.sync-header {
  cursor: pointer;
  color: #1976d2;
  font-weight: 500;
  text-decoration: underline;

  &:hover {
    color: #0d47a1;
    background-color: rgba(25, 118, 210, 0.1);
    padding: 2px 4px;
    border-radius: 4px;
  }

  &.disabled {
    cursor: not-allowed;
    color: #9e9e9e;
    text-decoration: none;

    &:hover {
      background-color: transparent;
      color: #9e9e9e;
    }
  }
}

.opacity-50 {
  opacity: 0.5;
}
