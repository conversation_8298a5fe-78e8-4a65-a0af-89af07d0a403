import { Component, Inject, Input, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material';
import { Subscription } from 'rxjs';
import { Values } from 'src/app/shared/constants/values.constants';
import { DesignEngineeringQuoteTracker } from '../../quote-tracker.model';
import { QuoteTrackerService } from '../../quote-tracker.service';
import { DesignEnggQuoteTrackerComponent } from '../design-engg-quote-tracker.component';

@Component({
  selector: 'app-design-engg-quote-tracker-row-colour',
  templateUrl: './design-engg-quote-tracker-row-colour.component.html',
  styleUrls: ['./design-engg-quote-tracker-row-colour.component.css']
})
export class DesignEnggQuoteTrackerRowColourComponent implements OnInit {
  highlightedColors = Values.highlightColors;
  highlightedRowColors = Values.highlightRowColors;
  rowHighlight: DesignEngineeringQuoteTracker = new DesignEngineeringQuoteTracker();
  subscription: Subscription = new Subscription();
  showLoader = false;
  quoteStatusId: number;
  @Input() designStatusId: number;
  @Input() id: number;
  @Input() primaryColorValue: string;
  quoteId: any;
  private _quotationId: any;
  private _quotationStatusId: any;
  selectedRawColor:string = this.primaryColorValue;
  constructor(
    private readonly quoteTrackerService: QuoteTrackerService,
    public dialogRef: MatDialogRef<DesignEnggQuoteTrackerComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
    this._quotationId = data.quotationId;
    this._quotationStatusId = data.designStatusId;
    if(data.row!==null && data.row!==undefined && data.row.colorValue!==null && data.row.colorValue!==undefined){
      this.selectedRawColor = data.row.colorValue;
    }
    if(this.selectedRawColor===undefined){
      this.selectedRawColor = 'default';
    }
  }

  ngOnInit() {

  }

  selectRow(colorValue: string) {
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.designRowHighlighting(this._quotationId, colorValue, this._quotationStatusId).subscribe(
        (res: DesignEngineeringQuoteTracker) => {
          if (res) {
            this.dialogRef.close(res);
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog(flag = false): void {
    this.dialogRef.close(flag);
  }

}
