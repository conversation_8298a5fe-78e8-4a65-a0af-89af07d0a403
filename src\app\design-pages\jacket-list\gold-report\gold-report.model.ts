export class GoldReportQuotations {
    constructor(
        public id?: number,
        public soNumber?: string,
        public customerName?: string,
        public shipBy?: string,
        public status?: string,
        public reportDate?: string
    ) { }
}

export class GoldReportQuotationPageable {
    constructor(
        public content?: GoldReportQuotations[],
        public totalElements?: number,
        public totalPages?: number,
        public number?: number
    ) { }
}

export class GoldReportList {
    constructor(
        public id?: number,
        public element?: string,
        public gold?: string,
        public heaterManPart?: string,
        public partNumber?: string,
        public quantity?: string,
        public revision?: string,
        public soNumber?: string,
        public soDetailId?: string,
    ) {}
}
export class GoldReport {
    constructor(
        public content?: GoldReportList[],
        public first?: boolean,
        public last?: boolean,
        public number?: number,
        public numberOfElements?: number,
        public pageable?: {},
        public size?: number,
        public sort?: {},
        public totalElements?: number,
        public totalPages?: number,
    ) { }
}

export class GoldReportPageable {
    constructor(
        public customerName?: string,
        public id?: number,
        public quotationId?: number,
        public reportDate?: string,
        public shipBy?: number,
        public soGoldReportList?: GoldReport,
        public soNumber?: string,
        public status?: string
    ) { }
}

export class AssemblyResponseDTO {
    constructor(
        public success?: boolean,
        public title?: string,
        public message?: string,
        public statusCode?: number,
        public data?: AssemblyData[]
    ) { }
}

export class AssemblyData {
    constructor(
        public soNumber?: string,
        public heaterManPart?: string,
        public partNumber?: string,
        public revision?: string
    ) {}
}
