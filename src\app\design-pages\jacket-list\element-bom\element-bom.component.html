<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Element & BoM
  <hr />
</h2>

<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <input
        matInput
        [matDatepicker]="date"
        placeholder="Date"
        name="elementBOMDate"
        [(ngModel)]="elementBom.elementBOMDate"
        #elementBOMDateDate="ngModel"
        autocomplete="off"
      />
      <mat-datepicker-toggle matSuffix [for]="date"></mat-datepicker-toggle>
      <mat-datepicker #date></mat-datepicker>
    </mat-form-field>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <div fxLayout="column wrap" fxFlex="49">
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <div class="col">
          <mat-checkbox name="materialBill" [(ngModel)]="elementBom.materialBill" #materialBillCheckBox="ngModel" color="warn"
            >Bill of Materials Completed
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="operationBill" [(ngModel)]="elementBom.operationBill" #operationBillCheckBox="ngModel"
            >Bill of Opretions Completed
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="laborHours" [(ngModel)]="elementBom.laborHours" #laborHoursCheckBox="ngModel"
            >Labor Hours Entered
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="labelEntered" [(ngModel)]="elementBom.labelEntered" #labelEnteredCheckBox="ngModel"
            >Labels Entered
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox
            color="warn"
            name="customerSpecificLabel"
            [(ngModel)]="elementBom.customerSpecificLabel"
            #customerSpecificLabelCheckBox="ngModel"
            >Customer Specific Label
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="newLabelCreated" [(ngModel)]="elementBom.newLabelCreated" #newLabelCreatedCheckBox="ngModel"
            >New Label Created
          </mat-checkbox>
        </div>
      </div>
    </div>
    <div fxLayout="column wrap" fxFlex="49">
      <div fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
        <div class="col">
          <mat-checkbox color="warn" name="efNoConcerns" [(ngModel)]="elementBom.efNoConcerns" #efNoConcernsCheckBox="ngModel"
            >EF-No Concerns
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="wInNoConcerns" [(ngModel)]="elementBom.wInNoConcerns" #wInNoConcernsCheckBox="ngModel"
            >W/in - No Concerns
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="wIn2NoConcerns" [(ngModel)]="elementBom.wIn2NoConcerns" #wIn2NoConcernsCheckBox="ngModel"
            >W/in<sup>2</sup> - No Concerns
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox
            color="warn"
            name="tempRatingNoConcerns"
            [(ngModel)]="elementBom.tempRatingNoConcerns"
            #tempRatingNoConcernsCheckBox="ngModel"
            >Temp. Rating - No Concerns
          </mat-checkbox>
        </div>
        <div class="col">
          <mat-checkbox color="warn" name="tsNotInterfere" [(ngModel)]="elementBom.tsNotInterfere" #tsNotInterfereCheckBox="ngModel"
            >T/S Does Not Interfere (or N/A)
          </mat-checkbox>
        </div>
      </div>
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <input
        matInput
        placeholder="Element Details"
        name="elementDetails"
        [(ngModel)]="elementBom.elementDetails"
        #elementDetailsInput="ngModel"
      />
    </mat-form-field>
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <input matInput placeholder="Comments" name="comments" [(ngModel)]="elementBom.comments" #commentsInput="ngModel" />
    </mat-form-field>
  </div>
</mat-dialog-content>
<hr />
<mat-dialog-actions>
  <button mat-raised-button color="warn" type="submit" (click)="saveElementBom()">Save</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button type="submit" (click)="undoElementBOM()">Undo Signature</button>
</mat-dialog-actions>
