
import {catchError, map} from 'rxjs/operators';
import { AppConfig } from './../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';



@Injectable({ providedIn: 'root' })
export class LevelOneReviewService {

    constructor(private http: HttpClient) { }

    saveLevelOneReview(review) {
        return this.http.post(AppConfig.LEVEL_ONE_REVIEW_SAVE, review).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }

    updateLevelOneReview(review, jacketId) {
      return this.http.delete(AppConfig.LEVEL_ONE_REVIEW_UPDATE + jacketId, review).pipe(
          map(utils.extractData),
          catchError(utils.handleError),);
  }

    getLevelOneReviewByJacketId(jacketId) {
        return this.http.get(AppConfig.LEVEL_ONE_REVIEW_SAVE + '/jacket/' + jacketId).pipe(
            map(utils.extractData),
            catchError(utils.handleError),);
    }
}
