import { DatePipe } from '@angular/common';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { Values } from 'src/app/shared/constants/values.constants';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { SweetAlertService } from 'src/app/shared/service/sweet-alert.service';
import { JacketStatus } from '../jacket-list.model';
import { UndoResponse } from '../pattern/pattern.model';
import { ChecklistFinalReviewDTO, FinalReviewModel } from './final-review.model';
import { FinalReviewService } from './final-review.service';

@Component({
  selector: 'sfl-final-review',
  templateUrl: './final-review.component.html'
})
export class FinalReviewComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  finalReview = new FinalReviewModel();
  checklistFinalReviewDTO = new ChecklistFinalReviewDTO();
  finalReviews: FinalReviewModel[];
  jacketList = new Array<number>();
  jacketId: number;
  quotationId: number;
  showLoader: boolean;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  undoResponseDTO: UndoResponse = new UndoResponse();
  JacketStatuses: JacketStatus = new JacketStatus();

  constructor(
    public readonly dialogRef: MatDialogRef<FinalReviewComponent>,
    private readonly datePipe: DatePipe,
    private readonly finalReviewService: FinalReviewService,
    private readonly sweetAlertService: SweetAlertService,
    @Inject(MAT_DIALOG_DATA) data
  ) {
    this.jacketList = data.jacketList;
    this.jacketId = data.jacketId;
    this.quotationId = data.quotationId;
  }

  ngOnInit() {
    this.finalReviews = new Array();
    this.finalReview.finalReviewDate = new Date();
    if (this.jacketId) {
      this.getFinalReviewByJacketId(this.jacketId);
    }
    this.checklistFinalReviewDTO.quotationID = this.quotationId;
  }

  getFinalReviewByJacketId(jacketId) {
    this.subscription.add(
      this.finalReviewService.getFinalReviewByJacketId(jacketId).subscribe((res: FinalReviewModel) => {
        if (res) {
          this.finalReview = res;
        }
      })
    );
  }

  saveFinalReview() {
    this.showLoader = true;
    this.finalReview.finalReviewDate = this.datePipe.transform(this.finalReview.finalReviewDate, Values.dateFormat.format);
    if (this.jacketList && this.jacketList.length > 0) {
      this.jacketList.forEach(ele => {
        const finalReviewConst = Object.assign({}, this.finalReview);
        finalReviewConst.jacketId = ele;
        finalReviewConst.checked = true;
        this.checklistFinalReviewDTO.finalReviewDTOs.push(finalReviewConst);
      });
    } else {
      if (this.jacketId) {
        this.finalReview.jacketId = this.jacketId;
        this.checklistFinalReviewDTO.finalReviewDTOs.push(this.finalReview);
      }
    }

    if (this.checklistFinalReviewDTO.finalReviewDTOs.length > 0) {
      this.subscription.add(
        this.finalReviewService.saveFinalReview(this.checklistFinalReviewDTO).subscribe(
          res => {
            this.checklistFinalReviewDTO = new ChecklistFinalReviewDTO();
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.SAVE, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    } else {
      this.showLoader = false;
    }
  }

  async undoFinalReview() {
    if (await this.sweetAlertService.warningWhenUndoSignature()) {
      this.subscription.add(
        this.finalReviewService.updateFinalReview(this.checklistFinalReviewDTO, this.jacketId).subscribe(
          res => {
            this.showLoader = false;
            this.closeDialog(this.JacketStatuses.UNDO, this.jacketId);
          },
          () => (this.showLoader = false)
        )
      );
    }
  }

  closeDialog(updated?: string, jacketId?: number): void {
    this.undoResponseDTO.status = updated;
    this.undoResponseDTO.id = jacketId;
    this.dialogRef.close(this.undoResponseDTO);
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
