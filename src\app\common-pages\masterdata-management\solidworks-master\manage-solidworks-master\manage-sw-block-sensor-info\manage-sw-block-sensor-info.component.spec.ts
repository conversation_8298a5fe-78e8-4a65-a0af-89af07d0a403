import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageSwBlockSensorInfoComponent } from './manage-sw-block-sensor-info.component';

describe('ManageSwBlockSensorInfoComponent', () => {
  let component: ManageSwBlockSensorInfoComponent;
  let fixture: ComponentFixture<ManageSwBlockSensorInfoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageSwBlockSensorInfoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageSwBlockSensorInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
