<mat-toolbar class="main-header bg-dark">
  <button (click)="toggleSidenav.emit()" mat-icon-button>
    <mat-icon>menu</mat-icon>
  </button>
  <div class="branding">
    <div (click)="gotDashboard()" class="logo"></div>
  </div>
  <div fxLayoutAlign="center">
    <div fxLayout="row">
      <div class="search-bar">
        <form class="search-form" #designQuotationFilterForm="ngForm" [ngStyle.xs]="{ display: 'none' }">
          <mat-icon>search</mat-icon>
          <input type="text" placeholder="Search for Quote # or Sales Order" name="quotation" [(ngModel)]="quotation" [matAutocomplete]="auto"
            (ngModelChange)="quotationFilter($event)" />
          <div class="searchButton">
            <mat-icon (click)="showFilter()">keyboard_arrow_down</mat-icon>
          </div>
          <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="getQuotNumberFn" (optionSelected)="selectQuotation($event.option.value)">
            <mat-option *ngFor="let option of quotationObservable$ | async" [value]="option" [routerLink]="['/design-eng/jacket-list']" [queryParams]="{ quotId: option?.id }">
              <span>
                Quotation Number: <strong>{{ option?.quotationNumber }} </strong>, SO Number:
                <strong>{{ option?.salesOrderNumber }}</strong>
              </span>
            </mat-option>
          </mat-autocomplete>&nbsp;
        </form>
        <div *ngIf="filter" class="filter-bar">
          <mat-card>
            <div fxLayout="column">
              <div fxLayout="row wrap" fxLayoutAlign="space-between">
                <mat-form-field fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <mat-select placeholder="Status" [(ngModel)]="quotationSearchFilter.status" name="status">
                    <mat-option *ngFor="let allstatus of status" [value]="allstatus.status">{{ allstatus?.status }} </mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field fxFlex.gt-lg="48" fxFlex.gt-md="48" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                  <mat-select placeholder="Associate" [(ngModel)]="quotationSearchFilter.salesAssociateName" name="salesAssociateName">
                    <mat-option *ngFor="let salesass of salesassociate" [value]="salesass.firstName + ' ' + salesass.lastName">{{ salesass?.firstName }} {{ salesass?.lastName }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100" fxFlex.gt-sm="100" fxFlex.gt-xs="100">
                <input matInput placeholder="Customer" [(ngModel)]="quotationSearchFilter.customerName" name="customerName" />
              </mat-form-field>
              <mat-dialog-actions fxLayoutAlign="end">
                <button mat-button (click)="reset()">Reset</button>&nbsp;
                <button mat-raised-button color="warn" type="submit" (click)="search(designQuotationFilterForm)">Search</button>
              </mat-dialog-actions>
            </div>
          </mat-card>
        </div>
      </div>
    </div>
  </div>
  <samp fxFlex></samp>
  <button *ngIf="!isFullScreen" (click)="fullScreenToggle()" mat-icon-button>
    <mat-icon>fullscreen</mat-icon>
  </button>
  <button *ngIf="isFullScreen" (click)="fullScreenToggle()" mat-icon-button>
    <mat-icon>fullscreen_exit</mat-icon>
  </button>
  <button [matMenuTriggerFor]="user" mat-icon-button class="ml-xs">
    <mat-icon>person</mat-icon>
  </button>
  <mat-menu #user="matMenu" x-position="before">
    <button *ngIf="userRole!=='ROLE_SALES'" mat-menu-item id="switchtosuperadmin" (click)="switchToSuperAdmin()">
      <mat-icon>supervisor_account</mat-icon>
      Super Admin
    </button>
    <button mat-menu-item id="switchtoappadmin" (click)="switchToAppAdmin()">
      <mat-icon>supervisor_account</mat-icon>
      Switch to App.Eng
    </button>
    <button *ngIf="userRole!=='ROLE_SALES'" mat-menu-item id="masterdata" (click)="switchToMasterdataManagement()">
      <mat-icon>account_balance</mat-icon>
      Master Data Management
    </button>
    <button mat-menu-item id="switchtoquotetracker" (click)="switchToQuoteTracker()">
      <mat-icon>timeline</mat-icon>
      Trackers
    </button>
    <a href="/view-jacket-reference" routerLink="/view-jacket-reference" target="_blank">
      <button mat-menu-item id="switchtojacketreference">
        <mat-icon>timeline</mat-icon>
        BOM Reference
      </button>
    </a>
    <button mat-menu-item id="logout" (click)="logout()">
      <mat-icon>exit_to_app</mat-icon>
      Log Out
    </button>
  </mat-menu>
</mat-toolbar>
