import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { AppConfig } from 'src/app/app.config';
import { utils } from 'src/app/shared/helpers/app.helper';

export interface BadgeItem {
  type: string;
  value: string;
}

export interface ChildrenItems {
  state: string;
  name: string;
  type: string;
}

export interface Menu {
  state: string;
  name: string;
  type: string;
  icon: string;
  badge?: BadgeItem[];
  children?: ChildrenItems[];
}


const MENUITEMS = [
  {
    state: 'design-eng/dashboard',
    name: 'Dashboard',
    type: 'link',
    icon: 'dashboard'
  }
];

@Injectable()
export class DesignEngNavbarService {
  sidePanelOpened: boolean;
  getAll(): Menu[] {
    return MENUITEMS;
  }

  constructor(private readonly http: HttpClient) { }


  add(menu) {
    MENUITEMS.push(menu);
  }

  getTopFiveSo(userId, type) {
    const queryParams = new HttpParams().append('type', type);
    return this.http
      .post(AppConfig.GET_DESIGN_TOP_SO + userId,queryParams).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

}
