<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div class="filter less-peding final-review-screen" fxLayout="row wrap">
  <!-- Order Information -->

  <div fxLayout="column" fxFlex.gt-lg="40" fxFlex.gt-md="40" fxFlex.gt-xs="100">
    <div fxLayout="row">
      <mat-card fxFlex="100">
        <div fxLayout="row wrap">
          <mat-card-title>Order Information</mat-card-title>
        </div>
        <hr/>
        <div class="mb-10"></div>
        <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
          <div class="lbl-view">
            <label class="lbl">Sales Order Number:</label>
            <label>{{
                type === 'Usa'
                  ? finalReviewDTO?.usaFinalDesignReviewDTO?.salesOrderNumber
                  : type === 'Costa Rica' ? finalReviewDTO.costaRicaFinalDesignReviewDTO.salesOrderNumber
                    : finalReviewDTO?.vietnamFinalDesignReviewDTO?.salesOrderNumber
              }}</label>
          </div>
        </div>
        <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
          <div class="lbl-view">
            <label class="lbl">Part Number:</label>
            <label>{{
                type === 'Usa'
                  ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.partNum
                  : type === 'Costa Rica'
                    ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.partNum
                    : finalReviewDTO?.vietnamFinalDesignReviewDTO?.jacket?.partNum
              }}</label>
          </div>
        </div>
      </mat-card>
    </div>

    <!-- Part Information -->
    <div fxLayout="row">
      <mat-card fxFlex="100">
        <div fxLayout="row wrap">
          <mat-card-title>Part</mat-card-title>
        </div>
        <hr/>
        <div class="sfl-block-loader" *ngIf="partInfoLoader; else partInfo">
          <mat-progress-spinner class="" [mode]="mode" [color]="color"
                                [diameter]="spinnerDiameter"></mat-progress-spinner>
        </div>
        <ng-template #partInfo>
          <div class="mb-10"></div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Description</label>
              <input
                class="effect quantity"
                matInput
                [ngModel]="finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.partDescription"
                (ngModelChange)="finalReviewDTO.usaFinalDesignReviewDTO.jacket.partDescription = $event"
                (change)="updateJacket()"
                *ngIf="type === 'Usa' && !noJacketUSA"
              />
              <input
                class="effect quantity"
                matInput
                [ngModel]="finalReviewDTO?.vietnamFinalDesignReviewDTO?.jacket?.partDescription"
                (ngModelChange)="finalReviewDTO.vietnamFinalDesignReviewDTO.jacket.partDescription = $event"
                (change)="updateJacket()"
                *ngIf="type === 'Vietnam' && !noJacketVietnam"
              />
              <input
                class="effect quantity"
                matInput
                [ngModel]="finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.partDescription"
                (ngModelChange)="finalReviewDTO.costaRicaFinalDesignReviewDTO.jacket.partDescription = $event"
                (change)="updateJacket()"
                *ngIf="type === 'Costa Rica' && !noJacketVietnam"
              />
            </div>
            <div class="lbl-view">
              <label class="lbl">Net Price</label>
              <label>{{
                  type === 'Usa'
                    ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.netPrice
                    : type === 'Costa Rica'
                      ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.netPrice
                      : finalReviewDTO?.vietnamFinalDesignReviewDTO?.jacket?.netPrice
                }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Type</label>
              <label>{{
                  type === 'Usa'
                    ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.type
                    : type === 'Costa Rica'
                      ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.type
                      : finalReviewDTO?.vietnamFinalDesignReviewDTO.jacket?.type
                }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">List Price</label>
              <label>{{
                  type === 'Usa'
                    ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.unitPrice
                    : type === 'Costa Rica'
                      ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.unitPrice
                      : finalReviewDTO?.vietnamFinalDesignReviewDTO.jacket?.unitPrice
                }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Group</label>
              <label>{{
                  type === 'Usa'
                    ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.description
                    : type === 'Costa Rica'
                      ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.description
                      : finalReviewDTO?.vietnamFinalDesignReviewDTO?.jacket?.description
                }}</label>
            </div>
            <div class="lbl-view">
              <label class="lbl">Rev</label>
              <label>{{
                  type === 'Usa'
                    ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.revisionName
                    : type === 'Costa Rica'
                      ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.revisionName
                      : finalReviewDTO?.vietnamFinalDesignReviewDTO?.jacket?.revisionName
                }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Class</label>
              <label>{{
                  type === 'Usa'
                    ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.classID
                    : type === 'Costa Rica'
                      ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.classID
                      : finalReviewDTO?.vietnamFinalDesignReviewDTO?.jacket?.classID
                }}</label>
            </div>
          </div>
          <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
            <div class="lbl-view">
              <label class="lbl">Internal Cross References</label>
              <label>{{
                  type === 'Usa'
                    ? finalReviewDTO?.usaFinalDesignReviewDTO?.jacket?.internalCrossReference
                    : type === 'Costa Rica'
                      ? finalReviewDTO?.costaRicaFinalDesignReviewDTO?.jacket?.internalCrossReference
                      : finalReviewDTO?.vietnamFinalDesignReviewDTO?.jacket?.internalCrossReference
                }}</label>
            </div>
          </div>
        </ng-template>
      </mat-card>
    </div>

    <!-- Operations USA-->
    <mat-card fxFlex="100" *ngIf="type === 'Usa'">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Operations</mat-card-title>
          <div fxLayoutAlign="end center" class="addIcon">
            <mat-icon class="open-doc" matTooltip="Add Operation" (click)="addOperations(1)">add</mat-icon>
          </div>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="operationsDataSourceUSA">
            <ng-container matColumnDef="sequence" let>
              <mat-header-cell *matHeaderCellDef fxFlex="18"> Seq</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="18">
                {{ operation?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="operation">
              <mat-header-cell *matHeaderCellDef fxFlex="22"> Operation</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="22">
                {{ operation?.operation }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="opNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Code</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.opNumber"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'opNumber', 'Usa')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="setupHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Setup Hours</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.setupHours"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'setupHours', 'Usa')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="prodHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Prod Hrs</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.prodHours"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'prodHours', 'Usa')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="action">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="10">
                <mat-icon class="open-doc" matTooltip="Delete Operation" (click)="deleteOperation(operation, 'Usa')">
                  delete
                </mat-icon>
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="operationsColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: operationsColumns"
                     [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
          </mat-table>
          <div class="no-records" *ngIf="!operationsDataSourceUSA?.data?.length">No data found</div>
        </div>
      </div>
    </mat-card>
    <!-- Operations Vietnam-->
    <mat-card fxFlex="100" *ngIf="type === 'Vietnam'">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Operations</mat-card-title>
          <div fxLayoutAlign="end center">
            <mat-icon class="open-doc" matTooltip="Add Operation" (click)="addOperations(1)">add</mat-icon>
          </div>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="operationsDataSourceVietnam">
            <ng-container matColumnDef="sequence" let>
              <mat-header-cell *matHeaderCellDef fxFlex="18"> Seq</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="18">
                {{ operation?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="operation">
              <mat-header-cell *matHeaderCellDef fxFlex="22"> Operation</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="22">
                {{ operation?.operation }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="opNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Code</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.opNumber"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'opNumber', 'Vietnam')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="setupHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Setup Hours</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.setupHours"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'setupHours', 'Vietnam')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="prodHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Prod Hrs</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.prodHours"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'prodHours', 'Vietnam')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="action">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="10">
                <mat-icon class="open-doc" matTooltip="Delete Operation" (click)="deleteOperation(operation, 'Vietnam')"
                >delete
                </mat-icon>
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="operationsColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: operationsColumns"
                     [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
          </mat-table>
          <div class="no-records" *ngIf="!operationsDataSourceUSA?.data?.length">No data found</div>
        </div>
      </div>
    </mat-card>
    <mat-card fxFlex="100" *ngIf="type === 'Costa Rica'">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Operations</mat-card-title>
          <div fxLayoutAlign="end center">
            <mat-icon class="open-doc" matTooltip="Add Operation" (click)="addOperations(1)">add</mat-icon>
          </div>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="operationsDataSourceCostaRica">
            <ng-container matColumnDef="sequence" let>
              <mat-header-cell *matHeaderCellDef fxFlex="18"> Seq</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="18">
                {{ operation?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="operation">
              <mat-header-cell *matHeaderCellDef fxFlex="22"> Operation</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="22">
                {{ operation?.operation }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="opNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Code</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.opNumber"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'opNumber', 'Costa Rica')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="setupHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Setup Hours</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.setupHours"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'setupHours', 'Costa Rica')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="prodHours">
              <mat-header-cell *matHeaderCellDef fxFlex="20"> Prod Hrs</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="20">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="operation.prodHours"
                  sflIsDecimal
                  type="number"
                  (change)="updateOperation(operation, 'prodHours', 'Costa Rica')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="action">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
              <mat-cell *matCellDef="let operation" fxFlex="10">
                <mat-icon class="open-doc" matTooltip="Delete Operation"
                          (click)="deleteOperation(operation, 'Costa Rica')"
                >delete
                </mat-icon>
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="operationsColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: operationsColumns"
                     [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
          </mat-table>
          <div class="no-records" *ngIf="!operationsDataSourceCostaRica?.data?.length">No data found</div>
        </div>
      </div>
    </mat-card>
  </div>

  <div fxLayout="column" fxFlex.gt-lg="60" fxFlex.gt-md="60" fxFlex.gt-xs="100">
    <div fxLayout="row">
      <mat-card fxFlex="100">
        <div fxLayout="row wrap">
          <mat-card-title>Made In</mat-card-title>
        </div>
        <hr/>
        <div class="mb-10" fxLayout="row wrap" fxLayoutAlign="space-between">
          <mat-form-field class="mt-5">
            <mat-select placeholder="Made In" [(ngModel)]="type" (selectionChange)="onMadeInChanged()">
              <mat-option value="Usa"> USA</mat-option>
              <mat-option value="Vietnam"> Vietnam</mat-option>
              <mat-option value="Costa Rica"> Costa Rica</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </mat-card>
    </div>
    <!-- Materials USA -->
    <mat-card fxFlex="100" *ngIf="type === 'Usa'">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Materials</mat-card-title>
          <div fxLayoutAlign="end center" class="addIcon">
            <mat-icon class="open-doc" matTooltip="Add Material" (click)="addMaterial(1)">add</mat-icon>
          </div>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="materialsDataSourceUSA">
            <ng-container matColumnDef="sequence">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Seq</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="partNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="15">
                {{ material.partNumber }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-header-cell *matHeaderCellDef fxFlex="35"> Description</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="35">
                {{ material?.description }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="quantity">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Qty</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="material.quantity"
                  type="number"
                  (change)="updateMaterial(material, 'quantity', 'Usa')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> UOM</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.uom }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOp">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Rel Op</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material.relOp }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="action">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                <mat-icon class="open-doc" matTooltip="Delete Material" (click)="deleteMaterial(material, 'Usa')">
                  delete
                </mat-icon>
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="materialsColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: materialsColumns"
                     [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
          </mat-table>
          <div class="no-records" *ngIf="!materialsDataSourceUSA?.data?.length">No data found</div>
        </div>
      </div>
    </mat-card>

    <!-- Material Vietnam -->
    <mat-card fxFlex="100" *ngIf="type === 'Vietnam'">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Materials</mat-card-title>
          <div fxLayoutAlign="end center">
            <mat-icon class="open-doc" matTooltip="Add Materials" (click)="addMaterial(1)">add</mat-icon>
          </div>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="materialsDataSourceVietnam">
            <ng-container matColumnDef="sequence">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Seq</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="partNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="15">
                {{ material.partNumber }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-header-cell *matHeaderCellDef fxFlex="35"> Description</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="35">
                {{ material?.description }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="quantity">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Qty</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="material.quantity"
                  type="number"
                  (change)="updateMaterial(material, 'quantity', 'Vietnam')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> UOM</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.uom }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOp">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Rel Op</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material.relOp }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="action">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                <mat-icon class="open-doc" matTooltip="Delete Material" (click)="deleteMaterial(material, 'Vietnam')">
                  delete
                </mat-icon>
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="materialsColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: materialsColumns"
                     [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
          </mat-table>
          <div class="no-records" *ngIf="!materialsDataSourceVietnam?.data?.length">No data found</div>
        </div>
      </div>
    </mat-card>

    <mat-card fxFlex="100" *ngIf="type === 'Costa Rica'">
      <div class="highlight-mat-table">
        <div fxLayout="row wrap">
          <mat-card-title>Materials</mat-card-title>
          <div fxLayoutAlign="end center">
            <mat-icon class="open-doc" matTooltip="Add Materials" (click)="addMaterial(1)">add</mat-icon>
          </div>
        </div>

        <!-- Mat Table -->
        <div class="cust_table">
          <mat-table [dataSource]="materialsDataSourceCostaRica">
            <ng-container matColumnDef="sequence">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Seq</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.sequence }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="partNumber">
              <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="15">
                {{ material.partNumber }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="description">
              <mat-header-cell *matHeaderCellDef fxFlex="35"> Description</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="35">
                {{ material?.description }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="quantity">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Qty</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                <input
                  class="effect quantity"
                  matInput
                  [(ngModel)]="material.quantity"
                  type="number"
                  (change)="updateMaterial(material, 'quantity', 'Costa Rica')"
                />
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="uom">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> UOM</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material?.uom }}
              </mat-cell>
            </ng-container>
            <ng-container matColumnDef="relOp">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Rel Op</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                {{ material.relOp }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="action">
              <mat-header-cell *matHeaderCellDef fxFlex="10"> Action</mat-header-cell>
              <mat-cell *matCellDef="let material" fxFlex="10">
                <mat-icon class="open-doc" matTooltip="Delete Material"
                          (click)="deleteMaterial(material, 'Costa Rica')">delete
                </mat-icon>
              </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="materialsColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: materialsColumns"
                     [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
          </mat-table>
          <div class="no-records" *ngIf="!materialsDataSourceCostaRica?.data?.length">No data found</div>
        </div>
      </div>
    </mat-card>
  </div>


  <!-- Labels USA -->

  <mat-card fxFlex="100">
    <div class="highlight-mat-table">
      <div fxLayout="row">
        <mat-card-title>Labels</mat-card-title>
        <div fxLayoutAlign="end center" class="addIcon">
          <mat-icon class="open-doc" matTooltip="Add Label" (click)="addLabel(1)">add</mat-icon>
        </div>
      </div>

      <!-- Mat Table USA -->
      <div class="cust_table" *ngIf="type === 'Usa'">
        <mat-table [dataSource]="labelsDataSourceUSA">
          <ng-container matColumnDef="number">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.number" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>

          <ng-container matColumnDef="format">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Format</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.format" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="volts">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Volts</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.volts" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="watts">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Watts</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.watts" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="amps">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> AMPS</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.amps" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="phase">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Phase</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.phase" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="size">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Size</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.size" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="width">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Width</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.width" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="length">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Length</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.length" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csv">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> CSV</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.csv" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csa">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> CSA</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.csa" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhlv">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> MHLV</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.mhlv" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhla">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> MHLA</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.mhla" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="tempRange">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Temp Range</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.tempRange" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="lowT">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> LowT</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.lowT" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="highT">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> HighT</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              <input class="label" matInput [(ngModel)]="label.highT" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="modelNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Model Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.modelNumber" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open1">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open1</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.open1" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open2">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open2</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.open2" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open3">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open3</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="label" matInput [(ngModel)]="label.open3" (change)="updatelabelConfig()"/>
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="effect quantity" matInput [(ngModel)]="label.partNumber"
                     (change)="updateLabel(label, 'partNumber')"/>
            </mat-cell>
          </ng-container>

          <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef fxFlex="5"> Action</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="5">
              <mat-icon class="open-doc" matTooltip="Delete Label" (click)="deleteLabel(label)">delete</mat-icon>
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="labelsColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: labelsColumns"
                   [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
        </mat-table>
        <div class="no-records" *ngIf="!labelsDataSourceUSA?.data?.length">No data found</div>
      </div>

      <!-- Mat Table Vietnam -->
      <div class="cust_table" *ngIf="type === 'Vietnam'">
        <mat-table [dataSource]="labelsDataSourceVietnam">
          <ng-container matColumnDef="number">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.number }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="format">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Format</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.format }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="volts">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Volts</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.volts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="watts">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Watts</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.watts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="amps">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> AMPS</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.amps }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="phase">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Phase</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.phase }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="size">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Size</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.size }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="width">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Width</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.width }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="length">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Length</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.length }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csv">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> CSV</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.csv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csa">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> CSA</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.csa }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhlv">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> MHLV</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.mhlv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhla">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> MHLA</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.mhla }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="tempRange">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Temp Range</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.tempRange }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="lowT">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> LowT</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.lowT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="highT">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> HighT</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.highT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="modelNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Model Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.modelNumber }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open1">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open1</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open1 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open2">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open2</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open2 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open3">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open3</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open3 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="effect quantity" matInput [(ngModel)]="label.partNumber"
                     (change)="updateLabel(label, 'partNumber')"/>
            </mat-cell>
          </ng-container>

          <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef fxFlex="5"> Action</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="5">
              <mat-icon class="open-doc" matTooltip="Delete Label" (click)="deleteLabel(label)">delete</mat-icon>
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="labelsColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: labelsColumns"
                   [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
        </mat-table>
        <div class="no-records" *ngIf="!labelsDataSourceVietnam?.data?.length">No data found</div>
      </div>

      <div class="cust_table" *ngIf="type === 'Costa Rica'">
        <mat-table [dataSource]="labelsDataSourceCostaRica">
          <ng-container matColumnDef="number">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.number }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="format">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Format</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.format }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="volts">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Volts</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.volts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="watts">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> Watts</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.watts }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="amps">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> AMPS</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.amps }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="phase">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Phase</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.phase }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="size">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Size</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.size }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="width">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Width</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.width }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="length">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Length</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.length }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csv">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> CSV</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.csv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="csa">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> CSA</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.csa }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhlv">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> MHLV</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.mhlv }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="mhla">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> MHLA</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.mhla }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="tempRange">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Temp Range</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.tempRange }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="lowT">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> LowT</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.lowT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="highT">
            <mat-header-cell *matHeaderCellDef fxFlex="10"> HighT</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="10">
              {{ label?.highT }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="modelNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Model Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.modelNumber }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open1">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open1</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open1 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open2">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open2</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open2 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="open3">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Open3</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              {{ label?.open3 }}
            </mat-cell>
          </ng-container>
          <ng-container matColumnDef="partNumber">
            <mat-header-cell *matHeaderCellDef fxFlex="15"> Part Number</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="15">
              <input class="effect quantity" matInput [(ngModel)]="label.partNumber"
                     (change)="updateLabel(label, 'partNumber')"/>
            </mat-cell>
          </ng-container>

          <ng-container matColumnDef="action">
            <mat-header-cell *matHeaderCellDef fxFlex="5"> Action</mat-header-cell>
            <mat-cell *matCellDef="let label" fxFlex="5">
              <mat-icon class="open-doc" matTooltip="Delete Label" (click)="deleteLabel(label)">delete</mat-icon>
            </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="labelsColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: labelsColumns"
                   [ngClass]="{ 'sfl-hide-row': row.status === 'Deleted' }"></mat-row>
        </mat-table>
        <div class="no-records" *ngIf="!labelsDataSourceCostaRica?.data?.length">No data found</div>
      </div>
    </div>
  </mat-card>


  <mat-card fxFlex="100">
    <mat-action-row>
      <!-- USA Submit -->
      <ng-container *ngIf="type === 'Usa'">
        <button
          mat-raised-button
          color="warn"
          type="button"
          matTooltip="Send Changes To Epicore"
          [disabled]="partNumberMissing || noJacketUSA"
          (click)="sendToEpicore()"
        >
          Send Changes To Epicore
        </button>
        <mat-hint *ngIf="noJacketUSA"><p class="no-records">Jacket is not available for USA so you can not sync to
          Epicore</p></mat-hint>
        <div class="final-review-bom-sync-status" *ngIf="syncInProcess === 'completed' && !noJacketUSA">
            <span
              class="bom-sync-status open-doc"
              matTooltip="Click here to check status"
              (click)="openDMTLog()"
              [ngClass]="{ 'sync-error': !isSyncSuccess }"
            >
              {{ syncStatus }}
            </span>
        </div>
      </ng-container>
      <!-- Vietnam Submit -->
      <ng-container *ngIf="type === 'Vietnam'">
        <button
          mat-raised-button
          color="warn"
          type="button"
          matTooltip="Send Changes To Epicore"
          [disabled]="partNumberMissing || noJacketVietnam"
          (click)="sendToEpicore()"
        >
          Send Changes To Epicore
        </button>
        <mat-hint *ngIf="noJacketVietnam"
        ><p class="no-records">Jacket is not available for VIETNAM so you can not sync to Epicore</p></mat-hint
        >
        <div class="final-review-bom-sync-status" *ngIf="syncInProcess === 'completed' && !noJacketVietnam">
            <span
              class="bom-sync-status open-doc"
              matTooltip="Click here to check status"
              (click)="openDMTLog()"
              [ngClass]="{ 'sync-error': !isSyncSuccess }"
            >
              {{ syncStatus }}
            </span>
        </div>
      </ng-container>

      <ng-container *ngIf="type === 'Costa Rica'">
        <button
          mat-raised-button
          color="warn"
          type="button"
          matTooltip="Send Changes To Epicore"
          [disabled]="partNumberMissing || noJacketCostaRica"
          (click)="sendToEpicore()"
        >
          Send Changes To Epicore
        </button>
        <mat-hint *ngIf="noJacketCostaRica"
        ><p class="no-records">Jacket is not available for Costa Rica so you can not sync to Epicore</p></mat-hint
        >
        <div class="final-review-bom-sync-status" *ngIf="syncInProcess === 'completed' && !noJacketCostaRica">
            <span
              class="bom-sync-status open-doc"
              matTooltip="Click here to check status"
              (click)="openDMTLog()"
              [ngClass]="{ 'sync-error': !isSyncSuccess }"
            >
              {{ syncStatus }}
            </span>
        </div>
      </ng-container>
    </mat-action-row>
  </mat-card>
</div>
