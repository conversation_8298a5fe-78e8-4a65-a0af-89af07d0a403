import { Component, OnInit, Inject, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { SensorConnectorsAndTypesMaster } from '../../masterdata-management.model';
import { Variable } from 'src/app/shared/constants/Variable.constants';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MasterdataManagementService } from '../../masterdata-management.service';

@Component({
  selector: 'sfl-manage-sensor-control-types',
  templateUrl: './manage-sensor-control-types.component.html'
})
export class ManageSensorControlTypesComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  sensorControlType: SensorConnectorsAndTypesMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  constructor(
    public readonly dialogRef: MatDialogRef<ManageSensorControlTypesComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService
  ) {
    this.sensorControlType = data;
  }

  ngOnInit() {
    this.sensorControlType = this.sensorControlType.id ? Object.assign({}, this.sensorControlType) : new SensorConnectorsAndTypesMaster();
    this.sensorControlType.id ? (this.title = 'Update Sensor Control Type') : (this.title = 'Add Sensor Control Type');
  }

  updateSensrControlType() {
    this.showLoader = true;
    if (this.sensorControlType.id) {
      this.subscription.add(
        this.masterDataService.updateSensorControlTypes(this.sensorControlType).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addSensorControlTypes(this.sensorControlType).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
