<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #leadTypeForm="ngForm" (ngSubmit)="updateLeadType()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="32" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Lead Name" [(ngModel)]="leadType.leadName" name="leadName" #leadNameInput="ngModel" required />
        </mat-form-field>
        <div *ngIf="leadNameInput.touched && leadNameInput.invalid">
          <small class="mat-text-warn" *ngIf="leadNameInput?.errors.required">Lead Name is required.</small>
        </div>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Part Number"
            [(ngModel)]="leadType.partNumber"
            name="partNumber"
            #partNumberInput="ngModel"
            required
            sflNoWhiteSpaces
          />
        </mat-form-field>
        <div *ngIf="partNumberInput.touched && partNumberInput.invalid">
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors.required">Lead type part number is required.</small>
          <small class="mat-text-warn" *ngIf="partNumberInput?.errors?.whitespace && !partNumberInput?.errors?.required">
            Invalid part number.
          </small>
        </div>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Max Temperature"
            [(ngModel)]="leadType.maxTemp"
            name="maxTemp"
            #maxTempInput="ngModel"
            sflIsDecimal
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <div fxFlex.gt-lg="32" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input matInput placeholder="Max Volts" [(ngModel)]="leadType.maxVolts" name="maxVolts" #maxVoltsInput="ngModel" sflIsDecimal />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-form-field>
          <input
            matInput
            placeholder="Cost per foot"
            [(ngModel)]="leadType.costPerFoot"
            name="costPerFoot"
            #costPerFootInput="ngModel"
            sflIsDecimal
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="32" fxFlex.gt-md="31" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
        <mat-checkbox name="isObsolete" color="warn" [(ngModel)]="leadType.isObsolete">Is Obsolete</mat-checkbox>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog()">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="!leadTypeForm.valid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
