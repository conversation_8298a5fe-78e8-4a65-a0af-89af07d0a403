
import {catchError, map} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AppConfig } from 'src/app/app.config';
import { utils } from '../../../shared/helpers/app.helper';
import { createRequestOption, SharedService } from 'src/app/shared';

@Injectable({
  providedIn: 'root'
})
export class GoldReportService {
  constructor(private http: HttpClient, private readonly sharedService: SharedService) {}
  // call to java api to get the details of current soNumber as well list of all completed quotations
  getGoldReportQuotations(soNumber, pageableObject) {
    return this.http
      .get(AppConfig.COMPLETED_QUOTE + soNumber, {
        params: createRequestOption(pageableObject)
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
  generateGoldReport(soGoldReportList, isDatePresent, soDetailId, pageableObject) {
    return this.http
      .post(AppConfig.GENERATE_GOLD_REPORT + soDetailId + '/' + isDatePresent, soGoldReportList, {
        params: createRequestOption(pageableObject)
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  readAssemblyForSONumber(data) {
    return this.http
      .post(AppConfig.READ_SLDWRK_ASSEMBLY_DOCUMENT, {
        ConnectionInfo: this.sharedService.getSharedDriveServerConfigurations(),
        soNumber: data.soNumber,
        assemblyFileLocation: data.assemblyFileLocation
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  getGeneratedGoldReport(soGoldReportList, soDetailId, isDatePresent, pageableObject) {
    return this.http
      .post(AppConfig.GENERATE_GOLD_REPORT + soDetailId + '/' + isDatePresent, soGoldReportList, {
        params: createRequestOption(pageableObject)
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  resetGoldReport(soDetailId, soGoldReportList, pageableObject) {
    return this.http
      .post(AppConfig.RE_GENERATE_GOLD_REPORT + soDetailId, soGoldReportList, {
        params: createRequestOption(pageableObject)
      }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }

  downloadGRExcel(soId) {
    return this.http
      .get(AppConfig.DOWNLOAD_GR_EXCEL_API + soId, { responseType: 'blob' }).pipe(
      map(utils.extractData),
      catchError(utils.handleError),);
  }
}
