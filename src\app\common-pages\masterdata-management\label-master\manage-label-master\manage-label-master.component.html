<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  {{ title }}
  <hr />
</h2>
<form class="forms_form" #labelConfigMasterForm="ngForm" (ngSubmit)="saveLabelConfiguration()">
  <mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Label Part Number" [(ngModel)]="labelMaster.labelPartNumber" name="labelPartNumber" required />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input
            matInput
            placeholder="Format"
            [(ngModel)]="labelMaster.format"
            name="format"
            (click)="addConfiguration('format')"
            readonly
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Volts" [(ngModel)]="labelMaster.volts" name="volts" (click)="addConfiguration('volts')" readonly />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Size" [(ngModel)]="labelMaster.size" name="size" (click)="addConfiguration('size')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Watts" [(ngModel)]="labelMaster.watts" name="watts" (click)="addConfiguration('watts')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Amps" [(ngModel)]="labelMaster.amps" name="amps" (click)="addConfiguration('amps')" readonly />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="CS Volts" [(ngModel)]="labelMaster.csv" name="csv" (click)="addConfiguration('csv')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Width" [(ngModel)]="labelMaster.width" name="width" (click)="addConfiguration('width')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input
            matInput
            placeholder="Length"
            [(ngModel)]="labelMaster.length"
            name="length"
            (click)="addConfiguration('length')"
            readonly
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="MHL Volts" [(ngModel)]="labelMaster.mhlv" name="mhlv" (click)="addConfiguration('mhlv')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="CS Amps" [(ngModel)]="labelMaster.csa" name="csa" (click)="addConfiguration('csa')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Phase" [(ngModel)]="labelMaster.phase" name="phase" (click)="addConfiguration('phase')" readonly />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Low Temp" [(ngModel)]="labelMaster.lowT" name="lowT" (click)="addConfiguration('lowT')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="MHL Amps" [(ngModel)]="labelMaster.mhla" name="mhla" (click)="addConfiguration('mhla')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input
            matInput
            placeholder="Temp Range"
            [(ngModel)]="labelMaster.tempRange"
            name="tempRange"
            (click)="addConfiguration('tempRange')"
            readonly
          />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">

      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input
            matInput
            placeholder="High Temp"
            [(ngModel)]="labelMaster.highT"
            name="highT"
            (click)="addConfiguration('highT')"
            readonly
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input
            matInput
            placeholder="Model Number"
            [(ngModel)]="labelMaster.modelNumber"
            name="modelNumber"
            (click)="addConfiguration('modelNumber')"
            readonly
          />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Open 1" [(ngModel)]="labelMaster.open1" name="open1" (click)="addConfiguration('open1')" readonly />
        </mat-form-field>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutGap="22px">

      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Open 2" [(ngModel)]="labelMaster.open2" name="open2" (click)="addConfiguration('open2')" readonly />
        </mat-form-field>
      </div>
      <div fxFlex.gt-lg="30" fxFlex.gt-md="30" fxFlex.gt-sm="30" fxFlex.gt-xs="220">
        <mat-form-field>
          <input matInput placeholder="Open 3" [(ngModel)]="labelMaster.open3" name="open3" (click)="addConfiguration('open3')" readonly />
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <div fxFlex.gt-lg="41" fxFlex.gt-md="41" fxFlex.gt-sm="59" fxFlex.gt-xs="100">
          <span>Label Master Image</span>
          <div fxLayout="column wrap" fxFlex="49" class="mat-img">
            <img class="jumper-product-img" src="{{ imageUrl }}" alt="Material Image" />
          </div>
        </div>
      <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100"  class="label-button">
        <div class="mb-10" fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" (click)="addAttachment()">Add Image</button>
        </div>
        <div fxLayoutAlign="start">
          <button type="button" mat-raised-button color="warn" (click)="removeAttachment()">
            Remove Image
          </button>
        </div>
      </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions fxLayoutAlign="space-between">
    <div fxLayoutAlign="start">
      <button mat-raised-button type="button" (click)="closeDialog(false)">Cancel</button>
    </div>
    <div fxLayoutAlign="end">
      <button mat-raised-button type="submit" color="warn" [disabled]="labelConfigMasterForm.invalid">Submit</button>
    </div>
  </mat-dialog-actions>
</form>
