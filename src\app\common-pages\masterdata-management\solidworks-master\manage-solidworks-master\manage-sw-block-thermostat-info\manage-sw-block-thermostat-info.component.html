<div fxLayout="row wrap" fxLayoutAlign="space-between">
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <mat-select
      placeholder="Thermostat Type"
      name="thermostatType"
      [(ngModel)]="selectedSWBlockThermostatInfo.thermostatTypeArray"
      #thermostatTypeArray="ngModel"
      multiple
    >
      <mat-option *ngFor="let thermostateType of thermostatTypes" [value]="thermostateType.id">
        {{ thermostateType?.id }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <mat-select
      placeholder="Installation Method"
      name="installationMethod"
      [(ngModel)]="selectedSWBlockThermostatInfo.installationMethodArray"
      #installationMethodArray="ngModel"
      multiple
    >
      <mat-option *ngFor="let installationMethod of thermostatInstallationMethods" [value]="installationMethod.methodName">
        {{ installationMethod?.methodName }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input
      matInput
      placeholder="Open Min Temp"
      name="openMinTemp"
      [(ngModel)]="selectedSWBlockThermostatInfo.minOpenTemp"
      #maxOpenTemp="ngModel"
    />
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input
      matInput
      placeholder="Open Max Temp"
      name="openMaxTemp"
      [(ngModel)]="selectedSWBlockThermostatInfo.maxOpenTemp"
      #maxOpenTemp="ngModel"
    />
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input
      matInput
      placeholder="Close Min Temp"
      name="closeMinTemp"
      [(ngModel)]="selectedSWBlockThermostatInfo.minCloseTemp"
      #closeMinTemp="ngModel"
    />
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input
      matInput
      placeholder="Close Max Temp"
      name="closeMaxTemp"
      [(ngModel)]="selectedSWBlockThermostatInfo.maxCloseTemp"
      #closeMaxTemp="ngModel"
    />
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input
      matInput
      placeholder="Min Tolerance"
      name="minTolerance"
      [(ngModel)]="selectedSWBlockThermostatInfo.minTolerance"
      #minTolerance="ngModel"
    />
  </mat-form-field>
  <mat-form-field fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="19" fxFlex.gt-xs="100">
    <input
      matInput
      placeholder="Max Tolerance"
      name="maxTolerance"
      [(ngModel)]="selectedSWBlockThermostatInfo.maxTolerance"
      #maxTolerance="ngModel"
    />
  </mat-form-field>
  <div fxLayout="row wrap" fxLayoutAlign="start" fxLayoutAlign="space-between">
    <mat-checkbox
      name="openOnRise"
      [indeterminate]="selectedSWBlockThermostatInfo?.openOnRise === null"
      [ngModel]="selectedSWBlockThermostatInfo?.openOnRise === true"
      color="warn"
      (ngModelChange)="setCheckBoxTriStateValues(selectedSWBlockThermostatInfo?.openOnRise, OpenOnRise)"
    >
      Open On Rise -
      {{ selectedSWBlockThermostatInfo?.openOnRise === true ? 'Yes' : selectedSWBlockThermostatInfo?.openOnRise === false ? 'No' : 'Null' }}
    </mat-checkbox>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="start" fxLayoutAlign="space-between">
    <mat-checkbox
      name="manualReset"
      [indeterminate]="selectedSWBlockThermostatInfo?.manualReset === null"
      [ngModel]="selectedSWBlockThermostatInfo?.manualReset === true"
      color="warn"
      (ngModelChange)="setCheckBoxTriStateValues(selectedSWBlockThermostatInfo?.manualReset, ManualReset)"
    >
      Manual Reset -
      {{
        selectedSWBlockThermostatInfo?.manualReset === true ? 'Yes' : selectedSWBlockThermostatInfo?.manualReset === false ? 'No' : 'Null'
      }}
    </mat-checkbox>
  </div>
  <div fxLayoutAlign="end" class="mb-10">
    <button
      [disabled]="anyThermostat === true || anyThermostat === false"
      mat-raised-button
      color="warn"
      type="button"
      (click)="addToThermostats()"
    >
      Add
    </button>
  </div>
</div>
<div class="highlight-mat-table cust_table mt-10">
  <mat-table [dataSource]="thermostatDataSource">
    <ng-container matColumnDef="thermostatType">
      <mat-header-cell *matHeaderCellDef fxFlex="20"> Thermostat Type </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="20">{{ element?.thermostatTypeArray }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="installationMethod">
      <mat-header-cell *matHeaderCellDef fxFlex="35"> Installation Method </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="35">{{ element?.installationMethodArray }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="minOpenTemp">
      <mat-header-cell *matHeaderCellDef fxFlex="15"> Min Open Temp </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="15">{{ element?.minOpenTemp }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="maxOpenTemp">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Max Open Temp</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.maxOpenTemp }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="minCloseTemp">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Min Close Temp</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.minCloseTemp }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="maxCloseTemp">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Max Close Temp</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.maxCloseTemp }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="minTolerance">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Min Tolerance</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.minTolerance }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="maxTolerance">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Max Tolerance</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.maxTolerance }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="openOnRise">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Open On Rise</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.openOnRise }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="manualReset">
      <mat-header-cell *matHeaderCellDef fxFlex="10">Manual Reset</mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">{{ element?.manualReset }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="action">
      <mat-header-cell *matHeaderCellDef fxFlex="10"> Action </mat-header-cell>
      <mat-cell *matCellDef="let element" fxFlex="10">
        <mat-icon class="open-doc" (click)="removeFromThermostats(element)">delete</mat-icon>
      </mat-cell>
    </ng-container>
    <mat-header-row *matHeaderRowDef="SensorsdisplayedColumns"></mat-header-row>
    <mat-row *matRowDef="let row; columns: SensorsdisplayedColumns"></mat-row>
  </mat-table>
</div>
