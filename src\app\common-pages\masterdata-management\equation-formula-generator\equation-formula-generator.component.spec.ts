import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EquationFormulaGeneratorComponent } from './equation-formula-generator.component';

describe('EquationFormulaGeneratorComponent', () => {
  let component: EquationFormulaGeneratorComponent;
  let fixture: ComponentFixture<EquationFormulaGeneratorComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EquationFormulaGeneratorComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EquationFormulaGeneratorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
