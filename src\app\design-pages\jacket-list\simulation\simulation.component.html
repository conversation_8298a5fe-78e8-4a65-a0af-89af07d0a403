<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<h2 mat-dialog-title>
  Simulation
  <hr/>
</h2>

<mat-dialog-content>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <input
        matInput
        [matDatepicker]="date"
        placeholder="Date"
        name="simulationDate"
        [(ngModel)]="simulation.simulationDate"
        #simulationDate="ngModel"
        autocomplete="off"
      />
      <mat-datepicker-toggle matSuffix [for]="date"></mat-datepicker-toggle>
      <mat-datepicker #date></mat-datepicker>
    </mat-form-field>
  </div>
  <div fxLayout="column wrap" fxLayoutAlign="space-between">
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="slitElementReview" [(ngModel)]="simulation.slitElementReview"
                    #slitElementReviewCheckBox="ngModel"
      >Slit and Element Reviewed
      </mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox
        color="warn"
        name="buttedRequirement"
        [(ngModel)]="simulation.buttedRequirement"
        #buttedRequirementCheckBox="ngModel"
      >Butted Slit/End Requirements
      </mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox
        color="warn"
        name="criticalRange"
        [(ngModel)]="simulation.criticalRange"
        #criticalRangeCheckBox="ngModel"
      >Critical Points within Range
      </mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="sensorSpecified" [(ngModel)]="simulation.sensorSpecified"
                    #sensorSpecifiedCheckBox="ngModel"
      >Sensor Location specified
      </mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="19" fxFlex.gt-md="19" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox color="warn" name="dutyCycleCal" [(ngModel)]="simulation.dutyCycleCal"
                    #dutyCycleCalCheckBox="ngModel"
      >Duty Cycle Calculation
      </mat-checkbox
      >
    </div>
    <div class="col" fxFlex.gt-lg="49" fxFlex.gt-md="48" fxFlex.gt-sm="49" fxFlex.gt-xs="100">
      <mat-checkbox
        color="warn"
        name="wattageDensity"
        [(ngModel)]="simulation.wattageDensity"
        #wattageDensityCheckBox="ngModel"
      >Wattage Density
      </mat-checkbox
      >
    </div>
  </div>
  <div fxLayout="row wrap" fxLayoutAlign="space-between">
    <mat-form-field fxFlex.gt-lg="100" fxFlex.gt-md="100">
      <input matInput placeholder="Comments" name="comments" [(ngModel)]="simulation.comments"
             #commentsCheckBox="ngModel"/>
    </mat-form-field>
  </div>
</mat-dialog-content>
<hr/>
<mat-dialog-actions>
  <button mat-raised-button color="warn" type="submit" (click)="saveSimulation()">Save</button>
  <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button type="submit" (click)="undoSimulation()">Undo Signature</button>
</mat-dialog-actions>
