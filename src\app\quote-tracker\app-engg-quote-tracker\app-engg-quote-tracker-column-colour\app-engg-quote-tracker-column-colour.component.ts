import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subscription } from 'rxjs';
import { Values } from 'src/app/shared/constants/values.constants';
import { AppEngineeringQuoteTracker } from '../../quote-tracker.model';
import { QuoteTrackerService } from '../../quote-tracker.service';
import { AppEnggQuoteTrackerComponent } from '../app-engg-quote-tracker.component';

@Component({
  selector: 'app-app-engg-quote-tracker-column-colour',
  templateUrl: './app-engg-quote-tracker-column-colour.component.html',
  styleUrls: ['./app-engg-quote-tracker-column-colour.component.css']
})
export class AppEnggQuoteTrackerColumnColourComponent implements OnInit {
  columnList = Values.appQuoteTrackerColumnList;
  highlightedColors = Values.highlightColors;
  showLoader = false;
  subscription: Subscription = new Subscription();
  quoteId: number;


  constructor(
    @Inject(MAT_DIALOG_DATA) public data: number,
    private readonly quoteTrackerService: QuoteTrackerService,
    public dialogRef: MatDialogRef<AppEnggQuoteTrackerComponent>,
  ) {
    this.quoteId = data;
  }

  ngOnInit() {
  }
  selectColumn(colorValue: string, columnName: string) {
    this.showLoader = true;
    this.subscription.add(
      this.quoteTrackerService.appColumnHighlighting(this.quoteId, columnName, colorValue).subscribe(
        (res: AppEngineeringQuoteTracker) => {
          if (res) {
            this.closeDialog(true);
          }
        },
        () => (this.showLoader = false)
      )
    );
  }

  closeDialog(flag = false): void {
    this.dialogRef.close(flag);
  }

}
