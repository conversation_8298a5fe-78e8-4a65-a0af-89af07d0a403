import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageBhxGoldWireTapeComponent } from './manage-bhx-gold-wire-tape.component';

describe('ManageBhxGoldWireTapeComponent', () => {
  let component: ManageBhxGoldWireTapeComponent;
  let fixture: ComponentFixture<ManageBhxGoldWireTapeComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageBhxGoldWireTapeComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageBhxGoldWireTapeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
