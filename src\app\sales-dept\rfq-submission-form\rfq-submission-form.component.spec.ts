import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RfqSubmissionFormComponent } from './rfq-submission-form.component';

describe('RfqSubmissionFormComponent', () => {
  let component: RfqSubmissionFormComponent;
  let fixture: ComponentFixture<RfqSubmissionFormComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RfqSubmissionFormComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RfqSubmissionFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
