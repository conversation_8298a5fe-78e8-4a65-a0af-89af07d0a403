<div class="sfl-loading" *ngIf="showLoader">
  <mat-progress-spinner class="sfl-global-spinner-loader" [mode]="mode" [color]="color" [diameter]="spinnerDiameter">
  </mat-progress-spinner>
</div>
<div fxLayout="row wrap" class="mb-10 cust_fields">
  <div fxFlex fxLayoutAlign="start center">
    <mat-card-title>{{ pageTitle }}</mat-card-title>
  </div>
<div fxFlex fxLayoutAlign="end center" fxLayoutGap="10px">
  <button mat-raised-button color="warn" class="w-auto text-wrap add-btn" (click)="addEcoLineMaterial()">Add New Eco Line Material</button>
</div>
</div>
<div class="less-peding">
  <mat-card class="cust_table">
    <div class="cust_table">
      <mat-table
        mat-table
        matSort
        matSortDisableClear
        [dataSource]="dataSource"
      >
        <ng-container matColumnDef="revisionId">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="55">Non Butted Revision Id </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="55"> {{ element?.revisionNumber }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="buttedRevisionId">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="55">Butted Revision Id </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="55"> {{ element?.buttedRevisionId }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="feature">
          <mat-header-cell *matHeaderCellDef mat-sort-header fxFlex="55"> Features </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="55"> {{ element?.feature }} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="action">
          <mat-header-cell *matHeaderCellDef fxFlex="11"> Action </mat-header-cell>
          <mat-cell *matCellDef="let element" fxFlex="11">           
            <button mat-icon-button [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu class="menu" #menu="matMenu">
              <button mat-menu-item  (click)="editEcoLineRevisionMaster(element)">
                <mat-icon>visibility</mat-icon>
                <span>View/Edit</span>
              </button>
              <button mat-menu-item (click)="deleteEcoLineMaterial(element.id)">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="ecoLineRevisionMasterDataColumns; sticky: true"></mat-header-row>
        <mat-row *matRowDef="let row; columns: ecoLineRevisionMasterDataColumns"></mat-row>
      </mat-table>
    </div>
  </mat-card>
</div>
