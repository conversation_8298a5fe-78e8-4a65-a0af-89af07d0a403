import { PopupSize } from './../../../shared/constants/popupsize.constants';
import { PowerCordComponent } from './../Power-Cord/power-cord.component';
import { Observable, Subscription } from 'rxjs';
import { AccessoriesService } from './../accessories.service';
import { AccessoriesData, AccessoryControllers } from './add-accessories.model';
import { Component, OnInit, OnDestroy, Inject, ViewChild } from '@angular/core';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialogRef, MatTableDataSource, MAT_DIALOG_DATA, MatDialog, MatPaginator } from '@angular/material';
import { AccessoriesComponent } from '../accessories.component';
import { FormControl } from '@angular/forms';
import { map, startWith } from 'rxjs/operators';
import { SnakbarService } from 'src/app/shared';

@Component({
    selector: 'sfl-add-accessories',
    templateUrl: './add-accessories.component.html'
})

export class AddAccessoriesComponent implements OnInit, OnDestroy {

    subscription: Subscription = new Subscription();
    accessoriesDataList: AccessoriesData[];
    accessoriesObservable$: Observable<AccessoriesData[]>;
    displayedColumns = ['select', 'partNumber', 'controllertype', 'description', 'listPrice'];
    dataSource = new MatTableDataSource<AccessoriesData>();
    selection = new SelectionModel<AccessoriesData>(true, []);
    selectedAccessories: AccessoriesData[];
    accessoryControllers: AccessoryControllers[];
    commonAccessories: AccessoriesData[];
    selectedController: string;
    revisionId: number;
    discount = 0;
    filterData: AccessoriesData = new AccessoriesData()
    powerCordPartNumber: string;
    powerCordListPrice: number;
    selectedPowerCord = new Array();
    pageSize = PopupSize.size.pageSize;
    accessoriesControl = new FormControl();
    lastFilter = '';

    constructor(
        private matDialog: MatDialog,
        private accessoriesService: AccessoriesService,
        private readonly snakbarService: SnakbarService,
        public dialogRef: MatDialogRef<AccessoriesComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {
        this.revisionId = data.revisionId;
        this.discount = data.discount;
    }

    @ViewChild(MatPaginator) paginator: MatPaginator;

    ngOnInit(): void {
        this.getAccessoryControllers();
        this.getAccessoriesList();
        this.selectedAccessories = [];
    }

    getAccessoryControllers() {
        this.subscription.add(this.accessoriesService.getAccessoryControllers().subscribe((res: AccessoryControllers[]) => {
            this.accessoryControllers = res;
            this.selectedController = this.accessoryControllers[0].name;
        }, 
        error => {
          if (error.applicationStatusCode === 1209) {
            this.snakbarService.error(error.message);
          }
        }
        ));
    }

    getAccessoriesList() {
        this.subscription.add(this.accessoriesService.getAccessoriesList().subscribe((res: AccessoriesData[]) => {
            this.accessoriesDataList = res;
            this.dataSource.data = this.accessoriesDataList;
            this.dataSource.paginator = this.paginator;
            this.accessoriesObservable$ = this.accessoriesControl.valueChanges.pipe(
              startWith<string | AccessoriesData[]>(''),
              map(value => (typeof value === 'string' ? value : this.lastFilter)),
              map(filter => this.filterPipeMaterial(filter))
            );
        },
        error => {
          if (error.applicationStatusCode === 1210) {
            this.snakbarService.error(error.message);
          }
        }
        ));
    }

    filterPipeMaterial(filter: string): AccessoriesData[] {
      this.lastFilter = filter;
      if (filter) {
        return this.accessoriesDataList.filter(option => {
          if (option.partNumber !== null) {
            return (
              option.partNumber.toLowerCase().indexOf(filter.toLowerCase()) >= 0
            );
          }
        });
      } else {
        return this.accessoriesDataList ? this.accessoriesDataList.slice() : [];
      }
    }

    displayAccessories(value: AccessoriesData[] | string): string {
      let displayValue = '';
      if (Array.isArray(value)) {
        displayValue = value[0].partNumber
      } else {
        displayValue = value;
      }
      return displayValue;
    }

    onSelectionChangesAccessories(material: AccessoriesData) {
      if (material) {
        this.filterData.id = material.id;
        this.filterData.partNumber = material.partNumber;
        this.onPartNumberChange(material.partNumber);
      }
    }

    saveAccessories() {
        this.selectedAccessories = this.selection.selected;
        for (let index = 0; index < this.selection.selected.length; index++) {
            if (this.selection.selected[index].partNumber.toLowerCase() === 'power cord') {
                this.selectedPowerCord.forEach(powerCord => {
                    if (powerCord.id === this.selection.selected[index].id) {
                        this.selection.selected[index].partNumber = powerCord.partNumber;
                        this.selection.selected[index].listPrice = powerCord.listPrice;
                    }
                });
            }
            this.selection.selected[index].revisionId = this.revisionId;
            if (this.discount) {
                this.selection.selected[index].discount = this.discount;
            } else {
                this.selection.selected[index].discount = 0;
            }
        }
        this.subscription.add(this.accessoriesService.saveAllAccesories(this.selectedAccessories, this.revisionId).subscribe((res) => {
            this.dialogRef.close(res);
        }));
    }

    onControllerChange(controllerName) {
        this.dataSource.data = this.accessoriesDataList.filter(e => {
            return e.controllerName === controllerName;
        });
        this.dataSource._updateChangeSubscription();
    }

    onPartNumberChange(partNumber) {
      this.dataSource.data = this.accessoriesDataList.filter(e => e.partNumber === partNumber);
      this.dataSource._updateChangeSubscription();
  }

  resetFilter() {
    this.getAccessoriesList();
    this.filterData.partNumber = null;
  }

    onAccessorySelectionChange(event, row) {
        if (event) {
            this.selection.toggle(row);
        }
        if (row.partNumber.toLowerCase().includes('power cord') && event.checked) {
            this.matDialog.open(PowerCordComponent, {
                width: PopupSize.size.popup_md,
                data: { 'id': row.id }
            }).afterClosed().subscribe(res => {
                if (res) {
                    this.powerCordPartNumber = res.partNumber;
                    this.powerCordListPrice = res.listPrice;
                    const count = (this.powerCordPartNumber.match(/[A-Z]/g) || this.powerCordPartNumber.match(/[0-9]/g)).length;
                    if (count < 2) {
                        this.selection.toggle(row);
                    } else {
                        this.selectedPowerCord.push(res);
                    }
                }
            });
        }
    }

    closeDialog(): void {
        this.dialogRef.close();
    }

    isAllSelected() {
        const numSelected = this.selection.selected.length;
        const numRows = this.dataSource.data.length;
        return numSelected === numRows;
    }

    masterToggle() {
        this.isAllSelected() ?
            this.selection.clear() :
            this.dataSource.data.forEach(row => this.selection.select(row));
    }

    applyFilter(filterValue: string) {
        this.dataSource.filter = filterValue.trim().toLowerCase();
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
