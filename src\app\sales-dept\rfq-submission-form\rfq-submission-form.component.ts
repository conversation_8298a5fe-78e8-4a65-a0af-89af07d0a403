import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { MatDialog, MatTableDataSource } from '@angular/material';
import { Title } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { CustomerDTO } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SnakbarService, SweetAlertService } from 'src/app/shared';
import { SalesOrderSummaryService } from '../../admin-pages/new-quotation/summary-sales-order.service';
import { Values } from '../../shared/constants/values.constants';
import { AccountManager, Document, Quotation } from '../sales-dept.model';
import { SalesDeptService } from '../sales-dept.service';
import { Messages } from './../../shared/constants/messages.constants';


@Component({
  selector: 'sfl-rfq-submission-form',
  templateUrl: './rfq-submission-form.component.html',
  styleUrls: ['./rfq-submission-form.component.css']
})
export class RfqSubmissionFormComponent implements OnInit {
  headingTitle = 'RFQ Submission Form';
  filedata: File;
  filename: string;
  isUploading = false;
  isValidFileSize = false;
  value: string;
  showLoader = false;
  accountMgr: AccountManager[];
  subscription = new Subscription();
  formData = new FormData();
  document: Document;
  documentDataSource = new MatTableDataSource<Document>();
  uploadedAttachments = [];
  attachments: string[];
  rfqForm: Quotation;
  selectedAttachment: string;
  quotationNumber: number;
  quotationId: number;
  RFQSubmissionForm: FormGroup;
  customerDTO: FormGroup;
  quoteNumber: string;
  success = false;
  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  appSubmittedDate: Date = new Date();

  constructor(
    private readonly salesdeptService: SalesDeptService,
    private readonly matDialog: MatDialog,
    private snakBarService: SnakbarService,
    private readonly sweetAlertService: SweetAlertService,
    private readonly formBuilder: FormBuilder,
    private readonly titleService: Title,
    private salesOrderSummaryService: SalesOrderSummaryService,
    private datePipe: DatePipe,
  ) {
  }

  ngOnInit() {
    this.titleService.setTitle('RFQ Form - Sales Department');
    this.getAccountManagers();
    this.createRFQForm();
    this.document = new Document();
  }

  getAccountManagers() {
    this.subscription.add(
      this.salesdeptService.getAllAccountManagers().subscribe((res: Array<AccountManager>) => {
        this.accountMgr = res;
      })
    );
  }

  getQuotationNumber() {
    this.RFQSubmissionForm.get('customerDTO').patchValue(new CustomerDTO());
    this.RFQSubmissionForm.controls.projectTitle.patchValue('');
    this.subscription.add(
      this.salesdeptService.getQuotation(this.quotationNumber).subscribe((res: Quotation) => {
        if (res.customerDTO) {
          this.quotationNumber = res.quotationNumber;
          this.RFQSubmissionForm.patchValue({customerDTO: res.customerDTO});
        }
        if (res.projectName != null) {
          this.RFQSubmissionForm.patchValue({projectTitle: res.projectName});
        }
      })
    );
  }

  // used to generate RFQ form
  createRFQForm() {
    this.RFQSubmissionForm = this.formBuilder.group({
      customerDTO: this.formBuilder.group({
        id: new FormControl(null),
        code: new FormControl(null),
        name: new FormControl(null),
        contact: new FormControl(null),
        email: new FormControl(null),
        engCustAbrev: new FormControl(null),
        faxNumber: new FormControl(null),
        addressLine1: new FormControl(null),
        addressLine2: new FormControl(null),
        phoneNumber: new FormControl(null),
        sqtLink: new FormControl(null),
        notes: new FormControl(null),
      }),
      projectTitle: new FormControl(null, Validators.required),
      quotationNumber: new FormControl(null, Validators.required),
      accountMgrid: new FormControl(null, Validators.required),
      sqtFolderLink: new FormControl(null, Validators.required),
      sendCopyOfResponse: new FormControl(null),
      dateSubmittedDateApp: new FormControl(null, Validators.required),
    });
  }

  saveRFQForm() {
    this.RFQSubmissionForm.controls.quotationNumber.patchValue(this.RFQSubmissionForm.controls.quotationNumber.value.trim());
    this.RFQSubmissionForm.controls.projectTitle.patchValue(this.RFQSubmissionForm.controls.projectTitle.value.trim());
    this.RFQSubmissionForm.get('customerDTO').get('name').patchValue(this.RFQSubmissionForm.get('customerDTO').get('name').value.trim());
    this.RFQSubmissionForm.get('customerDTO').get('sqtLink').patchValue(this.RFQSubmissionForm.get('customerDTO').get('sqtLink').value.trim());
    const dateSubmittedDateApp = this.datePipe.transform(this.RFQSubmissionForm.get('dateSubmittedDateApp').value, Values.dateFormat.formatHyphen);
    this.RFQSubmissionForm.patchValue({ dateSubmittedDateApp: dateSubmittedDateApp });

    this.subscription.add(
      this.salesdeptService.saveRfqForm(this.RFQSubmissionForm.value).subscribe((res: any) => {
        if (res) {
          if (res.id) {
            if (this.filedata) {
              this.quotationId = res.id;
              // this.uploadFile();
            }
          }
        }
        this.snakBarService.success(Messages.RFQ_FORM.form_successfull);
        this.formGroupDirective.resetForm();
      })
    );
  }

  isControlHasError(controlName: string, validationType: string): boolean {
    const control = this.RFQSubmissionForm.controls[controlName];
    if (!control) {
      return false;
    }
    const result = control.hasError(validationType) && (control.dirty || control.touched);
    return result;
  }

  readUrl(event) {
    if (event.target.files && event.target.files[0]) {
      this.isValidFileSize = false;
      this.filedata = event.target.files[0];
      this.filename = this.filedata.name;
      this.value = this.filename;
    } else {
      this.filedata = null;
      this.value = Messages.Quotation.upload_message;
    }
  }

  uploadFile() {
    if (this.filedata.size > 10485760) {
      this.isValidFileSize = true;
      this.filedata = null;
      this.filename = null;
    } else {
      this.isUploading = true;
      this.isValidFileSize = false;
      if (this.quotationId) {
        this.document.quotationId = this.quotationId;
        const formData = new FormData();
        formData.append('file', this.filedata);
        formData.append('document', JSON.stringify(this.document));
        this.salesOrderSummaryService.uploadDocument(formData).subscribe(() => {
          this.document = new Document();
          this.filedata = null;
          this.filename = '';
          this.value = Messages.Quotation.upload_message;
        });
      }
    }
  }
}
