<h2 mat-dialog-title>
    Revision Control
    <hr>
</h2>
<mat-dialog-content>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
            <input matInput placeholder="Current Revision" name="currentrevision" [(ngModel)]="currentRevision"
                autocomplete="off">
        </mat-form-field>
        <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
            <input matInput placeholder="New Revision" name="newrevision" [(ngModel)]="newRevision" autocomplete="off">
        </mat-form-field>
    </div>
    <div fxLayout="row wrap" class="mb-10">
        <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxLayoutAlign="center">
            <button mat-raised-button color="warn" type="submit" (click)="updateNewRevisionAndDirectory()"
                [disabled]="newRevision == undefined || newRevision == null && fixPath == undefined || fixPath == null">New
                Revision
                Update Directory</button>
        </div>
        <div fxFlex.gt-lg="49" fxFlex.gt-md="49" fxLayoutAlign="center">
            <button mat-raised-button color="warn" type="submit" (click)="updateNewRevisionBHXOnly()"
                [disabled]="newRevision == undefined || newRevision == null">New Revision BHX
                Only</button>
        </div>
    </div>
</mat-dialog-content>
<hr>
<mat-dialog-actions>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
</mat-dialog-actions>
