import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Location} from '@angular/common';
import {ActivatedRoute} from '@angular/router';
import {Subscription} from 'rxjs';
import {JacketGroupService} from '../new-quotation/manage-jacketgroups/manage-jacket-groups.service';
import {
  ApplicationInfo,
  CcdcWorkflow,
  ClosureInformation,
  ClosureMaterial,
  JacketGroup,
  Material,
  MaterialInfo,
  MaterialInfoReq,
  PluggingInformation,
  Quotation,
  Revision,
  SensorInformation,
  SensorInformationObject,
  ThermostatInfo,
  ThermostatList,
  Units
} from '../new-quotation/ccdc-model/ccdc.model';
import {AddApplicationService} from '../new-quotation/Add Applicaton/add-application.service';
import {SalesOrderSummaryService} from '../new-quotation/summary-sales-order.service';
import {QuotationComparison} from './quotation-comparison.model';
import {SharedService, SnakbarService} from 'src/app/shared';
import {AddClosureService} from '../new-quotation/Add Closure/add-closure.service';
import {SensorService} from '../new-quotation/Add Sensors/add-sensors.service';
import {ManageRevisionsService} from '../new-quotation/manage-revisions/manage-revisions.service';
import {Values} from 'src/app/shared/constants/values.constants';
import {Title} from '@angular/platform-browser';
import {ManageUnitsService} from '../new-quotation/manage-units/manage-units.service';

@Component({
  selector: 'sfl-quotation-comparison',
  templateUrl: './quotation-comparison.component.html',
})
export class QuotationComparisonComponent implements OnInit, OnDestroy {

  revisionId: number;
  jacketGroupId: number;
  subscription = new Subscription();
  jacketGroup: JacketGroup[] = [];
  appInfo: ApplicationInfo;
  initialData: QuotationComparison;
  pipeMaterial: Material[] = [];
  ccdcData = [];
  workflow: CcdcWorkflow;
  quotationComparison = [];
  measurementUnit: string;
  tempUnit: string;
  quotID: number;
  pluggingInformation: PluggingInformation;
  materials: ClosureMaterial[];
  closureMaterial: ClosureMaterial;
  closureInfo: ClosureInformation;
  thermostatInfo: ThermostatList;
  sensorInfo: SensorInformation;
  materialinfo: MaterialInfo;
  quotation: Quotation;
  revisionList: Revision[];
  lblHeight: number;
  dom: any;
  jacketTypes = Values.JacketTypeConst;
  phaseTypes = Values.PhaseTypeConst;
  contentMotions = Values.ContentMotionsConst;
  productTypes = Values.ProductTypeConst;

  constructor(private location: Location,
              private activatedRoute: ActivatedRoute,
              private jacketGroupService: JacketGroupService,
              private addApplicationService: AddApplicationService,
              private salesOrderSummaryService: SalesOrderSummaryService,
              private sharedService: SharedService,
              private addClosureService: AddClosureService,
              private sensorService: SensorService,
              private manageRevisionsService: ManageRevisionsService,
              private snakbarService: SnakbarService,
              private manageUnitService: ManageUnitsService,
              private titleService: Title) {
  }

  ngOnInit() {
    this.titleService.setTitle('Quotation Comparison - App Eng');
    this.activatedRoute
      .queryParams
      .subscribe(params => {
        this.quotID = params['quotId'];
      });
    this.getInitialData();
    this.getQuotDetails();
    this.getAllRevisionsByQuotationId();
    this.getMeasurementUnit();
  }

  getMeasurementUnit() {
    this.subscription.add(
      this.manageUnitService.getUnit(this.jacketGroupId).subscribe(
        (res: Units) => {
          if (res) {
            this.measurementUnit = res.measurementUnit;
            this.tempUnit = res.tempUnit;
          }
        }
      )
    );
  }

  getQuotDetails() {
    this.subscription.add(this.salesOrderSummaryService.getQUotById(this.quotID).subscribe((res: Quotation) => {
      this.quotation = res;
    }));
  }

  getAllRevisionsByQuotationId() {
    if (this.quotID) {
      this.subscription.add(this.manageRevisionsService.getRevisionsByQuotationId(this.quotID).subscribe((res: Revision[]) => {
        if (res) {
          this.revisionList = res;
          this.revisionId = this.revisionList[0].id;
          this.onRevisionChanged(this.revisionId);
        }
      }));
    }
  }

  onRevisionChanged(revisionId) {
    this.subscription.add(this.jacketGroupService.getJacketGroupsByRevisionId(revisionId).subscribe((res: JacketGroup[]) => {
      if (res.length > 0) {
        for (let i = 0; i < 3; i++) {
          this.remove(i);
        }
        this.jacketGroup = res;
      }
    }));
  }

  getInitialData() {
    for (let i = 0; i < 3; i++) {
      this.initialData = new QuotationComparison();
      this.quotationComparison.push(this.initialData);
    }
  }

  remove(i) {
    this.quotationComparison[i] = {};
  }

  onJacketGroupChanged(value, i) {
    const jacketGroup = this.jacketGroup.find(data => data.id === value);
    this.jacketGroupId = jacketGroup.id;
    this.getWorkFlowByJacketGroupId(i);
    this.getApplicationInfo(i);
    this.getPluggingInformationByJacketGroupId(i);
    this.getClosureInfo(i);
    this.getThermostatByJacketGroupId(i);
    this.getSensorsListByJacketGroupId(i);
    this.getMaterialByJacketGroupId(i);
  }

  getWorkFlowByJacketGroupId(index) {
    this.subscription.add(this.salesOrderSummaryService.getWorkFlowByJacketGroupId(this.jacketGroupId).subscribe((res: CcdcWorkflow) => {
      this.quotationComparison[index].workflow = res;
    }));
  }

  getApplicationInfo(index) {
    this.subscription.add(this.addApplicationService.getApplicationInfoByJacketGroup(this.jacketGroupId).subscribe((res: ApplicationInfo) => {
      this.quotationComparison[index].appInfo = res;
      const localAppInfo = this.quotationComparison[index].appInfo;
      if (localAppInfo) {
        if (localAppInfo.contentMotion) {
          localAppInfo.contentMotion = this.contentMotions.find(e => e.id === localAppInfo.contentMotion).value;
        }
        if (localAppInfo.jacketType) {
          localAppInfo.jacketType = this.jacketTypes.find(e => e.id === localAppInfo.jacketType).value;
        }
        if (localAppInfo.phase) {
          localAppInfo.phase = this.phaseTypes.find(e => e.id === localAppInfo.phase).value;
        }
      }
    }));
  }

  getPluggingInformationByJacketGroupId(index) {
    this.subscription.add(this.salesOrderSummaryService.getPluggingInformationByJacketGroupId(this.jacketGroupId).subscribe((res: PluggingInformation) => {
      this.quotationComparison[index].plugginginfo = res;
    }));
  }

  getClosureInfo(index) {
    this.subscription.add(this.addClosureService.getClosureInfoByJacketGroup(this.jacketGroupId).subscribe((res: ClosureInformation) => {
      this.quotationComparison[index].closureInfo = res;
    }));
  }

  getThermostatByJacketGroupId(index) {
    this.subscription.add(this.salesOrderSummaryService.getThermostatByJacketGroupId(this.jacketGroupId).subscribe((res: ThermostatInfo) => {
      this.quotationComparison[index].thermostatInfo = res.thermostatInformationDTOList;
    }));
  }

  getSensorsListByJacketGroupId(index) {
    this.subscription.add(this.sensorService.getSensorList(this.jacketGroupId).subscribe((res: SensorInformationObject) => {
      this.quotationComparison[index].sensorInfo = res.sensorsInformationDTOList;
    }));
  }

  getMaterialByJacketGroupId(index) {
    this.subscription.add(this.salesOrderSummaryService.getAllMaterial(this.jacketGroupId).subscribe((res: MaterialInfoReq) => {
      this.quotationComparison[index].materialinfo = res.materialInfoDTOList;
    }));
  }

  goBack() {
    this.location.back();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
