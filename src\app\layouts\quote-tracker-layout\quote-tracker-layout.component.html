<div class="app mat-typography" [dir]="options.dir">
  <sfl-quote-tracker-topbar (toggleSidenav)="sidemenu.toggle()"></sfl-quote-tracker-topbar>
  <mat-sidenav-container class="app-inner">
    <mat-sidenav
      #sidemenu
      class="sidebar-panel"
      id="sidebar-panel"
      [mode]="isOver() ? 'over' : 'side'"
      [opened]="!isOver()"
      (open)="sidePanelOpened = true"
      (close)="sidePanelOpened = false"
      (mouseover)="menuMouseOver()"
      (mouseout)="menuMouseOut()"
      [perfectScrollbar]="config"
      [disabled]="mediaMatcher.matches"
    >
      <sfl-quote-tracker-navbar (click)="updatePS()"></sfl-quote-tracker-navbar>
    </mat-sidenav>
    <div [perfectScrollbar]="config" [disabled]="mediaMatcher.matches">
      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-container>
</div>
