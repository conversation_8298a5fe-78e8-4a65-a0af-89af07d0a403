
import {catchError,  map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { AppConfig } from './../../app.config';
import { HttpClient } from '@angular/common/http';
import { LocalStorageService } from 'ngx-webstorage';
import { utils } from 'src/app/shared/helpers';

@Injectable({ providedIn: 'root' })
export class AuthenticationService {
    private resourceUrl = AppConfig.LOGIN_API;
    constructor(private http: HttpClient,
        private $localStorage: LocalStorageService) { }

    login(username: string, password: string) {
        return this.http.post<any>(this.resourceUrl, { username, password })
            .pipe(map(user => {
                // login successful if there's a jwt token in the response
                if (user && user.access_token) {
                    // store user details and jwt token in local storage to keep user logged in between page refreshes
                    this.$localStorage.store('currentUser', JSON.stringify(user));
                }

                return user;
            }));
    }

    setInitialName(userInitialNameDto) {
        return this.http.put(AppConfig.SET_INITIAL_NAME, userInitialNameDto).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
    }

    getAccountData() {
        return this.http.get(AppConfig.Account_API);
    }
}
