import {Component, Inject, OnD<PERSON>roy, OnInit} from '@angular/core';
import {Subscription} from 'rxjs';
import {EstEngRelDateMaster, QuotationStatusMaster} from '../../masterdata-management.model';
import {Variable} from 'src/app/shared/constants/Variable.constants';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material';
import {MasterdataManagementService} from '../../masterdata-management.service';
import {SnakbarService} from 'src/app/shared';
import {Values} from '../../../../shared/constants/values.constants';

@Component({
  selector: 'sfl-manage-est-eng-rel-master',
  templateUrl: './manage-estimated-eng-release-master.component.html'
})
export class ManageEstimatedEngReleaseMasterComponent implements OnInit, OnDestroy {
  subscription = new Subscription();
  showLoader = false;
  estEngRelDate: EstEngRelDateMaster;
  color = Variable.warnRed;
  mode = Variable.spinnerMode;
  spinnerDiameter = Variable.smallSpinnerDiameter75;
  title = '';
  formData = new FormData();
  productTypes = Values.TRACKER_FIELDS_PRODUCT_TYPES;
  quotStatus: QuotationStatusMaster[];

  constructor(
    public dialogRef: MatDialogRef<ManageEstimatedEngReleaseMasterComponent>,
    @Inject(MAT_DIALOG_DATA) data,
    private readonly masterDataService: MasterdataManagementService,
    private readonly matDialog: MatDialog,
    private readonly snakbarService: SnakbarService
  ) {
    this.estEngRelDate = data;
  }

  ngOnInit() {
    this.getAllQuotesStatus();
    this.estEngRelDate = this.estEngRelDate.id ? Object.assign({}, this.estEngRelDate) : new EstEngRelDateMaster();
    this.estEngRelDate.id ? (this.title = 'Update Estimate Engineering Release Date') : (this.title = 'Add Estimate Engineering Release Date');
  }

  getAllQuotesStatus(){
    this.masterDataService.getAllQuotationStatus().subscribe(res=>{
      this.quotStatus=res;
    })
  }

  updateEstEngRelDate() {
    this.showLoader = true;
    if (this.estEngRelDate.id) {
      this.subscription.add(
        this.masterDataService.updateEstEngRelDate(this.estEngRelDate).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    } else {
      this.subscription.add(
        this.masterDataService.addEstEngRelDate(this.estEngRelDate).subscribe(
          () => {
            this.showLoader = false;
            this.dialogRef.close(true);
          },
          error => {
            this.showLoader = false;
          }
        )
      );
    }
  }

  closeDialog(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
