import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { catchError, map } from 'rxjs/operators';
import { AppConfig } from 'src/app/app.config';
import { createRequestOption } from 'src/app/shared';
import { Values } from 'src/app/shared/constants/values.constants';
import { utils } from 'src/app/shared/helpers';
import { CcdcTemplateDTO } from './ccdc-master-data/manage-ccdc/manage-ccdc.model';
import {
    AlloyMaster,
    BHXGoldWireTapesMaster,
    BHXMaterialMaster,
    DepartmentsMaster,
    EcoLineMaster,
    ECRStatusesMaster,
    EstEngRelDateMaster,
    GenericPageable,
    GoldStandardTapeWidthMaster,
    HeatingTapesMaster,
    LaborMaster,
    LeadTypesMaster,
    PowerCordAmpsMaster,
    PowerCordMaterialsMaster,
    PowerCordOptionsMaster,
    PowerCordVoltagesMaster,
    SensorConnectorsAndTypesMaster,
    WarpsMaster
} from './masterdata-management.model';
import { SolidWorksBlockMasterDTO } from './solidworks-master/solidworks-master.model';

@Injectable({
    providedIn: 'root'
})
export class MasterdataManagementService {
    private ccdcMasterDataObs$: BehaviorSubject<CcdcTemplateDTO> = new BehaviorSubject(new CcdcTemplateDTO());

    constructor(private readonly http: HttpClient) {
    }

    getCcdcTemplateObs(): Observable<CcdcTemplateDTO> {
        return this.ccdcMasterDataObs$.asObservable();
    }

    getEcoLineMaster() {
        return this.http.get(AppConfig.ECO_LINE_REVISION_MASTER).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    updateEcoLineMaster(ecoLineRevisionMasterDTO: EcoLineMaster) {
        if (ecoLineRevisionMasterDTO.id) {
            return this.http.put(AppConfig.ECO_LINE_REVISION_MASTER, ecoLineRevisionMasterDTO).pipe(map(utils.extractData), catchError(utils.handleError),);
        } else {
            return this.http.post(AppConfig.ECO_LINE_REVISION_MASTER, ecoLineRevisionMasterDTO).pipe(map(utils.extractData), catchError(utils.handleError),);
        }
    }

    deleteEcoLineMaster(id) {
        return this.http.delete(AppConfig.DELETE_ECO_LINE_REVISION_MASTER + `${id}`).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    setCcdcTemplateObs(ccdcTemplateMasterData: CcdcTemplateDTO) {
        this.ccdcMasterDataObs$.next(ccdcTemplateMasterData);
    }

    saveAllCcdcTemplates(ccdcTemplateDTO: CcdcTemplateDTO) {
        return this.http.put(AppConfig.CCDC_TEMPLATE_API, ccdcTemplateDTO).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    deleteCcdcTemplate(ccdcTemplateId: number) {
        return this.http
            .delete(AppConfig.CCDC_TEMPLATE_API + '/' + ccdcTemplateId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // fetching all the quotation status with pageable
    getAllStatusesFromApi(quotationStatusFilter, pageableObject) {
        return this.http
            .post(AppConfig.STATUS_LIST_PAGEABLE, quotationStatusFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

  // update passed quotation status
  getAllQuotationStatus() {
    return this.http.get(AppConfig.STATUS_LIST_UPDATE).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

    // update passed quotation status
    updateQuotationStatus(quotationStatus) {
        return this.http.put(AppConfig.STATUS_LIST_UPDATE, quotationStatus).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // add new quotation status
    addQuotationStatus(quotationStatus) {
        return this.http.post(AppConfig.STATUS_LIST_UPDATE, quotationStatus).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // delete quotation status with provided id
    deleteQuotationStatus(quotationStatusId: number) {
        return this.http
            .delete(AppConfig.STATUS_LIST_UPDATE + '/' + quotationStatusId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // fetching all the accessories list with pageable
    getAccessoriesList(accessoryFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_ALL_ACCESSORIES_PAGEABLE, accessoryFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // update passed accessory
    updateAccessory(accessory) {
        return this.http.put(AppConfig.GET_ALL_ACCESSORIES_LIST, accessory).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // add new accessory
    addAccessory(accessory) {
        return this.http.post(AppConfig.GET_ALL_ACCESSORIES_LIST, accessory).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // delete accessory with the provided id
    deleteAccessory(accessoryId: number) {
        return this.http
            .delete(AppConfig.GET_ALL_ACCESSORIES_LIST + '/' + accessoryId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    deleteMasterPartNumber(Id: number) {
        return this.http
            .delete(AppConfig.DELETE_MASTER_PART_NUMBER + '/' + Id).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // Get materials properties
    getMaterialProperties(materialPropertiesFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_MATERIAL_PROPERTIES_PAGEABLE, materialPropertiesFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // materials properties master
    updateMaterialProperties(material) {
        return this.http.put(AppConfig.GET_MATERIALS_LIST, material).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // add new materials properties master
    addMaterialProperties(material) {
        return this.http.post(AppConfig.GET_MATERIALS_LIST, material).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // delete materials properties with materials property id
    deleteMaterialProperties(materialPropertyId: number) {
        return this.http
            .delete(AppConfig.GET_MATERIALS_LIST + '/' + materialPropertyId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // Get materials
    getMaterialLayer(materialFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_MATERIAL_PAGEABLE, materialFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // update materials master
    updateMaterial(material) {
        return this.http.put(AppConfig.GET_MATERIAL_LAYER, material).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // add new materials master
    addMaterial(material) {
        return this.http.post(AppConfig.GET_MATERIAL_LAYER, material).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // delete material with provided id
    deleteMaterial(materialId: number) {
        return this.http
            .delete(AppConfig.GET_MATERIAL_LAYER + '/' + materialId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // get closure material master data pageable
    getClosureMaterialList(closureMaterialFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_CLOSURE_MATERIAL_PAGEABLE, closureMaterialFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // update closure material master data
    updateClosureMaterial(closureMaterial) {
        return this.http.put(AppConfig.GET_CLOSURE_MATERIAL_LIST, closureMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // add new closure material master data
    addClosureMaterial(closureMaterial) {
        return this.http.post(AppConfig.GET_CLOSURE_MATERIAL_LIST, closureMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // delete closure material master data with id
    deleteClosureMaterial(closureMaterialId: number) {
        return this.http
            .delete(AppConfig.GET_CLOSURE_MATERIAL_LIST + '/' + closureMaterialId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // add new closure material master data
    getClosureMaterial() {
        return this.http.get(AppConfig.GET_CLOSURE_MATERIAL_LIST).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    getCurrencyList(currencyMaterialFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_CURRENCY_MATERIAL_PAGEABLE, currencyMaterialFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    addCurrencyMaterial(currencyMaterial) {
        return this.http.post(AppConfig.GET_CURRENCY_MATERIAL_LIST, currencyMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    editCurrencyMaterial(currencyMaterial) {
        return this.http.put(AppConfig.GET_CURRENCY_MATERIAL_LIST, currencyMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // get all plug master data
    getAllPlugs(plugFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_PLUG_PAGEABLE, plugFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // update plug master data
    updatePlug(plug) {
        return this.http.put(AppConfig.PLUGS_API, plug).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // add new plug master data
    addPlug(plug) {
        return this.http.post(AppConfig.PLUGS_API, plug).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // delete plug master data with id
    deletePlug(plugId: number) {
        return this.http
            .delete(AppConfig.PLUGS_API + '/' + plugId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // get plug list master data
    getPlugList() {
        return this.http.get(AppConfig.PLUGS_API).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // get plug by jacket type
    getPlugsByJacketType(jacketType: string) {
        return this.http
            .get(AppConfig.GET_PLUGS_BY_JACKET_TYPE + jacketType).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // get thermostat master data
    getThermostatsList(thermostatFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_THERMOSATS_PAGEABLE, thermostatFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // update thermostat master data
    updateThermostatsList(thermostat) {
        return this.http.put(AppConfig.GET_THERMOSATS, thermostat).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // add new thermostat master data
    addThermostatsList(thermostat) {
        return this.http.post(AppConfig.GET_THERMOSATS, thermostat).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // delete thermostat master data with id
    deleteThermostatsList(thermostatId: number) {
        return this.http
            .delete(AppConfig.GET_THERMOSATS + '/' + thermostatId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // get features master data
    getFeatures(featuresFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_FEATURES_PAGEABLE, featuresFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    addMasterPartNumber(partNumberMasterDTO) {
        return this.http.post(AppConfig.SAVE_MASTER_PART_NUMBER, partNumberMasterDTO).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    updateMasterPartNumber(partNumberMasterDTO) {
        return this.http.put(AppConfig.SAVE_MASTER_PART_NUMBER, partNumberMasterDTO).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets Accessory Controllers master data
    getAccessoryControllers(accessoryControllerFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_ACCESSORY_CONTROLLER_PAGEABLE, accessoryControllerFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Accessory Controllers master data
    updateAccessoryControllers(accessoryController) {
        return this.http.put(AppConfig.GET_ALL_ACCESSORY_CONTROLLERS, accessoryController).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Accessory Controllers master data
    addAccessoryControllers(accessoryController) {
        return this.http.post(AppConfig.GET_ALL_ACCESSORY_CONTROLLERS, accessoryController).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Accessory Controllers master data
    deleteAccessoryControllers(accessoryControllerId: number) {
        return this.http
            .delete(AppConfig.GET_ALL_ACCESSORY_CONTROLLERS + '/' + accessoryControllerId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to get all the Accessory Controllers master data
    getAccessoryControllersList() {
        return this.http.get(AppConfig.GET_ALL_ACCESSORY_CONTROLLERS).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

  getAllProductTypes() {
    return this.http.get(AppConfig.PRODUCT_TYPES).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

    // gets the Product Type Master Data pageable
    getProductTypeMasterData(productFilter, pageableObject) {
        return this.http
            .post(AppConfig.PRODUCT_TYPES_PAGEABLE, productFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Product Type Master data
    updateProductType(productType) {
        return this.http.put(AppConfig.PRODUCT_TYPES, productType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Product Type Master data
    addProductType(productType) {
        return this.http.post(AppConfig.PRODUCT_TYPES, productType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Product Type Master data
    deleteProductType(productTypeId: number) {
        return this.http
            .delete(AppConfig.PRODUCT_TYPES + '/' + productTypeId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the PQP Family master data
    getPQPFamilyMasterData(productFilter, pageableObject) {
        return this.http
            .post(AppConfig.GET_PQP_FAMILY_PAGEABLE, productFilter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update PQP Family master data
    updatePQPFamily(pqpFamily) {
        return this.http.put(AppConfig.GET_PQP_LIST, pqpFamily).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new PQP Family master data
    addPQPFamily(pqpFamily) {
        return this.http.post(AppConfig.GET_PQP_LIST, pqpFamily).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove PQP Family master data
    deletePQPFamily(pqpFamilyId: number) {
        return this.http
            .delete(AppConfig.GET_PQP_LIST + '/' + pqpFamilyId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Power Cord Connector Master data pagealbe
    getPowerCordConnectorMaster(powerCordConnector, pageableObject) {
        return this.http
            .post(AppConfig.GET_POWER_CORD_CONNECTOR_PAGEABLE, powerCordConnector, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Power Cord Connector Master data
    updatePowerCordConnector(powerCordConnector) {
        return this.http.put(AppConfig.POWER_CORD_CONNECTOR, powerCordConnector).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Power Cord Connector Master data
    addPowerCordConnector(powerCordConnector) {
        return this.http.post(AppConfig.POWER_CORD_CONNECTOR, powerCordConnector).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Power Cord Connector Master data
    deletePowerCordConnector(powerCordConnectorId: number) {
        return this.http
            .delete(AppConfig.POWER_CORD_CONNECTOR + '/' + powerCordConnectorId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to get all the Power Cord Connector Master data
    getPowerCordConnectorList() {
        return this.http.get(AppConfig.POWER_CORD_CONNECTOR).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets the Sleeving Types Master data
    getSleevingTypesMaster(sleevingType, pageableObject) {
        return this.http
            .post(AppConfig.SLEEVING_TYPES_PAGEABLE, sleevingType, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Sleeving Types Master data
    updateSleevingTypes(sleevingType) {
        return this.http.put(AppConfig.SLEEVING_TYPES, sleevingType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Sleeving Types Master data
    addSleevingTypes(sleevingType) {
        return this.http.post(AppConfig.SLEEVING_TYPES, sleevingType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Sleeving Types Master data
    deleteSleevingType(sleevingTypeId: number) {
        return this.http
            .delete(AppConfig.SLEEVING_TYPES + '/' + sleevingTypeId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets all the Sleeving Types Master data
    getSleevingTypesList() {
        return this.http.get(AppConfig.SLEEVING_TYPES).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets the Strain Reliefs Master data pageable
    getStrainReliefsMaster(sleevingType, pageableObject) {
        return this.http
            .post(AppConfig.STRAIN_RELIEFS_PAGEABLE, sleevingType, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Strain Reliefs Master data
    updateStrainReliefs(sleevingType) {
        return this.http.put(AppConfig.STRAIN_RELIEFS, sleevingType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Strain Reliefs Master data
    addStrainReliefs(sleevingType) {
        return this.http.post(AppConfig.STRAIN_RELIEFS, sleevingType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Strain Reliefs Master data
    deleteStrainReliefs(sleevingTypeId: number) {
        return this.http
            .delete(AppConfig.STRAIN_RELIEFS + '/' + sleevingTypeId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to get the available strain reliefs
    getStrainReliefs() {
        return this.http.get(AppConfig.STRAIN_RELIEFS).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets Plug Lights Master data
    getPlugLightsMaster(plugLights, pageableObject) {
        return this.http
            .post(AppConfig.PLUG_LIGHTS_PAGEABLE, plugLights, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Plug Lights Master data
    updatePlugLight(plugLights) {
        return this.http.put(AppConfig.PLUG_LIGHTS, plugLights).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Plug Lights Master data
    addPlugLight(plugLights) {
        return this.http.post(AppConfig.PLUG_LIGHTS, plugLights).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Plug Lights Master data
    deletePlugLight(plugLightsId: number) {
        return this.http
            .delete(AppConfig.PLUG_LIGHTS + '/' + plugLightsId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets Gold Standard Tape Width Master data
    getGoldStandardTapeWidthMaster(goldStandardTapeWidth, pageableObject): Observable<GenericPageable<GoldStandardTapeWidthMaster>> {
        return this.http
            .post(AppConfig.GOLD_STANDARD_TAPE_WIDTH_PAGEABLE, goldStandardTapeWidth, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Gold Standard Tape Width Master data
    updateGoldStandardTapeWidth(goldStandardTapeWidth) {
        return this.http.put(AppConfig.GOLD_STANDARD_TAPE_WIDTH, goldStandardTapeWidth).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Gold Standard Tape Width Master data
    addGoldStandardTapeWidth(goldStandardTapeWidth) {
        return this.http.post(AppConfig.GOLD_STANDARD_TAPE_WIDTH, goldStandardTapeWidth).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Gold Standard Tape Width Master data
    deleteGoldStandardTapeWidth(goldStandardTapeWidthId: number) {
        return this.http
            .delete(AppConfig.GOLD_STANDARD_TAPE_WIDTH + '/' + goldStandardTapeWidthId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Gold Wire Tapes Master data
    getGoldWireTapesMaster(goldWireTapes, pageableObject): Observable<GenericPageable<BHXGoldWireTapesMaster>> {
        return this.http
            .post(AppConfig.BHX_GOLD_WIRE_TAPE_PAGEABLE, goldWireTapes, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Gold Wire Tapes Master data
    updateGoldWireTapes(goldWireTapes) {
        return this.http.put(AppConfig.BHX_GOLD_WIRE_TAPE, goldWireTapes).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Gold Wire Tapes Master data
    addGoldWireTapes(goldWireTapes) {
        return this.http.post(AppConfig.BHX_GOLD_WIRE_TAPE, goldWireTapes).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Gold Wire Tapes Master data
    deleteGoldWireTapes(goldWireTapesId: number) {
        return this.http
            .delete(AppConfig.BHX_GOLD_WIRE_TAPE + '/' + goldWireTapesId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets alloy master data
    getAlloysMaster(alloy, pageableObject): Observable<GenericPageable<AlloyMaster>> {
        return this.http
            .post(AppConfig.ALLOYS_PAGEABLE, alloy, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update alloy master data
    updateAlloys(alloy) {
        return this.http.put(AppConfig.ALLOYS, alloy).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new alloy master data
    addAlloys(alloy) {
        return this.http.post(AppConfig.ALLOYS, alloy).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove alloy master data
    deleteAlloys(alloyId: number) {
        return this.http
            .delete(AppConfig.ALLOYS + '/' + alloyId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to get all the alloy master data
    getAlloysList() {
        return this.http.get(AppConfig.ALLOYS).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets the Heating Tapes Master data
    getHeatingTapesMaster(heatingTapes, pageableObject): Observable<GenericPageable<HeatingTapesMaster>> {
        return this.http
            .post(AppConfig.HEATING_TAPES_PAGEABLE, heatingTapes, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Heating Tapes Master data
    updateHeatingTapes(heatingTapes) {
        return this.http.put(AppConfig.HEATING_TAPES, heatingTapes).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Heating Tapes Master data
    addHeatingTapes(heatingTapes) {
        return this.http.post(AppConfig.HEATING_TAPES, heatingTapes).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Heating Tapes Master data
    deleteHeatingTapes(heatingTapesId: number) {
        return this.http
            .delete(AppConfig.HEATING_TAPES + '/' + heatingTapesId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to validate the heating tape partnumber
    validateHeatingTapePartNumber(heatingTapePartNumber: string) {
        return this.http
            .get(AppConfig.HEATING_TAPES_PARTNUMBER_VALIDATER + heatingTapePartNumber).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to generate heating tape partnumber
    generateHeatingTapePartNumber(tapeType: string) {
        return this.http
            .get(AppConfig.HEATING_TAPES_PARTNUMBER_GENERATOR + tapeType).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Departments Master data
    getDepartmentsMaster(department, pageableObject): Observable<GenericPageable<DepartmentsMaster>> {
        return this.http
            .post(AppConfig.DEPARTMENTS_PAGEABLE, department, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Departments Master data
    updateDepartment(department) {
        return this.http.put(AppConfig.DEPARTMENTS, department).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Departments Master data
    addDepartment(department) {
        return this.http.post(AppConfig.DEPARTMENTS, department).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Departments Master data
    deleteDepartment(departmentId: number) {
        return this.http
            .delete(AppConfig.DEPARTMENTS + '/' + departmentId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets ECR Statuses Master data
    getECRStatusesMaster(department, pageableObject): Observable<GenericPageable<ECRStatusesMaster>> {
        return this.http
            .post(AppConfig.ECR_STATUSES_PAGEABLE, department, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update ECR Statuses Master data
    updateECRStatus(department) {
        return this.http.put(AppConfig.ECR_STATUSES, department).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new ECR Statuses Master data
    addECRStatus(department) {
        return this.http.post(AppConfig.ECR_STATUSES, department).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove ECR Statuses Master data
    deleteECRStatus(departmentId: number) {
        return this.http
            .delete(AppConfig.ECR_STATUSES + '/' + departmentId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // get the BHX material master data
    getBHXMaterialsMaster(material, pageableObject): Observable<GenericPageable<BHXMaterialMaster>> {
        return this.http
            .post(AppConfig.BHX_MATERIAL_PAGEABLE, material, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update BHX material master data
    updateBHXMaterial(material) {
        return this.http.put(AppConfig.BHX_MATERIAL, material).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new BHX material master data
    addBHXMaterial(material) {
        return this.http.post(AppConfig.BHX_MATERIAL, material).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove BHX material master data
    deleteBHXMaterial(materialId: number) {
        return this.http
            .delete(AppConfig.BHX_MATERIAL + '/' + materialId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // get all the available material list
    getMaterialsList() {
        return this.http.get(AppConfig.GET_MATERIAL_LAYER).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets Sensor Connectors Master data
    getSensorConnectorsMaster(sensorsConnectors, pageableObject): Observable<GenericPageable<SensorConnectorsAndTypesMaster>> {
        return this.http
            .post(AppConfig.SENSOR_CONNECTORS_PAGEABLE, sensorsConnectors, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Sensor Connectors Master data
    updateSensorConnectors(sensorsConnectors) {
        return this.http.put(AppConfig.SENSOR_CONNECTORS, sensorsConnectors).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Sensor Connectors Master data
    addSensorConnectors(sensorsConnectors) {
        return this.http.post(AppConfig.SENSOR_CONNECTORS, sensorsConnectors).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Sensor Connectors Master data
    deleteSensorConnectors(sensorsConnectorsId: number) {
        return this.http
            .delete(AppConfig.SENSOR_CONNECTORS + '/' + sensorsConnectorsId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets all the list of Sensor Connectors Master data
    getSensorConnectorsList() {
        return this.http.get(AppConfig.SENSOR_CONNECTORS).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets the Sensor Types Master data
    getSensorTypesMaster(sensorTypes, pageableObject): Observable<GenericPageable<SensorConnectorsAndTypesMaster>> {
        return this.http
            .post(AppConfig.SENSOR_TYPES_PAGEABLE, sensorTypes, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // updates the Sensor Types Master data
    updateSensorTypes(sensorTypes) {
        return this.http.put(AppConfig.SENSOR_TYPES, sensorTypes).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Sensor Types Master data
    addSensorTypes(sensorTypes) {
        return this.http.post(AppConfig.SENSOR_TYPES, sensorTypes).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Sensor Types Master data
    deleteSensorTypes(sensorTypesId: number) {
        return this.http
            .delete(AppConfig.SENSOR_TYPES + '/' + sensorTypesId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to get all the list of Sensor Types List master data
    getSensorTypesList() {
        return this.http.get(AppConfig.SENSOR_TYPES).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // gets the Power Cord Materials Master
    getPowerCordMaterialsMaster(powerCordMaterial, pageableObject): Observable<GenericPageable<PowerCordMaterialsMaster>> {
        return this.http
            .post(AppConfig.POWER_CORD_MATERIALS_PAGEABLE, powerCordMaterial, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }


    getCCDCMaster(CcdcMaterial, pageableObject) {
        return this.http
            .post(AppConfig.CCDC_MASTER_DATA_PAGEABLE, CcdcMaterial, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Power Cord Materials Master data
    updatePowerCordMaterials(powerCordMaterial) {
        return this.http.put(AppConfig.POWER_CORD_MATERIALS, powerCordMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    getPartNumberMaster(partNumberMaster, pageableObject) {
        return this.http
            .post(AppConfig.GET_PART_NUMBER_MASTER, partNumberMaster, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to add new Power Cord Materials Master data
    addPowerCordMaterials(powerCordMaterial) {
        return this.http.post(AppConfig.POWER_CORD_MATERIALS, powerCordMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Power Cord Materials Master data record
    deletePowerCordMaterials(powerCordMaterialId: number) {
        return this.http
            .delete(AppConfig.POWER_CORD_MATERIALS + '/' + powerCordMaterialId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Power Cord Voltage Master data
    getPowerCordVoltageMaster(powerCordMaterial, pageableObject): Observable<GenericPageable<PowerCordVoltagesMaster>> {
        return this.http
            .post(AppConfig.POWER_CORD_VOLTAGES_PAGEABLE, powerCordMaterial, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update the Power Cord Voltage Master data
    updatePowerCordVoltage(powerCordMaterial) {
        return this.http.put(AppConfig.POWER_CORD_VOLTAGES, powerCordMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new the Power Cord Voltage Master data
    addPowerCordVoltage(powerCordMaterial) {
        return this.http.post(AppConfig.POWER_CORD_VOLTAGES, powerCordMaterial).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove the Power Cord Voltage Master data
    deletePowerCordVoltage(powerCordMaterialId: number) {
        return this.http
            .delete(AppConfig.POWER_CORD_VOLTAGES + '/' + powerCordMaterialId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Power Cord Amps Master data
    getPowerCordAmpsMaster(powerCordAmps, pageableObject): Observable<GenericPageable<PowerCordAmpsMaster>> {
        return this.http
            .post(AppConfig.POWER_CORD_AMPS_PAGEABLE, powerCordAmps, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update the Power Cord Amps Master data
    updatePowerCordAmps(powerCordAmps) {
        return this.http.put(AppConfig.POWER_CORD_AMPS, powerCordAmps).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Power Cord Amps Master data
    addPowerCordAmps(powerCordAmps) {
        return this.http.post(AppConfig.POWER_CORD_AMPS, powerCordAmps).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove the Power Cord Amps Master data
    deletePowerCordAmps(powerCordAmpsId: number) {
        return this.http
            .delete(AppConfig.POWER_CORD_AMPS + '/' + powerCordAmpsId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Power Cord Options Master data
    getPowerCordOptionsMaster(powerCordOptions, pageableObject): Observable<GenericPageable<PowerCordOptionsMaster>> {
        return this.http
            .post(AppConfig.POWER_CORD_OPTIONS_PAGEABLE, powerCordOptions, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update the Power Cord Options Master data
    updatePowerCordOptions(powerCordOptions) {
        return this.http.put(AppConfig.POWER_CORD_OPTIONS, powerCordOptions).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Power Cord Options Master data
    addPowerCordOptions(powerCordOptions) {
        return this.http.post(AppConfig.POWER_CORD_OPTIONS, powerCordOptions).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove the Power Cord Options Master data
    deletePowerCordOptions(powerCordOptionsId: number) {
        return this.http
            .delete(AppConfig.POWER_CORD_OPTIONS + '/' + powerCordOptionsId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets Sensor Control Types Master data
    getSensorControlTypesMaster(sensorControlType, pageableObject): Observable<GenericPageable<SensorConnectorsAndTypesMaster>> {
        return this.http
            .post(AppConfig.SENSOR_CONTROL_TYPES_PAGEABLE, sensorControlType, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Sensor Control Types Master data
    updateSensorControlTypes(sensorControlType) {
        return this.http.put(AppConfig.SENSOR_CONTROL_TYPES, sensorControlType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Sensor Control Types Master data
    addSensorControlTypes(sensorControlType) {
        return this.http.post(AppConfig.SENSOR_CONTROL_TYPES, sensorControlType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Sensor Control Types Master data
    deleteSensorControlTypes(sensorControlTypeId: number) {
        return this.http
            .delete(AppConfig.SENSOR_CONTROL_TYPES + '/' + sensorControlTypeId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets Warps Master data
    getWarpsMaster(warps, pageableObject): Observable<GenericPageable<WarpsMaster>> {
        return this.http
            .post(AppConfig.WARPS_PAGEABLE, warps, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

  getEstEngRelDate(warps, pageableObject): Observable<GenericPageable<EstEngRelDateMaster>> {
    return this.http
      .post(AppConfig.EST_ENG_REL_DATE_PAGEABLE, warps, {
        params: createRequestOption(pageableObject)
      }).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

  updateEstEngRelDate(warps) {
    return this.http.put(AppConfig.EST_ENG_REL_DATE, warps).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  addEstEngRelDate(warps) {
    return this.http.post(AppConfig.EST_ENG_REL_DATE, warps).pipe(map(utils.extractData), catchError(utils.handleError),);
  }

  deleteEstEngRelDate(warpId: number) {
    return this.http
      .delete(AppConfig.EST_ENG_REL_DATE + '/' + warpId).pipe(
        map(utils.extractData),
        catchError(utils.handleError),);
  }

    // used to update Warps Master data
    updateWarps(warps) {
        return this.http.put(AppConfig.WARPS, warps).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Warps Master data
    addWarps(warps) {
        return this.http.post(AppConfig.WARPS, warps).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Warps Master data
    deleteWarps(warpId: number) {
        return this.http
            .delete(AppConfig.WARPS + '/' + warpId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Thermostat Types Master data
    getThermostatTypesMaster(thermostatType, pageableObject): Observable<GenericPageable<LaborMaster>> {
        return this.http
            .post(AppConfig.THERMOSTAT_TYPES_PAGEABLE, thermostatType, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the Thermostat Types Master data
    getLaborMaster(): Observable<LaborMaster[]> {
        return this.http
            .get(AppConfig.LABOR_MASTER).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update Thermostat Types Master data
    updateLaborMaster(laborMaster: LaborMaster) {
        return this.http.put(AppConfig.LABOR_MASTER, laborMaster).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Thermostat Types Master data
    addLaborMaster(laborMaster: LaborMaster) {
        return this.http.post(AppConfig.LABOR_MASTER, laborMaster).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to update Thermostat Types Master data
    updateThermostatType(thermostatType) {
        return this.http.put(AppConfig.THERMOSTAT_TYPES, thermostatType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new Thermostat Types Master data
    addThermostatType(thermostatType) {
        return this.http.post(AppConfig.THERMOSTAT_TYPES, thermostatType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to remove Thermostat Types Master data
    deleteThermostatType(thermostatTypeId: string) {
        return this.http
            .delete(AppConfig.THERMOSTAT_TYPES + '/' + thermostatTypeId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to remove Thermostat Types Master data
    deleteLaborMaster(id: string) {
        return this.http
            .delete(AppConfig.LABOR_MASTER + '/' + id).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // gets the lead type master data as pageable also used to filter the master data
    getLeadTypesMaster(leadType, pageableObject): Observable<GenericPageable<LeadTypesMaster>> {
        return this.http
            .post(AppConfig.LEAD_TYPES_PAGEABLE, leadType, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to update the lead type master data record, takes the lead type dto
    updateLeadType(leadType): Observable<GenericPageable<LeadTypesMaster>> {
        return this.http.put(AppConfig.LEAD_TYPES, leadType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to add new lead type master data
    addLeadType(leadType): Observable<GenericPageable<LeadTypesMaster>> {
        return this.http.post(AppConfig.LEAD_TYPES, leadType).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to delete lead type master data record using leadTypeId
    deleteLeadType(leadTypeId: number) {
        return this.http
            .delete(AppConfig.LEAD_TYPES + '/' + leadTypeId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    // used to get the available installation methods
    getInstallationMethods() {
        return this.http.get(AppConfig.THERMOSTAT_INSTALLATION_METHOD).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to get all available plug light master
    getPluginMasterData() {
        return this.http.get(AppConfig.GET_PLUGIN_MASTER_DATA).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // used to get the master data for available lead types
    getLeadTypeMaster() {
        return this.http.get(AppConfig.LEAD_TYPE_MASTER).pipe(map(utils.extractData), catchError(utils.handleError),);
    }

    // takes Temperature type C or F and converts the Temperature from celcius to fahrenheit and vice-a-versa
    convertTemperature(temperatureValue: number, temperatureType: string): number {
        if (temperatureType === 'celcius') {
            return Number(((temperatureValue * 9) / 5 + 32).toFixed(2));
        }
        return Number((((temperatureValue - 32) * 5) / 9).toFixed(2));
    }

    // takes Temperature tolerance in C or F and converts the Temperature tolerance from celcius to fahrenheit and vice-a-versa
    convertTemperatureTolerance(temperatureValue: number, temperatureType: string): number {
        if (temperatureType === 'celcius') {
            return Number(((temperatureValue * 9) / 5).toFixed(2));
        }
        return Number(((temperatureValue * 5) / 9).toFixed(2));
    }

    // validates the equation created by the user on generate equation screen that whether it is valid or not
    equationValidator(equation: string) {
        return this.http
            .post(AppConfig.EXPRESSION_VALIDATOR + equation, {}).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }


    // takes the equation string and converts it to user readble form from the pre-defined const value. converts them to `CLOSURE LENGTH|LEAD LENGTH|SMALL BAR|THERMOSTAT COUNT|JUMPER QTY|BIG BAR|JACKET LENGTH|JUMPER LEAD LENGTH|DOUBLE BAR|TAPE THREAD`
    equationFormatterFromConstToFullName(equation: string): string {
        const Formula = Values.BHXMaterialFormulaConst;
        if (equation) {
            return equation.replace(/A|B|C|D|E|F|G|H|I|Z|LAYER|J|K|L|M|N|VCR12|VCR14|REMOVABLE_LAYER|P|ROUND_DOWN|ROUND_UP/gi, function (matched) {
                return Formula[matched];
            });
        }
    }

    // takes the equation string and converts it to user readble form from the pre-defined const value which are `A|B|C|D|E|F|G|H|I|Z` as backend expect const values while storing and manipulating the formula
    equationFormatterFromFullNameToConst(equation: string): string {
        const Formula = Values.BHXMaterialFormulaFullName;
        if (equation) {
            return equation.replace(
                /CLOSURE LENGTH|LEAD LENGTH|SMALL BAR|THERMOSTAT COUNT|JUMPER QTY|BIG BAR|JACKET LENGTH|JUMPER LEAD LENGTH|DOUBLE BAR|TAPE THREAD|LAYER|WELDMENT LENGTH|WELDMENT DIAMETER|Silicone Width|QTR VCR|HALF VCR|REMOVABLE LAYER|REMOVABLE QTY|ROUND_UP|ROUND_DOWN/gi,
                function (matched) {
                    return Formula[matched];
                }
            );
        }
    }

    getLabelConfigMasterData(labelConfigurationMaster, pageableObject) {
        return this.http
            .post(AppConfig.LABEL_CONFIG_MASTER_API + '/filter', labelConfigurationMaster, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    saveLabelConfigurationMaster(labelConfigurationMaster) {
        return this.http
            .post(AppConfig.LABEL_CONFIG_MASTER_API, labelConfigurationMaster).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    updateLabelConfigurationMaster(labelConfigurationMaster) {
        return this.http
            .put(AppConfig.LABEL_CONFIG_MASTER_API, labelConfigurationMaster).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    deleteLabelConfigurationMaster(labelConfigurationMasterId: number) {
        return this.http
            .delete(AppConfig.LABEL_CONFIG_MASTER_API + '/' + labelConfigurationMasterId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    getSolidWorksMasterData(filter, pageableObject) {
        return this.http
            .post(AppConfig.SOLID_WORKS_BLOCK + 'filter', filter, {
                params: createRequestOption(pageableObject)
            }).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    addSolidWorksBlockMaster(solidWorksBlockMaster: SolidWorksBlockMasterDTO) {
        return this.http
            .post(AppConfig.SOLID_WORKS_BLOCK, solidWorksBlockMaster).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    updateSolidWorksBlockMaster(solidWorksBlockMaster: SolidWorksBlockMasterDTO) {
        return this.http
            .put(AppConfig.SOLID_WORKS_BLOCK, solidWorksBlockMaster).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

    deleteSolidWorksMaster(blockId: number) {
        return this.http
            .delete(AppConfig.SOLID_WORKS_BLOCK + blockId).pipe(
                map(utils.extractData),
                catchError(utils.handleError),);
    }

}
