<h2 mat-dialog-title>
  Add Operation
  <hr/>
</h2>
<form #operationForm="ngForm" role="form">
  <mat-dialog-content>
    <mat-form-field>
      <mat-select
        placeholder="Select"
        name="operation"
        [(ngModel)]="selection"
        (selectionChange)="onOperationSelect($event.value)"
        required
      >
        <mat-option *ngFor="let operation of operationList; let i = index" [value]="i"> {{ operation.operationName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Sequence" [(ngModel)]="operationDetails.sequence" name="sequence"
               autocomplete="off"/>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Op. Number" [(ngModel)]="operationDetails.opNumber" name="opNumber"
               autocomplete="off"/>
      </mat-form-field>
    </div>
    <div>
      <mat-form-field>
        <input
          matInput
          placeholder="Operation"
          [(ngModel)]="operationDetails.operationName"
          name="operationName"
          autocomplete="off"
          readonly
        />
      </mat-form-field>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between">
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Prod Hrs" [(ngModel)]="operationDetails.prodHrs" name="prodHrs"
               autocomplete="off"/>
      </mat-form-field>
      <mat-form-field fxFlex.gt-lg="49" fxFlex.gt-md="49">
        <input matInput placeholder="Setup Hrs" [(ngModel)]="operationDetails.setupHrs" name="setupHrs"
               autocomplete="off"/>
      </mat-form-field>
    </div>
  </mat-dialog-content>
  <hr/>
  <mat-dialog-actions>
    <button mat-raised-button color="warn" type="submit" [disabled]="operationForm.invalid" (click)="saveOperaton()">Add
      Operation
    </button>
    <button mat-raised-button type="submit" (click)="closeDialog()">Cancel</button>
  </mat-dialog-actions>
</form>
