import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageSensorConnectorComponent } from './manage-sensor-connector.component';

describe('ManageSensorConnectorComponent', () => {
  let component: ManageSensorConnectorComponent;
  let fixture: ComponentFixture<ManageSensorConnectorComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ManageSensorConnectorComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageSensorConnectorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
