
import {catchError, map} from 'rxjs/operators';
import { Revision } from '../ccdc-model/ccdc.model';
import { AppConfig } from '../../../app.config';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { utils } from '../../../shared/helpers/app.helper';

@Injectable()
export class ManageRevisionsService {

    constructor(private http: HttpClient) { }

    getRevisionsByQuotationId(quotationId: number) {
        return this.http.get(AppConfig.GET_REVISIONS + '/quotation/' + quotationId).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }

    createRevision(revision: Revision) {
        return this.http.post(AppConfig.GET_REVISIONS + '/copy', revision).pipe(map(utils.extractData),
            catchError(utils.handleError),);
    }
}
