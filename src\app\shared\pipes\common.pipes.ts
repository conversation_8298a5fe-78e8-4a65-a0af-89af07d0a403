import { Pipe, PipeTransform } from '@angular/core';
import { pipe } from '@angular/core/src/render3';
import { Status } from 'src/app/admin-pages/dashboard/dashboard.model';
import { AccountManager } from 'src/app/admin-pages/new-quotation/ccdc-model/ccdc.model';
import { SalesAssociate } from 'src/app/design-pages/dashboard/dashboard.model';
import { Values } from '../constants/values.constants';

@Pipe({
  name: 'approvalFormats'
})
export class ApprovalFormatPipe implements PipeTransform {
  transform(value) {
    if (value) {
      let approvalFormats = '';
      for (let i = 0; i < value.length; i++) {
        approvalFormats = Values.approvalFormats.find(data => data.approvalLevel === value[i].approvalLevel).name + ', ' + approvalFormats;
      }
      return approvalFormats.substring(0, approvalFormats.length - 2);
    } else {
      return '';
    }
  }
}

@Pipe({
  name: 'markings'
})
export class MarkingsPipe implements PipeTransform {
  transform(value) {
    if (value) {
      let markings = '';
      for (let i = 0; i < value.length; i++) {
        markings = Values.markings.find(data => data.markings === value[i].markings).name + ', ' + markings;
      }
      return markings.substring(0, markings.length - 2);
    } else {
      return '';
    }
  }
}

@Pipe({
  name: 'approvalLevel'
})
export class ApprovalLevelPipe implements PipeTransform {
  transform(value: string) {
    return Values.approvalLevels.find(data => data.value === value).name;
  }
}

@Pipe({
  name: 'productType'
})
export class ProductTypePipe implements PipeTransform {
  transform(value: string) {
    return value != null ? Values.CCDC_WorkFlow_ProductTypeConst.find(data => data.id === value).value : '';
  }
}

@Pipe({
  name: 'thermostatType'
})
export class ThermostatTypePipe implements PipeTransform {
  transform(value) {
    if (value) {
      let thermostat = '';
      for (let i = 0; i < value.length; i++) {
        thermostat = !value[i].thermostatType
          ? value[i].otherThermostatType && value[i].otherThermostatType.id + ' | ' + thermostat
          : value[i].thermostatType.id + ' | ' + thermostat;
      }
      return thermostat.substring(0, thermostat.length - 2);
    } else {
      return '';
    }
  }
}

@Pipe({
  name: 'sensors'
})
export class SensorsPipe implements PipeTransform {
  transform(sensorInfo: any[]) {
    let sensorArray = [];
    if (sensorInfo && sensorInfo.length > 0) {
      sensorArray = this.prepareSensorType(sensorInfo);
      return sensorArray;
    }
  }
  prepareSensorType(sensorInfo: any[]): any[] {
    let sensorString = '';
    const sensorTypeArray = [];
    for (let i = 0; i < sensorInfo.length; i++) {
      sensorString = !sensorInfo[i].sensorType
        ? (sensorInfo[i].sensorType = '- -')
        : sensorInfo[i].otherSensorType
          ? sensorInfo[i].otherSensorType.id
          : sensorInfo[i].sensorType.id;
      sensorString += ' | ';
      sensorString +=
        sensorInfo[i].sensorLocation === null
          ? (sensorInfo[i].sensorLocation = '- -')
          : sensorInfo[i].sensorLocation === 'Other'
            ? (sensorInfo[i].sensorLocation = sensorInfo[i].otherSensorLocation)
            : sensorInfo[i].sensorLocation;
      sensorString += ' | ';
      sensorString += !sensorInfo[i].sensorConnector
        ? (sensorInfo[i].sensorConnector = '- -')
        : sensorInfo[i].otherSensorConnector
          ? sensorInfo[i].otherSensorConnector.id
          : sensorInfo[i].sensorConnector.id;
      sensorString += ' | ';
      sensorString += sensorInfo[i].sensorLeadLength === null ? '- -' : sensorInfo[i].sensorLeadLength;

      sensorTypeArray.push(sensorString);
    }
    return sensorTypeArray;
  }
}

@Pipe({
  name: 'material'
})
export class MaterialPipe implements PipeTransform {
  transform(materialInfo: any[]) {
    let materialArray = [];
    if (materialInfo && materialInfo.length > 0) {
      materialArray = this.prepareMaterialInfo(materialInfo);
      return materialArray;
    }
  }

  prepareMaterialInfo(materialInfo: any[]): any[] {
    let materialString = '';
    const materialInfoArray = [];
    for (let i = 0; i < materialInfo.length; i++) {
      materialString = materialInfo[i].layerName;
      materialString += ' | ';
      materialString +=
        materialInfo[i].layerName === 'Facing'
          ? materialInfo[i].material === 'Other'
            ? materialInfo[i].otherFacing === null
              ? '--'
              : materialInfo[i].otherFacing
            : materialInfo[i].material
          : materialInfo[i].layerName === 'Liner'
            ? materialInfo[i].material === 'Other'
              ? materialInfo[i].otherLiner === null
                ? '--'
                : materialInfo[i].otherLiner
              : materialInfo[i].material
            : materialInfo[i].layerName === 'Insulation'
              ? materialInfo[i].material === 'Other'
                ? materialInfo[i].otherInsulation === null
                  ? '--'
                  : materialInfo[i].otherInsulation
                : materialInfo[i].material
              : materialInfo[i].material;
      materialInfoArray.push(materialString);
    }
    return materialInfoArray;
  }
}

// converts array values to comma seperated values example: array [a,b,c] = 'a,b,c'
@Pipe({
  name: 'arrayToCSV'
})
export class ArrayToCSVPipe implements PipeTransform {
  // takes array and filed name using which to extract that field out of array of object to form a string value
  transform(arrayValue: any[], fieldName = '') {
    let csvString = '';
    if (arrayValue.length > 0 && fieldName) {
      csvString = arrayValue.map(element => element[fieldName]).toString();
      return csvString;
    } else if (arrayValue.length > 0) {
      csvString = arrayValue.toString();
      return csvString;
    }
  }
}

// converts given value either boolean or string accepted to the string 'Yes | No' in return for display purpose
@Pipe({
  name: 'convertToYesNo'
})
export class ConvertToYesNoPipe implements PipeTransform {
  transform(boolValue: boolean | string): string {
    return boolValue === true || boolValue === 'true' ? 'Yes' : 'No';
  }
}

// converts given user id to first name and last name/ full name
@Pipe({
  name: 'getFirstNameLastName'
})
export class GetFullUserName implements PipeTransform {
  transform(id: number, users: SalesAssociate[]): string {
    if (id && users && users.length) {
      const name = users.find(user => user.id === id);
      return name ? name.firstName + ' ' + name.lastName : '';
    }
    return '';
  }
}

@Pipe({
  name: 'getFirstNameLastNameDesign'
})
export class GetFirstNameDesignQuoteTracker implements PipeTransform {
  transform(id: number, users: SalesAssociate[]): string {
    if (id && users && users.length) {
      const name = users.find(user => user.id === id);
      return name ? name.firstName : '';
    }
    return '';
  }
}

// converts given quotation status id to its quote status name
@Pipe({
  name: 'getQuoteStatusName'
})
export class GetQuoteStatusName implements PipeTransform {
  transform(quoteId: number, quoteStatuses: Status[]): string {
    if (quoteId && quoteStatuses.length) {
      const name = quoteStatuses.find(quotes => quotes.id === quoteId);
      if (name) {
        return name.status;
      }
      return '';
    }
    return '';
  }
}

// converts given account manager id to name
@Pipe({
  name: 'getAccountManagerName'
})
export class GetAccountManagerName implements PipeTransform {
  transform(id: number, users: AccountManager[]): string {
    if (id && users && users.length) {
      const accountMgr = users.find(user => user.id === id);
      return accountMgr ? accountMgr.name : '';
    }
    return '';
  }
}


// sorts the dropdown alphabetically
@Pipe({
  name: 'orderBy'
})
export class SortCcdcDropdown implements PipeTransform {
  transform(sortOrder: Array<string>, value: string): Array<string> {
    sortOrder.sort((a: any, b: any) => {
	    if ( a[value] < b[value] ){
	    	return -1;
	    }else if( a[value] > b[value] ){
	        return 1;
	    }else{
	    	return 0;
	    }
    });
    return sortOrder;
  }
}
